# XQ360 Data Migration - Complete Remote Server Status Fix
# This script fixes all common SignalR and status update issues

param(
    [string]$ServerName = "localhost",
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [string]$AppPoolName = "XQ360MigrationPool"
)

Write-Host "XQ360 Data Migration - Complete Remote Server Status Fix" -ForegroundColor Green
Write-Host "Target Server: $ServerName" -ForegroundColor Yellow
Write-Host "Deploy Path: $DeployPath" -ForegroundColor Yellow

# Step 1: Enable WebSocket Protocol (if not already enabled)
Write-Host "`n=== ENABLING WEBSOCKET PROTOCOL ===" -ForegroundColor Cyan
try {
    $webSocketFeature = Get-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -ErrorAction SilentlyContinue
    if (-not $webSocketFeature -or $webSocketFeature.State -ne "Enabled") {
        Write-Host "Enabling WebSocket Protocol..." -ForegroundColor Yellow
        Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -NoRestart
        Write-Host "✅ WebSocket Protocol enabled" -ForegroundColor Green
    } else {
        Write-Host "✅ WebSocket Protocol already enabled" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Could not enable WebSocket Protocol: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "You may need to run this script as Administrator" -ForegroundColor Yellow
}

# Step 2: Create logs directory with proper permissions
Write-Host "`n=== SETTING UP LOGS DIRECTORY ===" -ForegroundColor Cyan
$logsPath = Join-Path $DeployPath "logs"
if (-not (Test-Path $logsPath)) {
    New-Item -ItemType Directory -Path $logsPath -Force
    Write-Host "✅ Created logs directory: $logsPath" -ForegroundColor Green
} else {
    Write-Host "✅ Logs directory already exists: $logsPath" -ForegroundColor Green
}

# Set comprehensive permissions
try {
    $acl = Get-Acl $logsPath
    
    # Add IIS_IUSRS permissions
    $rule1 = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($rule1)
    
    # Add NETWORK SERVICE permissions (alternative identity)
    $rule2 = New-Object System.Security.AccessControl.FileSystemAccessRule("NETWORK SERVICE", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($rule2)
    
    # Add ApplicationPoolIdentity permissions
    $rule3 = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS AppPool\$AppPoolName", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($rule3)
    
    Set-Acl $logsPath $acl
    Write-Host "✅ Set comprehensive permissions on logs directory" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not set permissions on logs directory: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 3: Backup and update web.config
Write-Host "`n=== UPDATING WEB.CONFIG ===" -ForegroundColor Cyan
$webConfigPath = Join-Path $DeployPath "web.config"
$backupPath = Join-Path $DeployPath "web.config.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"

if (Test-Path $webConfigPath) {
    Copy-Item $webConfigPath $backupPath
    Write-Host "✅ Backed up web.config to: $backupPath" -ForegroundColor Green
} else {
    Write-Host "⚠️  No existing web.config found at: $webConfigPath" -ForegroundColor Yellow
}

# Copy the ultra minimal web.config
$ultraMinimalWebConfig = Join-Path $PSScriptRoot "ultra-minimal-remote-web.config"
if (Test-Path $ultraMinimalWebConfig) {
    Copy-Item $ultraMinimalWebConfig $webConfigPath -Force
    Write-Host "✅ Updated web.config with ultra minimal fixes" -ForegroundColor Green
} else {
    Write-Host "❌ Ultra minimal web.config not found at: $ultraMinimalWebConfig" -ForegroundColor Red
    exit 1
}

# Step 4: Configure application pool for SignalR
Write-Host "`n=== CONFIGURING APPLICATION POOL ===" -ForegroundColor Cyan
try {
    Import-Module WebAdministration
    
    # Set application pool to .NET CLR Version "No Managed Code" for ASP.NET Core
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "managedRuntimeVersion" -Value ""
    Write-Host "✅ Set application pool to No Managed Code" -ForegroundColor Green
    
    # Enable 32-bit applications (if needed)
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "enable32BitAppOnWin64" -Value $false
    Write-Host "✅ Disabled 32-bit applications" -ForegroundColor Green
    
    # Set idle timeout to prevent recycling during long migrations
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"
    Write-Host "✅ Disabled idle timeout" -ForegroundColor Green
    
    # Set ping period to prevent premature recycling
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.pingPeriod" -Value "00:01:30"
    Write-Host "✅ Set ping period to 90 seconds" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Could not configure application pool: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 5: Restart application pool
Write-Host "`n=== RESTARTING APPLICATION POOL ===" -ForegroundColor Cyan
try {
    Restart-WebAppPool -Name $AppPoolName
    Write-Host "✅ Application pool '$AppPoolName' restarted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restart application pool: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please restart the application pool manually in IIS Manager." -ForegroundColor Yellow
}

# Step 6: Wait and test connectivity
Write-Host "`n=== TESTING CONNECTIVITY ===" -ForegroundColor Cyan
Start-Sleep -Seconds 10  # Wait for app pool to restart

try {
    $testUrl = "http://$ServerName/"
    $response = Invoke-WebRequest -Uri $testUrl -Method GET -TimeoutSec 15 -ErrorAction Stop
    Write-Host "✅ Application is accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Application test failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 7: Test SignalR hub
try {
    $signalRUrl = "http://$ServerName/migrationHub"
    $response = Invoke-WebRequest -Uri $signalRUrl -Method GET -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ SignalR hub is accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "⚠️  SignalR hub test failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "This may be normal if WebSocket is not fully configured yet" -ForegroundColor Yellow
}

# Step 8: Verify logs directory permissions
Write-Host "`n=== VERIFYING LOGS DIRECTORY ===" -ForegroundColor Cyan
if (Test-Path $logsPath) {
    try {
        $testFile = Join-Path $logsPath "test-write.tmp"
        "Test write access $(Get-Date)" | Out-File -FilePath $testFile -Encoding UTF8
        Remove-Item $testFile -Force
        Write-Host "✅ Logs directory is writable" -ForegroundColor Green
    } catch {
        Write-Host "❌ Logs directory is not writable: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please check permissions manually in IIS Manager" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Logs directory does not exist: $logsPath" -ForegroundColor Red
}

# Step 9: Display comprehensive summary
Write-Host "`n=== COMPLETE FIX SUMMARY ===" -ForegroundColor Cyan
Write-Host "✅ Enabled WebSocket Protocol at server level" -ForegroundColor Green
Write-Host "✅ Updated web.config with ultra minimal SignalR and logging fixes" -ForegroundColor Green
Write-Host "✅ Created logs directory with comprehensive permissions" -ForegroundColor Green
Write-Host "✅ Configured application pool for ASP.NET Core" -ForegroundColor Green
Write-Host "✅ Restarted application pool" -ForegroundColor Green
Write-Host "✅ Tested basic connectivity" -ForegroundColor Green

Write-Host "`nKey changes made:" -ForegroundColor Yellow
Write-Host "  • Enabled WebSocket Protocol (required for SignalR)" -ForegroundColor White
Write-Host "  • Enabled stdoutLogEnabled='true'" -ForegroundColor White
Write-Host "  • Changed hostingModel to 'outofprocess'" -ForegroundColor White
Write-Host "  • Set application pool to 'No Managed Code'" -ForegroundColor White
Write-Host "  • Disabled idle timeout to prevent recycling" -ForegroundColor White
Write-Host "  • Set comprehensive permissions on logs directory" -ForegroundColor White

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test the migration status updates in the web interface" -ForegroundColor White
Write-Host "2. Check browser console for any WebSocket connection errors" -ForegroundColor White
Write-Host "3. Monitor logs in real-time: Get-Content '$logsPath\stdout_*.log' -Wait" -ForegroundColor White
Write-Host "4. If issues persist, run the diagnostic script: .\check-remote-signalr-status.ps1" -ForegroundColor White

Write-Host "`n✅ Complete remote server status fix completed!" -ForegroundColor Green 