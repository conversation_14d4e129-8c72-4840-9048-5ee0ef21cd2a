using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Configuration;
using FleetXQ.Tools.BulkImporter.Logging;

namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Main service for bulk import operations
/// </summary>
public class BulkImportService : IBulkImportService
{
    private readonly ILogger<BulkImportService> _logger;
    private readonly BulkImporterOptions _options;
    private readonly ISqlDataGenerationService _sqlDataService;

    public BulkImportService(
        ILogger<BulkImportService> logger,
        IOptions<BulkImporterOptions> options,
        ISqlDataGenerationService sqlDataService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _sqlDataService = sqlDataService ?? throw new ArgumentNullException(nameof(sqlDataService));
    }

    /// <summary>
    /// Executes the bulk import operation
    /// </summary>
    public async Task<ImportResult> ExecuteImportAsync(ImportOptions options, CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid().ToString("N")[..8];
        CorrelationContext.CreateOperationId($"IMPORT_{sessionId}");

        _logger.LogInformation("Starting bulk import operation {SessionId}", sessionId);

        var startTime = DateTime.UtcNow;
        var result = new ImportResult
        {
            SessionId = sessionId
        };

        try
        {
            // Validate options
            ValidateImportOptions(options);

            // Apply defaults
            ApplyDefaultOptions(options);

            _logger.LogInformation("Import options: Drivers={DriversCount}, Vehicles={VehiclesCount}, BatchSize={BatchSize}, DryRun={DryRun}, Generate={GenerateData}",
                options.DriversCount, options.VehiclesCount, options.BatchSize, options.DryRun, options.GenerateData);

            if (options.DryRun)
            {
                _logger.LogInformation("DRY RUN MODE - No data will be modified");
            }

            // Execute SQL-based import operation
            await ExecuteSqlImportAsync(options, result, cancellationToken);

            result.Success = true;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Import completed successfully. Processed {result.ProcessedRows} rows in {result.Duration.TotalSeconds:F2} seconds.";

            _logger.LogInformation("Bulk import completed successfully {SessionId}. Duration: {Duration}",
                sessionId, result.Duration);

            return result;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Import failed: {ex.Message}";

            _logger.LogError(ex, "Bulk import failed {SessionId}", sessionId);
            throw;
        }
    }

    private void ValidateImportOptions(ImportOptions options)
    {
        if (options.DriversCount.HasValue && options.DriversCount <= 0)
            throw new ArgumentException("Drivers count must be positive", nameof(options.DriversCount));

        if (options.VehiclesCount.HasValue && options.VehiclesCount <= 0)
            throw new ArgumentException("Vehicles count must be positive", nameof(options.VehiclesCount));

        if (options.BatchSize.HasValue && (options.BatchSize <= 0 || options.BatchSize > _options.MaxBatchSize))
            throw new ArgumentException($"Batch size must be between 1 and {_options.MaxBatchSize}", nameof(options.BatchSize));
    }

    private void ApplyDefaultOptions(ImportOptions options)
    {
        options.DriversCount ??= _options.DefaultDriversCount;
        options.VehiclesCount ??= _options.DefaultVehiclesCount;
        options.BatchSize ??= _options.DefaultBatchSize;
    }

    private async Task ExecuteSqlImportAsync(ImportOptions options, ImportResult result, CancellationToken cancellationToken)
    {
        var driversCount = options.DriversCount ?? 0;
        var vehiclesCount = options.VehiclesCount ?? 0;
        var totalRows = driversCount + vehiclesCount;
        result.TotalRows = totalRows;

        _logger.LogInformation("Starting SQL-based import: {DriversCount} drivers, {VehiclesCount} vehicles", driversCount, vehiclesCount);

        // Create import session
        var sessionName = $"BulkImport_{DateTime.UtcNow:yyyyMMdd_HHmmss}";
        var sessionId = await _sqlDataService.CreateImportSessionAsync(sessionName, cancellationToken);

        try
        {
            var totalGenerated = 0;

            // Generate driver data if requested
            if (driversCount > 0)
            {
                _logger.LogInformation("Generating {DriversCount} driver records", driversCount);
                var driverResult = await _sqlDataService.GenerateDriverDataAsync(sessionId, driversCount, cancellationToken);

                if (!driverResult.Success)
                {
                    result.Errors.AddRange(driverResult.Errors);
                    throw new InvalidOperationException($"Driver data generation failed: {driverResult.Summary}");
                }

                totalGenerated += driverResult.GeneratedRows;
                result.ProcessedRows += driverResult.GeneratedRows;
                result.SuccessfulRows += driverResult.GeneratedRows;
            }

            // Generate vehicle data if requested
            if (vehiclesCount > 0)
            {
                _logger.LogInformation("Generating {VehiclesCount} vehicle records", vehiclesCount);
                var vehicleResult = await _sqlDataService.GenerateVehicleDataAsync(sessionId, vehiclesCount, cancellationToken);

                if (!vehicleResult.Success)
                {
                    result.Errors.AddRange(vehicleResult.Errors);
                    throw new InvalidOperationException($"Vehicle data generation failed: {vehicleResult.Summary}");
                }

                totalGenerated += vehicleResult.GeneratedRows;
                result.ProcessedRows += vehicleResult.GeneratedRows;
                result.SuccessfulRows += vehicleResult.GeneratedRows;
            }

            // Validate generated data
            if (totalGenerated > 0)
            {
                _logger.LogInformation("Validating generated data");
                var validationResult = await _sqlDataService.ValidateStagedDataAsync(sessionId, cancellationToken);

                if (!validationResult.Success)
                {
                    result.Warnings.AddRange(validationResult.ValidationErrors);
                    _logger.LogWarning("Data validation found issues: {ValidationSummary}", validationResult.Summary);
                }

                // Process data to production tables (or dry run)
                if (!options.DryRun)
                {
                    _logger.LogInformation("Processing data to production tables");
                    var processingResult = await _sqlDataService.ProcessStagedDataAsync(sessionId, false, cancellationToken);

                    if (!processingResult.Success)
                    {
                        result.Errors.AddRange(processingResult.ProcessingErrors);
                        throw new InvalidOperationException($"Data processing failed: {processingResult.Summary}");
                    }

                    _logger.LogInformation("Data processing completed: {ProcessingSummary}", processingResult.Summary);
                }
                else
                {
                    _logger.LogInformation("DRY RUN: Skipping data processing to production tables");
                }
            }

            // Update session status
            await _sqlDataService.UpdateImportSessionAsync(sessionId, "Completed", totalGenerated, result.SuccessfulRows, result.FailedRows, cancellationToken);

            _logger.LogInformation("SQL import completed successfully. Generated {TotalGenerated} records", totalGenerated);
        }
        catch (Exception)
        {
            // Update session status on failure
            await _sqlDataService.UpdateImportSessionAsync(sessionId, "Failed", result.TotalRows, result.SuccessfulRows, result.FailedRows, cancellationToken);
            throw;
        }
    }
}