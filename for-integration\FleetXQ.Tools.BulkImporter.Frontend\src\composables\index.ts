// Validation composables
export { useValidation } from './useValidation'
export type { ValidationRule, FieldValidation, FormValidation, ValidationOptions } from './useValidation'

// Form field composables
export { 
  useFormField, 
  useTextField, 
  useNumberField, 
  useSelectField, 
  useCheckboxField, 
  useMultiSelectField 
} from './useFormField'
export type { FormFieldOptions } from './useFormField'

// Business validation composables
export { useBusinessValidation } from './useBusinessValidation'
export type { BusinessValidationContext } from './useBusinessValidation'

// Error handling composables
export { useErrorHandling } from './useErrorHandling'
export type { 
  ErrorContext, 
  ErrorRecoveryAction, 
  ProcessedError 
} from './useErrorHandling'

// Combined form validation composable
import { useValidation } from './useValidation'
import { useBusinessValidation } from './useBusinessValidation'
import { useErrorHandling } from './useErrorHandling'

export function useFormValidation() {
  const validation = useValidation()
  const businessValidation = useBusinessValidation(validation)
  const errorHandling = useErrorHandling()

  return {
    ...validation,
    ...businessValidation,
    ...errorHandling
  }
}
