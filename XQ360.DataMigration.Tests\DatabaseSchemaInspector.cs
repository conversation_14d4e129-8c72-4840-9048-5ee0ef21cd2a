using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Utility to inspect actual database schema
    /// Use this to see what tables and columns actually exist
    /// </summary>
    public class DatabaseSchemaInspector
    {
        private readonly string _connectionString;

        public DatabaseSchemaInspector()
        {
            _connectionString = TestConfigurationHelper.GetTestConnectionString();
        }

        public async Task<List<string>> GetAllTablesAsync()
        {
            var tables = new List<string>();
            
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var sql = @"
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_NAME";
            
            using var cmd = new SqlCommand(sql, connection);
            using var reader = await cmd.ExecuteReaderAsync();
            
            while (reader.Read())
            {
                tables.Add(reader["TABLE_NAME"].ToString() ?? "");
            }
            
            return tables;
        }

        public async Task<List<string>> GetTableColumnsAsync(string tableName)
        {
            var columns = new List<string>();
            
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var sql = @"
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = @TableName
                ORDER BY ORDINAL_POSITION";
            
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@TableName", tableName);
            using var reader = await cmd.ExecuteReaderAsync();
            
            while (reader.Read())
            {
                var columnName = reader["COLUMN_NAME"].ToString() ?? "";
                var dataType = reader["DATA_TYPE"].ToString() ?? "";
                var columnInfo = $"{columnName} ({dataType})";
                columns.Add(columnInfo);
            }
            
            return columns;
        }

        public async Task PrintDatabaseSchemaAsync()
        {
            Console.WriteLine("=== DATABASE SCHEMA INSPECTION ===");
            
            var tables = await GetAllTablesAsync();
            Console.WriteLine($"\nFound {tables.Count} tables:");
            
            foreach (var table in tables)
            {
                Console.WriteLine($"\n📋 Table: {table}");
                var columns = await GetTableColumnsAsync(table);
                
                if (columns.Count > 0)
                {
                    foreach (var column in columns)
                    {
                        Console.WriteLine($"   • {column}");
                    }
                }
                else
                {
                    Console.WriteLine("   (No columns found)");
                }
            }
        }
    }
} 