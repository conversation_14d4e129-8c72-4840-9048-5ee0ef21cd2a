using Microsoft.Data.SqlClient;

namespace XQ360.DataMigration.Web.Services.TransactionManagement
{
    /// <summary>
    /// Service for managing distributed transactions across SQL and API operations
    /// </summary>
    public interface IDistributedTransactionService
    {
        /// <summary>
        /// Begins a new distributed transaction
        /// </summary>
        Task<IDistributedTransaction> BeginTransactionAsync(DistributedTransactionOptions options, CancellationToken cancellationToken = default);

        /// <summary>
        /// Executes operations within a distributed transaction
        /// </summary>
        Task<DistributedTransactionResult> ExecuteInTransactionAsync<T>(
            Func<IDistributedTransaction, CancellationToken, Task<T>> operation,
            DistributedTransactionOptions options,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Coordinates SQL and API operations in a single transaction
        /// </summary>
        Task<DistributedTransactionResult> CoordinateOperationsAsync(
            IEnumerable<ITransactionOperation> operations,
            DistributedTransactionOptions options,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a compensating transaction for rollback
        /// </summary>
        Task<ICompensatingTransaction> CreateCompensatingTransactionAsync(
            IEnumerable<ICompensatingAction> actions,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates transaction consistency across systems
        /// </summary>
        Task<ConsistencyValidationResult> ValidateConsistencyAsync(
            Guid transactionId,
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Distributed transaction interface
    /// </summary>
    public interface IDistributedTransaction : IDisposable
    {
        /// <summary>
        /// Unique transaction identifier
        /// </summary>
        Guid TransactionId { get; }

        /// <summary>
        /// Transaction status
        /// </summary>
        TransactionStatus Status { get; }

        /// <summary>
        /// SQL connection for database operations
        /// </summary>
        SqlConnection SqlConnection { get; }

        /// <summary>
        /// SQL transaction for database operations
        /// </summary>
        SqlTransaction SqlTransaction { get; }

        /// <summary>
        /// Adds an operation to the transaction
        /// </summary>
        Task AddOperationAsync(ITransactionOperation operation, CancellationToken cancellationToken = default);

        /// <summary>
        /// Adds a compensating action for rollback
        /// </summary>
        Task AddCompensatingActionAsync(ICompensatingAction action, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a savepoint for partial rollback
        /// </summary>
        Task<ISavepoint> CreateSavepointAsync(string name, CancellationToken cancellationToken = default);

        /// <summary>
        /// Commits the distributed transaction
        /// </summary>
        Task CommitAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Rolls back the distributed transaction
        /// </summary>
        Task RollbackAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Rolls back to a specific savepoint
        /// </summary>
        Task RollbackToSavepointAsync(ISavepoint savepoint, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Transaction operation interface
    /// </summary>
    public interface ITransactionOperation
    {
        /// <summary>
        /// Operation identifier
        /// </summary>
        string OperationId { get; }

        /// <summary>
        /// Operation type
        /// </summary>
        OperationType Type { get; }

        /// <summary>
        /// Operation priority for execution order
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// Executes the operation
        /// </summary>
        Task<OperationResult> ExecuteAsync(IDistributedTransaction transaction, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates the operation before execution
        /// </summary>
        Task<ValidationResult> ValidateAsync(IDistributedTransaction transaction, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates compensating action for this operation
        /// </summary>
        ICompensatingAction CreateCompensatingAction();
    }

    /// <summary>
    /// Compensating action interface for rollback scenarios
    /// </summary>
    public interface ICompensatingAction
    {
        /// <summary>
        /// Action identifier
        /// </summary>
        string ActionId { get; }

        /// <summary>
        /// Related operation identifier
        /// </summary>
        string RelatedOperationId { get; }

        /// <summary>
        /// Action priority for execution order
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// Executes the compensating action
        /// </summary>
        Task<CompensatingActionResult> ExecuteAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates the compensating action
        /// </summary>
        Task<ValidationResult> ValidateAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Savepoint interface for partial rollback
    /// </summary>
    public interface ISavepoint
    {
        /// <summary>
        /// Savepoint name
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Transaction ID this savepoint belongs to
        /// </summary>
        Guid TransactionId { get; }

        /// <summary>
        /// Timestamp when savepoint was created
        /// </summary>
        DateTime CreatedAt { get; }

        /// <summary>
        /// Operations executed before this savepoint
        /// </summary>
        IReadOnlyList<string> OperationIds { get; }
    }

    /// <summary>
    /// Compensating transaction interface
    /// </summary>
    public interface ICompensatingTransaction : IDisposable
    {
        /// <summary>
        /// Compensating transaction identifier
        /// </summary>
        Guid CompensatingTransactionId { get; }

        /// <summary>
        /// Original transaction identifier
        /// </summary>
        Guid OriginalTransactionId { get; }

        /// <summary>
        /// Executes all compensating actions
        /// </summary>
        Task<CompensatingTransactionResult> ExecuteAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Distributed transaction options
    /// </summary>
    public class DistributedTransactionOptions
    {
        public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(30);
        public IsolationLevel IsolationLevel { get; set; } = IsolationLevel.ReadCommitted;
        public bool EnableSavepoints { get; set; } = true;
        public bool EnableCompensatingActions { get; set; } = true;
        public bool ValidateConsistency { get; set; } = true;
        public int MaxRetryAttempts { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);
        public string ConnectionString { get; set; } = string.Empty;
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// Transaction status enumeration
    /// </summary>
    public enum TransactionStatus
    {
        NotStarted,
        Active,
        Preparing,
        Prepared,
        Committing,
        Committed,
        RollingBack,
        RolledBack,
        Failed,
        Aborted
    }

    /// <summary>
    /// Operation types
    /// </summary>
    public enum OperationType
    {
        SqlInsert,
        SqlUpdate,
        SqlDelete,
        ApiCall,
        FileOperation,
        Custom
    }

    /// <summary>
    /// Isolation levels for transactions
    /// </summary>
    public enum IsolationLevel
    {
        ReadUncommitted,
        ReadCommitted,
        RepeatableRead,
        Serializable,
        Snapshot
    }

    /// <summary>
    /// Distributed transaction result
    /// </summary>
    public class DistributedTransactionResult
    {
        public bool Success { get; set; }
        public Guid TransactionId { get; set; }
        public TransactionStatus FinalStatus { get; set; }
        public List<OperationResult> OperationResults { get; set; } = new();
        public List<CompensatingActionResult> CompensatingActionResults { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Operation execution result
    /// </summary>
    public class OperationResult
    {
        public string OperationId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public object? Result { get; set; }
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Compensating action execution result
    /// </summary>
    public class CompensatingActionResult
    {
        public string ActionId { get; set; } = string.Empty;
        public string RelatedOperationId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Compensating transaction result
    /// </summary>
    public class CompensatingTransactionResult
    {
        public bool Success { get; set; }
        public Guid CompensatingTransactionId { get; set; }
        public Guid OriginalTransactionId { get; set; }
        public List<CompensatingActionResult> ActionResults { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public List<string> Errors { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Consistency validation result
    /// </summary>
    public class ConsistencyValidationResult
    {
        public bool IsConsistent { get; set; }
        public List<ConsistencyIssue> Issues { get; set; } = new();
        public Dictionary<string, ConsistencyStatus> SystemStatuses { get; set; } = new();
        public DateTime ValidatedAt { get; set; }
        public TimeSpan ValidationDuration { get; set; }
    }

    /// <summary>
    /// Consistency issue
    /// </summary>
    public class ConsistencyIssue
    {
        public string IssueId { get; set; } = string.Empty;
        public ConsistencyIssueType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public string AffectedSystem { get; set; } = string.Empty;
        public object? ExpectedValue { get; set; }
        public object? ActualValue { get; set; }
        public string Resolution { get; set; } = string.Empty;
    }

    /// <summary>
    /// Consistency issue types
    /// </summary>
    public enum ConsistencyIssueType
    {
        DataMismatch,
        MissingRecord,
        ExtraRecord,
        StateInconsistency,
        TimestampMismatch
    }

    /// <summary>
    /// Consistency status for individual systems
    /// </summary>
    public enum ConsistencyStatus
    {
        Consistent,
        Inconsistent,
        Unknown,
        Unreachable
    }

    /// <summary>
    /// Validation result
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> Context { get; set; } = new();
    }
}
