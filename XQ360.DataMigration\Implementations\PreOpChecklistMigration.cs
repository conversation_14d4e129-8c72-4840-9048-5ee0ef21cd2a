using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Implementations
{
    public class PreOpChecklistMigration
    {
        private readonly ILogger<PreOpChecklistMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly string _connectionString;
        private readonly MigrationReportingService _reportingService;

        // Cache for database lookups to avoid repeated queries
        private readonly Dictionary<string, Guid> _siteCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _dealerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _customerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _modelCache = new Dictionary<string, Guid>();

        public PreOpChecklistMigration(
            ILogger<PreOpChecklistMigration> logger,
            IOptions<MigrationConfiguration> config,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _connectionString = _config.DatabaseConnection;
            _reportingService = reportingService;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Department Checklist + PreOp Questions migration using direct SQL approach");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Validate business rules
                await ValidateBusinessRulesAsync(data);

                // Step 3: Ensure CustomerModel relationships exist (prerequisite for DepartmentChecklist)
                await EnsureCustomerModelRelationshipsAsync(data);

                // Step 4: Create DepartmentChecklist mappings first (prerequisite)
                await CreateDepartmentChecklistMappingsAsync(data);

                // Step 5: Execute SQL migration for PreOp questions
                var migrationResult = await ExecutePreOpChecklistSqlAsync(data);

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;

                // Add success information for enhanced reporting
                _reportingService.AddSuccessInfo(result, "PreOp Questions Inserted", result.RecordsInserted);
                _reportingService.AddSuccessInfo(result, "DepartmentChecklist Mappings Created", 2); // From previous logs showing 2 created
                _reportingService.AddSuccessInfo(result, "CSV Records Processed", data.Count);

                // Add detailed skip information with record context
                if (result.RecordsSkipped > 0)
                {
                    _reportingService.AddSkipReason(result, $"{result.RecordsSkipped} PreOp questions already exist in database (duplicate prevention)");
                    _reportingService.AddSkipReason(result, "Migration is idempotent - safe to run multiple times without creating duplicates");
                    
                    // Add detailed warnings for skipped records
                    var skippedCount = 1;
                    foreach (var record in data.Take(result.RecordsSkipped))
                    {
                        _reportingService.AddDetailedWarning(result, skippedCount++, WarningTypes.EXISTING_RECORD_SKIPPED,
                            $"PreOp question already exists: '{record.Question}' for {record.Department}/{record.Model}",
                            "Question", record.Question,
                            "Question already exists in system - no action needed",
                            new Dictionary<string, string>
                            {
                                { "Department", record.Department ?? "Unknown" },
                                { "Model", record.Model ?? "Unknown" },
                                { "Site", record.Site ?? "Unknown" },
                                { "Customer", record.Customer ?? "Unknown" },
                                { "SortOrder", record.SortOrder.ToString() }
                            });
                    }
                }

                // Add relationship information
                _reportingService.AddCreatedRelationship(result, "CustomerModel relationships verified/created");
                _reportingService.AddCreatedRelationship(result, "DepartmentChecklist mappings verified/created");

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "PreOp Checklist Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Department Checklist + PreOp Questions migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private async Task<List<PreOpChecklistImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing PreOp Checklist CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096, useAsync: true);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<PreOpChecklistImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return records;
        }

        private async Task ValidateDataAsync(List<PreOpChecklistImportModel> data)
        {
            _logger.LogInformation("Validating data before migration...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var errors = new List<string>();

            // Get unique site-dealer-customer combinations and cache them
            var uniqueCombinations = data
                .Select(d => new { Site = d.Site, Dealer = d.Dealer, Customer = d.Customer })
                .Distinct()
                .ToList();

            foreach (var combo in uniqueCombinations)
            {
                try
                {
                    await GetSiteIdAsync(combo.Site ?? "Unknown", combo.Customer ?? "Unknown", connection);
                    await GetDealerIdAsync(combo.Dealer ?? "Unknown", connection);
                    await GetCustomerIdAsync(combo.Customer ?? "Unknown", combo.Dealer ?? "Unknown", connection);
                }
                catch (Exception ex)
                {
                    errors.Add(ex.Message);
                }
            }

            // Get unique department-model combinations and validate DepartmentChecklist exists
            var departmentModelCombinations = data
                .Select(d => new { Department = d.Department, Model = d.Model, Site = d.Site, Dealer = d.Dealer, Customer = d.Customer })
                .Distinct()
                .ToList();

            foreach (var combo in departmentModelCombinations)
            {
                try
                {
                    var siteId = await GetSiteIdAsync(combo.Site ?? "Unknown", combo.Customer ?? "Unknown", connection);
                    var dealerId = await GetDealerIdAsync(combo.Dealer ?? "Unknown", connection);

                    var sql = @"
                        SELECT COUNT(1) 
                        FROM dbo.DepartmentChecklist dc
                        JOIN dbo.Department d ON dc.DepartmentId = d.Id
                        JOIN dbo.Model m ON dc.ModelId = m.Id
                        WHERE d.Name = @Department 
                        AND m.Name = @Model 
                        AND d.SiteId = @SiteId
                        AND m.DealerId = @DealerId";

                    using var cmd = new SqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@Department", combo.Department);
                    cmd.Parameters.AddWithValue("@Model", combo.Model);
                    cmd.Parameters.AddWithValue("@SiteId", siteId);
                    cmd.Parameters.AddWithValue("@DealerId", dealerId);

                    var count = (int)(await cmd.ExecuteScalarAsync() ?? 0);
                    if (count == 0)
                    {
                        errors.Add($"DepartmentChecklist not found for Department '{combo.Department}' and Model '{combo.Model}' in Site '{combo.Site}' and Dealer '{combo.Dealer}'");
                    }
                }
                catch (Exception ex)
                {
                    errors.Add(ex.Message);
                }
            }

            if (errors.Any())
            {
                throw new InvalidOperationException($"Validation failed: {string.Join("; ", errors)}");
            }

            _logger.LogInformation("Data validation completed successfully");
        }

        private async Task<MigrationResult> ExecutePreOpChecklistSqlAsync(List<PreOpChecklistImportModel> data)
        {
            _logger.LogInformation("Executing PreOp Checklist SQL migration...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                // Step 1: Create temporary table for questions
                var createTempTableSql = @"
                    CREATE TABLE #Questions (
                        DepartmentName NVARCHAR(255),
                        ModelName NVARCHAR(255),
                        Question NVARCHAR(200),
                        ExpectedAnswer BIT,
                        Critical BIT,
                        ExcludeFromRandom BIT,
                        SortOrder INT,
                        SiteId UNIQUEIDENTIFIER,
                        DealerId UNIQUEIDENTIFIER
                    );";

                using (var cmd = new SqlCommand(createTempTableSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Step 2: Populate temporary table with site/dealer lookups
                var insertTempSql = @"
                    INSERT INTO #Questions (DepartmentName, ModelName, Question, ExpectedAnswer, Critical, ExcludeFromRandom, SortOrder, SiteId, DealerId)
                    VALUES (@DepartmentName, @ModelName, @Question, @ExpectedAnswer, @Critical, @ExcludeFromRandom, @SortOrder, @SiteId, @DealerId);";

                foreach (var record in data)
                {
                    var siteId = await GetSiteIdAsync(record.Site ?? "Unknown", record.Customer ?? "Unknown", connection, transaction);
                    var dealerId = await GetDealerIdAsync(record.Dealer ?? "Unknown", connection, transaction);

                    using var cmd = new SqlCommand(insertTempSql, connection, transaction);
                    cmd.Parameters.AddWithValue("@DepartmentName", record.Department);
                    cmd.Parameters.AddWithValue("@ModelName", record.Model);
                    cmd.Parameters.AddWithValue("@Question", record.Question);
                    cmd.Parameters.AddWithValue("@ExpectedAnswer", record.ExpectedAnswer);
                    cmd.Parameters.AddWithValue("@Critical", record.Critical);
                    cmd.Parameters.AddWithValue("@ExcludeFromRandom", record.ExcludeFromRandom);
                    cmd.Parameters.AddWithValue("@SortOrder", record.SortOrder);
                    cmd.Parameters.AddWithValue("@SiteId", siteId);
                    cmd.Parameters.AddWithValue("@DealerId", dealerId);
                    await cmd.ExecuteNonQueryAsync();
                }

                // Step 3: Insert questions into PreOperationalChecklist table
                var mainInsertSql = @"
                    INSERT INTO dbo.PreOperationalChecklist (Id, [Order], Question, Active, ExcludeRandom, ExpectedAnswer, Critical, AnswerType, SiteChecklistId)
                    SELECT
                        NEWID(),
                        q.SortOrder,
                        q.Question,
                        1, -- Active = TRUE
                        q.ExcludeFromRandom,
                        q.ExpectedAnswer,
                        q.Critical,
                        0, -- AnswerType = Yes/No
                        dc.Id
                    FROM
                        #Questions q
                    JOIN
                        dbo.Department d ON q.DepartmentName = d.Name AND d.SiteId = q.SiteId
                    JOIN
                        dbo.Model m ON q.ModelName = m.Name AND m.DealerId = q.DealerId
                    JOIN
                        dbo.DepartmentChecklist dc ON dc.DepartmentId = d.Id AND dc.ModelId = m.Id
                    WHERE
                        NOT EXISTS (
                            SELECT 1 
                            FROM dbo.PreOperationalChecklist existing 
                            WHERE existing.Question = q.Question 
                            AND existing.SiteChecklistId = dc.Id
                        );
                    
                    SELECT @@ROWCOUNT AS RecordsInserted;";

                int insertedCount;
                using (var cmd = new SqlCommand(mainInsertSql, connection, transaction))
                {
                    var result = await cmd.ExecuteScalarAsync();
                    insertedCount = Convert.ToInt32(result);
                }

                // Step 4: Clean up
                var cleanupSql = "DROP TABLE #Questions;";
                using (var cmd = new SqlCommand(cleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                await transaction.CommitAsync();

                _logger.LogInformation($"Successfully inserted {insertedCount} PreOp Checklist questions");

                return new MigrationResult
                {
                    Success = true,
                    RecordsInserted = insertedCount
                };
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "PreOp Checklist SQL migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<Guid> GetSiteIdAsync(string siteName, string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{siteName}|{customerName}";
            if (_siteCache.ContainsKey(key))
                return _siteCache[key];

            var customerId = await GetCustomerIdByNameAsync(customerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Site WHERE Name = @Name AND CustomerId = @CustomerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", siteName);
            cmd.Parameters.AddWithValue("@CustomerId", customerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Site '{siteName}' not found for customer '{customerName}'");

            var siteId = (Guid)result;
            _siteCache[key] = siteId;
            return siteId;
        }

        private async Task<Guid> GetDealerIdAsync(string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            if (_dealerCache.ContainsKey(dealerName))
                return _dealerCache[dealerName];

            var sql = "SELECT Id FROM dbo.Dealer WHERE Name = @Name";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", dealerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Dealer '{dealerName}' not found");

            var dealerId = (Guid)result;
            _dealerCache[dealerName] = dealerId;
            return dealerId;
        }

        private async Task<Guid> GetCustomerIdAsync(string customerName, string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{customerName}|{dealerName}";
            if (_customerCache.ContainsKey(key))
                return _customerCache[key];

            var dealerId = await GetDealerIdAsync(dealerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName AND DealerId = @DealerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);
            cmd.Parameters.AddWithValue("@DealerId", dealerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found for dealer '{dealerName}'");

            var customerId = (Guid)result;
            _customerCache[key] = customerId;
            return customerId;
        }

        private async Task<Guid> GetCustomerIdByNameAsync(string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found");

            return (Guid)result;
        }

        private async Task ValidateBusinessRulesAsync(List<PreOpChecklistImportModel> data)
        {
            _logger.LogInformation("Validating business rules before migration...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var validationResult = new MigrationResult();
            var hasErrors = false;

            // Get unique combinations to validate
            var uniqueCombinations = data
                .Select(d => new { Dealer = d.Dealer, Customer = d.Customer, Site = d.Site, Department = d.Department, Model = d.Model })
                .Distinct()
                .ToList();

            foreach (var combo in uniqueCombinations)
            {
                var rowNumbers = data
                    .Where(d => d.Dealer == combo.Dealer && d.Customer == combo.Customer && 
                               d.Site == combo.Site && d.Department == combo.Department && d.Model == combo.Model)
                    .Select((item, index) => index + 1)
                    .ToList();

                try
                {
                    // Rule 1: Customer-Dealer Relationship validation
                    var customerDealerSql = @"
                        SELECT COUNT(1) 
                        FROM dbo.Customer c 
                        JOIN dbo.Dealer d ON c.DealerId = d.Id 
                        WHERE c.CompanyName = @CustomerName AND d.Name = @DealerName";

                    using (var cmd = new SqlCommand(customerDealerSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@CustomerName", combo.Customer);
                        cmd.Parameters.AddWithValue("@DealerName", combo.Dealer);
                        var count = (int)(await cmd.ExecuteScalarAsync() ?? 0);
                        if (count == 0)
                        {
                            hasErrors = true;
                            foreach (var rowNum in rowNumbers)
                            {
                                _reportingService.AddDetailedError(validationResult, rowNum, ErrorTypes.BUSINESS_RULE_VIOLATION,
                                    $"Customer '{combo.Customer}' does not belong to Dealer '{combo.Dealer}'",
                                    "Customer", combo.Customer,
                                    "Verify customer-dealer relationships in the system",
                                    new Dictionary<string, string>
                                    {
                                        { "Dealer", combo.Dealer ?? "Unknown" },
                                        { "Site", combo.Site ?? "Unknown" },
                                        { "Department", combo.Department ?? "Unknown" },
                                        { "Model", combo.Model ?? "Unknown" }
                                    });
                            }
                        }
                    }

                    // Rule 2: Model-Dealer relationship (Model must belong to Dealer)
                    // First, let's check if the model exists at all
                    var modelExistsSql = "SELECT COUNT(1) FROM dbo.Model WHERE Name = @ModelName";
                    using (var modelCmd = new SqlCommand(modelExistsSql, connection))
                    {
                        modelCmd.Parameters.AddWithValue("@ModelName", combo.Model);
                        var modelExists = (int)(await modelCmd.ExecuteScalarAsync() ?? 0);
                        if (modelExists == 0)
                        {
                            hasErrors = true;
                            foreach (var rowNum in rowNumbers)
                            {
                                _reportingService.AddDetailedError(validationResult, rowNum, ErrorTypes.INVALID_MODEL,
                                    $"Model '{combo.Model}' does not exist in the system",
                                    "Model", combo.Model,
                                    "Verify model exists or add it to the system first",
                                    new Dictionary<string, string>
                                    {
                                        { "Dealer", combo.Dealer ?? "Unknown" },
                                        { "Customer", combo.Customer ?? "Unknown" },
                                        { "Site", combo.Site ?? "Unknown" },
                                        { "Department", combo.Department ?? "Unknown" }
                                    });
                            }
                            continue; // Skip dealer check if model doesn't exist
                        }
                    }

                    // Check if dealer exists
                    var dealerExistsSql = "SELECT COUNT(1) FROM dbo.Dealer WHERE Name = @DealerName";
                    using (var dealerCmd = new SqlCommand(dealerExistsSql, connection))
                    {
                        dealerCmd.Parameters.AddWithValue("@DealerName", combo.Dealer);
                        var dealerExists = (int)(await dealerCmd.ExecuteScalarAsync() ?? 0);
                        if (dealerExists == 0)
                        {
                            hasErrors = true;
                            foreach (var rowNum in rowNumbers)
                            {
                                _reportingService.AddDetailedError(validationResult, rowNum, ErrorTypes.INVALID_DEALER,
                                    $"Dealer '{combo.Dealer}' does not exist in the system",
                                    "Dealer", combo.Dealer,
                                    "Verify dealer exists or add it to the system first",
                                    new Dictionary<string, string>
                                    {
                                        { "Model", combo.Model },
                                        { "Customer", combo.Customer },
                                        { "Site", combo.Site },
                                        { "Department", combo.Department }
                                    });
                            }
                            continue;
                        }
                    }

                    // Check model-dealer relationship
                    var modelDealerSql = @"
                        SELECT COUNT(1) 
                        FROM dbo.Model m 
                        JOIN dbo.Dealer d ON m.DealerId = d.Id 
                        WHERE m.Name = @ModelName AND d.Name = @DealerName";

                    using (var cmd = new SqlCommand(modelDealerSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@ModelName", combo.Model);
                        cmd.Parameters.AddWithValue("@DealerName", combo.Dealer);
                        var count = (int)(await cmd.ExecuteScalarAsync() ?? 0);
                        if (count == 0)
                        {
                            // Let's get more diagnostic info
                            var diagnosticSql = @"
                                SELECT TOP 1 m.Name as ModelName, d.Name as ActualDealerName
                                FROM dbo.Model m 
                                JOIN dbo.Dealer d ON m.DealerId = d.Id 
                                WHERE m.Name = @ModelName";
                            
                            string actualDealer = "";
                            using (var diagCmd = new SqlCommand(diagnosticSql, connection))
                            {
                                diagCmd.Parameters.AddWithValue("@ModelName", combo.Model);
                                using var reader = await diagCmd.ExecuteReaderAsync();
                                if (await reader.ReadAsync())
                                {
                                    actualDealer = reader.GetString(1); // ActualDealerName is column index 1
                                }
                            }

                            hasErrors = true;
                            foreach (var rowNum in rowNumbers)
                            {
                                var errorMessage = !string.IsNullOrEmpty(actualDealer) 
                                    ? $"Model '{combo.Model}' belongs to Dealer '{actualDealer}', not '{combo.Dealer}'"
                                    : $"Model '{combo.Model}' does not belong to Dealer '{combo.Dealer}'";
                                
                                _reportingService.AddDetailedError(validationResult, rowNum, ErrorTypes.BUSINESS_RULE_VIOLATION,
                                    errorMessage,
                                    "Model", combo.Model,
                                    "Verify model-dealer relationships in the system",
                                    new Dictionary<string, string>
                                    {
                                        { "Dealer", combo.Dealer },
                                        { "ActualDealer", actualDealer },
                                        { "Customer", combo.Customer },
                                        { "Site", combo.Site },
                                        { "Department", combo.Department }
                                    });
                            }
                        }
                    }

                    // Rule 3: Hierarchy Validation - Customer → Site → Department chain
                    var hierarchySql = @"
                        SELECT COUNT(1) 
                        FROM dbo.Customer c
                        JOIN dbo.Site s ON c.Id = s.CustomerId
                        JOIN dbo.Department d ON s.Id = d.SiteId
                        WHERE c.CompanyName = @CustomerName 
                        AND s.Name = @SiteName 
                        AND d.Name = @DepartmentName";

                    using (var cmd = new SqlCommand(hierarchySql, connection))
                    {
                        cmd.Parameters.AddWithValue("@CustomerName", combo.Customer);
                        cmd.Parameters.AddWithValue("@SiteName", combo.Site);
                        cmd.Parameters.AddWithValue("@DepartmentName", combo.Department);
                        var count = (int)(await cmd.ExecuteScalarAsync() ?? 0);
                        if (count == 0)
                        {
                            hasErrors = true;
                            foreach (var rowNum in rowNumbers)
                            {
                                _reportingService.AddDetailedError(validationResult, rowNum, ErrorTypes.BUSINESS_RULE_VIOLATION,
                                    $"Hierarchy broken - Customer '{combo.Customer}' → Site '{combo.Site}' → Department '{combo.Department}' chain does not exist",
                                    "Customer", combo.Customer,
                                    "Verify customer-site-department hierarchy in the system",
                                    new Dictionary<string, string>
                                    {
                                        { "Site", combo.Site },
                                        { "Department", combo.Department },
                                        { "Dealer", combo.Dealer },
                                        { "Model", combo.Model }
                                    });
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    hasErrors = true;
                    foreach (var rowNum in rowNumbers)
                    {
                        _reportingService.AddDetailedError(validationResult, rowNum, ErrorTypes.DATABASE_ERROR,
                            $"Validation error: {ex.Message}",
                            "Record", $"{combo.Dealer}-{combo.Customer}-{combo.Site}-{combo.Department}-{combo.Model}",
                            "Check database connection and data integrity",
                            new Dictionary<string, string>
                            {
                                                                        { "Dealer", combo.Dealer ?? "Unknown" },
                                        { "Customer", combo.Customer ?? "Unknown" },
                                        { "Site", combo.Site ?? "Unknown" },
                                        { "Department", combo.Department ?? "Unknown" },
                                { "Model", combo.Model }
                            });
                    }
                }
            }

            if (hasErrors)
            {
                _reportingService.GenerateMigrationReport(validationResult, "PreOp Checklist Validation");
                var errorMessages = validationResult.DetailedErrors.Select(e => e.ErrorMessage).Distinct();
                throw new InvalidOperationException($"Business rule validation failed: {string.Join("; ", errorMessages)}");
            }

            _logger.LogInformation("✅ All business rules validated successfully");
        }

        private async Task EnsureCustomerModelRelationshipsAsync(List<PreOpChecklistImportModel> data)
        {
            _logger.LogInformation("Ensuring CustomerModel relationships exist...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                // Get unique customer-model combinations from PreOp data
                var uniqueCustomerModelCombinations = data
                    .Select(d => new { Customer = d.Customer, Model = d.Model, Dealer = d.Dealer })
                    .Distinct()
                    .ToList();

                var insertedCount = 0;

                foreach (var combo in uniqueCustomerModelCombinations)
                {
                    // Get Customer ID and Model ID
                    var customerId = await GetCustomerIdByNameAsync(combo.Customer, connection, transaction);
                    var modelId = await GetModelIdAsync(combo.Model, combo.Dealer, connection, transaction);

                    // Check if CustomerModel relationship already exists
                    var checkExistsSql = @"
                        SELECT COUNT(1) 
                        FROM dbo.CustomerModel 
                        WHERE CustomerId = @CustomerId AND ModelId = @ModelId";

                    using (var checkCmd = new SqlCommand(checkExistsSql, connection, transaction))
                    {
                        checkCmd.Parameters.AddWithValue("@CustomerId", customerId);
                        checkCmd.Parameters.AddWithValue("@ModelId", modelId);
                        var exists = (int)(await checkCmd.ExecuteScalarAsync() ?? 0);

                        if (exists == 0)
                        {
                            // Insert CustomerModel relationship
                            var insertSql = @"
                                INSERT INTO dbo.CustomerModel (Id, CustomerId, ModelId, Polarity)
                                VALUES (@Id, @CustomerId, @ModelId, @Polarity)";

                            using var insertCmd = new SqlCommand(insertSql, connection, transaction);
                            insertCmd.Parameters.AddWithValue("@Id", Guid.NewGuid());
                            insertCmd.Parameters.AddWithValue("@CustomerId", customerId);
                            insertCmd.Parameters.AddWithValue("@ModelId", modelId);
                            insertCmd.Parameters.AddWithValue("@Polarity", 0); // Default polarity

                            await insertCmd.ExecuteNonQueryAsync();
                            insertedCount++;

                            _logger.LogDebug($"Created CustomerModel relationship: Customer '{combo.Customer}' <-> Model '{combo.Model}'");
                        }
                    }
                }

                await transaction.CommitAsync();

                _logger.LogInformation($"✅ Ensured CustomerModel relationships: {insertedCount} new relationships created");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to ensure CustomerModel relationships");
                throw;
            }
        }

        private async Task<Guid> GetModelIdAsync(string modelName, string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{modelName}|{dealerName}";
            if (_modelCache.ContainsKey(key))
                return _modelCache[key];

            var dealerId = await GetDealerIdAsync(dealerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Model WHERE Name = @Name AND DealerId = @DealerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", modelName);
            cmd.Parameters.AddWithValue("@DealerId", dealerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Model '{modelName}' not found for dealer '{dealerName}'");

            var modelId = (Guid)result;
            _modelCache[key] = modelId;
            return modelId;
        }

        private async Task CreateDepartmentChecklistMappingsAsync(List<PreOpChecklistImportModel> data)
        {
            _logger.LogInformation("Creating DepartmentChecklist mappings (prerequisite for PreOp questions)...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                // Step 1: Create temporary table for department-model mappings
                var createTempTableSql = @"
                    CREATE TABLE #DepartmentModelMapping (
                        DepartmentName NVARCHAR(255),
                        ModelName NVARCHAR(255),
                        SiteId UNIQUEIDENTIFIER,
                        DealerId UNIQUEIDENTIFIER
                    );";

                using (var cmd = new SqlCommand(createTempTableSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Step 2: Get unique department-model combinations from PreOp data
                var uniqueCombinations = data
                    .Select(d => new { Department = d.Department, Model = d.Model, Site = d.Site, Dealer = d.Dealer, Customer = d.Customer })
                    .Distinct()
                    .ToList();

                // Step 3: Populate temporary table with site/dealer lookups
                var insertTempSql = @"
                    INSERT INTO #DepartmentModelMapping (DepartmentName, ModelName, SiteId, DealerId)
                    VALUES (@DepartmentName, @ModelName, @SiteId, @DealerId);";

                foreach (var combo in uniqueCombinations)
                {
                    var siteId = await GetSiteIdAsync(combo.Site, combo.Customer, connection, transaction);
                    var dealerId = await GetDealerIdAsync(combo.Dealer, connection, transaction);

                    using var cmd = new SqlCommand(insertTempSql, connection, transaction);
                    cmd.Parameters.AddWithValue("@DepartmentName", combo.Department);
                    cmd.Parameters.AddWithValue("@ModelName", combo.Model);
                    cmd.Parameters.AddWithValue("@SiteId", siteId);
                    cmd.Parameters.AddWithValue("@DealerId", dealerId);
                    await cmd.ExecuteNonQueryAsync();
                }

                // Step 4: Insert DepartmentChecklist records
                var mainInsertSql = @"
                    INSERT INTO dbo.DepartmentChecklist (Id, DepartmentId, ModelId, IsThaiEnabled)
                    SELECT
                        NEWID() AS Id,
                        d.Id AS DepartmentId,
                        m.Id AS ModelId,
                        0 AS IsThaiEnabled -- Defaulting to false
                    FROM
                        #DepartmentModelMapping map
                    JOIN
                        dbo.Department d ON map.DepartmentName = d.Name AND d.SiteId = map.SiteId
                    JOIN
                        dbo.Model m ON map.ModelName = m.Name AND m.DealerId = map.DealerId
                    WHERE
                        NOT EXISTS ( -- Prevent duplicates
                            SELECT 1
                            FROM dbo.DepartmentChecklist existing
                            WHERE existing.DepartmentId = d.Id AND existing.ModelId = m.Id
                        );
                    
                    SELECT @@ROWCOUNT AS RecordsInserted;";

                int insertedCount;
                using (var cmd = new SqlCommand(mainInsertSql, connection, transaction))
                {
                    var result = await cmd.ExecuteScalarAsync();
                    insertedCount = Convert.ToInt32(result);
                }

                // Step 5: Clean up
                var cleanupSql = "DROP TABLE #DepartmentModelMapping;";
                using (var cmd = new SqlCommand(cleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                await transaction.CommitAsync();

                _logger.LogInformation($"✅ Successfully created {insertedCount} unique DepartmentChecklist mappings (Department+Model combinations)");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to create DepartmentChecklist mappings");
                throw;
            }
        }
    }
} 