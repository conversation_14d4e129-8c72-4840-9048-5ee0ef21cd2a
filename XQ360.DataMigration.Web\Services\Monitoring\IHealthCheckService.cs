using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace XQ360.DataMigration.Web.Services.Monitoring
{
    public interface IHealthCheckService
    {
        Task<HealthCheckResult> GetSystemHealthAsync(CancellationToken cancellationToken = default);
        Task<DatabaseHealthResult> CheckDatabaseHealthAsync(CancellationToken cancellationToken = default);
        Task<ApiHealthResult> CheckApiHealthAsync(CancellationToken cancellationToken = default);
        Task<ResourceHealthResult> CheckResourceHealthAsync(CancellationToken cancellationToken = default);
        Task<ServiceHealthResult> CheckServiceHealthAsync(CancellationToken cancellationToken = default);
        Task<List<DiagnosticResult>> RunDiagnosticsAsync(CancellationToken cancellationToken = default);
        Task<PerformanceReport> GeneratePerformanceReportAsync(CancellationToken cancellationToken = default);
        Task<SystemConfiguration> GetSystemConfigurationAsync(CancellationToken cancellationToken = default);
    }

    public class HealthCheckResult
    {
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public OverallHealthStatus Status { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public string Version { get; set; } = string.Empty;
        public string Environment { get; set; } = string.Empty;
        public DatabaseHealthResult Database { get; set; } = new();
        public ApiHealthResult Api { get; set; } = new();
        public ResourceHealthResult Resources { get; set; } = new();
        public ServiceHealthResult Services { get; set; } = new();
        public List<HealthAlert> Alerts { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public enum OverallHealthStatus
    {
        Healthy,
        Degraded,
        Unhealthy,
        Critical
    }

    public class DatabaseHealthResult
    {
        public HealthStatus Status { get; set; }
        public TimeSpan ConnectionTime { get; set; }
        public bool CanConnect { get; set; }
        public bool CanQuery { get; set; }
        public bool CanWrite { get; set; }
        public int ActiveConnections { get; set; }
        public int MaxConnections { get; set; }
        public double ConnectionPoolUtilization => MaxConnections > 0 ? (double)ActiveConnections / MaxConnections * 100 : 0;
        public long DatabaseSizeMB { get; set; }
        public long AvailableSpaceMB { get; set; }
        public double SpaceUtilization => DatabaseSizeMB + AvailableSpaceMB > 0 ? (double)DatabaseSizeMB / (DatabaseSizeMB + AvailableSpaceMB) * 100 : 0;
        public List<TableHealthInfo> TableHealth { get; set; } = new();
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
    }

    public class ApiHealthResult
    {
        public HealthStatus Status { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public bool IsReachable { get; set; }
        public bool CanAuthenticate { get; set; }
        public string Version { get; set; } = string.Empty;
        public int RateLimitRemaining { get; set; }
        public DateTime? RateLimitReset { get; set; }
        public List<EndpointHealth> EndpointHealths { get; set; } = new();
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
    }

    public class ResourceHealthResult
    {
        public HealthStatus Status { get; set; }
        public CpuHealth Cpu { get; set; } = new();
        public MemoryHealth Memory { get; set; } = new();
        public DiskHealth Disk { get; set; } = new();
        public NetworkHealth Network { get; set; } = new();
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
    }

    public class ServiceHealthResult
    {
        public HealthStatus Status { get; set; }
        public List<ServiceComponentHealth> Components { get; set; } = new();
        public bool AllCriticalServicesRunning { get; set; }
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
    }

    public enum HealthStatus
    {
        Healthy,
        Warning,
        Critical,
        Unknown
    }

    public class TableHealthInfo
    {
        public string TableName { get; set; } = string.Empty;
        public long RecordCount { get; set; }
        public long SizeMB { get; set; }
        public DateTime LastUpdated { get; set; }
        public bool HasIndexes { get; set; }
        public double FragmentationPercent { get; set; }
        public HealthStatus Status { get; set; }
    }

    public class EndpointHealth
    {
        public string Endpoint { get; set; } = string.Empty;
        public HealthStatus Status { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public int StatusCode { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class CpuHealth
    {
        public HealthStatus Status { get; set; }
        public double CurrentUsagePercent { get; set; }
        public double AverageUsagePercent { get; set; }
        public double PeakUsagePercent { get; set; }
        public int CoreCount { get; set; }
        public bool IsThrottled { get; set; }
    }

    public class MemoryHealth
    {
        public HealthStatus Status { get; set; }
        public long TotalMB { get; set; }
        public long UsedMB { get; set; }
        public long AvailableMB { get; set; }
        public double UsagePercent => TotalMB > 0 ? (double)UsedMB / TotalMB * 100 : 0;
        public long GcMemoryMB { get; set; }
        public int GcGen0Collections { get; set; }
        public int GcGen1Collections { get; set; }
        public int GcGen2Collections { get; set; }
        public bool MemoryPressure { get; set; }
    }

    public class DiskHealth
    {
        public HealthStatus Status { get; set; }
        public long TotalGB { get; set; }
        public long UsedGB { get; set; }
        public long AvailableGB { get; set; }
        public double UsagePercent => TotalGB > 0 ? (double)UsedGB / TotalGB * 100 : 0;
        public double ReadLatencyMs { get; set; }
        public double WriteLatencyMs { get; set; }
        public double IopsUtilization { get; set; }
    }

    public class NetworkHealth
    {
        public HealthStatus Status { get; set; }
        public bool IsConnected { get; set; }
        public double LatencyMs { get; set; }
        public double BandwidthUtilization { get; set; }
        public int PacketLoss { get; set; }
        public List<string> ActiveConnections { get; set; } = new();
    }

    public class ServiceComponentHealth
    {
        public string ComponentName { get; set; } = string.Empty;
        public string ComponentType { get; set; } = string.Empty;
        public HealthStatus Status { get; set; }
        public bool IsRunning { get; set; }
        public TimeSpan Uptime { get; set; }
        public DateTime LastChecked { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metrics { get; set; } = new();
    }

    public class HealthAlert
    {
        public Guid AlertId { get; set; }
        public AlertSeverity Severity { get; set; }
        public string Component { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public bool IsActive { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
    }

    public class DiagnosticResult
    {
        public string TestName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public DiagnosticStatus Status { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Recommendation { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public Dictionary<string, object> Details { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public enum DiagnosticStatus
    {
        Pass,
        Warning,
        Fail,
        Skipped,
        Error
    }

    public class PerformanceReport
    {
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan ReportPeriod { get; set; }
        public SystemPerformanceMetrics System { get; set; } = new();
        public DatabasePerformanceMetrics Database { get; set; } = new();
        public ApplicationPerformanceMetrics Application { get; set; } = new();
        public List<PerformanceRecommendation> Recommendations { get; set; } = new();
        public Dictionary<string, object> RawMetrics { get; set; } = new();
    }

    public class SystemPerformanceMetrics
    {
        public double AverageCpuUsage { get; set; }
        public double PeakCpuUsage { get; set; }
        public double AverageMemoryUsage { get; set; }
        public double PeakMemoryUsage { get; set; }
        public double AverageDiskUsage { get; set; }
        public double NetworkThroughput { get; set; }
        public int ProcessCount { get; set; }
        public TimeSpan SystemUptime { get; set; }
    }

    public class DatabasePerformanceMetrics
    {
        public double AverageQueryTime { get; set; }
        public double LongestQueryTime { get; set; }
        public int QueriesPerSecond { get; set; }
        public int CacheHitRatio { get; set; }
        public int DeadlockCount { get; set; }
        public int BlockedProcessCount { get; set; }
        public long TotalConnections { get; set; }
        public double AverageWaitTime { get; set; }
    }

    public class ApplicationPerformanceMetrics
    {
        public double AverageResponseTime { get; set; }
        public double RequestsPerSecond { get; set; }
        public double ErrorRate { get; set; }
        public int ActiveSessions { get; set; }
        public double ThroughputRecordsPerSecond { get; set; }
        public long MemoryAllocations { get; set; }
        public int GarbageCollections { get; set; }
        public double CacheEfficiency { get; set; }
    }

    public class PerformanceRecommendation
    {
        public string Category { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public RecommendationPriority Priority { get; set; }
        public string Impact { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public Dictionary<string, object> Metrics { get; set; } = new();
    }

    public enum RecommendationPriority
    {
        Low,
        Medium,
        High,
        Critical
    }

    public class SystemConfiguration
    {
        public DateTime RetrievedAt { get; set; } = DateTime.UtcNow;
        public EnvironmentInfo Environment { get; set; } = new();
        public HardwareInfo Hardware { get; set; } = new();
        public SoftwareInfo Software { get; set; } = new();
        public NetworkInfo Network { get; set; } = new();
        public SecurityInfo Security { get; set; } = new();
        public Dictionary<string, string> AppSettings { get; set; } = new();
        public Dictionary<string, string> ConnectionStrings { get; set; } = new();
    }

    public class EnvironmentInfo
    {
        public string EnvironmentName { get; set; } = string.Empty;
        public string MachineName { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string WorkingDirectory { get; set; } = string.Empty;
        public Dictionary<string, string> EnvironmentVariables { get; set; } = new();
    }

    public class HardwareInfo
    {
        public int ProcessorCount { get; set; }
        public string ProcessorArchitecture { get; set; } = string.Empty;
        public long TotalMemoryMB { get; set; }
        public string[] LogicalDrives { get; set; } = Array.Empty<string>();
        public Dictionary<string, long> DriveSpaceGB { get; set; } = new();
    }

    public class SoftwareInfo
    {
        public string OperatingSystem { get; set; } = string.Empty;
        public string OsVersion { get; set; } = string.Empty;
        public string RuntimeVersion { get; set; } = string.Empty;
        public string ApplicationVersion { get; set; } = string.Empty;
        public DateTime ProcessStartTime { get; set; }
        public TimeSpan ProcessUptime { get; set; }
    }

    public class NetworkInfo
    {
        public string[] NetworkInterfaces { get; set; } = Array.Empty<string>();
        public string[] IpAddresses { get; set; } = Array.Empty<string>();
        public bool InternetConnectivity { get; set; }
        public Dictionary<string, bool> ServiceConnectivity { get; set; } = new();
    }

    public class SecurityInfo
    {
        public bool IsElevated { get; set; }
        public string[] SecurityProtocols { get; set; } = Array.Empty<string>();
        public bool EncryptionEnabled { get; set; }
        public DateTime? LastSecurityScan { get; set; }
        public List<string> SecurityRecommendations { get; set; } = new();
    }
}
