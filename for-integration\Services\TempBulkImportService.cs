using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Configuration;
using FleetXQ.Tools.BulkImporter.Logging;

namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Updated bulk import service that uses temporary staging tables instead of permanent ones
/// Maintains all functionality while working within table creation constraints
/// </summary>
public class TempBulkImportService : IBulkImportService
{
    private readonly ILogger<TempBulkImportService> _logger;
    private readonly BulkImporterOptions _options;
    private readonly ConnectionStringOptions _connectionOptions;
    private readonly ITempStagingService _tempStagingService;
    private readonly ISqlDataGenerationService _dataGenerationService;

    public TempBulkImportService(
        ILogger<TempBulkImportService> logger,
        IOptions<BulkImporterOptions> options,
        IOptions<ConnectionStringOptions> connectionOptions,
        ITempStagingService tempStagingService,
        ISqlDataGenerationService dataGenerationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _connectionOptions = connectionOptions?.Value ?? throw new ArgumentNullException(nameof(connectionOptions));
        _tempStagingService = tempStagingService ?? throw new ArgumentNullException(nameof(tempStagingService));
        _dataGenerationService = dataGenerationService ?? throw new ArgumentNullException(nameof(dataGenerationService));
    }

    public async Task<ImportResult> ExecuteImportAsync(ImportOptions options, CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();
        var sessionName = $"TempImport_{sessionId:N}";

        CorrelationContext.CreateOperationId($"TEMP_IMPORT_{sessionId:N}");

        _logger.LogInformation("Starting temporary table-based bulk import operation {SessionId}", sessionId);

        var startTime = DateTime.UtcNow;
        var result = new ImportResult
        {
            SessionId = sessionId.ToString()
        };

        // Use a single connection for the entire session to maintain temp table lifetime
        using var connection = new SqlConnection(_connectionOptions.FleetXQConnection);
        await connection.OpenAsync(cancellationToken);

        try
        {
            // Validate options
            ValidateImportOptions(options);

            // Apply defaults
            ApplyDefaultOptions(options);

            _logger.LogInformation("Import options: Drivers={DriversCount}, Vehicles={VehiclesCount}, BatchSize={BatchSize}, DryRun={DryRun}, Generate={GenerateData}",
                options.DriversCount, options.VehiclesCount, options.BatchSize, options.DryRun, options.GenerateData);

            if (options.DryRun)
            {
                _logger.LogInformation("DRY RUN MODE - No data will be modified");
            }

            // Step 1: Create temporary staging tables
            await _tempStagingService.CreateTempStagingTablesAsync(connection, sessionId, cancellationToken);

            // Step 2: Populate staging tables with data
            if (options.GenerateData)
            {
                await PopulateWithGeneratedDataAsync(connection, sessionId, options, result, cancellationToken);
            }
            else
            {
                await PopulateWithFileDataAsync(connection, sessionId, options, result, cancellationToken);
            }

            // Step 3: Validate staged data
            var validationResult = await _tempStagingService.ValidateTempDataAsync(connection, sessionId, cancellationToken);
            result.FailedRows = validationResult.InvalidDriverRows + validationResult.InvalidVehicleRows;
            result.Errors.AddRange(validationResult.ValidationErrors);

            if (!validationResult.Success && _options.StopOnFirstError)
            {
                throw new InvalidOperationException($"Validation failed with {result.FailedRows} invalid rows. Stopping due to StopOnFirstError setting.");
            }

            // Step 4: Process valid data (merge to production or dry run)
            var processingResult = await _tempStagingService.MergeTempToProductionAsync(connection, sessionId, options.DryRun, cancellationToken);
            result.ProcessedRows = processingResult.ProcessedDrivers + processingResult.ProcessedVehicles;
            result.SuccessfulRows = result.ProcessedRows;
            result.Errors.AddRange(processingResult.ProcessingErrors);

            // Step 5: Generate summary
            var summary = await _tempStagingService.GetTempStagingSummaryAsync(connection, sessionId, cancellationToken);

            result.Success = validationResult.Success && processingResult.Success;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = CreateImportSummary(result, summary, options.DryRun);

            _logger.LogInformation("Temporary table bulk import completed {SessionId}. Success: {Success}, Duration: {Duration}",
                sessionId, result.Success, result.Duration);

            return result;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Import failed: {ex.Message}";

            _logger.LogError(ex, "Temporary table bulk import failed {SessionId}", sessionId);
            throw;
        }
        // Note: Temp tables will be automatically cleaned up when connection closes
    }

    private async Task PopulateWithGeneratedDataAsync(SqlConnection connection, Guid sessionId, ImportOptions options, ImportResult result, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Generating synthetic data for session {SessionId}", sessionId);

        // Generate driver data
        var driverData = new List<DriverImportModel>();
        if (options.DriversCount > 0)
        {
            driverData = GenerateDriverData(options.DriversCount.Value).ToList();
        }

        // Generate vehicle data
        var vehicleData = new List<VehicleImportModel>();
        if (options.VehiclesCount > 0)
        {
            vehicleData = GenerateVehicleData(options.VehiclesCount.Value).ToList();
        }

        // Populate temp tables
        await _tempStagingService.PopulateTempTablesAsync(connection, sessionId, driverData, vehicleData, cancellationToken);

        result.TotalRows = driverData.Count + vehicleData.Count;
        _logger.LogInformation("Generated and populated {TotalRows} rows in temp staging tables", result.TotalRows);
    }

    private async Task PopulateWithFileDataAsync(SqlConnection connection, Guid sessionId, ImportOptions options, ImportResult result, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing input files for session {SessionId}", sessionId);

        // For now, simulate file processing with empty data
        // In a real implementation, you would:
        // 1. Parse CSV files from options.InputFiles
        // 2. Convert to DriverImportModel/VehicleImportModel objects
        // 3. Populate temp tables

        var driverData = new List<DriverImportModel>();
        var vehicleData = new List<VehicleImportModel>();

        if (options.InputFiles.Any())
        {
            // TODO: Implement CSV file parsing
            _logger.LogWarning("File-based import not yet implemented. Using empty data set.");
        }

        await _tempStagingService.PopulateTempTablesAsync(connection, sessionId, driverData, vehicleData, cancellationToken);
        result.TotalRows = driverData.Count + vehicleData.Count;
    }

    private IEnumerable<DriverImportModel> GenerateDriverData(int count)
    {
        var random = new Random();
        var firstNames = new[] { "John", "Jane", "Michael", "Sarah", "David", "Lisa", "Robert", "Emily", "James", "Jessica" };
        var lastNames = new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez" };
        var customers = new[] { "FleetXQ Corp", "Transport Solutions", "Logistics Inc" };
        var sites = new[] { "Main Site", "North Branch", "South Branch", "East Depot", "West Terminal" };
        var departments = new[] { "Operations", "Maintenance", "Logistics", "Administration" };

        for (int i = 1; i <= count; i++)
        {
            var firstName = firstNames[random.Next(firstNames.Length)];
            var lastName = lastNames[random.Next(lastNames.Length)];
            var customer = customers[random.Next(customers.Length)];
            var site = sites[random.Next(sites.Length)];
            var department = departments[random.Next(departments.Length)];

            yield return new DriverImportModel
            {
                ExternalDriverId = $"EXT_DRV_{i:D6}",
                PersonFirstName = firstName,
                PersonLastName = lastName,
                PersonEmail = $"{firstName.ToLower()}.{lastName.ToLower()}@{customer.Replace(" ", "").ToLower()}.com",
                PersonPhone = $"555-{random.Next(100, 999)}-{random.Next(1000, 9999)}",
                DriverActive = random.Next(10) < 9, // 90% active
                DriverLicenseMode = random.Next(3),
                DriverVehicleAccess = random.Next(10) < 8, // 80% have access
                PersonIsActiveDriver = true,
                PersonHasLicense = true,
                PersonLicenseActive = true,
                PersonVehicleAccess = true,
                PersonCanUnlockVehicle = random.Next(10) < 5, // 50% can unlock
                PersonNormalDriverAccess = true,
                CustomerName = customer,
                SiteName = site,
                DepartmentName = department,
                Notes = $"Generated driver {i}"
            };
        }
    }

    private IEnumerable<VehicleImportModel> GenerateVehicleData(int count)
    {
        var random = new Random();
        var customers = new[] { "FleetXQ Corp", "Transport Solutions", "Logistics Inc" };
        var sites = new[] { "Main Site", "North Branch", "South Branch", "East Depot", "West Terminal" };
        var departments = new[] { "Operations", "Maintenance", "Logistics", "Administration" };
        var models = new[] { "Linde H25", "Toyota 8FB", "Crown FC5200", "Hyster J2.0XN" };
        var manufacturers = new[] { "Linde", "Toyota", "Crown", "Hyster" };
        var descriptions = new[] { "Forklift - Warehouse", "Reach Truck - High Bay", "Pallet Truck - Floor Level", "Order Picker - Multi Level", "Counterbalance - Outdoor" };

        for (int i = 1; i <= count; i++)
        {
            var customer = customers[random.Next(customers.Length)];
            var site = sites[random.Next(sites.Length)];
            var department = departments[random.Next(departments.Length)];
            var modelIndex = random.Next(models.Length);

            yield return new VehicleImportModel
            {
                ExternalVehicleId = $"EXT_VEH_{i:D6}",
                HireNo = $"FLT{i:D5}",
                SerialNo = $"SN{i + 50000:D5}",
                Description = descriptions[random.Next(descriptions.Length)],
                OnHire = random.Next(10) < 9, // 90% on hire
                ImpactLockout = random.Next(10) < 2, // 20% have impact lockout
                IsCanbus = random.Next(10) < 8, // 80% are CANbus
                TimeoutEnabled = true,
                ModuleIsConnected = random.Next(10) < 9, // 90% connected
                IDLETimer = random.Next(10) < 8 ? 300 : 600, // Most have 300, some 600
                CustomerName = customer,
                SiteName = site,
                DepartmentName = department,
                ModelName = models[modelIndex],
                ManufacturerName = manufacturers[modelIndex],
                ModuleSerialNumber = $"MOD{i + 10000:D5}"
            };
        }
    }

    private string CreateImportSummary(ImportResult result, TempStagingSummary summary, bool dryRun)
    {
        var mode = dryRun ? "DRY RUN" : "PRODUCTION";
        var status = result.Success ? "COMPLETED" : "FAILED";

        return $"{mode} Import {status}: " +
               $"Total: {result.TotalRows}, " +
               $"Processed: {result.ProcessedRows}, " +
               $"Successful: {result.SuccessfulRows}, " +
               $"Failed: {result.FailedRows}, " +
               $"Duration: {result.Duration.TotalSeconds:F2}s, " +
               $"Throughput: {(result.ProcessedRows / Math.Max(result.Duration.TotalSeconds, 1)):F0} rows/sec";
    }

    private void ValidateImportOptions(ImportOptions options)
    {
        if (options.DriversCount.HasValue && options.DriversCount <= 0)
            throw new ArgumentException("Drivers count must be positive", nameof(options.DriversCount));

        if (options.VehiclesCount.HasValue && options.VehiclesCount <= 0)
            throw new ArgumentException("Vehicles count must be positive", nameof(options.VehiclesCount));

        if (options.BatchSize.HasValue && (options.BatchSize <= 0 || options.BatchSize > _options.MaxBatchSize))
            throw new ArgumentException($"Batch size must be between 1 and {_options.MaxBatchSize}", nameof(options.BatchSize));

        if (!options.GenerateData && !options.InputFiles.Any())
            throw new ArgumentException("Must specify either data generation or input files");
    }

    private void ApplyDefaultOptions(ImportOptions options)
    {
        options.DriversCount ??= _options.DefaultDriversCount;
        options.VehiclesCount ??= _options.DefaultVehiclesCount;
        options.BatchSize ??= _options.DefaultBatchSize;
    }
}






