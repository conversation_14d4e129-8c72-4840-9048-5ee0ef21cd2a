using Serilog;
using XQ360.DataMigration.Implementations;
using XQ360.DataMigration.Interfaces;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Web.Services;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Web.Models;
using Microsoft.Extensions.Options;

var builder = WebApplication.CreateBuilder(args);

// Add Serilog
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

// Add services to the container.
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = true;
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
});

// Configure environment settings from appsettings.json
builder.Services.Configure<XQ360.DataMigration.Models.EnvironmentConfiguration>(
    builder.Configuration.GetSection("Migration"));

// Configure authentication settings
builder.Services.Configure<XQ360.DataMigration.Web.Models.AuthenticationConfiguration>(
    builder.Configuration.GetSection("Authentication"));

// Configure bulk seeder settings
builder.Services.Configure<BulkSeederConfiguration>(
    builder.Configuration.GetSection(BulkSeederConfiguration.SectionName));

// Configure observability settings
builder.Services.Configure<XQ360.DataMigration.Models.ObservabilityConfiguration>(
    builder.Configuration.GetSection("Observability"));

// Register migration services from the main project
builder.Services.AddScoped<MigrationOrchestrator>();
builder.Services.AddScoped<MigrationReportingService>();
builder.Services.AddScoped<EnvironmentConfigurationService>();
builder.Services.AddScoped<IEnvironmentConfigurationService>(provider =>
    provider.GetRequiredService<EnvironmentConfigurationService>());
builder.Services.AddScoped<IPermissionService, PermissionService>();

// Register observability service
builder.Services.AddScoped<XQ360.DataMigration.Services.IObservabilityService, XQ360.DataMigration.Services.ObservabilityService>();

// Register migration implementations
builder.Services.AddScoped<CardMigration>();
builder.Services.AddScoped<DriverBlacklistMigration>();
builder.Services.AddScoped<PersonMigration>();
builder.Services.AddScoped<PreOpChecklistMigration>();
builder.Services.AddScoped<SpareModuleMigration>();
builder.Services.AddScoped<SupervisorAccessMigration>();
builder.Services.AddScoped<VehicleAccessMigration>();
builder.Services.AddScoped<VehicleMigration>();
builder.Services.AddScoped<VehicleSyncMigration>();
builder.Services.AddScoped<WebsiteUserMigration>();

// Register web-specific services
builder.Services.AddScoped<IMigrationService, MigrationService>();
builder.Services.AddScoped<ICsvFormatValidator, CsvFormatValidator>();

// Register bulk seeder services
builder.Services.AddScoped<IBulkSeederService, BulkSeederService>();
builder.Services.AddScoped<ISqlDataGenerationService, SqlDataGenerationService>();

// Register Phase 1 optimized services - Foundation Infrastructure Enhancement
builder.Services.AddScoped<IStagingSchemaService, StagingSchemaService>();

// Register factory for singleton services to access scoped IEnvironmentConfigurationService
builder.Services.AddSingleton<IServiceScopeFactory>(provider => provider.GetRequiredService<IServiceScopeFactory>());

// Register singleton services with factory pattern to resolve scoped dependencies
builder.Services.AddSingleton<IForeignKeyLookupCacheService, ForeignKeyLookupCacheService>();
builder.Services.AddSingleton<IConnectionPoolService, ConnectionPoolService>();

builder.Services.AddScoped<IBulkInsertOptimizationService, BulkInsertOptimizationService>();

// Register Phase 4 services - Business Logic and Data Quality
builder.Services.AddScoped<XQ360.DataMigration.Web.Services.BusinessRules.IBusinessRuleValidationService, XQ360.DataMigration.Web.Services.BusinessRules.BusinessRuleValidationService>();
builder.Services.AddScoped<XQ360.DataMigration.Web.Services.DataQuality.IDataQualityService, XQ360.DataMigration.Web.Services.DataQuality.DataQualityService>();
builder.Services.AddScoped<XQ360.DataMigration.Web.Services.DuplicateDetection.IDuplicateDetectionService, XQ360.DataMigration.Web.Services.DuplicateDetection.DuplicateDetectionService>();
builder.Services.AddScoped<XQ360.DataMigration.Web.Services.TransactionManagement.IDistributedTransactionService, XQ360.DataMigration.Web.Services.TransactionManagement.DistributedTransactionService>();
builder.Services.AddScoped<XQ360.DataMigration.Web.Services.TransactionManagement.IGranularRollbackService, XQ360.DataMigration.Web.Services.TransactionManagement.GranularRollbackService>();

// Register Phase 5 services - Production Readiness and Monitoring (Conditional based on configuration)
var observabilityConfig = builder.Configuration.GetSection("Observability").Get<XQ360.DataMigration.Models.ObservabilityConfiguration>() ?? new XQ360.DataMigration.Models.ObservabilityConfiguration();

if (observabilityConfig.Monitoring.Enabled)
{
    builder.Services.AddSingleton<XQ360.DataMigration.Web.Services.Monitoring.IPerformanceMonitoringService, XQ360.DataMigration.Web.Services.Monitoring.PerformanceMonitoringService>();
}
else
{
    builder.Services.AddSingleton<XQ360.DataMigration.Web.Services.Monitoring.IPerformanceMonitoringService>(provider => new NullPerformanceMonitoringService());
}

if (observabilityConfig.Audit.Enabled)
{
    builder.Services.AddScoped<XQ360.DataMigration.Web.Services.Monitoring.IAuditTrailService, XQ360.DataMigration.Web.Services.Monitoring.AuditTrailService>();
}
else
{
    builder.Services.AddScoped<XQ360.DataMigration.Web.Services.Monitoring.IAuditTrailService>(provider => new NullAuditTrailService());
}

if (observabilityConfig.HealthChecks.Enabled)
{
    builder.Services.AddScoped<XQ360.DataMigration.Web.Services.Monitoring.IHealthCheckService, XQ360.DataMigration.Web.Services.Monitoring.HealthCheckService>();
}
else
{
    builder.Services.AddScoped<XQ360.DataMigration.Web.Services.Monitoring.IHealthCheckService>(provider => new NullHealthCheckService());
}

if (observabilityConfig.Alerting.Enabled)
{
    builder.Services.AddSingleton<XQ360.DataMigration.Web.Services.Monitoring.IAlertingService, XQ360.DataMigration.Web.Services.Monitoring.AlertingService>();
}
else
{
    builder.Services.AddSingleton<XQ360.DataMigration.Web.Services.Monitoring.IAlertingService>(provider => new NullAlertingService());
}

builder.Services.AddScoped<XQ360.DataMigration.Web.Services.Security.ISecurityService, XQ360.DataMigration.Web.Services.Security.SecurityService>();

// Configure Phase 5 options
builder.Services.Configure<XQ360.DataMigration.Web.Services.Monitoring.AuditConfiguration>(options =>
{
    options.DefaultRetentionPeriod = TimeSpan.FromDays(90);
    options.EnableAutomaticCleanup = true;
    options.MaskSensitiveData = true;
});

builder.Services.Configure<XQ360.DataMigration.Web.Services.Monitoring.AlertConfiguration>(options =>
{
    options.EnableAutoResolution = true;
    options.EnableEscalation = true;
    options.EnableDeduplication = true;
});

// Register Phase 2 services - Migration Pattern Integration
builder.Services.AddScoped<IApiOrchestrationService, ApiOrchestrationService>();
builder.Services.AddScoped<IComplexEntityCreationService, ComplexEntityCreationService>();
builder.Services.AddScoped<IMigrationPatternSeederService, MigrationPatternSeederService>();

// Add HTTP client for API calls - specifically configured for XQ360ApiClient
builder.Services.AddHttpClient<XQ360ApiClient>();

// Configure XQ360ApiClient to get MigrationConfiguration from EnvironmentConfigurationService
builder.Services.AddScoped<IOptions<MigrationConfiguration>>(provider =>
{
    var envService = provider.GetRequiredService<IEnvironmentConfigurationService>();
    var migrationConfig = envService.CurrentMigrationConfiguration;
    return Microsoft.Extensions.Options.Options.Create(migrationConfig);
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.UseAuthorization();

// Add SignalR hub for real-time progress updates
app.MapHub<MigrationHub>("/migrationHub");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
