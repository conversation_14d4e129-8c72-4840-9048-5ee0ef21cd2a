export interface Notification {
    id: string
    type: NotificationType
    title: string
    message: string
    timestamp: Date
    isRead: boolean
    isPersistent: boolean
    actionUrl?: string
    actionText?: string
    autoHideDelay?: number
    data?: Record<string, any>
}

export type NotificationType =
    | 'success'
    | 'info'
    | 'warning'
    | 'error'

export interface CreateNotificationRequest {
    type: NotificationType
    title: string
    message: string
    isPersistent?: boolean
    actionUrl?: string
    actionText?: string
    autoHideDelay?: number
    duration?: number  // Alias for autoHideDelay
    data?: Record<string, any>
}

export interface NotificationFilter {
    type?: NotificationType[]
    isRead?: boolean
    fromDate?: Date
    toDate?: Date
    limit?: number
    offset?: number
}

export interface NotificationSearchResult {
    notifications: Notification[]
    total: number
    unreadCount: number
    hasMore: boolean
}
