# XQ360 Data Migration - Remote Deployment Script
# This script automates deployment to a remote Windows Server with IIS

param(
    [string]$ServerName = "localhost",
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [string]$AppPoolName = "XQ360MigrationPool",
    [string]$WebsiteName = "XQ360Migration",
    [int]$Port = 80,
    [switch]$SkipBuild = $false,
    [switch]$SkipIISConfig = $false,
    [switch]$TestOnly = $false
)

Write-Host "XQ360 Data Migration - Remote Deployment Script" -ForegroundColor Green
Write-Host "Target Server: $ServerName" -ForegroundColor Yellow
Write-Host "Deploy Path: $DeployPath" -ForegroundColor Yellow
Write-Host "Port: $Port" -ForegroundColor Yellow

# Step 1: Build Applications
if (-not $SkipBuild) {
    Write-Host "Building applications..." -ForegroundColor Yellow
    
    # Build main application
    Write-Host "Building main application..." -ForegroundColor Cyan
    dotnet build XQ360.DataMigration -c Release
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Main application build failed!" -ForegroundColor Red
        exit 1
    }
    
    # Build web application
    Write-Host "Building web application..." -ForegroundColor Cyan
    dotnet build XQ360.DataMigration.Web -c Release
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Web application build failed!" -ForegroundColor Red
        exit 1
    }
    
    # Build test application
    Write-Host "Building test application..." -ForegroundColor Cyan
    dotnet build XQ360.DataMigration.Tests -c Release
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Test application build failed!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ All applications built successfully!" -ForegroundColor Green
}

# Step 2: Publish Applications
Write-Host "Publishing applications..." -ForegroundColor Yellow

$tempPath = "C:\temp\XQ360Deploy"
if (Test-Path $tempPath) {
    Remove-Item $tempPath -Recurse -Force
}
New-Item -ItemType Directory -Path $tempPath -Force

# Publish web application
Write-Host "Publishing web application..." -ForegroundColor Cyan
# Try multiple methods to handle appsettings.json conflicts
$publishSuccess = $false

# Method 1: Standard publish
dotnet publish XQ360.DataMigration.Web -c Release -o "$tempPath\Web" --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Web application published successfully!" -ForegroundColor Green
    $publishSuccess = $true
} else {
    Write-Host "❌ Standard publish failed, trying alternative methods..." -ForegroundColor Yellow
    
    # Method 2: Publish with different output directory
    dotnet publish XQ360.DataMigration.Web -c Release -o "$tempPath\WebTemp" --verbosity quiet
    if ($LASTEXITCODE -eq 0) {
        if (Test-Path "$tempPath\Web") {
            Remove-Item "$tempPath\Web" -Recurse -Force
        }
        Move-Item "$tempPath\WebTemp" "$tempPath\Web"
        Write-Host "✅ Web application published successfully (alternative method)!" -ForegroundColor Green
        $publishSuccess = $true
    } else {
        Write-Host "❌ Alternative publish failed, trying manual build..." -ForegroundColor Yellow
        
        # Method 3: Manual build and copy
        dotnet build XQ360.DataMigration.Web -c Release -o "$tempPath\WebBuild"
        if ($LASTEXITCODE -eq 0) {
            if (Test-Path "$tempPath\Web") {
                Remove-Item "$tempPath\Web" -Recurse -Force
            }
            New-Item -ItemType Directory -Path "$tempPath\Web" -Force
            Copy-Item "$tempPath\WebBuild\*" -Destination "$tempPath\Web" -Recurse -Force
            Write-Host "✅ Web application built and copied successfully!" -ForegroundColor Green
            $publishSuccess = $true
        } else {
            Write-Host "❌ All publish methods failed!" -ForegroundColor Red
            Write-Host "Please check for build errors and try again." -ForegroundColor Yellow
            exit 1
        }
    }
}

if (-not $publishSuccess) {
    Write-Host "❌ Web application deployment failed!" -ForegroundColor Red
    exit 1
}

# Publish main application
Write-Host "Publishing main application..." -ForegroundColor Cyan
dotnet publish XQ360.DataMigration -c Release -o "$tempPath\Main"

# Copy test application
Write-Host "Copying test application..." -ForegroundColor Cyan
Copy-Item "XQ360.DataMigration.Tests" -Destination "$tempPath\Tests" -Recurse

Write-Host "✅ Applications published successfully!" -ForegroundColor Green

# Step 3: Configure IIS (if not skipped)
if (-not $SkipIISConfig) {
    Write-Host "Configuring IIS..." -ForegroundColor Yellow
    
    # Import WebAdministration module
Import-Module WebAdministration -ErrorAction SilentlyContinue
if (-not (Get-Module WebAdministration)) {
    Write-Host "❌ WebAdministration module not available." -ForegroundColor Red
    Write-Host "This script should be run on the remote Windows Server with IIS installed." -ForegroundColor Yellow
    Write-Host "Please install IIS with ASP.NET Core Hosting Bundle on the target server." -ForegroundColor Yellow
    Write-Host "For local testing, use: -SkipIISConfig" -ForegroundColor Cyan
    exit 1
}

# Enable WebSockets in IIS
Write-Host "Enabling WebSockets in IIS..." -ForegroundColor Cyan
try {
    # Enable WebSockets feature
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -All -ErrorAction SilentlyContinue
    Write-Host "✅ WebSockets feature enabled" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not enable WebSockets feature: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "This may already be enabled or require manual installation" -ForegroundColor Yellow
}
    
    # Create Application Pool
    Write-Host "Creating Application Pool: $AppPoolName" -ForegroundColor Cyan
    if (Get-WebAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
        Write-Host "Application Pool already exists, removing..." -ForegroundColor Yellow
        Remove-WebAppPool -Name $AppPoolName
    }
    New-WebAppPool -Name $AppPoolName
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    
    # Configure application pool for WebSockets
    Write-Host "Configuring application pool for WebSockets..." -ForegroundColor Cyan
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.pingInterval" -Value "00:00:30"
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.pingResponseTime" -Value "00:01:30"
    Write-Host "✅ Application pool configured for WebSockets" -ForegroundColor Green
    
    # Create website directory
    if (Test-Path $DeployPath) {
        Write-Host "Removing existing deployment directory..." -ForegroundColor Yellow
        Remove-Item $DeployPath -Recurse -Force
    }
    New-Item -ItemType Directory -Path $DeployPath -Force
    
    # Create website
Write-Host "Creating website: $WebsiteName" -ForegroundColor Cyan
$existingWebsite = Get-Website -Name $WebsiteName -ErrorAction SilentlyContinue
if ($existingWebsite) {
    Write-Host "Website already exists, stopping and removing..." -ForegroundColor Yellow
    try {
        Stop-Website -Name $WebsiteName -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 2
    } catch {
        Write-Host "Warning: Could not stop existing website" -ForegroundColor Yellow
    }
    Remove-Website -Name $WebsiteName -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 1
}

try {
    New-Website -Name $WebsiteName -PhysicalPath $DeployPath -ApplicationPool $AppPoolName -Port $Port
    Write-Host "✅ Website created successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create website: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Trying alternative creation method..." -ForegroundColor Yellow
    try {
        # Alternative method using New-WebSite with different parameters
        New-Website -Name $WebsiteName -PhysicalPath $DeployPath -ApplicationPool $AppPoolName -Port $Port -Force
        Write-Host "✅ Website created successfully (alternative method)!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to create website with alternative method: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}
    
    Write-Host "✅ IIS configuration completed!" -ForegroundColor Green
}

# Step 4: Deploy Files
Write-Host "Deploying application files..." -ForegroundColor Yellow

# Copy web application files
Write-Host "Copying web application files..." -ForegroundColor Cyan
Copy-Item "$tempPath\Web\*" -Destination $DeployPath -Recurse -Force

# Ensure wwwroot files are copied (static files)
Write-Host "Ensuring static files are copied..." -ForegroundColor Cyan
if (Test-Path "$tempPath\Web\wwwroot") {
    Write-Host "✅ wwwroot folder found in published output" -ForegroundColor Green
} else {
    Write-Host "⚠️ wwwroot folder not found in published output, copying manually..." -ForegroundColor Yellow
    if (Test-Path "XQ360.DataMigration.Web\wwwroot") {
        Copy-Item "XQ360.DataMigration.Web\wwwroot" -Destination "$DeployPath\wwwroot" -Recurse -Force
        Write-Host "✅ wwwroot folder copied manually" -ForegroundColor Green
    } else {
        Write-Host "❌ wwwroot folder not found in source!" -ForegroundColor Red
    }
}

# Ensure Views folder is copied
Write-Host "Ensuring Views folder is copied..." -ForegroundColor Cyan
if (Test-Path "$tempPath\Web\Views") {
    Write-Host "✅ Views folder found in published output" -ForegroundColor Green
} else {
    Write-Host "⚠️ Views folder not found in published output, copying manually..." -ForegroundColor Yellow
    if (Test-Path "XQ360.DataMigration.Web\Views") {
        Copy-Item "XQ360.DataMigration.Web\Views" -Destination "$DeployPath\Views" -Recurse -Force
        Write-Host "✅ Views folder copied manually" -ForegroundColor Green
    } else {
        Write-Host "❌ Views folder not found in source!" -ForegroundColor Red
    }
}

# Copy main application (for CLI operations)
Write-Host "Copying main application files..." -ForegroundColor Cyan
try {
    # Remove existing bin directory if it exists
    if (Test-Path "$DeployPath\bin") {
        Remove-Item "$DeployPath\bin" -Recurse -Force -ErrorAction SilentlyContinue
    }
    # Create bin directory and copy files
    New-Item -ItemType Directory -Path "$DeployPath\bin" -Force -ErrorAction SilentlyContinue
    Copy-Item "$tempPath\Main\*" -Destination "$DeployPath\bin" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Main application files copied successfully!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Warning: Could not copy main application files: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Copy test application (for database checking)
Write-Host "Copying test application files..." -ForegroundColor Cyan
if (Test-Path "$tempPath\Tests") {
    try {
        New-Item -ItemType Directory -Path "$DeployPath\tests" -Force -ErrorAction SilentlyContinue
        Copy-Item "$tempPath\Tests\*" -Destination "$DeployPath\tests" -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "✅ Test application files copied successfully!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Warning: Could not copy test application files: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️ Warning: Test application files not found in temp directory" -ForegroundColor Yellow
}

# Copy production configuration
Write-Host "Copying production configuration..." -ForegroundColor Cyan
if (Test-Path "deploy\production-appsettings.json") {
    try {
        Copy-Item "deploy\production-appsettings.json" -Destination "$DeployPath\appsettings.json" -Force -ErrorAction SilentlyContinue
        Write-Host "✅ Production configuration copied successfully!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Warning: Could not copy production configuration: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️ Warning: production-appsettings.json not found. Using default configuration." -ForegroundColor Yellow
}

# Create logs directory
try {
    New-Item -ItemType Directory -Path "$DeployPath\Logs" -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Logs directory created successfully!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Warning: Could not create logs directory: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "✅ Files deployed successfully!" -ForegroundColor Green

# Step 5: Set Permissions
Write-Host "Setting file permissions..." -ForegroundColor Yellow

# Set permissions for IIS_IUSRS
try {
    icacls $DeployPath /grant "IIS_IUSRS:(OI)(CI)(RX)" /T 2>$null
    icacls $DeployPath /grant "IIS_IUSRS:(OI)(CI)(M)" /T 2>$null
    Write-Host "✅ IIS_IUSRS permissions set successfully!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Warning: Could not set IIS_IUSRS permissions: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Set permissions for Application Pool Identity
try {
    icacls $DeployPath /grant "IIS AppPool\${AppPoolName}:(OI)(CI)(RX)" /T 2>$null
    icacls $DeployPath /grant "IIS AppPool\${AppPoolName}:(OI)(CI)(M)" /T 2>$null
    Write-Host "✅ Application Pool permissions set successfully!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Warning: Could not set Application Pool permissions: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Set permissions for logs directory
try {
    icacls "$DeployPath\Logs" /grant "IIS_IUSRS:(OI)(CI)(F)" /T 2>$null
    icacls "$DeployPath\Logs" /grant "IIS AppPool\${AppPoolName}:(OI)(CI)(F)" /T 2>$null
    Write-Host "✅ Logs directory permissions set successfully!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Warning: Could not set logs directory permissions: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "✅ Permissions set successfully!" -ForegroundColor Green

# Step 6: Configure Firewall
Write-Host "Configuring firewall..." -ForegroundColor Yellow

# Allow HTTP traffic
try {
    $firewallRule = Get-NetFirewallRule -DisplayName "XQ360 Migration Web" -ErrorAction SilentlyContinue
    if (-not $firewallRule) {
        New-NetFirewallRule -DisplayName "XQ360 Migration Web" -Direction Inbound -Protocol TCP -LocalPort $Port -Action Allow -ErrorAction SilentlyContinue
        Write-Host "✅ Firewall rule created for port $Port" -ForegroundColor Green
    } else {
        Write-Host "Firewall rule already exists" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Warning: Could not configure firewall (requires admin privileges): $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Please run this script as Administrator or configure firewall manually" -ForegroundColor Cyan
}

# Step 7: Start Website
Write-Host "Starting website..." -ForegroundColor Yellow

# Check if website exists and get its current state
$website = Get-Website -Name $WebsiteName -ErrorAction SilentlyContinue
if ($website) {
    if ($website.State -eq "Started") {
        Write-Host "Website is already running" -ForegroundColor Green
    } else {
        try {
            Start-Website -Name $WebsiteName
            Write-Host "✅ Website started successfully!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to start website: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Trying to restart website..." -ForegroundColor Yellow
            try {
                Stop-Website -Name $WebsiteName -ErrorAction SilentlyContinue
                Start-Sleep -Seconds 2
                Start-Website -Name $WebsiteName
                Write-Host "✅ Website restarted successfully!" -ForegroundColor Green
            } catch {
                Write-Host "❌ Failed to restart website: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "⚠️ Warning: Website start failed, but deployment may still be successful" -ForegroundColor Yellow
            }
        }
    }
} else {
    Write-Host "❌ Website '$WebsiteName' not found!" -ForegroundColor Red
    Write-Host "⚠️ Warning: Website not found, but deployment may still be successful" -ForegroundColor Yellow
}

# Step 8: Test Deployment
Write-Host "Testing deployment..." -ForegroundColor Yellow

$testUrl = "http://$ServerName"
if ($ServerName -ne "localhost") {
    $testUrl = "http://$ServerName"
}

try {
    $response = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Web application is accessible at: $testUrl" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Web application responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to access web application: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "This may be normal if testing from a different machine or if firewall is blocking access." -ForegroundColor Yellow
    Write-Host "Please test manually by accessing: $testUrl" -ForegroundColor Cyan
}

# Step 9: Cleanup
Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
if (Test-Path $tempPath) {
    Remove-Item $tempPath -Recurse -Force
}

# Step 10: Summary
Write-Host "" -ForegroundColor White
Write-Host "Deployment Summary:" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green
Write-Host "✅ Applications built and published" -ForegroundColor White
Write-Host "✅ IIS configured with Application Pool: $AppPoolName" -ForegroundColor White
Write-Host "✅ Website created: $WebsiteName" -ForegroundColor White
Write-Host "✅ Files deployed to: $DeployPath" -ForegroundColor White
Write-Host "✅ Permissions configured" -ForegroundColor White
Write-Host "✅ Firewall rules added" -ForegroundColor White
Write-Host "✅ Website started" -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host "Access URLs:" -ForegroundColor Cyan
Write-Host "   Local: http://localhost" -ForegroundColor White
Write-Host "   Remote: http://$ServerName" -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "   1. Update production-appsettings.json with real credentials" -ForegroundColor White
Write-Host "   2. Test environment selection in web UI" -ForegroundColor White
Write-Host "   3. Test database checking functionality" -ForegroundColor White
Write-Host "   4. Configure SSL certificate if needed" -ForegroundColor White
Write-Host "   5. Set up monitoring and logging" -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host "For detailed instructions, see: deploy\README-REMOTE-DEPLOYMENT.md" -ForegroundColor Cyan 