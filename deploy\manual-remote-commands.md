# Manual Remote Server Commands

## Quick Fix Commands

Run these commands on your remote server to fix the "Migration status unclear" issues:

### 1. Fix Missing CSV_Input Directory

```powershell
# Create CSV_Input directory
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" -Force

# Copy CSV files from template to input
Copy-Item "C:\inetpub\wwwroot\XQ360Migration\CSV_Template\*.csv" "C:\inetpub\wwwroot\XQ360Migration\CSV_Input\" -Force

# Set permissions
icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "NETWORK SERVICE:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "IIS AppPool\XQ360Migration:(OI)(CI)F" /T
```

### 2. Fix Logs Directory Permissions

```powershell
# Create Logs directory if it doesn't exist
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration\Logs" -Force

# Set permissions for Logs directory
icacls "C:\inetpub\wwwroot\XQ360Migration\Logs" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\Logs" /grant "NETWORK SERVICE:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\Logs" /grant "IIS AppPool\XQ360Migration:(OI)(CI)F" /T
```

### 3. Test Write Access

```powershell
# Test CSV_Input write access
$testFile = "C:\inetpub\wwwroot\XQ360Migration\CSV_Input\test-write.tmp"
"Test write access $(Get-Date)" | Out-File -FilePath $testFile -Encoding UTF8
if (Test-Path $testFile) {
    Remove-Item $testFile -Force
    Write-Host "✓ CSV_Input write access OK" -ForegroundColor Green
} else {
    Write-Host "✗ CSV_Input write access failed" -ForegroundColor Red
}

# Test Logs write access
$testLogFile = "C:\inetpub\wwwroot\XQ360Migration\Logs\test-write.tmp"
"Test write access $(Get-Date)" | Out-File -FilePath $testLogFile -Encoding UTF8
if (Test-Path $testLogFile) {
    Remove-Item $testLogFile -Force
    Write-Host "✓ Logs write access OK" -ForegroundColor Green
} else {
    Write-Host "✗ Logs write access failed" -ForegroundColor Red
}
```

### 4. Check Current Status

```powershell
# Check if CSV files exist
$csvFiles = Get-ChildItem "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" -Filter "*.csv"
Write-Host "Found $($csvFiles.Count) CSV files in CSV_Input:" -ForegroundColor Cyan
foreach ($file in $csvFiles) {
    Write-Host "  - $($file.Name)" -ForegroundColor Gray
}

# Check application pool status
$appPool = Get-IISAppPool -Name "XQ360Migration" -ErrorAction SilentlyContinue
if ($appPool) {
    Write-Host "Application pool status: $($appPool.State)" -ForegroundColor Cyan
} else {
    Write-Host "Application pool 'XQ360Migration' not found!" -ForegroundColor Red
}

# Check running processes
$dotnetProcesses = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue
Write-Host "Found $($dotnetProcesses.Count) dotnet processes running" -ForegroundColor Cyan
```

### 5. Restart Application Pool

```powershell
# Restart the application pool
Restart-WebAppPool -Name "XQ360Migration"
Write-Host "✓ Application pool restarted" -ForegroundColor Green
```

## Diagnostic Commands

### Check Recent Logs

```powershell
# Find the most recent log file
$logsPath = "C:\inetpub\wwwroot\XQ360Migration\Logs"
$latestLog = Get-ChildItem $logsPath -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($latestLog) {
    Write-Host "Latest log: $($latestLog.Name)" -ForegroundColor Cyan
    Write-Host "Last modified: $($latestLog.LastWriteTime)" -ForegroundColor Gray
    
    # Show last 20 lines
    Write-Host "Last 20 lines:" -ForegroundColor Yellow
    Get-Content $latestLog.FullName | Select-Object -Last 20
} else {
    Write-Host "No log files found!" -ForegroundColor Red
}
```

### Check for Specific Errors

```powershell
# Check for DirectoryNotFoundException errors
$errorLines = Get-Content $latestLog.FullName | Select-String -Pattern "DirectoryNotFoundException|Could not find a part of the path" | Select-Object -Last 5
if ($errorLines) {
    Write-Host "Found DirectoryNotFoundException errors:" -ForegroundColor Red
    foreach ($line in $errorLines) {
        Write-Host "  $($line)" -ForegroundColor Red
    }
}

# Check for ObjectDisposedException errors
$disposedErrors = Get-Content $latestLog.FullName | Select-String -Pattern "ObjectDisposedException|Cannot access a disposed object" | Select-Object -Last 5
if ($disposedErrors) {
    Write-Host "Found ObjectDisposedException errors:" -ForegroundColor Red
    foreach ($line in $disposedErrors) {
        Write-Host "  $($line)" -ForegroundColor Red
    }
}
```

### Test SignalR Hub

```powershell
# Test SignalR hub accessibility
try {
    $response = Invoke-WebRequest -Uri "http://localhost/migrationHub/negotiate?negotiateVersion=1" -Method POST -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ SignalR hub is accessible" -ForegroundColor Green
    } else {
        Write-Host "✗ SignalR hub returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ SignalR hub test failed: $($_.Exception.Message)" -ForegroundColor Red
}
```

## Complete Fix Sequence

Run these commands in order:

```powershell
# 1. Create and populate CSV_Input directory
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" -Force
Copy-Item "C:\inetpub\wwwroot\XQ360Migration\CSV_Template\*.csv" "C:\inetpub\wwwroot\XQ360Migration\CSV_Input\" -Force

# 2. Set permissions
icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "NETWORK SERVICE:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "IIS AppPool\XQ360Migration:(OI)(CI)F" /T

# 3. Ensure Logs directory exists with permissions
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration\Logs" -Force
icacls "C:\inetpub\wwwroot\XQ360Migration\Logs" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\Logs" /grant "NETWORK SERVICE:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\Logs" /grant "IIS AppPool\XQ360Migration:(OI)(CI)F" /T

# 4. Test write access
$testFile = "C:\inetpub\wwwroot\XQ360Migration\CSV_Input\test-write.tmp"
"Test write access $(Get-Date)" | Out-File -FilePath $testFile -Encoding UTF8
if (Test-Path $testFile) {
    Remove-Item $testFile -Force
    Write-Host "✓ Write access test successful" -ForegroundColor Green
}

# 5. Restart application pool
Restart-WebAppPool -Name "XQ360Migration"

# 6. Verify the fix
$csvFiles = Get-ChildItem "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" -Filter "*.csv"
Write-Host "✓ CSV_Input now contains $($csvFiles.Count) files" -ForegroundColor Green
```

## After Running the Fix

1. **Test the migration**: Try uploading a CSV file and starting a migration
2. **Check the status**: The status should now update properly instead of showing "unclear"
3. **Monitor logs**: Watch the logs for any remaining errors

## If Issues Persist

If you still see "Migration status unclear" after running these fixes:

1. **Check the logs** for new error messages
2. **Verify CSV files** are in the correct format
3. **Test SignalR connection** in the browser console
4. **Restart the entire IIS service** if needed: `iisreset`

The main issues causing "Migration status unclear" are:
- ✅ **Missing CSV_Input directory** (fixed by creating directory and copying files)
- ✅ **Permission issues** (fixed by setting proper ACLs)
- ⚠️ **ObjectDisposedException** (requires code fix and redeployment) 