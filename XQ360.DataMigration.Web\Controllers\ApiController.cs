using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// General API controller for supporting the seeder UI
/// Provides endpoints for dealers, customers, and validation
/// </summary>
[ApiController]
[Route("api")]
[Produces("application/json")]
public class ApiController : ControllerBase
{
    private readonly ILogger<ApiController> _logger;
    private readonly IEnvironmentConfigurationService _environmentService;

    public ApiController(ILogger<ApiController> logger, IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    /// <summary>
    /// Get all active dealers
    /// </summary>
    /// <param name="query">Optional search query to filter dealers</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 100)</param>
    /// <param name="activeOnly">Whether to return only active dealers (default: true)</param>
    /// <returns>List of dealers</returns>
    [HttpGet("dealers")]
    [ProducesResponseType(typeof(DealerListResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DealerListResponse>> GetDealers(
        [FromQuery] string? query = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            // Validate parameters
            if (pageNumber < 1)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page number",
                    Detail = "Page number must be greater than 0",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page size",
                    Detail = "Page size must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Getting dealers with query: {Query}, page: {PageNumber}, size: {PageSize}, activeOnly: {ActiveOnly}",
                query, pageNumber, pageSize, activeOnly);

            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();

            // Build the SQL query with optional filtering
            var whereConditions = new List<string>();
            var parameters = new List<SqlParameter>();

            if (activeOnly)
            {
                whereConditions.Add("Active = @Active");
                parameters.Add(new SqlParameter("@Active", true));
            }

            if (!string.IsNullOrWhiteSpace(query))
            {
                whereConditions.Add("(Name LIKE @Query OR PortalURL LIKE @Query)");
                parameters.Add(new SqlParameter("@Query", $"%{query.Trim()}%"));
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countSql = $"SELECT COUNT(*) FROM dbo.Dealer {whereClause}";
            using var countCommand = new SqlCommand(countSql, connection);
            countCommand.Parameters.AddRange(parameters.ToArray());
            var totalCount = (int)await countCommand.ExecuteScalarAsync();

            // Get paginated results
            var sql = $@"
                SELECT Id, Name, PortalURL, Active
                FROM dbo.Dealer
                {whereClause}
                ORDER BY Name
                OFFSET @Offset ROWS
                FETCH NEXT @PageSize ROWS ONLY";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddRange(parameters.ToArray());
            command.Parameters.Add(new SqlParameter("@Offset", (pageNumber - 1) * pageSize));
            command.Parameters.Add(new SqlParameter("@PageSize", pageSize));

            var dealers = new List<DealerInfo>();
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                dealers.Add(new DealerInfo
                {
                    Id = reader.GetGuid(reader.GetOrdinal("Id")).ToString(),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Subdomain = reader.IsDBNull(reader.GetOrdinal("PortalURL")) ? "" : reader.GetString(reader.GetOrdinal("PortalURL")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("Active"))
                });
            }

            var response = new DealerListResponse
            {
                Dealers = dealers,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            _logger.LogDebug("Retrieved {Count} dealers out of {TotalCount} total", dealers.Count, totalCount);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dealers");
            return Problem(
                title: "Database Error",
                detail: "An error occurred while retrieving dealers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get customers for a specific dealer
    /// </summary>
    /// <param name="dealerId">Dealer ID to get customers for</param>
    /// <param name="query">Optional search query to filter customers</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 100)</param>
    /// <param name="activeOnly">Whether to return only active customers (default: true)</param>
    /// <returns>List of customers for the dealer</returns>
    [HttpGet("customers")]
    [ProducesResponseType(typeof(CustomerListResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<CustomerListResponse>> GetCustomers(
        [FromQuery] string dealerId,
        [FromQuery] string? query = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            // Validate parameters
            if (string.IsNullOrEmpty(dealerId) || !Guid.TryParse(dealerId, out var dealerGuid))
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid dealer ID",
                    Detail = "Dealer ID must be a valid GUID",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageNumber < 1)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page number",
                    Detail = "Page number must be greater than 0",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page size",
                    Detail = "Page size must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Getting customers for dealer: {DealerId}, query: {Query}, page: {PageNumber}, size: {PageSize}, activeOnly: {ActiveOnly}",
                dealerId, query, pageNumber, pageSize, activeOnly);

            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();

            // Build the SQL query with filtering
            var whereConditions = new List<string> { "DealerId = @DealerId" };
            var parameters = new List<SqlParameter> { new SqlParameter("@DealerId", dealerGuid) };

            if (activeOnly)
            {
                whereConditions.Add("Active = @Active");
                parameters.Add(new SqlParameter("@Active", true));
            }

            if (!string.IsNullOrWhiteSpace(query))
            {
                whereConditions.Add("(CompanyName LIKE @Query OR ContactNumber LIKE @Query OR Email LIKE @Query)");
                parameters.Add(new SqlParameter("@Query", $"%{query.Trim()}%"));
            }

            var whereClause = "WHERE " + string.Join(" AND ", whereConditions);

            // Get total count
            var countSql = $"SELECT COUNT(*) FROM dbo.Customer {whereClause}";
            using var countCommand = new SqlCommand(countSql, connection);
            countCommand.Parameters.AddRange(parameters.ToArray());
            var totalCount = (int)await countCommand.ExecuteScalarAsync();

            // Get paginated results
            var sql = $@"
                SELECT Id, CompanyName, ContactNumber, Email, Active
                FROM dbo.Customer
                {whereClause}
                ORDER BY CompanyName
                OFFSET @Offset ROWS
                FETCH NEXT @PageSize ROWS ONLY";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddRange(parameters.ToArray());
            command.Parameters.Add(new SqlParameter("@Offset", (pageNumber - 1) * pageSize));
            command.Parameters.Add(new SqlParameter("@PageSize", pageSize));

            var customers = new List<CustomerInfo>();
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                customers.Add(new CustomerInfo
                {
                    Id = reader.GetGuid(reader.GetOrdinal("Id")).ToString(),
                    Name = reader.GetString(reader.GetOrdinal("CompanyName")),
                    ContactName = reader.IsDBNull(reader.GetOrdinal("ContactNumber")) ? null : reader.GetString(reader.GetOrdinal("ContactNumber")),
                    ContactEmail = reader.IsDBNull(reader.GetOrdinal("Email")) ? null : reader.GetString(reader.GetOrdinal("Email")),
                    DealerId = dealerId,
                    IsActive = reader.GetBoolean(reader.GetOrdinal("Active"))
                });
            }

            var response = new CustomerListResponse
            {
                Customers = customers,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                DealerId = dealerId
            };

            _logger.LogDebug("Retrieved {Count} customers out of {TotalCount} total for dealer {DealerId}", customers.Count, totalCount, dealerId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customers for dealer: {DealerId}", dealerId);
            return Problem(
                title: "Database Error",
                detail: "An error occurred while retrieving customers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Validate seeder configuration before starting
    /// </summary>
    /// <param name="request">Validation request</param>
    /// <returns>Validation result</returns>
    [HttpPost("bulk-seeder/validate")]
    public ActionResult<ValidationResult> ValidateSeederConfiguration([FromBody] ValidateSeederRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            var errors = new List<string>();

            // Validate dealer
            if (string.IsNullOrEmpty(request.DealerId))
            {
                errors.Add("Dealer selection is required");
            }

            // Validate customer
            if (string.IsNullOrEmpty(request.CustomerId))
            {
                errors.Add("Customer selection is required");
            }

            // Validate counts
            if (request.VehicleCount <= 0)
            {
                errors.Add("Vehicle count must be greater than 0");
            }

            if (request.DriverCount <= 0)
            {
                errors.Add("Driver count must be greater than 0");
            }

            // Check for reasonable limits
            if (request.VehicleCount > 100000)
            {
                errors.Add("Vehicle count exceeds maximum limit of 100,000");
            }

            if (request.DriverCount > 200000)
            {
                errors.Add("Driver count exceeds maximum limit of 200,000");
            }

            var result = new ValidationResult
            {
                Success = errors.Count == 0,
                ValidationErrors = errors,
                Summary = errors.Count == 0 ? "Configuration is valid" : $"Found {errors.Count} validation error(s)"
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating seeder configuration");
            return Problem(
                title: "Validation Error",
                detail: "An error occurred while validating the configuration",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Start a seeding operation
    /// </summary>
    /// <param name="request">Seeding start request</param>
    /// <returns>Session information</returns>
    [HttpPost("bulk-seeder/start")]
    public ActionResult<object> StartSeeding([FromBody] CreateSeederSessionRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // For now, return a mock session ID
            // In a real implementation, this would start the actual seeding process
            var sessionId = Guid.NewGuid();

            _logger.LogInformation("Started seeding session: {SessionId}", sessionId);

            return Ok(new
            {
                sessionId = sessionId,
                status = "Started",
                message = "Seeding operation started successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting seeding operation");
            return Problem(
                title: "Start Error",
                detail: "An error occurred while starting the seeding operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Cancel a seeding operation
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Cancellation result</returns>
    [HttpPost("bulk-seeder/cancel/{sessionId}")]
    public ActionResult<object> CancelSeeding(Guid sessionId)
    {
        try
        {
            _logger.LogInformation("Cancelled seeding session: {SessionId}", sessionId);

            return Ok(new
            {
                sessionId = sessionId,
                status = "Cancelled",
                message = "Seeding operation cancelled successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling seeding operation: {SessionId}", sessionId);
            return Problem(
                title: "Cancellation Error",
                detail: "An error occurred while cancelling the seeding operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }


}

/// <summary>
/// Request model for validating seeder configuration
/// </summary>
public class ValidateSeederRequest
{
    public string? DealerId { get; set; }
    public string? CustomerId { get; set; }
    public int VehicleCount { get; set; }
    public int DriverCount { get; set; }
}
