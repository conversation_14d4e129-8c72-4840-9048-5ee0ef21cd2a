using Microsoft.AspNetCore.Mvc;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// General API controller for supporting the seeder UI
/// Provides endpoints for dealers, customers, and validation
/// </summary>
[ApiController]
[Route("api")]
[Produces("application/json")]
public class ApiController : ControllerBase
{
    private readonly ILogger<ApiController> _logger;

    public ApiController(ILogger<ApiController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }



    /// <summary>
    /// Validate seeder configuration before starting
    /// </summary>
    /// <param name="request">Validation request</param>
    /// <returns>Validation result</returns>
    [HttpPost("bulk-seeder/validate")]
    public ActionResult<ValidationResult> ValidateSeederConfiguration([FromBody] ValidateSeederRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            var errors = new List<string>();

            // Validate dealer
            if (string.IsNullOrEmpty(request.DealerId))
            {
                errors.Add("Dealer selection is required");
            }

            // Validate customer
            if (string.IsNullOrEmpty(request.CustomerId))
            {
                errors.Add("Customer selection is required");
            }

            // Validate counts
            if (request.VehicleCount <= 0)
            {
                errors.Add("Vehicle count must be greater than 0");
            }

            if (request.DriverCount <= 0)
            {
                errors.Add("Driver count must be greater than 0");
            }

            // Check for reasonable limits
            if (request.VehicleCount > 100000)
            {
                errors.Add("Vehicle count exceeds maximum limit of 100,000");
            }

            if (request.DriverCount > 200000)
            {
                errors.Add("Driver count exceeds maximum limit of 200,000");
            }

            var result = new ValidationResult
            {
                Success = errors.Count == 0,
                ValidationErrors = errors,
                Summary = errors.Count == 0 ? "Configuration is valid" : $"Found {errors.Count} validation error(s)"
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating seeder configuration");
            return Problem(
                title: "Validation Error",
                detail: "An error occurred while validating the configuration",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Start a seeding operation
    /// </summary>
    /// <param name="request">Seeding start request</param>
    /// <returns>Session information</returns>
    [HttpPost("bulk-seeder/start")]
    public ActionResult<object> StartSeeding([FromBody] CreateSeederSessionRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // For now, return a mock session ID
            // In a real implementation, this would start the actual seeding process
            var sessionId = Guid.NewGuid();

            _logger.LogInformation("Started seeding session: {SessionId}", sessionId);

            return Ok(new 
            { 
                sessionId = sessionId,
                status = "Started",
                message = "Seeding operation started successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting seeding operation");
            return Problem(
                title: "Start Error",
                detail: "An error occurred while starting the seeding operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Cancel a seeding operation
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Cancellation result</returns>
    [HttpPost("bulk-seeder/cancel/{sessionId}")]
    public ActionResult<object> CancelSeeding(Guid sessionId)
    {
        try
        {
            _logger.LogInformation("Cancelled seeding session: {SessionId}", sessionId);

            return Ok(new 
            { 
                sessionId = sessionId,
                status = "Cancelled",
                message = "Seeding operation cancelled successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling seeding operation: {SessionId}", sessionId);
            return Problem(
                title: "Cancellation Error",
                detail: "An error occurred while cancelling the seeding operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }


}

/// <summary>
/// Request model for validating seeder configuration
/// </summary>
public class ValidateSeederRequest
{
    public string? DealerId { get; set; }
    public string? CustomerId { get; set; }
    public int VehicleCount { get; set; }
    public int DriverCount { get; set; }
}
