# Manual Deployment Guide for XQ360 Data Migration

This guide provides step-by-step instructions for manually deploying the XQ360 Data Migration application to IIS.

## Prerequisites

### Server Requirements
- Windows Server with IIS installed
- ASP.NET Core Hosting Bundle installed
- PowerShell with execution policy allowing scripts
- Administrator access

### Required Software
1. **IIS with ASP.NET Core Hosting Bundle**
   - Download from: https://dotnet.microsoft.com/download/dotnet/9.0
   - Install the "ASP.NET Core Runtime 9.0.x" and "Windows Hosting Bundle"

2. **IIS WebSockets Feature**
   - Enable via Windows Features or PowerShell

## Step 1: Prepare the Application

### 1.1 Build the Application
```powershell
# Navigate to your project directory
cd C:\path\to\XQ360.DataMigration

# Build all projects
dotnet build XQ360.DataMigration -c Release
dotnet build XQ360.DataMigration.Web -c Release
dotnet build XQ360.DataMigration.Tests -c Release
```

### 1.2 Publish the Web Application
```powershell
# Create a temporary publish directory
$publishPath = "C:\temp\XQ360Publish"
if (Test-Path $publishPath) {
    Remove-Item $publishPath -Recurse -Force
}
New-Item -ItemType Directory -Path $publishPath -Force

# Publish the web application
dotnet publish XQ360.DataMigration.Web -c Release -o "$publishPath\Web"

# Publish the main application
dotnet publish XQ360.DataMigration -c Release -o "$publishPath\Main"

# Copy test application
Copy-Item "XQ360.DataMigration.Tests" -Destination "$publishPath\Tests" -Recurse
```

## Step 2: Configure IIS

### 2.1 Enable Required IIS Features
```powershell
# Run as Administrator
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -All
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServer
Enable-WindowsOptionalFeature -Online -FeatureName IIS-CommonHttpFeatures
Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpErrors
Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpLogging
Enable-WindowsOptionalFeature -Online -FeatureName IIS-RequestFiltering
Enable-WindowsOptionalFeature -Online -FeatureName IIS-StaticContent
Enable-WindowsOptionalFeature -Online -FeatureName IIS-DefaultDocument
Enable-WindowsOptionalFeature -Online -FeatureName IIS-DirectoryBrowsing
Enable-WindowsOptionalFeature -Online -FeatureName IIS-ASPNET45
```

### 2.2 Create Application Pool
```powershell
# Import WebAdministration module
Import-Module WebAdministration

# Create application pool
$appPoolName = "XQ360MigrationPool"
if (Get-WebAppPool -Name $appPoolName -ErrorAction SilentlyContinue) {
    Remove-WebAppPool -Name $appPoolName
}
New-WebAppPool -Name $appPoolName

# Configure application pool
Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "managedRuntimeVersion" -Value ""
Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"

# Configure for WebSockets
Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"
Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.pingInterval" -Value "00:00:30"
Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.pingResponseTime" -Value "00:01:30"
```

### 2.3 Create Website Directory
```powershell
$deployPath = "C:\inetpub\wwwroot\XQ360Migration"
if (Test-Path $deployPath) {
    Remove-Item $deployPath -Recurse -Force
}
New-Item -ItemType Directory -Path $deployPath -Force
```

### 2.4 Create Website
```powershell
$websiteName = "XQ360Migration"
$port = 80

# Remove existing website if it exists
$existingWebsite = Get-Website -Name $websiteName -ErrorAction SilentlyContinue
if ($existingWebsite) {
    Stop-Website -Name $websiteName -ErrorAction SilentlyContinue
    Remove-Website -Name $websiteName -ErrorAction SilentlyContinue
}

# Create new website
New-Website -Name $websiteName -PhysicalPath $deployPath -ApplicationPool $appPoolName -Port $port
```

## Step 3: Deploy Application Files

### 3.1 Copy Web Application Files
```powershell
# Copy web application files
Copy-Item "$publishPath\Web\*" -Destination $deployPath -Recurse -Force

# Ensure wwwroot files are copied
if (-not (Test-Path "$deployPath\wwwroot")) {
    Copy-Item "XQ360.DataMigration.Web\wwwroot" -Destination "$deployPath\wwwroot" -Recurse -Force
}
```

### 3.2 Copy Supporting Applications
```powershell
# Copy main application (for CLI operations)
New-Item -ItemType Directory -Path "$deployPath\bin" -Force -ErrorAction SilentlyContinue
Copy-Item "$publishPath\Main\*" -Destination "$deployPath\bin" -Recurse -Force -ErrorAction SilentlyContinue

# Copy test application
New-Item -ItemType Directory -Path "$deployPath\tests" -Force -ErrorAction SilentlyContinue
Copy-Item "$publishPath\Tests\*" -Destination "$deployPath\tests" -Recurse -Force -ErrorAction SilentlyContinue
```

### 3.3 Copy Configuration Files
```powershell
# Copy production configuration if it exists
if (Test-Path "deploy\production-appsettings.json") {
    Copy-Item "deploy\production-appsettings.json" -Destination "$deployPath\appsettings.json" -Force
}

# Create logs directory
New-Item -ItemType Directory -Path "$deployPath\Logs" -Force -ErrorAction SilentlyContinue
```

## Step 4: Set File Permissions

### 4.1 Set IIS Permissions
```powershell
# Set permissions for IIS_IUSRS
icacls $deployPath /grant "IIS_IUSRS:(OI)(CI)(RX)" /T
icacls $deployPath /grant "IIS_IUSRS:(OI)(CI)(M)" /T

# Set permissions for Application Pool Identity
icacls $deployPath /grant "IIS AppPool\XQ360MigrationPool:(OI)(CI)(RX)" /T
icacls $deployPath /grant "IIS AppPool\XQ360MigrationPool:(OI)(CI)(M)" /T

# Set permissions for logs directory
icacls "$deployPath\Logs" /grant "IIS_IUSRS:(OI)(CI)(F)" /T
icacls "$deployPath\Logs" /grant "IIS AppPool\XQ360MigrationPool:(OI)(CI)(F)" /T
```

## Step 5: Configure Firewall

### 5.1 Create Firewall Rule
```powershell
# Create firewall rule for web traffic
$firewallRule = Get-NetFirewallRule -DisplayName "XQ360 Migration Web" -ErrorAction SilentlyContinue
if (-not $firewallRule) {
    New-NetFirewallRule -DisplayName "XQ360 Migration Web" -Direction Inbound -Protocol TCP -LocalPort $port -Action Allow
}
```

## Step 6: Start the Website

### 6.1 Start Website
```powershell
# Start the website
Start-Website -Name $websiteName

# Verify website is running
$website = Get-Website -Name $websiteName
Write-Host "Website State: $($website.State)"
```

## Step 7: Test the Deployment

### 7.1 Test Web Access
```powershell
# Test web access
$testUrl = "http://localhost"
try {
    $response = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Web application is accessible" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Web application responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to access web application: $($_.Exception.Message)" -ForegroundColor Red
}
```

### 7.2 Test Static Files
```powershell
# Test static file access
$staticUrls = @(
    "/lib/bootstrap/dist/css/bootstrap.min.css",
    "/lib/bootstrap/dist/js/bootstrap.bundle.min.js",
    "/lib/jquery/dist/jquery.min.js",
    "/css/site.css",
    "/js/site.js",
    "/XQ360.DataMigration.Web.styles.css"
)

foreach ($url in $staticUrls) {
    $fullUrl = "$testUrl$url"
    try {
        $response = Invoke-WebRequest -Uri $fullUrl -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $url is accessible" -ForegroundColor Green
        } else {
            Write-Host "❌ $url returned status: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $url is not accessible: $($_.Exception.Message)" -ForegroundColor Red
    }
}
```

### 7.3 Test SignalR
```powershell
# Test SignalR endpoint
try {
    $signalRUrl = "$testUrl/migrationHub"
    $response = Invoke-WebRequest -Uri $signalRUrl -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ SignalR hub endpoint is accessible" -ForegroundColor Green
    } else {
        Write-Host "⚠️ SignalR hub responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ SignalR hub endpoint not accessible: $($_.Exception.Message)" -ForegroundColor Red
}
```

## Step 8: Troubleshooting

### 8.1 Check IIS Logs
```powershell
# View recent IIS logs
Get-Content "C:\inetpub\logs\LogFiles\W3SVC1\*.log" | Select-Object -Last 50
```

### 8.2 Check Application Logs
```powershell
# View application logs
if (Test-Path "$deployPath\Logs") {
    Get-ChildItem "$deployPath\Logs\*.log" | ForEach-Object {
        Write-Host "=== $($_.Name) ===" -ForegroundColor Cyan
        Get-Content $_.FullName | Select-Object -Last 20
    }
}
```

### 8.3 Check Application Pool Status
```powershell
# Check application pool status
$appPool = Get-WebAppPool -Name $appPoolName
Write-Host "Application Pool State: $($appPool.State)" -ForegroundColor Yellow
```

### 8.4 Common Issues and Solutions

#### Issue: 500.19 Error
**Solution:** Install ASP.NET Core Hosting Bundle
```powershell
# Download and install from Microsoft website
# https://dotnet.microsoft.com/download/dotnet/9.0
```

#### Issue: 500.30 Error
**Solution:** Check application pool configuration
```powershell
# Ensure .NET CLR Version is set to "No Managed Code"
Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "managedRuntimeVersion" -Value ""
```

#### Issue: Static Files Not Loading
**Solution:** Check file permissions and ensure wwwroot is copied
```powershell
# Re-copy wwwroot folder
Copy-Item "XQ360.DataMigration.Web\wwwroot" -Destination "$deployPath\wwwroot" -Recurse -Force

# Check permissions
icacls "$deployPath\wwwroot" /grant "IIS_IUSRS:(OI)(CI)(RX)" /T
```

#### Issue: SignalR WebSocket Errors
**Solution:** Enable WebSockets and configure application pool
```powershell
# Enable WebSockets
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -All

# Configure application pool for WebSockets
Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"
```

## Step 9: Verification Checklist

- [ ] IIS is installed and configured
- [ ] ASP.NET Core Hosting Bundle is installed
- [ ] WebSockets feature is enabled
- [ ] Application pool is created and configured
- [ ] Website is created and running
- [ ] Application files are copied to deployment directory
- [ ] wwwroot folder is copied with all static files
- [ ] File permissions are set correctly
- [ ] Firewall rules are configured
- [ ] Web application is accessible
- [ ] Static files are loading
- [ ] SignalR hub is accessible
- [ ] No errors in IIS logs
- [ ] No errors in application logs

## Access URLs

- **Local Access:** http://localhost
- **Remote Access:** http://your-server-ip
- **SignalR Test:** http://your-server-ip/Home/SignalRTest
- **Bootstrap Test:** http://your-server-ip/Home/Test

## Next Steps

1. Update `appsettings.json` with your production database credentials
2. Test all migration functionality
3. Configure SSL certificate if needed
4. Set up monitoring and logging
5. Configure backup procedures 