using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using System.IO;
using System.Globalization;

namespace XQ360.DataMigration.Implementations
{
    public class VehicleSyncMigration
    {
        private readonly ILogger<VehicleSyncMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly XQ360ApiClient _apiClient;
        private readonly MigrationReportingService _reportingService;
        private readonly string _connectionString;

        public VehicleSyncMigration(
            ILogger<VehicleSyncMigration> logger,
            IOptions<MigrationConfiguration> config,
            XQ360ApiClient apiClient,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _apiClient = apiClient;
            _reportingService = reportingService;
            _connectionString = _config.DatabaseConnection;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath, Func<MigrationResult, Task>? progressCallback = null)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Vehicle Sync Settings migration using XQ360 API");

                // Step 1: Get Vehicle Device IDs from the CSV file used for import
                var deviceIds = await GetVehicleDeviceIdsFromCsvAsync(csvFilePath);
                if (deviceIds.Count == 0)
                {
                    _logger.LogWarning("No vehicles with Device IDs found in CSV file");
                    return new MigrationResult 
                    { 
                        Success = true, 
                        RecordsProcessed = 0,
                        Duration = DateTime.UtcNow - startTime
                    };
                }

                _logger.LogInformation($"Found {deviceIds.Count} vehicles with Device IDs to sync");

                // Step 2: Authenticate with API
                var authResult = await _apiClient.AuthenticateAsync();
                if (!authResult)
                {
                    return new MigrationResult
                    {
                        Success = false,
                        Errors = new List<string> { "Failed to authenticate with XQ360 API" },
                        Duration = DateTime.UtcNow - startTime
                    };
                }

                // Step 3: Execute sync for each vehicle
                result.RecordsProcessed = deviceIds.Count;
                var syncResult = await ExecuteVehicleSyncAsync(deviceIds, result);

                result.Success = syncResult.Success;
                result.RecordsInserted = syncResult.RecordsInserted; // Number of successful syncs
                result.RecordsSkipped = deviceIds.Count - syncResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = syncResult.Errors;
                result.Warnings = syncResult.Warnings;

                // Transfer detailed errors and warnings
                if (syncResult.DetailedErrors?.Any() == true)
                    result.DetailedErrors.AddRange(syncResult.DetailedErrors);
                if (syncResult.DetailedWarnings?.Any() == true)
                    result.DetailedWarnings.AddRange(syncResult.DetailedWarnings);

                _logger.LogInformation($"Vehicle Sync Settings migration completed: {result.RecordsInserted} synced, {result.RecordsSkipped} failed, Duration: {result.Duration}");

                // CRITICAL: Call progress callback BEFORE report generation so UI gets immediate update
                if (progressCallback != null)
                {
                    await progressCallback(result);
                }

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Vehicle Sync Settings Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Vehicle Sync Settings migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { $"Migration failed: {ex.Message}" },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private Task<List<string>> GetVehicleDeviceIdsFromCsvAsync(string csvFilePath)
        {
            _logger.LogInformation($"Reading Vehicle Device IDs from CSV file: {csvFilePath}");

            var deviceIds = new List<string>();

            if (!File.Exists(csvFilePath))
            {
                _logger.LogError($"CSV file not found: {csvFilePath}");
                return Task.FromResult(deviceIds);
            }

            try
            {
                using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                using var reader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
                
                var lineNumber = 0;
                var deviceIdColumnIndex = -1;
                
                while (!reader.EndOfStream)
                {
                    lineNumber++;
                    var line = reader.ReadLine();
                    
                    if (string.IsNullOrWhiteSpace(line))
                        continue;
                    
                    var columns = line.Split(',');
                    
                    // First line is header - find Device ID column index
                    if (lineNumber == 1)
                    {
                        for (int i = 0; i < columns.Length; i++)
                        {
                            if (columns[i].Trim('"').Equals("Device ID", StringComparison.OrdinalIgnoreCase))
                            {
                                deviceIdColumnIndex = i;
                                break;
                            }
                        }
                        
                        if (deviceIdColumnIndex == -1)
                        {
                            _logger.LogError("Device ID column not found in CSV header");
                            return Task.FromResult(deviceIds);
                        }
                        
                        _logger.LogDebug($"Found Device ID column at index {deviceIdColumnIndex}");
                        continue;
                    }
                    
                    // Data lines - extract Device ID
                    if (deviceIdColumnIndex < columns.Length)
                    {
                        var deviceId = columns[deviceIdColumnIndex].Trim().Trim('"');
                        
                        if (!string.IsNullOrWhiteSpace(deviceId) && !deviceId.Equals("NULL", StringComparison.OrdinalIgnoreCase))
                        {
                            if (!deviceIds.Contains(deviceId)) // Avoid duplicates
                            {
                                deviceIds.Add(deviceId);
                                _logger.LogDebug($"Found Device ID: {deviceId} (line {lineNumber})");
                            }
                        }
                        else
                        {
                            _logger.LogDebug($"Line {lineNumber} has no valid Device ID - skipped");
                        }
                    }
                }

                _logger.LogInformation($"Retrieved {deviceIds.Count} unique Device IDs from CSV file");
                return Task.FromResult(deviceIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error reading CSV file: {csvFilePath}");
                throw;
            }
        }

        private async Task<MigrationResult> ExecuteVehicleSyncAsync(List<string> deviceIds, MigrationResult sharedResult)
        {
            _logger.LogInformation("Executing Vehicle Sync Settings API calls...");

            var result = new MigrationResult();
            var errors = new List<string>();
            var warnings = new List<string>();
            int successCount = 0;
            int failureCount = 0;

            for (int i = 0; i < deviceIds.Count; i++)
            {
                var deviceId = deviceIds[i];
                
                try
                {
                    _logger.LogInformation($"Syncing vehicle settings for Device ID: {deviceId} ({i + 1}/{deviceIds.Count})");

                    // Prepare form data for the API call
                    var formData = new Dictionary<string, string>
                    {
                        { "DeviceId", deviceId }
                    };

                    // Call the sync vehicle settings API
                    var apiResult = await _apiClient.PostFormAsync<object>("iothubmanager/syncvehiclesettings", formData);

                    if (apiResult.Success)
                    {
                        successCount++;
                        _logger.LogInformation($"✅ Successfully synced settings for Device ID: {deviceId}");
                    }
                    else
                    {
                        failureCount++;
                        var errorMessage = $"Failed to sync settings for Device ID '{deviceId}': {apiResult.ErrorMessage ?? "Unknown error"}";
                        _logger.LogError(errorMessage);
                        errors.Add(errorMessage);

                        // Add detailed error for reporting
                        _reportingService.AddDetailedError(sharedResult, i + 1, ErrorTypes.DATABASE_ERROR,
                            $"API call failed for Device ID '{deviceId}'",
                            "DeviceId", deviceId,
                            "Check API connectivity and Device ID validity",
                            new Dictionary<string, string> 
                            {
                                { "DeviceId", deviceId },
                                { "ApiError", apiResult.ErrorMessage ?? "Unknown error" },
                                { "StatusCode", apiResult.StatusCode?.ToString() ?? "Unknown" }
                            });
                    }
                }
                catch (Exception ex)
                {
                    failureCount++;
                    var errorMessage = $"Exception while syncing Device ID '{deviceId}': {ex.Message}";
                    _logger.LogError(ex, errorMessage);
                    errors.Add(errorMessage);

                    // Add detailed error for reporting
                    _reportingService.AddDetailedError(sharedResult, i + 1, ErrorTypes.DATABASE_ERROR,
                        $"Exception during sync for Device ID '{deviceId}'",
                        "DeviceId", deviceId,
                        "Check logs for detailed exception information",
                        new Dictionary<string, string> 
                        {
                            { "DeviceId", deviceId },
                            { "Exception", ex.Message }
                        });
                }

                // Add a small delay between API calls to avoid overwhelming the server
                if (i < deviceIds.Count - 1)
                {
                    await Task.Delay(500); // 500ms delay
                }
            }

            result.Success = failureCount == 0; // Success only if no failures
            result.RecordsInserted = successCount;
            result.Errors = errors;
            result.Warnings = warnings;

            _logger.LogInformation($"Vehicle Sync completed: {successCount} successful, {failureCount} failed");

            return result;
        }
    }
} 