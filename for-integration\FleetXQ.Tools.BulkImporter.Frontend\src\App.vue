<template>
  <div id="app" class="min-vh-100 d-flex flex-column">
    <!-- Header -->
    <header class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
      <div class="container-fluid">
        <router-link to="/" class="navbar-brand text-dark">
          <strong>FleetXQ</strong> Bulk Importer
        </router-link>

        <div class="navbar-nav ms-auto">
          <span class="navbar-text text-muted small">
            Environment: <span class="badge bg-secondary text-dark">{{ currentEnvironment }}</span>
          </span>
        </div>
      </div>
    </header>

    <div class="d-flex flex-grow-1">
      <!-- Sidebar Navigation -->
      <nav class="sidebar sidebar-open">
        <div class="sidebar-content">
          <ul class="nav nav-pills flex-column">
            <li class="nav-item">
              <router-link
                to="/"
                class="nav-link"
                :class="{ active: $route.name === 'Home' }"
              >
                <i class="fas fa-home me-2"></i>
                Dashboard
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/import"
                class="nav-link"
                :class="{ active: $route.name === 'Import' }"
              >
                <i class="fas fa-upload me-2"></i>
                Bulk Import
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/sessions"
                class="nav-link"
                :class="{ active: $route.name === 'Sessions' }"
              >
                <i class="fas fa-history me-2"></i>
                Sessions
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/settings"
                class="nav-link"
                :class="{ active: $route.name === 'Settings' }"
              >
                <i class="fas fa-cog me-2"></i>
                Settings
              </router-link>
            </li>
          </ul>
        </div>
      </nav>

      <!-- Main Content -->
      <main class="main-content flex-grow-1">
        <router-view />
      </main>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-auto">
      <div class="container-fluid">
        <div class="row align-items-center">
          <div class="col-md-6">
            <small class="text-muted">
              © {{ currentYear }} FleetXQ. All rights reserved.
            </small>
          </div>
          <div class="col-md-6 text-md-end">
            <small class="text-muted">
              Version 1.0.0 | Status: <span class="text-dark">Online</span>
            </small>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useEnvironmentStore } from '@/stores/environment'

const environmentStore = useEnvironmentStore()

const currentEnvironment = computed(() => environmentStore.currentEnvironment?.name || 'Development')
const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
.navbar-brand {
  font-size: 1.2rem;
}

.badge {
  font-size: 0.65rem;
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  background-color: #ffffff;
  border-right: 1px solid #e9ecef;
  position: relative;
  overflow-y: auto;
  flex-shrink: 0;
}

.sidebar-content {
  padding: 0.75rem;
}

.sidebar .nav-link {
  color: #6c757d;
  border-radius: 0.375rem;
  margin-bottom: 0.125rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.sidebar .nav-link.active {
  background-color: #6c757d;
  color: white;
}

.main-content {
  flex-grow: 1;
  padding: 0.75rem;
}


</style>
