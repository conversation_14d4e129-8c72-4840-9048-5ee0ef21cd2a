using Microsoft.Data.SqlClient;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Configuration;
using FleetXQ.Tools.BulkImporter.Services;

namespace FleetXQ.Tools.BulkImporter.Examples;

/// <summary>
/// Example demonstrating temporary table-based bulk import functionality
/// Shows how to use the TempStagingService and TempBulkImportService
/// </summary>
public class TempTableExample
{
    /// <summary>
    /// Example 1: Basic temp table import with synthetic data
    /// </summary>
    public static async Task BasicTempTableImportExample()
    {
        Console.WriteLine("=== Basic Temp Table Import Example ===");

        // Setup services (in real app, this would be done via DI container)
        var serviceProvider = CreateServiceProvider();
        var tempBulkImportService = serviceProvider.GetRequiredService<ITempBulkImportService>();

        try
        {
            var options = new ImportOptions
            {
                DriversCount = 100,
                VehiclesCount = 50,
                GenerateData = true,
                DryRun = false,
                BatchSize = 25
            };

            Console.WriteLine($"Starting import: {options.DriversCount} drivers, {options.VehiclesCount} vehicles");

            var result = await tempBulkImportService.ExecuteImportAsync(options);

            Console.WriteLine($"Import Result:");
            Console.WriteLine($"  Success: {result.Success}");
            Console.WriteLine($"  Session ID: {result.SessionId}");
            Console.WriteLine($"  Total Rows: {result.TotalRows}");
            Console.WriteLine($"  Processed: {result.ProcessedRows}");
            Console.WriteLine($"  Successful: {result.SuccessfulRows}");
            Console.WriteLine($"  Failed: {result.FailedRows}");
            Console.WriteLine($"  Duration: {result.Duration.TotalSeconds:F2} seconds");
            Console.WriteLine($"  Summary: {result.Summary}");

            if (result.Errors.Any())
            {
                Console.WriteLine("Errors:");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"  - {error}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Example 2: Dry run with temp tables to validate data without changes
    /// </summary>
    public static async Task DryRunTempTableExample()
    {
        Console.WriteLine("=== Dry Run Temp Table Example ===");

        var serviceProvider = CreateServiceProvider();
        var tempBulkImportService = serviceProvider.GetRequiredService<ITempBulkImportService>();

        try
        {
            var options = new ImportOptions
            {
                DriversCount = 1000,
                VehiclesCount = 500,
                GenerateData = true,
                DryRun = true, // This is the key difference
                BatchSize = 100
            };

            Console.WriteLine($"Starting DRY RUN: {options.DriversCount} drivers, {options.VehiclesCount} vehicles");

            var result = await tempBulkImportService.ExecuteImportAsync(options);

            Console.WriteLine($"Dry Run Result:");
            Console.WriteLine($"  Would Process: {result.ProcessedRows} rows");
            Console.WriteLine($"  Would Succeed: {result.SuccessfulRows} rows");
            Console.WriteLine($"  Would Fail: {result.FailedRows} rows");
            Console.WriteLine($"  Validation Time: {result.Duration.TotalSeconds:F2} seconds");
            Console.WriteLine($"  Summary: {result.Summary}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Example 3: Manual temp table operations using TempStagingService directly
    /// </summary>
    public static async Task ManualTempTableExample()
    {
        Console.WriteLine("=== Manual Temp Table Operations Example ===");

        var serviceProvider = CreateServiceProvider();
        var tempStagingService = serviceProvider.GetRequiredService<ITempStagingService>();
        var connectionOptions = serviceProvider.GetRequiredService<IOptions<ConnectionStringOptions>>().Value;

        var sessionId = Guid.NewGuid();

        using var connection = new SqlConnection(connectionOptions.FleetXQConnection);
        await connection.OpenAsync();

        try
        {
            Console.WriteLine($"Session ID: {sessionId}");

            // Step 1: Create temp staging tables
            Console.WriteLine("Creating temporary staging tables...");
            await tempStagingService.CreateTempStagingTablesAsync(connection, sessionId);

            // Step 2: Create sample data
            var driverData = CreateSampleDriverData(10);
            var vehicleData = CreateSampleVehicleData(5);

            // Step 3: Populate temp tables
            Console.WriteLine("Populating temporary tables...");
            await tempStagingService.PopulateTempTablesAsync(connection, sessionId, driverData, vehicleData);

            // Step 4: Validate data
            Console.WriteLine("Validating staged data...");
            var validationResult = await tempStagingService.ValidateTempDataAsync(connection, sessionId);

            Console.WriteLine($"Validation Results:");
            Console.WriteLine($"  Valid Drivers: {validationResult.ValidDriverRows}");
            Console.WriteLine($"  Invalid Drivers: {validationResult.InvalidDriverRows}");
            Console.WriteLine($"  Valid Vehicles: {validationResult.ValidVehicleRows}");
            Console.WriteLine($"  Invalid Vehicles: {validationResult.InvalidVehicleRows}");
            Console.WriteLine($"  Overall Success: {validationResult.Success}");

            // Step 5: Get staging summary
            var summary = await tempStagingService.GetTempStagingSummaryAsync(connection, sessionId);
            Console.WriteLine($"Staging Summary:");
            Console.WriteLine($"  Total Driver Rows: {summary.TotalDriverRows}");
            Console.WriteLine($"  Total Vehicle Rows: {summary.TotalVehicleRows}");
            Console.WriteLine($"  Status: {summary.Status}");

            // Step 6: Merge to production (dry run)
            Console.WriteLine("Performing dry run merge...");
            var processingResult = await tempStagingService.MergeTempToProductionAsync(connection, sessionId, dryRun: true);

            Console.WriteLine($"Processing Results (DRY RUN):");
            Console.WriteLine($"  Would Process {processingResult.ProcessedDrivers} drivers");
            Console.WriteLine($"  Would Process {processingResult.ProcessedVehicles} vehicles");
            Console.WriteLine($"  Summary: {processingResult.Summary}");

            // Note: Temp tables will be automatically cleaned up when connection closes
            Console.WriteLine("Temp tables will be automatically cleaned up when connection closes.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    /// <summary>
    /// Example 4: Performance comparison between temp table and in-memory processing
    /// </summary>
    public static async Task PerformanceComparisonExample()
    {
        Console.WriteLine("=== Performance Comparison Example ===");

        var serviceProvider = CreateServiceProvider();
        var tempBulkImportService = serviceProvider.GetRequiredService<ITempBulkImportService>();

        var sizes = new[] { 100, 500, 1000, 5000 };

        foreach (var size in sizes)
        {
            Console.WriteLine($"\nTesting with {size} drivers and {size / 2} vehicles:");

            var options = new ImportOptions
            {
                DriversCount = size,
                VehiclesCount = size / 2,
                GenerateData = true,
                DryRun = true, // Use dry run for performance testing
                BatchSize = Math.Min(size / 4, 1000)
            };

            var startTime = DateTime.UtcNow;
            var result = await tempBulkImportService.ExecuteImportAsync(options);
            var endTime = DateTime.UtcNow;

            var duration = endTime - startTime;
            var throughput = result.TotalRows / Math.Max(duration.TotalSeconds, 0.001);

            Console.WriteLine($"  Duration: {duration.TotalSeconds:F2} seconds");
            Console.WriteLine($"  Throughput: {throughput:F0} rows/second");
            Console.WriteLine($"  Memory Efficiency: Using temp tables (constant memory)");
        }
    }

    /// <summary>
    /// Example 5: Error handling and recovery with temp tables
    /// </summary>
    public static async Task ErrorHandlingExample()
    {
        Console.WriteLine("=== Error Handling Example ===");

        var serviceProvider = CreateServiceProvider();
        var tempStagingService = serviceProvider.GetRequiredService<ITempStagingService>();
        var connectionOptions = serviceProvider.GetRequiredService<IOptions<ConnectionStringOptions>>().Value;

        var sessionId = Guid.NewGuid();

        using var connection = new SqlConnection(connectionOptions.FleetXQConnection);
        await connection.OpenAsync();

        try
        {
            Console.WriteLine("Creating temp tables...");
            await tempStagingService.CreateTempStagingTablesAsync(connection, sessionId);

            // Create data with intentional validation errors
            var driverData = new List<DriverImportModel>
            {
                // Valid driver
                new DriverImportModel
                {
                    PersonFirstName = "John",
                    PersonLastName = "Doe",
                    PersonEmail = "<EMAIL>",
                    CustomerName = "FleetXQ Corp",
                    SiteName = "Main Site",
                    DepartmentName = "Operations"
                },
                // Invalid driver - missing required fields
                new DriverImportModel
                {
                    PersonFirstName = "Jane",
                    // Missing LastName
                    PersonEmail = "<EMAIL>",
                    CustomerName = "FleetXQ Corp",
                    SiteName = "Main Site",
                    DepartmentName = "Operations"
                },
                // Invalid driver - invalid email format
                new DriverImportModel
                {
                    PersonFirstName = "Bob",
                    PersonLastName = "Smith",
                    PersonEmail = "invalid-email", // Bad email format
                    CustomerName = "Unknown Customer", // Non-existent customer
                    SiteName = "Main Site",
                    DepartmentName = "Operations"
                }
            };

            Console.WriteLine("Populating temp tables with test data (including invalid records)...");
            await tempStagingService.PopulateTempTablesAsync(connection, sessionId, driverData, new List<VehicleImportModel>());

            Console.WriteLine("Validating data (expecting some failures)...");
            var validationResult = await tempStagingService.ValidateTempDataAsync(connection, sessionId);

            Console.WriteLine($"Validation Results:");
            Console.WriteLine($"  Valid Drivers: {validationResult.ValidDriverRows}");
            Console.WriteLine($"  Invalid Drivers: {validationResult.InvalidDriverRows}");
            Console.WriteLine($"  Success: {validationResult.Success}");

            if (validationResult.ValidationErrors.Any())
            {
                Console.WriteLine("Validation Errors:");
                foreach (var error in validationResult.ValidationErrors)
                {
                    Console.WriteLine($"  - {error}");
                }
            }

            // Even with errors, we can still process valid records
            if (validationResult.ValidDriverRows > 0)
            {
                Console.WriteLine("Processing valid records only...");
                var processingResult = await tempStagingService.MergeTempToProductionAsync(connection, sessionId, dryRun: true);
                Console.WriteLine($"Would process {processingResult.ProcessedDrivers} valid drivers");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    #region Helper Methods

    private static ServiceProvider CreateServiceProvider()
    {
        var services = new ServiceCollection();

        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        // Add configuration
        services.Configure<BulkImporterOptions>(options =>
        {
            options.DefaultDriversCount = 1000;
            options.DefaultVehiclesCount = 500;
            options.DefaultBatchSize = 100;
            options.UseTempTables = true;
            options.TempTableMode = "SessionScoped";
            options.ValidationEnabled = true;
        });

        services.Configure<ConnectionStringOptions>(options =>
        {
            options.FleetXQConnection = "Server=(localdb)\\mssqllocaldb;Database=FleetXQ;Trusted_Connection=true;MultipleActiveResultSets=true;";
        });

        services.Configure<DataGenerationOptions>(options =>
        {
            options.GenerationBatchSize = 100;
        });

        // Add services
        services.AddSingleton<ITempStagingService, TempStagingService>();
        services.AddSingleton<ITempBulkImportService, TempBulkImportService>();
        services.AddSingleton<ISqlDataGenerationService, SqlDataGenerationService>();

        return services.BuildServiceProvider();
    }

    private static List<DriverImportModel> CreateSampleDriverData(int count)
    {
        var drivers = new List<DriverImportModel>();
        var random = new Random();

        for (int i = 1; i <= count; i++)
        {
            drivers.Add(new DriverImportModel
            {
                ExternalDriverId = $"EXT_DRV_{i:D3}",
                PersonFirstName = $"Driver{i}",
                PersonLastName = "Test",
                PersonEmail = $"driver{i}@example.com",
                CustomerName = "FleetXQ Corp",
                SiteName = "Main Site",
                DepartmentName = "Operations",
                DriverActive = true,
                PersonIsActiveDriver = true,
                PersonHasLicense = true,
                PersonVehicleAccess = true
            });
        }

        return drivers;
    }

    private static List<VehicleImportModel> CreateSampleVehicleData(int count)
    {
        var vehicles = new List<VehicleImportModel>();

        for (int i = 1; i <= count; i++)
        {
            vehicles.Add(new VehicleImportModel
            {
                ExternalVehicleId = $"EXT_VEH_{i:D3}",
                HireNo = $"FLT{i:D3}",
                SerialNo = $"SN{i:D3}",
                Description = $"Test Vehicle {i}",
                CustomerName = "FleetXQ Corp",
                SiteName = "Main Site",
                DepartmentName = "Operations",
                ModelName = "Test Model",
                ModuleSerialNumber = $"MOD{i:D3}",
                OnHire = true,
                IsCanbus = true
            });
        }

        return vehicles;
    }

    #endregion

    /// <summary>
    /// Interface for temp bulk import service (to avoid dependency issues in example)
    /// </summary>
    public interface ITempBulkImportService
    {
        Task<ImportResult> ExecuteImportAsync(ImportOptions options, CancellationToken cancellationToken = default);
    }
}

/// <summary>
/// Console program to run the temp table examples
/// </summary>
public class TempTableExampleProgram
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("FleetXQ Bulk Importer - Temporary Table Examples");
        Console.WriteLine("================================================");

        try
        {
            // Run examples
            await TempTableExample.BasicTempTableImportExample();
            Console.WriteLine();

            await TempTableExample.DryRunTempTableExample();
            Console.WriteLine();

            await TempTableExample.ManualTempTableExample();
            Console.WriteLine();

            await TempTableExample.PerformanceComparisonExample();
            Console.WriteLine();

            await TempTableExample.ErrorHandlingExample();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Example failed: {ex.Message}");
            Console.WriteLine($"Stack Trace: {ex.StackTrace}");
        }

        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}

