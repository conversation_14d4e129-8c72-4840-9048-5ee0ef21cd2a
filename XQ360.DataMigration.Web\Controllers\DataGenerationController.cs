using Microsoft.AspNetCore.Mvc;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// Controller for data generation operations
/// Provides endpoints for generating synthetic test data
/// </summary>
[ApiController]
[Route("api/data-generation")]
[Produces("application/json")]
public class DataGenerationController : ControllerBase
{
    private readonly ILogger<DataGenerationController> _logger;
    private readonly ISqlDataGenerationService _dataGenerationService;
    private readonly IEnvironmentConfigurationService _environmentService;

    public DataGenerationController(
        ILogger<DataGenerationController> logger,
        ISqlDataGenerationService dataGenerationService,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dataGenerationService = dataGenerationService ?? throw new ArgumentNullException(nameof(dataGenerationService));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    /// <summary>
    /// Generates synthetic driver data
    /// </summary>
    /// <param name="request">Driver generation request</param>
    /// <returns>Generation result</returns>
    /// <response code="200">Data generated successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("drivers")]
    [ProducesResponseType(typeof(DataGenerationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DataGenerationResult>> GenerateDrivers([FromBody] GenerateDriversRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new ValidationProblemDetails(ModelState));
            }

            _logger.LogInformation("Generating {Count} driver records for environment {Environment}", 
                request.Count, _environmentService.CurrentEnvironmentKey);

            // Set environment if specified
            if (!string.IsNullOrWhiteSpace(request.Environment))
            {
                _environmentService.SetCurrentEnvironment(request.Environment);
            }

            // Create a session for this generation operation
            var sessionId = await _dataGenerationService.CreateSeederSessionAsync(
                $"DriverGeneration_{DateTime.UtcNow:yyyyMMdd_HHmmss}");

            // Generate the driver data
            var result = await _dataGenerationService.GenerateDriverDataAsync(sessionId, request.Count);

            _logger.LogInformation("Driver generation completed: {Success}, {GeneratedRows} rows, Duration: {Duration}",
                result.Success, result.GeneratedRows, result.Duration);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate driver data");
            return Problem(
                title: "Internal Server Error",
                detail: "An error occurred while generating driver data",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Generates synthetic vehicle data
    /// </summary>
    /// <param name="request">Vehicle generation request</param>
    /// <returns>Generation result</returns>
    /// <response code="200">Data generated successfully</response>
    /// <response code="400">Invalid request parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("vehicles")]
    [ProducesResponseType(typeof(DataGenerationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DataGenerationResult>> GenerateVehicles([FromBody] GenerateVehiclesRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new ValidationProblemDetails(ModelState));
            }

            _logger.LogInformation("Generating {Count} vehicle records for environment {Environment}", 
                request.Count, _environmentService.CurrentEnvironmentKey);

            // Set environment if specified
            if (!string.IsNullOrWhiteSpace(request.Environment))
            {
                _environmentService.SetCurrentEnvironment(request.Environment);
            }

            // Create a session for this generation operation
            var sessionId = await _dataGenerationService.CreateSeederSessionAsync(
                $"VehicleGeneration_{DateTime.UtcNow:yyyyMMdd_HHmmss}");

            // Generate the vehicle data
            var result = await _dataGenerationService.GenerateVehicleDataAsync(sessionId, request.Count);

            _logger.LogInformation("Vehicle generation completed: {Success}, {GeneratedRows} rows, Duration: {Duration}",
                result.Success, result.GeneratedRows, result.Duration);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate vehicle data");
            return Problem(
                title: "Internal Server Error",
                detail: "An error occurred while generating vehicle data",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Validates staged data for a specific session
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Validation result</returns>
    /// <response code="200">Validation completed</response>
    /// <response code="404">Session not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("sessions/{sessionId}/validate")]
    [ProducesResponseType(typeof(ValidationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ValidationResult>> ValidateSession(Guid sessionId)
    {
        try
        {
            _logger.LogInformation("Validating staged data for session {SessionId}", sessionId);

            var result = await _dataGenerationService.ValidateStagedDataAsync(sessionId);

            _logger.LogInformation("Validation completed for session {SessionId}: {Success}, {ValidRows} valid, {InvalidRows} invalid",
                sessionId, result.Success, result.ValidRows, result.InvalidRows);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate session {SessionId}", sessionId);
            return Problem(
                title: "Internal Server Error",
                detail: "An error occurred while validating the session data",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Processes staged data for a specific session
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="dryRun">Whether to perform a dry run without actual changes</param>
    /// <returns>Processing result</returns>
    /// <response code="200">Processing completed</response>
    /// <response code="404">Session not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("sessions/{sessionId}/process")]
    [ProducesResponseType(typeof(ProcessingResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ProcessingResult>> ProcessSession(Guid sessionId, [FromQuery] bool dryRun = false)
    {
        try
        {
            _logger.LogInformation("Processing staged data for session {SessionId}, DryRun: {DryRun}", sessionId, dryRun);

            var result = await _dataGenerationService.ProcessStagedDataAsync(sessionId, dryRun);

            _logger.LogInformation("Processing completed for session {SessionId}: {Success}, {ProcessedRows} processed, {InsertedRows} inserted",
                sessionId, result.Success, result.ProcessedRows, result.InsertedRows);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process session {SessionId}", sessionId);
            return Problem(
                title: "Internal Server Error",
                detail: "An error occurred while processing the session data",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets generation statistics and limits for the current environment
    /// </summary>
    /// <returns>Generation statistics</returns>
    /// <response code="200">Statistics retrieved successfully</response>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(GenerationStatistics), StatusCodes.Status200OK)]
    public ActionResult<GenerationStatistics> GetStatistics()
    {
        var stats = new GenerationStatistics
        {
            Environment = _environmentService.CurrentEnvironmentKey,
            MaxDriversPerOperation = 100000,
            MaxVehiclesPerOperation = 50000,
            SupportedEnvironments = _environmentService.GetAvailableEnvironments()
                .Select(e => new EnvironmentInfo { Key = e.Key, Name = e.DisplayName, Description = e.Description })
                .ToList()
        };

        return Ok(stats);
    }
}

/// <summary>
/// Request model for generating drivers
/// </summary>
public class GenerateDriversRequest
{
    /// <summary>
    /// Target environment for generation
    /// </summary>
    public string? Environment { get; set; }

    /// <summary>
    /// Number of drivers to generate
    /// </summary>
    [System.ComponentModel.DataAnnotations.Range(1, 100000)]
    public int Count { get; set; }
}

/// <summary>
/// Request model for generating vehicles
/// </summary>
public class GenerateVehiclesRequest
{
    /// <summary>
    /// Target environment for generation
    /// </summary>
    public string? Environment { get; set; }

    /// <summary>
    /// Number of vehicles to generate
    /// </summary>
    [System.ComponentModel.DataAnnotations.Range(1, 50000)]
    public int Count { get; set; }
}

/// <summary>
/// Generation statistics and limits
/// </summary>
public class GenerationStatistics
{
    public string Environment { get; set; } = string.Empty;
    public int MaxDriversPerOperation { get; set; }
    public int MaxVehiclesPerOperation { get; set; }
    public List<EnvironmentInfo> SupportedEnvironments { get; set; } = new();
}

/// <summary>
/// Environment information
/// </summary>
public class EnvironmentInfo
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
