# XQ360 Data Migration - Ultra Minimal Remote Server Status Fix
# This script fixes the status update problems with ultra minimal configuration

param(
    [string]$ServerName = "localhost",
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [string]$AppPoolName = "XQ360MigrationPool"
)

Write-Host "XQ360 Data Migration - Ultra Minimal Remote Server Status Fix" -ForegroundColor Green
Write-Host "Target Server: $ServerName" -ForegroundColor Yellow
Write-Host "Deploy Path: $DeployPath" -ForegroundColor Yellow

# Step 1: Create logs directory if it doesn't exist
Write-Host "Creating logs directory..." -ForegroundColor Yellow
$logsPath = Join-Path $DeployPath "logs"
if (-not (Test-Path $logsPath)) {
    New-Item -ItemType Directory -Path $logsPath -Force
    Write-Host "✅ Created logs directory: $logsPath" -ForegroundColor Green
} else {
    Write-Host "✅ Logs directory already exists: $logsPath" -ForegroundColor Green
}

# Step 2: Backup current web.config
Write-Host "Backing up current web.config..." -ForegroundColor Yellow
$webConfigPath = Join-Path $DeployPath "web.config"
$backupPath = Join-Path $DeployPath "web.config.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"

if (Test-Path $webConfigPath) {
    Copy-Item $webConfigPath $backupPath
    Write-Host "✅ Backed up web.config to: $backupPath" -ForegroundColor Green
} else {
    Write-Host "⚠️  No existing web.config found at: $webConfigPath" -ForegroundColor Yellow
}

# Step 3: Copy the ultra minimal web.config
Write-Host "Updating web.config with ultra minimal SignalR and logging fixes..." -ForegroundColor Yellow
$ultraMinimalWebConfig = Join-Path $PSScriptRoot "ultra-minimal-remote-web.config"

if (Test-Path $ultraMinimalWebConfig) {
    Copy-Item $ultraMinimalWebConfig $webConfigPath -Force
    Write-Host "✅ Updated web.config with ultra minimal fixes (no WebSocket conflicts)" -ForegroundColor Green
} else {
    Write-Host "❌ Ultra minimal web.config not found at: $ultraMinimalWebConfig" -ForegroundColor Red
    Write-Host "Please ensure the ultra-minimal-remote-web.config file exists in the deploy directory." -ForegroundColor Yellow
    exit 1
}

# Step 4: Set basic permissions on logs directory
Write-Host "Setting basic permissions on logs directory..." -ForegroundColor Yellow
try {
    $acl = Get-Acl $logsPath
    $rule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "Modify", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($rule)
    Set-Acl $logsPath $acl
    Write-Host "✅ Set basic permissions on logs directory" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not set permissions on logs directory: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "You may need to set permissions manually in IIS Manager." -ForegroundColor Yellow
}

# Step 5: Restart Application Pool
Write-Host "Restarting application pool..." -ForegroundColor Yellow
try {
    Import-Module WebAdministration
    Restart-WebAppPool -Name $AppPoolName
    Write-Host "✅ Application pool '$AppPoolName' restarted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restart application pool: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please restart the application pool manually in IIS Manager." -ForegroundColor Yellow
}

# Step 6: Test basic connectivity
Write-Host "Testing basic connectivity..." -ForegroundColor Yellow
Start-Sleep -Seconds 5  # Wait for app pool to restart

try {
    $testUrl = "http://$ServerName/"
    $response = Invoke-WebRequest -Uri $testUrl -Method GET -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ Application is accessible" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Application test failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "This might be normal if the application requires specific configuration." -ForegroundColor Yellow
}

# Step 7: Check logs directory permissions
Write-Host "Verifying logs directory permissions..." -ForegroundColor Yellow
if (Test-Path $logsPath) {
    try {
        $testFile = Join-Path $logsPath "test-write.tmp"
        "Test write access" | Out-File -FilePath $testFile -Encoding UTF8
        Remove-Item $testFile -Force
        Write-Host "✅ Logs directory is writable" -ForegroundColor Green
    } catch {
        Write-Host "❌ Logs directory is not writable: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please check IIS_IUSRS permissions on: $logsPath" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Logs directory does not exist: $logsPath" -ForegroundColor Red
}

# Step 8: Display summary and WebSocket instructions
Write-Host "`n=== ULTRA MINIMAL FIX SUMMARY ===" -ForegroundColor Cyan
Write-Host "✅ Updated web.config with ultra minimal SignalR and logging fixes" -ForegroundColor Green
Write-Host "✅ Created logs directory with basic permissions" -ForegroundColor Green
Write-Host "✅ Restarted application pool" -ForegroundColor Green
Write-Host "✅ Avoided WebSocket configuration conflicts" -ForegroundColor Green
Write-Host "`nKey changes made:" -ForegroundColor Yellow
Write-Host "  • Enabled stdoutLogEnabled='true'" -ForegroundColor White
Write-Host "  • Changed hostingModel to 'outofprocess'" -ForegroundColor White
Write-Host "  • Removed WebSocket section to avoid locked configuration" -ForegroundColor White
Write-Host "  • Ultra minimal configuration only" -ForegroundColor White

Write-Host "`n⚠️  IMPORTANT: WebSocket Configuration Required" -ForegroundColor Yellow
Write-Host "For SignalR to work properly, you need to enable WebSocket at the server level:" -ForegroundColor White
Write-Host "1. Open IIS Manager" -ForegroundColor White
Write-Host "2. Select your server in the left panel" -ForegroundColor White
Write-Host "3. Double-click on 'WebSocket Protocol' in the middle panel" -ForegroundColor White
Write-Host "4. If not installed, install it via 'Turn Windows features on or off'" -ForegroundColor White
Write-Host "5. Enable WebSocket Protocol for your application" -ForegroundColor White

Write-Host "`nAlternative: Enable WebSocket via PowerShell (Run as Administrator):" -ForegroundColor Yellow
Write-Host "Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets" -ForegroundColor Cyan

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Enable WebSocket at server level (see instructions above)" -ForegroundColor White
Write-Host "2. Test the migration status updates in the web interface" -ForegroundColor White
Write-Host "3. Check the logs directory for any error messages" -ForegroundColor White
Write-Host "4. If SignalR still doesn't work, check browser console for WebSocket errors" -ForegroundColor White

Write-Host "`n✅ Ultra minimal remote server status fix completed!" -ForegroundColor Green 