using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Services
{
    public interface IEnvironmentConfigurationService
    {
        EnvironmentSettings CurrentEnvironment { get; }
        MigrationConfiguration CurrentMigrationConfiguration { get; }
        List<(string Key, string DisplayName, string Description)> GetAvailableEnvironments();
        void SetCurrentEnvironment(string environmentKey);
        string CurrentEnvironmentKey { get; }
    }

    public class EnvironmentConfigurationService : IEnvironmentConfigurationService
    {
        private readonly EnvironmentConfiguration _environmentConfig;
        private string _currentEnvironmentKey = "Development"; // Default to Development
        private MigrationConfiguration? _currentMigrationConfig;

        public EnvironmentConfigurationService(IOptions<EnvironmentConfiguration> environmentConfig)
        {
            _environmentConfig = environmentConfig.Value;
            UpdateCurrentMigrationConfiguration();
        }

        public string CurrentEnvironmentKey => _currentEnvironmentKey;

        public EnvironmentSettings CurrentEnvironment
        {
            get
            {
                if (!_environmentConfig.Environments.ContainsKey(_currentEnvironmentKey))
                {
                    throw new InvalidOperationException($"Environment '{_currentEnvironmentKey}' not found in configuration");
                }
                return _environmentConfig.Environments[_currentEnvironmentKey];
            }
        }

        public MigrationConfiguration CurrentMigrationConfiguration
        {
            get
            {
                if (_currentMigrationConfig == null)
                {
                    UpdateCurrentMigrationConfiguration();
                }
                return _currentMigrationConfig!;
            }
        }

        public List<(string Key, string DisplayName, string Description)> GetAvailableEnvironments()
        {
            return _environmentConfig.Environments.Select(env => (
                env.Key,
                env.Value.Name,
                env.Value.Description ?? ""
            )).ToList();
        }

        public void SetCurrentEnvironment(string environmentKey)
        {
            if (!_environmentConfig.Environments.ContainsKey(environmentKey))
            {
                throw new ArgumentException($"Environment '{environmentKey}' not found in configuration");
            }

            _currentEnvironmentKey = environmentKey;
            UpdateCurrentMigrationConfiguration();
        }

        private void UpdateCurrentMigrationConfiguration()
        {
            var currentEnv = CurrentEnvironment;
            _currentMigrationConfig = new MigrationConfiguration
            {
                DatabaseConnection = currentEnv.DatabaseConnection,
                ApiBaseUrl = currentEnv.ApiBaseUrl,
                ApiUsername = currentEnv.ApiUsername,
                ApiPassword = currentEnv.ApiPassword,
                BatchSize = _environmentConfig.BatchSize,
                MaxRetryAttempts = _environmentConfig.MaxRetryAttempts,
                BackupEnabled = _environmentConfig.BackupEnabled,
                ValidateBeforeMigration = _environmentConfig.ValidateBeforeMigration,
                ContinueOnError = _environmentConfig.ContinueOnError
            };
        }
    }
} 