import { computed, watch } from 'vue'
import { useValidation, type ValidationRule } from './useValidation'
import { useEnvironmentStore } from '@/stores/environment'
import { useDealerStore } from '@/stores/dealer'
import { useCustomerStore } from '@/stores/customer'
import type { Environment } from '@/types/environment'
import type { Dealer } from '@/types/dealer'
import type { Customer } from '@/types/customer'

export interface BusinessValidationContext {
  environment?: Environment
  dealer?: Dealer
  customer?: Customer
  driversCount?: number
  vehiclesCount?: number
}

export function useBusinessValidation(validation: ReturnType<typeof useValidation>) {
  const environmentStore = useEnvironmentStore()
  const dealerStore = useDealerStore()
  const customerStore = useCustomerStore()

  // Environment-specific validation rules
  const createEnvironmentLimitRule = (fieldName: 'driversCount' | 'vehiclesCount'): ValidationRule => {
    return validation.custom(async (value: number) => {
      if (!value || !environmentStore.currentEnvironment) return true
      
      const maxSize = environmentStore.currentEnvironment.maxOperationSize
      const isDrivers = fieldName === 'driversCount'
      const limit = isDrivers ? Math.min(maxSize, 5000) : Math.min(maxSize, 10000)
      
      return value <= limit
    }, `Maximum ${fieldName === 'driversCount' ? 'drivers' : 'vehicles'} for ${environmentStore.currentEnvironment?.displayName || 'this environment'} is ${environmentStore.currentEnvironment?.maxOperationSize || 'unknown'}`)
  }

  const createMaintenanceWindowRule = (): ValidationRule => {
    return validation.custom(async () => {
      if (!environmentStore.currentEnvironment) return true
      return !environmentStore.isInMaintenanceWindow
    }, 'Operations are not allowed during maintenance windows')
  }

  const createApprovalRequiredRule = (): ValidationRule => {
    return validation.custom(async (value: number) => {
      if (!value || !environmentStore.currentEnvironment) return true
      
      const validation = await environmentStore.validateOperation({
        operationSize: value
      })
      
      if (validation.requiresApproval) {
        // This is more of a warning than an error
        return true
      }
      
      return validation.isValid
    }, 'This operation requires approval')
  }

  // Dealer validation rules
  const createDealerExistsRule = (): ValidationRule => {
    return validation.custom(async (dealerId: string) => {
      if (!dealerId) return true
      
      try {
        const validation = await dealerStore.validateDealer(dealerId)
        return validation.exists && validation.isActive
      } catch {
        return false
      }
    }, 'Selected dealer does not exist or is not active')
  }

  const createDealerActiveRule = (): ValidationRule => {
    return validation.custom(async (dealerId: string) => {
      if (!dealerId) return true
      
      try {
        const dealer = await dealerStore.getDealerById(dealerId)
        return dealer?.active ?? false
      } catch {
        return false
      }
    }, 'Selected dealer is not active')
  }

  // Customer validation rules
  const createCustomerExistsRule = (): ValidationRule => {
    return validation.custom(async (customerId: string) => {
      if (!customerId || !dealerStore.selectedDealer) return true
      
      try {
        const validation = await customerStore.validateCustomer({
          dealerId: dealerStore.selectedDealer.id,
          customerId
        })
        return validation.exists && validation.isActive && validation.belongsToDealer
      } catch {
        return false
      }
    }, 'Selected customer does not exist or does not belong to the selected dealer')
  }

  const createCustomerDealerRelationRule = (): ValidationRule => {
    return validation.custom(async (customerId: string) => {
      if (!customerId || !dealerStore.selectedDealer) return true
      
      try {
        const validation = await customerStore.validateCustomer({
          dealerId: dealerStore.selectedDealer.id,
          customerId
        })
        return validation.belongsToDealer
      } catch {
        return false
      }
    }, 'Customer does not belong to the selected dealer')
  }

  // Cross-field validation rules
  const createTotalOperationSizeRule = (): ValidationRule => {
    return validation.custom(async (value: number) => {
      // This would be called on both drivers and vehicles count
      // We need to get both values to validate total
      const driversCount = validation.formValidation.driversCount?.isValid ? 
        parseInt(String(value)) : 0
      const vehiclesCount = validation.formValidation.vehiclesCount?.isValid ? 
        parseInt(String(value)) : 0
      
      const totalSize = driversCount + vehiclesCount
      
      if (!environmentStore.currentEnvironment) return true
      
      return totalSize <= environmentStore.currentEnvironment.maxOperationSize
    }, `Total operation size exceeds environment limit of ${environmentStore.currentEnvironment?.maxOperationSize || 'unknown'}`)
  }

  const createReasonableRatioRule = (): ValidationRule => {
    return validation.custom(async (value: number) => {
      // Check if the ratio of drivers to vehicles is reasonable
      const driversCount = validation.formValidation.driversCount ? 
        parseInt(String(validation.formValidation.driversCount)) : 0
      const vehiclesCount = validation.formValidation.vehiclesCount ? 
        parseInt(String(validation.formValidation.vehiclesCount)) : 0
      
      if (!driversCount || !vehiclesCount) return true
      
      const ratio = driversCount / vehiclesCount
      
      // Warn if ratio is unusual (more than 3 drivers per vehicle or less than 0.1)
      if (ratio > 3 || ratio < 0.1) {
        // This is a warning, not an error
        return true
      }
      
      return true
    }, 'Unusual ratio of drivers to vehicles detected')
  }

  // Batch size validation
  const createBatchSizeRule = (): ValidationRule => {
    return validation.custom(async (batchSize: number) => {
      if (!batchSize) return true
      
      const driversCount = validation.formValidation.driversCount ? 
        parseInt(String(validation.formValidation.driversCount)) : 0
      const vehiclesCount = validation.formValidation.vehiclesCount ? 
        parseInt(String(validation.formValidation.vehiclesCount)) : 0
      
      const totalRecords = driversCount + vehiclesCount
      
      // Batch size should not be larger than total records
      if (batchSize > totalRecords) {
        return false
      }
      
      // Batch size should be reasonable (not too small for large operations)
      if (totalRecords > 1000 && batchSize < 100) {
        return false
      }
      
      return true
    }, 'Batch size is not appropriate for the operation size')
  }

  // Validation helpers
  const validateEnvironmentOperation = async (context: BusinessValidationContext) => {
    if (!context.environment) return { isValid: true, warnings: [], errors: [] }
    
    const warnings: string[] = []
    const errors: string[] = []
    
    // Check maintenance window
    if (environmentStore.isInMaintenanceWindow) {
      errors.push('Operations are not allowed during maintenance windows')
    }
    
    // Check operation size limits
    const totalSize = (context.driversCount || 0) + (context.vehiclesCount || 0)
    if (totalSize > context.environment.maxOperationSize) {
      errors.push(`Operation size (${totalSize}) exceeds environment limit (${context.environment.maxOperationSize})`)
    }
    
    // Check if approval is required
    if (context.environment.requiresApproval || totalSize > 1000) {
      warnings.push('This operation may require approval')
    }
    
    return { isValid: errors.length === 0, warnings, errors }
  }

  const validateDealerCustomerRelation = async (dealerId: string, customerId: string) => {
    if (!dealerId || !customerId) return { isValid: true, warnings: [], errors: [] }
    
    try {
      const validation = await customerStore.validateCustomer({ dealerId, customerId })
      
      const errors: string[] = []
      const warnings: string[] = []
      
      if (!validation.exists) {
        errors.push('Customer does not exist')
      } else if (!validation.isActive) {
        errors.push('Customer is not active')
      } else if (!validation.belongsToDealer) {
        errors.push('Customer does not belong to the selected dealer')
      }
      
      return { isValid: errors.length === 0, warnings, errors }
    } catch (error) {
      return { 
        isValid: false, 
        warnings: [], 
        errors: ['Failed to validate customer-dealer relationship'] 
      }
    }
  }

  const validateOperationFeasibility = async (context: BusinessValidationContext) => {
    const warnings: string[] = []
    const errors: string[] = []
    
    const driversCount = context.driversCount || 0
    const vehiclesCount = context.vehiclesCount || 0
    
    // Check for reasonable numbers
    if (driversCount > 10000) {
      warnings.push('Large number of drivers may take significant time to process')
    }
    
    if (vehiclesCount > 20000) {
      warnings.push('Large number of vehicles may take significant time to process')
    }
    
    // Check ratio
    if (driversCount > 0 && vehiclesCount > 0) {
      const ratio = driversCount / vehiclesCount
      if (ratio > 5) {
        warnings.push('High driver-to-vehicle ratio detected')
      } else if (ratio < 0.1) {
        warnings.push('Low driver-to-vehicle ratio detected')
      }
    }
    
    return { isValid: errors.length === 0, warnings, errors }
  }

  return {
    // Rule creators
    createEnvironmentLimitRule,
    createMaintenanceWindowRule,
    createApprovalRequiredRule,
    createDealerExistsRule,
    createDealerActiveRule,
    createCustomerExistsRule,
    createCustomerDealerRelationRule,
    createTotalOperationSizeRule,
    createReasonableRatioRule,
    createBatchSizeRule,
    
    // Validation helpers
    validateEnvironmentOperation,
    validateDealerCustomerRelation,
    validateOperationFeasibility
  }
}
