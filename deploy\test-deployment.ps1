# XQ360 Data Migration - Deployment Test Script
# This script tests the deployment and verifies static files are working

param(
    [string]$ServerName = "localhost",
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [int]$Port = 80
)

Write-Host "XQ360 Data Migration - Deployment Test Script" -ForegroundColor Green
Write-Host "Testing deployment on: $ServerName" -ForegroundColor Yellow

# Test 1: Check if deployment directory exists
Write-Host "`nTest 1: Checking deployment directory..." -ForegroundColor Cyan
if (Test-Path $DeployPath) {
    Write-Host "✅ Deployment directory exists: $DeployPath" -ForegroundColor Green
} else {
    Write-Host "❌ Deployment directory not found: $DeployPath" -ForegroundColor Red
    exit 1
}

# Test 2: Check if static files exist
Write-Host "`nTest 2: Checking static files..." -ForegroundColor Cyan
$staticFiles = @(
    "wwwroot\lib\bootstrap\dist\css\bootstrap.min.css",
    "wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js",
    "wwwroot\lib\jquery\dist\jquery.min.js",
    "wwwroot\css\site.css",
    "wwwroot\js\site.js",
    "wwwroot\XQ360.DataMigration.Web.styles.css"
)

foreach ($file in $staticFiles) {
    $fullPath = Join-Path $DeployPath $file
    if (Test-Path $fullPath) {
        Write-Host "✅ $file exists" -ForegroundColor Green
    } else {
        Write-Host "❌ $file missing" -ForegroundColor Red
    }
}

# Test 3: Check if website is running
Write-Host "`nTest 3: Checking website status..." -ForegroundColor Cyan
try {
    $website = Get-Website -Name "XQ360Migration" -ErrorAction SilentlyContinue
    if ($website) {
        if ($website.State -eq "Started") {
            Write-Host "✅ Website is running" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Website exists but is not running (State: $($website.State))" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Website 'XQ360Migration' not found" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️ Could not check website status: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Test 4: Test web access
Write-Host "`nTest 4: Testing web access..." -ForegroundColor Cyan
$testUrl = "http://$ServerName"
if ($ServerName -eq "localhost") {
    $testUrl = "http://localhost"
}

try {
    $response = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Web application is accessible at: $testUrl" -ForegroundColor Green
        
        # Check if Bootstrap CSS is loaded
        if ($response.Content -match "bootstrap\.min\.css") {
            Write-Host "✅ Bootstrap CSS reference found in HTML" -ForegroundColor Green
        } else {
            Write-Host "❌ Bootstrap CSS reference not found in HTML" -ForegroundColor Red
        }
        
        # Check if Bootstrap JS is loaded
        if ($response.Content -match "bootstrap\.bundle\.min\.js") {
            Write-Host "✅ Bootstrap JS reference found in HTML" -ForegroundColor Green
        } else {
            Write-Host "❌ Bootstrap JS reference not found in HTML" -ForegroundColor Red
        }
        
    } else {
        Write-Host "⚠️ Web application responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to access web application: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Test static file access
Write-Host "`nTest 5: Testing static file access..." -ForegroundColor Cyan
$staticUrls = @(
    "/lib/bootstrap/dist/css/bootstrap.min.css",
    "/lib/bootstrap/dist/js/bootstrap.bundle.min.js",
    "/lib/jquery/dist/jquery.min.js",
    "/css/site.css",
    "/js/site.js",
    "/XQ360.DataMigration.Web.styles.css"
)

foreach ($url in $staticUrls) {
    $fullUrl = "$testUrl$url"
    try {
        $response = Invoke-WebRequest -Uri $fullUrl -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $url is accessible" -ForegroundColor Green
        } else {
            Write-Host "❌ $url returned status: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $url is not accessible: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 6: Test SignalR endpoint
Write-Host "`nTest 6: Testing SignalR endpoint..." -ForegroundColor Cyan
try {
    $signalRUrl = "$testUrl/migrationHub"
    $response = Invoke-WebRequest -Uri $signalRUrl -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ SignalR hub endpoint is accessible" -ForegroundColor Green
    } else {
        Write-Host "⚠️ SignalR hub responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ SignalR hub endpoint not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Check file permissions
Write-Host "`nTest 7: Checking file permissions..." -ForegroundColor Cyan
try {
    $acl = Get-Acl $DeployPath
    $permissions = $acl.Access | Where-Object { $_.IdentityReference -like "*IIS_IUSRS*" -or $_.IdentityReference -like "*IIS AppPool*" }
    
    if ($permissions) {
        Write-Host "✅ IIS permissions found:" -ForegroundColor Green
        foreach ($perm in $permissions) {
            Write-Host "   $($perm.IdentityReference): $($perm.FileSystemRights)" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️ No IIS-specific permissions found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Could not check permissions: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`nDeployment Test Summary:" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green
Write-Host "If you see any ❌ errors above, the deployment may have issues." -ForegroundColor Yellow
Write-Host "Common issues:" -ForegroundColor Cyan
Write-Host "  1. Static files not copied - Check wwwroot folder in deployment" -ForegroundColor White
Write-Host "  2. File permissions - Run deployment script as Administrator" -ForegroundColor White
Write-Host "  3. IIS configuration - Ensure ASP.NET Core Hosting Bundle is installed" -ForegroundColor White
Write-Host "  4. Firewall blocking - Check Windows Firewall settings" -ForegroundColor White
Write-Host "  5. SignalR WebSockets - Ensure IIS WebSockets feature is enabled" -ForegroundColor White
Write-Host "`nTo fix static file issues, run the deployment script again with:" -ForegroundColor Cyan
Write-Host "  .\deploy-remote.ps1 -SkipBuild" -ForegroundColor White 