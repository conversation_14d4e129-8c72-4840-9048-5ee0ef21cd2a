using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Reflection;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Web.Services.BusinessRules.Rules;

namespace XQ360.DataMigration.Web.Services.BusinessRules
{
    /// <summary>
    /// Implementation of business rule validation service
    /// </summary>
    public class BusinessRuleValidationService : IBusinessRuleValidationService
    {
        private readonly ILogger<BusinessRuleValidationService> _logger;
        private readonly MigrationConfiguration _config;
        private readonly IServiceProvider _serviceProvider;
        private readonly Dictionary<string, IBusinessRule> _rules;
        private readonly Dictionary<string, object> _cache;

        public BusinessRuleValidationService(
            ILogger<BusinessRuleValidationService> logger,
            IOptions<MigrationConfiguration> config,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _config = config.Value;
            _serviceProvider = serviceProvider;
            _rules = new Dictionary<string, IBusinessRule>();
            _cache = new Dictionary<string, object>();
            
            LoadBusinessRules();
        }

        public async Task<ValidationResult> ValidateEntityAsync<T>(T entity, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new ValidationResult();
            
            try
            {
                var entityType = typeof(T).Name;
                var applicableRules = _rules.Values.Where(r => r.ApplicableEntityTypes.Contains(entityType) && r.IsEnabled).OrderBy(r => r.Priority);
                
                _logger.LogDebug("Validating {EntityType} with {RuleCount} applicable rules", entityType, applicableRules.Count());
                
                foreach (var rule in applicableRules)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;
                        
                    try
                    {
                        var ruleResult = await rule.ValidateAsync(entity, context, cancellationToken);
                        result.Issues.AddRange(ruleResult.Issues);
                        result.RulesEvaluated++;
                        
                        // Apply validation mode logic
                        if (context.Mode == ValidationMode.Advisory)
                        {
                            // Convert all errors to warnings in advisory mode
                            foreach (var issue in ruleResult.Issues.Where(i => i.Severity == ValidationSeverity.Error))
                            {
                                issue.Severity = ValidationSeverity.Warning;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error executing business rule {RuleId} for {EntityType}", rule.Id, entityType);
                        result.Issues.Add(new ValidationIssue
                        {
                            RuleId = rule.Id,
                            RuleName = rule.Name,
                            Severity = ValidationSeverity.Error,
                            Message = $"Rule execution failed: {ex.Message}",
                            FieldName = "System",
                            Suggestion = "Contact system administrator"
                        });
                    }
                }
                
                // Determine overall validity based on mode
                result.IsValid = context.Mode switch
                {
                    ValidationMode.Strict => !result.Issues.Any(i => i.Severity == ValidationSeverity.Error),
                    ValidationMode.Lenient => !result.Issues.Any(i => i.Severity == ValidationSeverity.Error),
                    ValidationMode.Advisory => true,
                    _ => !result.Issues.Any(i => i.Severity == ValidationSeverity.Error)
                };
                
                result.Duration = stopwatch.Elapsed;
                
                _logger.LogDebug("Entity validation completed: {IsValid}, {IssueCount} issues, {Duration}ms", 
                    result.IsValid, result.Issues.Count, result.Duration.TotalMilliseconds);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Entity validation failed for {EntityType}", typeof(T).Name);
                result.IsValid = false;
                result.Issues.Add(new ValidationIssue
                {
                    RuleId = "SYSTEM_ERROR",
                    RuleName = "System Error",
                    Severity = ValidationSeverity.Error,
                    Message = $"Validation system error: {ex.Message}",
                    FieldName = "System",
                    Suggestion = "Contact system administrator"
                });
                result.Duration = stopwatch.Elapsed;
                return result;
            }
        }

        public async Task<BatchValidationResult> ValidateBatchAsync<T>(IEnumerable<T> entities, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new BatchValidationResult();
            var entityList = entities.ToList();
            
            _logger.LogInformation("Starting batch validation for {EntityCount} entities of type {EntityType}", 
                entityList.Count, typeof(T).Name);
            
            try
            {
                var tasks = new List<Task<EntityValidationResult>>();
                
                for (int i = 0; i < entityList.Count; i++)
                {
                    var index = i;
                    var entity = entityList[i];
                    
                    tasks.Add(Task.Run(async () =>
                    {
                        var entityResult = await ValidateEntityAsync(entity, context, cancellationToken);
                        return new EntityValidationResult
                        {
                            EntityIndex = index,
                            EntityIdentifier = GetEntityIdentifier(entity),
                            Result = entityResult
                        };
                    }, cancellationToken));
                }
                
                var entityResults = await Task.WhenAll(tasks);
                result.EntityResults.AddRange(entityResults);
                
                // Calculate summary statistics
                result.Summary.ValidEntities = entityResults.Count(r => r.Result.IsValid);
                result.Summary.InvalidEntities = entityResults.Count(r => !r.Result.IsValid);
                result.Summary.TotalErrors = entityResults.Sum(r => r.Result.Errors.Count());
                result.Summary.TotalWarnings = entityResults.Sum(r => r.Result.Warnings.Count());
                result.Summary.TotalInformation = entityResults.Sum(r => r.Result.Information.Count());
                
                // Group errors and warnings by rule
                foreach (var entityResult in entityResults)
                {
                    foreach (var error in entityResult.Result.Errors)
                    {
                        result.Summary.ErrorsByRule[error.RuleId] = result.Summary.ErrorsByRule.GetValueOrDefault(error.RuleId, 0) + 1;
                    }
                    foreach (var warning in entityResult.Result.Warnings)
                    {
                        result.Summary.WarningsByRule[warning.RuleId] = result.Summary.WarningsByRule.GetValueOrDefault(warning.RuleId, 0) + 1;
                    }
                }
                
                result.IsValid = result.Summary.InvalidEntities == 0;
                result.TotalEntitiesValidated = entityList.Count;
                result.TotalRulesEvaluated = entityResults.Sum(r => r.Result.RulesEvaluated);
                result.Duration = stopwatch.Elapsed;
                
                _logger.LogInformation("Batch validation completed: {ValidCount}/{TotalCount} valid, {ErrorCount} errors, {WarningCount} warnings, {Duration}ms",
                    result.Summary.ValidEntities, result.TotalEntitiesValidated, result.Summary.TotalErrors, 
                    result.Summary.TotalWarnings, result.Duration.TotalMilliseconds);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Batch validation failed for {EntityType}", typeof(T).Name);
                result.IsValid = false;
                result.Duration = stopwatch.Elapsed;
                return result;
            }
        }

        public async Task<ValidationResult> ValidateVehicleModuleAssignmentAsync(Guid vehicleId, Guid moduleId, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var rule = _rules.Values.OfType<VehicleModuleAssignmentRule>().FirstOrDefault();
            if (rule == null)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Issues = new List<ValidationIssue>
                    {
                        new ValidationIssue
                        {
                            RuleId = "MISSING_RULE",
                            RuleName = "Missing Rule",
                            Severity = ValidationSeverity.Error,
                            Message = "Vehicle module assignment rule not found",
                            FieldName = "System"
                        }
                    }
                };
            }
            
            var request = new { VehicleId = vehicleId, ModuleId = moduleId };
            return await rule.ValidateAsync(request, context, cancellationToken);
        }

        public async Task<ValidationResult> ValidateDriverCardAllocationAsync(Guid driverId, Guid cardId, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var rule = _rules.Values.OfType<DriverCardAllocationRule>().FirstOrDefault();
            if (rule == null)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Issues = new List<ValidationIssue>
                    {
                        new ValidationIssue
                        {
                            RuleId = "MISSING_RULE",
                            RuleName = "Missing Rule",
                            Severity = ValidationSeverity.Error,
                            Message = "Driver card allocation rule not found",
                            FieldName = "System"
                        }
                    }
                };
            }
            
            var request = new { DriverId = driverId, CardId = cardId };
            return await rule.ValidateAsync(request, context, cancellationToken);
        }

        public async Task<ValidationResult> ValidateAccessPermissionAsync(AccessPermissionRequest request, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var rule = _rules.Values.OfType<AccessPermissionRule>().FirstOrDefault();
            if (rule == null)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Issues = new List<ValidationIssue>
                    {
                        new ValidationIssue
                        {
                            RuleId = "MISSING_RULE",
                            RuleName = "Missing Rule",
                            Severity = ValidationSeverity.Error,
                            Message = "Access permission rule not found",
                            FieldName = "System"
                        }
                    }
                };
            }
            
            return await rule.ValidateAsync(request, context, cancellationToken);
        }

        public async Task<ValidationResult> ValidateUniquenessAsync<T>(T entity, string[] uniqueFields, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var rule = _rules.Values.OfType<UniquenessRule>().FirstOrDefault();
            if (rule == null)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Issues = new List<ValidationIssue>
                    {
                        new ValidationIssue
                        {
                            RuleId = "MISSING_RULE",
                            RuleName = "Missing Rule",
                            Severity = ValidationSeverity.Error,
                            Message = "Uniqueness rule not found",
                            FieldName = "System"
                        }
                    }
                };
            }
            
            var request = new { Entity = entity, UniqueFields = uniqueFields };
            return await rule.ValidateAsync(request, context, cancellationToken);
        }

        public async Task<ValidationResult> ValidateReferentialIntegrityAsync<T>(T entity, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var rule = _rules.Values.OfType<ReferentialIntegrityRule>().FirstOrDefault();
            if (rule == null)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Issues = new List<ValidationIssue>
                    {
                        new ValidationIssue
                        {
                            RuleId = "MISSING_RULE",
                            RuleName = "Missing Rule",
                            Severity = ValidationSeverity.Error,
                            Message = "Referential integrity rule not found",
                            FieldName = "System"
                        }
                    }
                };
            }
            
            return await rule.ValidateAsync(entity, context, cancellationToken);
        }

        public async Task<IEnumerable<BusinessRule>> GetBusinessRulesAsync<T>(CancellationToken cancellationToken = default)
        {
            var entityType = typeof(T).Name;
            return await Task.FromResult(_rules.Values
                .Where(r => r.ApplicableEntityTypes.Contains(entityType))
                .Select(r => new BusinessRule
                {
                    Id = r.Id,
                    Name = r.Name,
                    Description = r.Description,
                    Category = r.Category,
                    DefaultSeverity = r.DefaultSeverity,
                    IsEnabled = r.IsEnabled,
                    Priority = r.Priority,
                    ApplicableEntityTypes = r.ApplicableEntityTypes,
                    Configuration = r.Configuration
                }));
        }

        private void LoadBusinessRules()
        {
            _logger.LogInformation("Loading business rules...");
            
            // Load rules from assembly using reflection
            var ruleTypes = Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(t => typeof(IBusinessRule).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract);
            
            foreach (var ruleType in ruleTypes)
            {
                try
                {
                    var rule = (IBusinessRule)ActivatorUtilities.CreateInstance(_serviceProvider, ruleType);
                    _rules[rule.Id] = rule;
                    _logger.LogDebug("Loaded business rule: {RuleId} - {RuleName}", rule.Id, rule.Name);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to load business rule: {RuleType}", ruleType.Name);
                }
            }
            
            _logger.LogInformation("Loaded {RuleCount} business rules", _rules.Count);
        }

        private string GetEntityIdentifier<T>(T entity)
        {
            if (entity == null) return "null";
            
            // Try to get a meaningful identifier from the entity
            var type = entity.GetType();
            
            // Look for common identifier properties
            var idProperty = type.GetProperty("Id") ?? type.GetProperty("ID");
            if (idProperty != null)
            {
                var id = idProperty.GetValue(entity);
                if (id != null) return id.ToString() ?? "unknown";
            }
            
            // Look for name properties
            var nameProperty = type.GetProperty("Name") ?? type.GetProperty("FirstName") ?? type.GetProperty("Title");
            if (nameProperty != null)
            {
                var name = nameProperty.GetValue(entity);
                if (name != null) return name.ToString() ?? "unknown";
            }
            
            return $"{type.Name}@{entity.GetHashCode()}";
        }
    }
}
