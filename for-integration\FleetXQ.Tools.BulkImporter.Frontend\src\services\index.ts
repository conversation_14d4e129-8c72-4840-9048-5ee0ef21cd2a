// Import service classes for internal use
import { ApiBaseService } from './api-base'
import { EnvironmentService } from './environment'
import { DealerService } from './dealer'
import { CustomerService } from './customer'
import { BulkImportService } from './bulk-import'
import { DataGenerationService } from './data-generation'
import { SignalRService } from './signalr'

// Base API service
export { ApiBaseService } from './api-base'
export type { ApiError, ApiResponse, RetryConfig } from './api-base'

// Environment service
export { EnvironmentService } from './environment'
export type {
    EnvironmentListResponse,
    EnvironmentValidationRequest,
    EnvironmentValidationResponse
} from './environment'

// Dealer service
export { DealerService } from './dealer'
export type {
    DealerListRequest,
    DealerListResponse,
    DealerSearchRequest,
    DealerValidationResponse
} from './dealer'

// Customer service
export { CustomerService } from './customer'
export type {
    CustomerListRequest,
    CustomerListResponse,
    CreateCustomerRequest,
    CustomerValidationRequest,
    CustomerValidationResponse
} from './customer'

// Bulk import service
export { BulkImportService } from './bulk-import'
export type {
    CreateImportSessionRequest,
    ExecuteImportRequest,
    ImportSessionResponse,
    ImportProgressResponse,
    ImportResultResponse
} from './bulk-import'

// Data generation service
export { DataGenerationService } from './data-generation'
export type {
    DataValidationRequest,
    DataValidationResponse,
    DataPreviewRequest,
    DataPreviewResponse,
    DataTemplate,
    DataTemplatesResponse,
    GenerateDataRequest
} from './data-generation'

// SignalR service (already exists)
export { SignalRService } from './signalr'
export type { ProgressUpdate, StatusUpdate, ErrorNotification } from './signalr'

// Service instances - singleton pattern for shared state
let environmentService: EnvironmentService | null = null
let dealerService: DealerService | null = null
let customerService: CustomerService | null = null
let bulkImportService: BulkImportService | null = null
let dataGenerationService: DataGenerationService | null = null
let signalRService: SignalRService | null = null

/**
 * Get singleton instance of EnvironmentService
 */
export function getEnvironmentService(): EnvironmentService {
    if (!environmentService) {
        environmentService = new EnvironmentService()
    }
    return environmentService
}

/**
 * Get singleton instance of DealerService
 */
export function getDealerService(): DealerService {
    if (!dealerService) {
        dealerService = new DealerService()
    }
    return dealerService
}

/**
 * Get singleton instance of CustomerService
 */
export function getCustomerService(): CustomerService {
    if (!customerService) {
        customerService = new CustomerService()
    }
    return customerService
}

/**
 * Get singleton instance of BulkImportService
 */
export function getBulkImportService(): BulkImportService {
    if (!bulkImportService) {
        bulkImportService = new BulkImportService()
    }
    return bulkImportService
}

/**
 * Get singleton instance of DataGenerationService
 */
export function getDataGenerationService(): DataGenerationService {
    if (!dataGenerationService) {
        dataGenerationService = new DataGenerationService()
    }
    return dataGenerationService
}

/**
 * Get singleton instance of SignalRService
 */
export function getSignalRService(baseUrl?: string): SignalRService {
    if (!signalRService) {
        signalRService = new SignalRService(baseUrl)
    }
    return signalRService
}

/**
 * Configure all services with base URL and authentication
 */
export function configureServices(config: {
    apiBaseUrl?: string
    signalRBaseUrl?: string
    authToken?: string
}): void {
    const { apiBaseUrl = '/api', signalRBaseUrl = '/hub/bulkimport', authToken } = config

    // Update base URLs for existing services
    if (environmentService) {
        environmentService.updateBaseURL(apiBaseUrl)
    }
    if (dealerService) {
        dealerService.updateBaseURL(apiBaseUrl)
    }
    if (customerService) {
        customerService.updateBaseURL(apiBaseUrl)
    }
    if (bulkImportService) {
        bulkImportService.updateBaseURL(apiBaseUrl)
    }
    if (dataGenerationService) {
        dataGenerationService.updateBaseURL(apiBaseUrl)
    }

    // Set authentication token
    if (authToken) {
        if (environmentService) environmentService.setAuthToken(authToken)
        if (dealerService) dealerService.setAuthToken(authToken)
        if (customerService) customerService.setAuthToken(authToken)
        if (bulkImportService) bulkImportService.setAuthToken(authToken)
        if (dataGenerationService) dataGenerationService.setAuthToken(authToken)
    }

    // Recreate SignalR service with new URL
    if (signalRService) {
        signalRService = new SignalRService(signalRBaseUrl)
    }
}

/**
 * Clear authentication for all services
 */
export function clearAuthentication(): void {
    if (environmentService) environmentService.clearAuthToken()
    if (dealerService) dealerService.clearAuthToken()
    if (customerService) customerService.clearAuthToken()
    if (bulkImportService) bulkImportService.clearAuthToken()
    if (dataGenerationService) dataGenerationService.clearAuthToken()
}

/**
 * Reset all service instances (useful for testing or logout)
 */
export function resetServices(): void {
    environmentService = null
    dealerService = null
    customerService = null
    bulkImportService = null
    dataGenerationService = null
    signalRService = null
}
