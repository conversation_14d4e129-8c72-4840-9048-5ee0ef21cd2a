using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service interface for SQL-based data generation and processing
/// </summary>
public interface ISqlDataGenerationService
{
    /// <summary>
    /// Generates synthetic driver data directly in SQL staging tables
    /// </summary>
    /// <param name="sessionId">Seeding session identifier</param>
    /// <param name="count">Number of drivers to generate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generation result summary</returns>
    Task<DataGenerationResult> GenerateDriverDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates synthetic vehicle data directly in SQL staging tables
    /// </summary>
    /// <param name="sessionId">Seeding session identifier</param>
    /// <param name="count">Number of vehicles to generate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generation result summary</returns>
    Task<DataGenerationResult> GenerateVehicleDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new seeding session for tracking operations
    /// </summary>
    /// <param name="sessionName">Name for the seeding session</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Session identifier</returns>
    Task<Guid> CreateSeederSessionAsync(string sessionName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates seeding session status and statistics
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="status">Session status</param>
    /// <param name="totalRows">Total rows processed</param>
    /// <param name="successfulRows">Successfully processed rows</param>
    /// <param name="failedRows">Failed rows</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateSeederSessionAsync(Guid sessionId, string status, int totalRows, int successfulRows, int failedRows, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates staged data using SQL stored procedures
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result summary</returns>
    Task<ValidationResult> ValidateStagedDataAsync(Guid sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes validated staging data into production tables
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="dryRun">Whether to perform a dry run without actual changes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Processing result summary</returns>
    Task<ProcessingResult> ProcessStagedDataAsync(Guid sessionId, bool dryRun = false, CancellationToken cancellationToken = default);
}
