# XQ360 Data Migration Patterns Analysis

## Overview

This document provides a comprehensive analysis of the XQ360 data migration patterns and dependencies based on examination of the existing migration implementations. It serves as a reference for implementing bulk seeding functionality by understanding the complete data setup process.

## Table of Contents

1. [Migration Execution Order](#migration-execution-order)
2. [Driver Data Analysis](#driver-data-analysis)
3. [Vehicle Data Analysis](#vehicle-data-analysis)
4. [Data Dependency Hierarchy](#data-dependency-hierarchy)
5. [Table Creation Sequences](#table-creation-sequences)
6. [Foreign Key Relationships](#foreign-key-relationships)
7. [Business Rules and Constraints](#business-rules-and-constraints)
8. [Bulk Seeding Implementation Guidelines](#bulk-seeding-implementation-guidelines)

## Migration Execution Order

The XQ360 migration system follows a strict dependency-based execution order to maintain data integrity:

1. **Spare Module Migration** (Order 1)
   - Creates IoT modules in the system
   - No dependencies
   - API-based migration

2. **Department Checklist + PreOp Questions Migration** (Order 2)
   - Creates department-model mappings and checklist questions
   - Depends on: Spare Modules
   - SQL-based migration

3. **Vehicle Migration** (Order 3)
   - Creates vehicles with associated settings
   - Depends on: Department Checklist
   - SQL-based migration

4. **Person Migration** (Order 4)
   - Creates Person records and associated Driver entities
   - Depends on: Vehicle Migration
   - API-based migration

5. **Card + Vehicle Access Migration** (Order 5)
   - Creates card records and assigns vehicle access permissions
   - Depends on: Person Migration
   - SQL-based migration

6. **Supervisor Access Migration** (Order 6)
   - Enhanced permissions for supervisors
   - Depends on: Card + Vehicle Access
   - SQL-based migration

7. **Driver Blacklist Migration** (Order 7)
   - Removes vehicle access for blacklisted drivers
   - Depends on: Supervisor Access
   - SQL-based migration

8. **Website User Migration** (Order 8)
   - Creates website users with role assignments
   - Depends on: Driver Blacklist
   - API-based migration

9. **Vehicle Sync Settings** (Order 9)
   - Synchronizes vehicle IoT device settings
   - Depends on: Website Users
   - API-based migration

## Driver Data Analysis

### Person Migration Implementation

The driver data setup is handled through the **Person Migration** which:

#### Primary Table Creation
- **Person Table**: Core person record with basic information
  - Fields: FirstName, LastName, SiteId, DepartmentId, CustomerId
  - Flags: IsDriver, IsSupervisor, WebsiteAccess, etc.
  - Relationships: Links to Site, Department, Customer

#### Supporting Dataset Creation
When `IsDriver = true`, the Person API automatically creates:

1. **Driver Entity**: 
   - Created automatically by the Person API when `IsDriver = true`
   - Links back to Person via `Person.DriverId`
   - Initially has `CardDetailsId = NULL` (populated later by Card migration)

2. **Language Settings**:
   - Default language set to "en-US"
   - Stored in Person record

3. **Access Permissions**:
   - `VehicleAccess = true` (default)
   - `OnDemand = false` (default)
   - `MaintenanceMode = false` (default)
   - Various permission flags based on CSV input

#### Key Dependencies
- **Customer**: Must exist (foreign key)
- **Site**: Must exist and belong to specified Customer
- **Department**: Must exist and belong to specified Site

#### Migration Flow
1. Validate CSV data and lookup foreign key IDs
2. Authenticate with XQ360 API
3. Create PersonApiEntity with all required fields
4. Call Person API endpoint (creates Person + Driver if IsDriver=true)
5. Handle duplicate checking based on FirstName + LastName + Site + Customer

### Data Structure Example
```csharp
PersonApiEntity {
    IsNew = true,
    FirstName = "John",
    LastName = "Smith",
    SiteId = Guid,
    DepartmentId = Guid,
    CustomerId = Guid,
    IsDriver = true,        // Triggers Driver entity creation
    IsActiveDriver = true,
    // ... other permission flags
}
```

## Vehicle Data Analysis

### Vehicle Migration Implementation

The vehicle data setup is complex and creates multiple related records:

#### Primary Table Creation
- **Vehicle Table**: Core vehicle record
  - Fields: SerialNo, HireNo, ModelId, SiteId, DepartmentId, CustomerId
  - Module relationship: ModuleId1 (links to IoT device)
  - Settings: ChecklistSettingsId, VehicleOtherSettingsId, CanruleId

#### Supporting Dataset Creation
Each vehicle creation involves creating:

1. **ChecklistSettings Record**:
   - Unique ID generated for each vehicle
   - Fields: QuestionTimeout, ShowComment, Randomisation, Type
   - Time slots: TimeslotOne, TimeslotTwo, TimeslotThree, TimeslotFour
   - Checklist type logic:
     - "Time Based" → Type = 2 (uses time slots)
     - "Driver Based" → Type = 3 (time slots set to NULL)
     - Unknown/Other → Type = 2 (default with time slots)

2. **VehicleOtherSettings Record**:
   - Unique ID generated for each vehicle
   - Fields: FullLockoutTimeout, VORStatus, AmberAlertEnabled
   - Safety settings: VORStatusConfirmed, FullLockout, DefaultTechnicianAccess, PedestrianSafety

3. **Module Updates**:
   - Sets `IsAllocatedToVehicle = 1` for assigned modules
   - Sets `Status = 2` for allocated modules
   - Handles null Calibration and BlueImpact values

4. **Department Checklist Assignments**:
   - Updates `Vehicle.DepartmentChecklistId` based on ModelId + DepartmentId matches

#### Key Dependencies
- **Site**: Must exist for specified Customer
- **Customer**: Must exist for specified Dealer  
- **Department**: Must exist for specified Site
- **Model**: Must exist for specified Dealer
- **Module**: Must exist with matching IoTDevice (DeviceID)
- **Canrule**: Must exist in system

#### Migration Flow
1. Process CSV with NULL value preprocessing
2. For each vehicle record:
   - Validate module exists and is not already assigned
   - Lookup all foreign key IDs with error handling
   - Generate new GUIDs for vehicle, checklist, and other settings
   - Create ChecklistSettings record
   - Create VehicleOtherSettings record  
   - Create Vehicle record with all relationships
3. Update department checklist assignments
4. Update module allocation status

### Data Structure Example
```csharp
Vehicle Creation Flow:
1. ChecklistSettings { Id: Guid, Type: 2|3, Timeslots... }
2. VehicleOtherSettings { Id: Guid, Safety settings... }
3. Vehicle { 
     Id: Guid,
     SerialNo: "V001",
     ModuleId1: Module.Id,
     ChecklistSettingsId: ChecklistSettings.Id,
     VehicleOtherSettingsId: VehicleOtherSettings.Id,
     // All foreign keys
   }
```

## Data Dependency Hierarchy

```
Dealer (Root)
├── Customer
│   ├── Site
│   │   ├── Department
│   │   │   ├── Person (requires Customer, Site, Department)
│   │   │   │   └── Driver (auto-created when Person.IsDriver=true)
│   │   │   │       └── Card (links to Driver via CardDetailsId)
│   │   │   └── Vehicle (requires Site, Department, Customer, Model, Module, Canrule)
│   │   │       ├── ChecklistSettings
│   │   │       └── VehicleOtherSettings
│   │   └── Model (requires Dealer)
│   └── Module (IoT devices, requires Dealer)
└── Canrule (Global, no specific dealer dependency)
```

## Table Creation Sequences

### For Driver Setup (Person Migration)
1. **Prerequisite Lookups**: Customer → Site → Department
2. **API Call**: Person API (creates Person + Driver if IsDriver=true)
3. **Result**: Person record + Driver record (if applicable)

### For Vehicle Setup (Vehicle Migration)  
1. **Prerequisite Lookups**: 
   - Customer (via Dealer)
   - Site (via Customer) 
   - Department (via Site)
   - Model (via Dealer)
   - Module (via IoTDevice)
   - Canrule (global lookup)
2. **Supporting Records**:
   - ChecklistSettings (new GUID)
   - VehicleOtherSettings (new GUID)
3. **Primary Record**:
   - Vehicle (references all supporting records)
4. **Post-Creation Updates**:
   - Module allocation status
   - Department checklist assignments

### For Card and Access Setup (Card + Vehicle Access Migration)
1. **Card Creation**:
   - Create Card record
   - Link to Driver via CardDetailsId
2. **Access Permission Creation** (based on AccessLevel):
   - Site level: SiteVehicleNormalCardAccess
   - Department level: DepartmentVehicleNormalCardAccess  
   - Model level: ModelVehicleNormalCardAccess
   - Vehicle level: PerVehicleNormalCardAccess

## Foreign Key Relationships

### Core Entity Relationships
- `Person.SiteId` → `Site.Id`
- `Person.DepartmentId` → `Department.Id`
- `Person.CustomerId` → `Customer.Id`
- `Person.DriverId` → `Driver.Id` (nullable, only for drivers)

- `Vehicle.SiteId` → `Site.Id`
- `Vehicle.DepartmentId` → `Department.Id`
- `Vehicle.CustomerId` → `Customer.Id`
- `Vehicle.ModelId` → `Model.Id`
- `Vehicle.ModuleId1` → `Module.Id`
- `Vehicle.CanruleId` → `Canrule.Id`
- `Vehicle.ChecklistSettingsId` → `ChecklistSettings.Id`
- `Vehicle.VehicleOtherSettingsId` → `VehicleOtherSettings.Id`

- `Driver.CardDetailsId` → `Card.Id` (nullable, populated by Card migration)

### Hierarchy Relationships
- `Site.CustomerId` → `Customer.Id`
- `Department.SiteId` → `Site.Id`
- `Customer.DealerId` → `Dealer.Id`
- `Model.DealerId` → `Dealer.Id`

## Business Rules and Constraints

### Person/Driver Rules
1. **Uniqueness**: Person checked by FirstName + LastName + Site + Customer
2. **Driver Creation**: Only when `IsDriver = true` in Person data
3. **API Requirement**: Person creation must use XQ360 API (not direct SQL)
4. **Language Default**: Always set to "en-US"

### Vehicle Rules
1. **Module Assignment**: Each Module can only be assigned to one Vehicle
2. **Module Validation**: Module must exist before Vehicle creation
3. **Checklist Type Logic**:
   - "Time Based" → Type 2, use time slots
   - "Driver Based" → Type 3, NULL time slots
   - Other → Type 2 default
4. **Hire Time**: Always set to current UTC time
5. **Module Connection**: Always set ModuleIsConnected = 0

### Card Rules
1. **Driver Requirement**: Card can only be assigned to existing Driver
2. **Uniqueness**: Weigand number must be unique within Site
3. **One Card Per Driver**: Driver.CardDetailsId can only reference one Card
4. **Access Levels**: Determines scope of vehicle access permissions

### Access Permission Rules
1. **Permission Hierarchy**: Site > Department > Model > Vehicle
2. **Card Requirement**: All access records require valid Card
3. **Driver-Card Link**: Access based on Driver having CardDetailsId
4. **Duplicate Prevention**: Built-in checks prevent duplicate access records

## Bulk Seeding Implementation Guidelines

### Recommended Approach for Bulk Seeder

#### 1. Data Generation Sequence
Follow the migration dependency order:
1. Generate Module data (IoT devices)
2. Generate Department Checklist data  
3. Generate Vehicle data
4. Generate Person/Driver data
5. Generate Card data
6. Generate Access permissions

#### 2. Key Implementation Considerations

**For Driver/Person Generation**:
- Use XQ360 API endpoints (not direct SQL)
- Ensure proper Customer/Site/Department hierarchy exists
- Set `IsDriver = true` for drivers to auto-create Driver entities
- Handle authentication and API rate limiting

**For Vehicle Generation**:
- Pre-validate Module availability and non-assignment
- Generate ChecklistSettings and VehicleOtherSettings records first
- Use proper checklist type logic for time-based vs driver-based
- Handle NULL value preprocessing for optional integer fields
- Update module allocation status post-creation

**For Card Generation**:
- Ensure Driver entities exist before creating Cards
- Implement Weigand uniqueness validation within Site
- Create appropriate access permissions based on AccessLevel
- Handle the four-tier access permission structure

#### 3. Data Validation Requirements
- **Foreign Key Validation**: Verify all referenced entities exist
- **Business Rule Compliance**: Follow uniqueness and assignment rules
- **Data Type Handling**: Proper NULL handling for optional fields
- **Dependency Checking**: Ensure prerequisite data exists

#### 4. Error Handling Strategy
- **Graceful Degradation**: Skip individual records with validation failures
- **Detailed Logging**: Capture reasons for skipped records
- **Rollback Capability**: Use transactions where appropriate
- **Report Generation**: Provide comprehensive success/failure reporting

#### 5. Performance Considerations
- **Batch Processing**: Process records in batches for memory efficiency
- **Caching**: Cache lookup results for repeated foreign key references
- **API Rate Limiting**: Implement delays for API-based operations
- **Transaction Management**: Balance consistency with performance

### Sample Bulk Generation Flow

```csharp
// 1. Generate and create Modules
var modules = GenerateModules(dealerCount, moduleCountPerDealer);
await CreateModulesViaAPI(modules);

// 2. Generate and create Department Checklists
var checklists = GenerateDepartmentChecklists(departments, models);
await CreateChecklistsViaSQL(checklists);

// 3. Generate and create Vehicles
var vehicles = GenerateVehicles(sites, departments, models, modules);
await CreateVehiclesViaSQL(vehicles);

// 4. Generate and create Persons/Drivers
var persons = GeneratePersons(sites, departments, driverRatio);
await CreatePersonsViaAPI(persons);

// 5. Generate and create Cards
var cards = GenerateCards(drivers, accessLevelDistribution);
await CreateCardsViaSQL(cards);

// 6. Generate Vehicle Access Permissions
await CreateVehicleAccessPermissions(cards, accessLevels);
```

This approach ensures data integrity, follows the established patterns, and provides a foundation for implementing efficient bulk data generation for the XQ360 system.
