-- =============================================
-- FleetXQ Bulk Importer - Temp Table Merge Procedures
-- Creates stored procedures for merging temporary staging data to production
-- These procedures work with session temp tables instead of permanent staging tables
-- =============================================

-- =============================================
-- Procedure: usp_MergeDriversToProductionTemp
-- Merges validated drivers from temp staging to production tables
-- =============================================
IF OBJECT_ID('[dbo].[usp_MergeDriversToProductionTemp]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[usp_MergeDriversToProductionTemp]
GO

CREATE PROCEDURE [dbo].[usp_MergeDriversToProductionTemp]
    @ImportSessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000,
    @DryRun BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @ProcessedCount INT = 0;
    DECLARE @SuccessCount INT = 0;
    DECLARE @ErrorCount INT = 0;
    DECLARE @BatchStart BIGINT = 1;
    DECLARE @BatchEnd BIGINT;
    DECLARE @MaxId BIGINT;
    
    BEGIN TRY
        -- Validate that temp table exists
        IF NOT EXISTS (SELECT 1 FROM tempdb.sys.objects WHERE name LIKE '#DriverImport%' AND type = 'U')
        BEGIN
            RAISERROR('Temp table #DriverImport does not exist. Ensure CreateTempStagingTables was called first.', 16, 1);
            RETURN;
        END
        
        -- Get the range of IDs to process
        SELECT @MaxId = MAX([Id])
        FROM #DriverImport
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Valid'
            AND [ProcessingAction] IN ('Insert', 'Update');
        
        IF @MaxId IS NULL
        BEGIN
            PRINT 'No valid driver records to process';
            
            -- Return summary even for empty set
            SELECT 
                0 as ProcessedRows,
                0 as InsertedRows,
                0 as UpdatedRows,
                0 as SkippedRows;
            RETURN;
        END
        
        PRINT CONCAT('Starting driver merge for session: ', @ImportSessionId);
        PRINT CONCAT('Processing up to ID: ', @MaxId, ' in batches of ', @BatchSize);
        PRINT CONCAT('Dry Run Mode: ', CASE WHEN @DryRun = 1 THEN 'YES' ELSE 'NO' END);
        
        -- Process in batches to avoid lock escalation
        WHILE @BatchStart <= @MaxId
        BEGIN
            SET @BatchEnd = @BatchStart + @BatchSize - 1;
            
            IF @DryRun = 0
            BEGIN
                BEGIN TRANSACTION;
            END
            
            -- Step 1: MERGE Person records
            IF @DryRun = 0
            BEGIN
                WITH PersonData AS (
                    SELECT 
                        di.[Id],
                        COALESCE(di.[ExistingPersonId], NEWID()) as [PersonId],
                        di.[PersonFirstName],
                        di.[PersonLastName],
                        di.[PersonEmail],
                        di.[PersonPhone],
                        di.[Notes],
                        di.[CustomerId],
                        di.[SiteId],
                        di.[DepartmentId],
                        COALESCE(di.[PersonHasLicense], 1) as [HasLicense],
                        COALESCE(di.[PersonIsActiveDriver], 1) as [IsActiveDriver],
                        COALESCE(di.[PersonIsActiveDriver], 1) as [IsDriver],
                        COALESCE(di.[PersonVehicleAccess], 1) as [VehicleAccess],
                        COALESCE(di.[PersonLicenseActive], 1) as [LicenseActive],
                        COALESCE(di.[PersonCanUnlockVehicle], 0) as [CanUnlockVehicle],
                        COALESCE(di.[PersonNormalDriverAccess], 1) as [NormalDriverAccess],
                        0 as [OnDemand], -- Default values for required fields
                        0 as [VORActivateDeactivate],
                        0 as [MaintenanceMode],
                        di.[ProcessingAction]
                    FROM #DriverImport di
                    WHERE di.[ImportSessionId] = @ImportSessionId
                        AND di.[ValidationStatus] = 'Valid'
                        AND di.[ProcessingAction] IN ('Insert', 'Update')
                        AND di.[Id] BETWEEN @BatchStart AND @BatchEnd
                )
                MERGE [dbo].[Person] AS target
                USING PersonData AS source ON target.[Id] = source.[PersonId]
                WHEN MATCHED THEN
                    UPDATE SET
                        [FirstName] = source.[PersonFirstName],
                        [LastName] = source.[PersonLastName],
                        [Email] = source.[PersonEmail],
                        [Phone] = source.[PersonPhone],
                        [Notes] = source.[Notes],
                        [CustomerId] = source.[CustomerId],
                        [SiteId] = source.[SiteId],
                        [DepartmentId] = source.[DepartmentId],
                        [HasLicense] = source.[HasLicense],
                        [IsActiveDriver] = source.[IsActiveDriver],
                        [IsDriver] = source.[IsDriver],
                        [VehicleAccess] = source.[VehicleAccess],
                        [LicenseActive] = source.[LicenseActive],
                        [CanUnlockVehicle] = source.[CanUnlockVehicle],
                        [NormalDriverAccess] = source.[NormalDriverAccess]
                WHEN NOT MATCHED THEN
                    INSERT ([Id], [FirstName], [LastName], [Email], [Phone], [Notes],
                           [CustomerId], [SiteId], [DepartmentId], [HasLicense], [IsActiveDriver],
                           [IsDriver], [VehicleAccess], [LicenseActive], [CanUnlockVehicle],
                           [NormalDriverAccess], [OnDemand], [VORActivateDeactivate], [MaintenanceMode])
                    VALUES (source.[PersonId], source.[PersonFirstName], source.[PersonLastName],
                           source.[PersonEmail], source.[PersonPhone], source.[Notes],
                           source.[CustomerId], source.[SiteId], source.[DepartmentId],
                           source.[HasLicense], source.[IsActiveDriver], source.[IsDriver],
                           source.[VehicleAccess], source.[LicenseActive], source.[CanUnlockVehicle],
                           source.[NormalDriverAccess], source.[OnDemand], source.[VORActivateDeactivate],
                           source.[MaintenanceMode]);
                
                -- Step 2: MERGE Driver records
                WITH DriverData AS (
                    SELECT 
                        di.[Id],
                        COALESCE(di.[ExistingDriverId], NEWID()) as [DriverId],
                        COALESCE(di.[ExistingPersonId], NEWID()) as [PersonId], -- This should match PersonData
                        COALESCE(di.[DriverActive], 1) as [Active],
                        COALESCE(di.[DriverLicenseMode], 0) as [LicenseMode],
                        di.[DriverVehicleAccess] as [VehicleAccess],
                        di.[CustomerId],
                        di.[SiteId],
                        di.[DepartmentId]
                    FROM #DriverImport di
                    WHERE di.[ImportSessionId] = @ImportSessionId
                        AND di.[ValidationStatus] = 'Valid'
                        AND di.[ProcessingAction] IN ('Insert', 'Update')
                        AND di.[Id] BETWEEN @BatchStart AND @BatchEnd
                )
                MERGE [dbo].[Driver] AS target
                USING DriverData AS source ON target.[Id] = source.[DriverId]
                WHEN MATCHED THEN
                    UPDATE SET
                        [Active] = source.[Active],
                        [LicenseMode] = source.[LicenseMode],
                        [VehicleAccess] = source.[VehicleAccess],
                        [CustomerId] = source.[CustomerId],
                        [SiteId] = source.[SiteId],
                        [DepartmentId] = source.[DepartmentId]
                WHEN NOT MATCHED THEN
                    INSERT ([Id], [Active], [LicenseMode], [VehicleAccess], [CustomerId], [SiteId], [DepartmentId])
                    VALUES (source.[DriverId], source.[Active], source.[LicenseMode], source.[VehicleAccess],
                           source.[CustomerId], source.[SiteId], source.[DepartmentId]);
                
                -- Step 3: Update Person.DriverId relationships
                UPDATE p
                SET [DriverId] = COALESCE(di.[ExistingDriverId], NEWID())
                FROM [dbo].[Person] p
                INNER JOIN #DriverImport di ON p.[Id] = COALESCE(di.[ExistingPersonId], NEWID())
                WHERE di.[ImportSessionId] = @ImportSessionId
                    AND di.[ValidationStatus] = 'Valid'
                    AND di.[ProcessingAction] IN ('Insert', 'Update')
                    AND di.[Id] BETWEEN @BatchStart AND @BatchEnd;
            END
            
            -- Step 4: Update staging with processing results
            UPDATE #DriverImport
            SET [ProcessingAction] = CASE WHEN @DryRun = 1 THEN 'DryRun' ELSE 'Processed' END,
                [ProcessedAt] = GETUTCDATE(),
                [ValidationStatus] = CASE WHEN @DryRun = 1 THEN 'DryRun' ELSE 'Processed' END
            WHERE [ImportSessionId] = @ImportSessionId
                AND [ValidationStatus] = 'Valid'
                AND [Id] BETWEEN @BatchStart AND @BatchEnd;
            
            SET @ProcessedCount = @ProcessedCount + @@ROWCOUNT;
            SET @SuccessCount = @SuccessCount + @@ROWCOUNT;
            
            IF @DryRun = 0
            BEGIN
                COMMIT TRANSACTION;
            END
            
            PRINT CONCAT('Processed batch: ', @BatchStart, ' to ', @BatchEnd, ' (', @@ROWCOUNT, ' rows)');
            
            SET @BatchStart = @BatchEnd + 1;
        END
        
        -- Return processing summary
        SELECT 
            @ProcessedCount as ProcessedRows,
            (SELECT COUNT(*) FROM #DriverImport 
             WHERE [ImportSessionId] = @ImportSessionId 
               AND [ProcessingAction] IN ('Insert', 'DryRun', 'Processed')) as InsertedRows,
            (SELECT COUNT(*) FROM #DriverImport 
             WHERE [ImportSessionId] = @ImportSessionId 
               AND [ProcessingAction] = 'Update') as UpdatedRows,
            0 as SkippedRows;
        
        PRINT CONCAT('Driver merge completed. Processed: ', @ProcessedCount, ', Successful: ', @SuccessCount);
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 AND @DryRun = 0
            ROLLBACK TRANSACTION;
            
        -- Update staging with error information
        UPDATE #DriverImport
        SET [ProcessingErrors] = ERROR_MESSAGE(),
            [ValidationStatus] = 'Error'
        WHERE [ImportSessionId] = @ImportSessionId
            AND [Id] BETWEEN @BatchStart AND @BatchEnd;
        
        SET @ErrorMessage = CONCAT('Driver merge failed at batch ', @BatchStart, ': ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_MergeVehiclesToProductionTemp
-- Merges validated vehicles from temp staging to production tables
-- =============================================
IF OBJECT_ID('[dbo].[usp_MergeVehiclesToProductionTemp]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[usp_MergeVehiclesToProductionTemp]
GO

CREATE PROCEDURE [dbo].[usp_MergeVehiclesToProductionTemp]
    @ImportSessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000,
    @DryRun BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @ProcessedCount INT = 0;
    
    BEGIN TRY
        -- Validate that temp table exists
        IF NOT EXISTS (SELECT 1 FROM tempdb.sys.objects WHERE name LIKE '#VehicleImport%' AND type = 'U')
        BEGIN
            RAISERROR('Temp table #VehicleImport does not exist. Ensure CreateTempStagingTables was called first.', 16, 1);
            RETURN;
        END
        
        PRINT CONCAT('Starting vehicle merge for session: ', @ImportSessionId);
        PRINT CONCAT('Dry Run Mode: ', CASE WHEN @DryRun = 1 THEN 'YES' ELSE 'NO' END);
        
        -- Check if there are records to process
        IF NOT EXISTS (
            SELECT 1 FROM #VehicleImport
            WHERE [ImportSessionId] = @ImportSessionId
                AND [ValidationStatus] = 'Valid'
                AND [ProcessingAction] IN ('Insert', 'Update')
        )
        BEGIN
            PRINT 'No valid vehicle records to process';
            
            -- Return summary even for empty set
            SELECT 
                0 as ProcessedRows,
                0 as InsertedRows,
                0 as UpdatedRows,
                0 as SkippedRows;
            RETURN;
        END
        
        IF @DryRun = 0
        BEGIN
            -- MERGE Vehicle records in single operation for simplicity
            -- In production, you might want to batch this as well
            WITH VehicleData AS (
                SELECT 
                    COALESCE(vi.[ExistingVehicleId], NEWID()) as [VehicleId],
                    vi.[HireNo],
                    vi.[SerialNo],
                    vi.[Description],
                    COALESCE(vi.[OnHire], 1) as [OnHire],
                    COALESCE(vi.[ImpactLockout], 0) as [ImpactLockout],
                    COALESCE(vi.[IsCanbus], 0) as [IsCanbus],
                    COALESCE(vi.[TimeoutEnabled], 1) as [TimeoutEnabled],
                    COALESCE(vi.[ModuleIsConnected], 0) as [ModuleIsConnected],
                    vi.[IDLETimer],
                    vi.[CustomerId],
                    vi.[SiteId],
                    vi.[DepartmentId],
                    vi.[ModelId],
                    vi.[ModuleId],
                    vi.[AssignedDriverId],
                    vi.[AssignedPersonId]
                FROM #VehicleImport vi
                WHERE vi.[ImportSessionId] = @ImportSessionId
                    AND vi.[ValidationStatus] = 'Valid'
                    AND vi.[ProcessingAction] IN ('Insert', 'Update')
            )
            MERGE [dbo].[Vehicle] AS target
            USING VehicleData AS source ON target.[Id] = source.[VehicleId]
            WHEN MATCHED THEN
                UPDATE SET
                    [HireNo] = source.[HireNo],
                    [SerialNo] = source.[SerialNo],
                    [Description] = source.[Description],
                    [OnHire] = source.[OnHire],
                    [ImpactLockout] = source.[ImpactLockout],
                    [IsCanbus] = source.[IsCanbus],
                    [TimeoutEnabled] = source.[TimeoutEnabled],
                    [ModuleIsConnected] = source.[ModuleIsConnected],
                    [IDLETimer] = source.[IDLETimer],
                    [DriverId] = source.[AssignedDriverId],
                    [PersonId] = source.[AssignedPersonId]
            WHEN NOT MATCHED THEN
                INSERT ([Id], [HireNo], [SerialNo], [Description], [OnHire], [ImpactLockout],
                       [IsCanbus], [TimeoutEnabled], [ModuleIsConnected], [IDLETimer],
                       [CustomerId], [SiteId], [DepartmentId], [ModelId], [ModuleId1],
                       [DriverId], [PersonId])
                VALUES (source.[VehicleId], source.[HireNo], source.[SerialNo], source.[Description],
                       source.[OnHire], source.[ImpactLockout], source.[IsCanbus], source.[TimeoutEnabled],
                       source.[ModuleIsConnected], source.[IDLETimer], source.[CustomerId],
                       source.[SiteId], source.[DepartmentId], source.[ModelId], source.[ModuleId],
                       source.[AssignedDriverId], source.[AssignedPersonId]);
            
            SET @ProcessedCount = @@ROWCOUNT;
        END
        ELSE
        BEGIN
            -- For dry run, just count what would be processed
            SELECT @ProcessedCount = COUNT(*)
            FROM #VehicleImport
            WHERE [ImportSessionId] = @ImportSessionId
                AND [ValidationStatus] = 'Valid'
                AND [ProcessingAction] IN ('Insert', 'Update');
        END
        
        -- Update staging with processing results
        UPDATE #VehicleImport
        SET [ProcessingAction] = CASE WHEN @DryRun = 1 THEN 'DryRun' ELSE 'Processed' END,
            [ProcessedAt] = GETUTCDATE(),
            [ValidationStatus] = CASE WHEN @DryRun = 1 THEN 'DryRun' ELSE 'Processed' END
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Valid';
        
        -- Return processing summary
        SELECT 
            @ProcessedCount as ProcessedRows,
            (SELECT COUNT(*) FROM #VehicleImport 
             WHERE [ImportSessionId] = @ImportSessionId 
               AND [ProcessingAction] IN ('Insert', 'DryRun', 'Processed')) as InsertedRows,
            (SELECT COUNT(*) FROM #VehicleImport 
             WHERE [ImportSessionId] = @ImportSessionId 
               AND [ProcessingAction] = 'Update') as UpdatedRows,
            0 as SkippedRows;
        
        PRINT CONCAT('Vehicle merge completed. Processed: ', @ProcessedCount, ' rows');
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Vehicle merge failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_MergeAllTempToProduction
-- Merges all validated temp data to production tables
-- =============================================
IF OBJECT_ID('[dbo].[usp_MergeAllTempToProduction]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[usp_MergeAllTempToProduction]
GO

CREATE PROCEDURE [dbo].[usp_MergeAllTempToProduction]
    @ImportSessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000,
    @DryRun BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TotalProcessed INT = 0;
    DECLARE @TotalInserted INT = 0;
    DECLARE @TotalUpdated INT = 0;
    DECLARE @TotalSkipped INT = 0;
    
    BEGIN TRY
        PRINT CONCAT('Starting merge all temp data for session: ', @ImportSessionId);
        PRINT CONCAT('Dry Run Mode: ', CASE WHEN @DryRun = 1 THEN 'YES' ELSE 'NO' END);
        
        -- Merge drivers if temp table exists
        IF EXISTS (SELECT 1 FROM tempdb.sys.objects WHERE name LIKE '#DriverImport%' AND type = 'U')
        BEGIN
            PRINT 'Merging driver data...';
            
            CREATE TABLE #DriverResults (
                ProcessedRows INT,
                InsertedRows INT,
                UpdatedRows INT,
                SkippedRows INT
            );
            
            INSERT INTO #DriverResults
            EXEC [dbo].[usp_MergeDriversToProductionTemp] 
                @ImportSessionId = @ImportSessionId,
                @BatchSize = @BatchSize,
                @DryRun = @DryRun;
            
            SELECT 
                @TotalProcessed = @TotalProcessed + ProcessedRows,
                @TotalInserted = @TotalInserted + InsertedRows,
                @TotalUpdated = @TotalUpdated + UpdatedRows,
                @TotalSkipped = @TotalSkipped + SkippedRows
            FROM #DriverResults;
            
            DROP TABLE #DriverResults;
        END
        ELSE
        BEGIN
            PRINT 'No driver temp table found, skipping driver merge';
        END
        
        -- Merge vehicles if temp table exists
        IF EXISTS (SELECT 1 FROM tempdb.sys.objects WHERE name LIKE '#VehicleImport%' AND type = 'U')
        BEGIN
            PRINT 'Merging vehicle data...';
            
            CREATE TABLE #VehicleResults (
                ProcessedRows INT,
                InsertedRows INT,
                UpdatedRows INT,
                SkippedRows INT
            );
            
            INSERT INTO #VehicleResults
            EXEC [dbo].[usp_MergeVehiclesToProductionTemp] 
                @ImportSessionId = @ImportSessionId,
                @BatchSize = @BatchSize,
                @DryRun = @DryRun;
            
            SELECT 
                @TotalProcessed = @TotalProcessed + ProcessedRows,
                @TotalInserted = @TotalInserted + InsertedRows,
                @TotalUpdated = @TotalUpdated + UpdatedRows,
                @TotalSkipped = @TotalSkipped + SkippedRows
            FROM #VehicleResults;
            
            DROP TABLE #VehicleResults;
        END
        ELSE
        BEGIN
            PRINT 'No vehicle temp table found, skipping vehicle merge';
        END
        
        -- Return combined summary
        SELECT 
            @TotalProcessed as ProcessedRows,
            @TotalInserted as InsertedRows,
            @TotalUpdated as UpdatedRows,
            @TotalSkipped as SkippedRows;
        
        PRINT 'All temp table merge operations completed successfully';
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX) = CONCAT('Temp table merge failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

PRINT 'Created temp table merge procedures successfully'

