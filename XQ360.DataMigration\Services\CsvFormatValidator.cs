using Microsoft.Extensions.Logging;

namespace XQ360.DataMigration.Services
{
    public interface ICsvFormatValidator
    {
        Task<CsvValidationResult> ValidateAsync(string csvFilePath, string migrationType);
        Task<List<string>> GetRequiredHeadersAsync(string migrationType);
    }

    public class CsvValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public List<string> ExpectedHeaders { get; set; } = new();
        public List<string> ActualHeaders { get; set; } = new();
        public int RecordCount { get; set; }
    }

    public class CsvFormatValidator : ICsvFormatValidator
    {
        private readonly ILogger<CsvFormatValidator> _logger;

        public CsvFormatValidator(ILogger<CsvFormatValidator> logger)
        {
            _logger = logger;
        }

        public async Task<CsvValidationResult> ValidateAsync(string csvFilePath, string migrationType)
        {
            var result = new CsvValidationResult();
            
            try
            {
                _logger.LogInformation($"Validating CSV format for {migrationType}: {csvFilePath}");
                
                if (!File.Exists(csvFilePath))
                {
                    result.Errors.Add($"CSV file not found: {csvFilePath}");
                    return result;
                }

                // Get expected headers from template file
                result.ExpectedHeaders = await GetRequiredHeadersAsync(migrationType);
                if (!result.ExpectedHeaders.Any())
                {
                    result.Errors.Add($"Template not found for migration type: {migrationType}");
                    return result;
                }
                
                // Read only the first line (header) to validate structure
                using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
                
                var headerLine = await streamReader.ReadLineAsync();
                if (string.IsNullOrWhiteSpace(headerLine))
                {
                    result.Errors.Add("CSV file is empty or has no header row");
                    return result;
                }

                result.ActualHeaders = headerLine.Split(',').Select(h => h.Trim().Trim('"')).ToList();
                
                // Validate headers only (no data validation)
                ValidateHeaders(result, result.ExpectedHeaders, result.ActualHeaders);
                
                // Count data rows for reporting (without parsing them)
                result.RecordCount = 0;
                while (await streamReader.ReadLineAsync() != null)
                {
                    result.RecordCount++;
                }
                
                if (result.RecordCount == 0)
                {
                    result.Warnings.Add("No data records found in CSV file (only header row)");
                }
                
                result.IsValid = !result.Errors.Any();
                
                _logger.LogInformation($"CSV header validation completed for {migrationType}: {result.RecordCount} data rows, {result.Errors.Count} errors, {result.Warnings.Count} warnings");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Validation failed: {ex.Message}");
                _logger.LogError(ex, "CSV validation error");
            }

            return result;
        }

        private void ValidateHeaders(CsvValidationResult result, List<string> expectedHeaders, List<string> actualHeaders)
        {
            // Check for missing required headers
            var missingHeaders = expectedHeaders.Except(actualHeaders, StringComparer.OrdinalIgnoreCase).ToList();
            if (missingHeaders.Any())
            {
                result.Errors.Add($"Missing required headers: {string.Join(", ", missingHeaders)}");
            }

            // Check for extra headers (warnings only)
            var extraHeaders = actualHeaders.Except(expectedHeaders, StringComparer.OrdinalIgnoreCase).ToList();
            if (extraHeaders.Any())
            {
                result.Warnings.Add($"Extra headers found (will be ignored): {string.Join(", ", extraHeaders)}");
            }

            // Check header order (warning only)
            var expectedOrderedHeaders = expectedHeaders.Take(Math.Min(expectedHeaders.Count, actualHeaders.Count)).ToList();
            var actualOrderedHeaders = actualHeaders.Take(Math.Min(expectedHeaders.Count, actualHeaders.Count)).ToList();
            
            if (!expectedOrderedHeaders.SequenceEqual(actualOrderedHeaders, StringComparer.OrdinalIgnoreCase))
            {
                result.Warnings.Add("Header order differs from template (this is usually fine, but check for any mapping issues)");
            }
        }

        public async Task<List<string>> GetRequiredHeadersAsync(string migrationType)
        {
            try
            {
                var templateFileName = GetTemplateFileName(migrationType);
                if (string.IsNullOrEmpty(templateFileName))
                {
                    _logger.LogWarning($"No template file defined for migration type: {migrationType}");
                    return new List<string>();
                }

                // Get the CSV_Template directory path - handle both development and deployment environments
                var currentDirectory = Directory.GetCurrentDirectory();
                string templatePath;
                
                // Check if we're running from IIS deployment (XQ360Migration)
                if (currentDirectory.EndsWith("XQ360Migration"))
                {
                    // When deployed to IIS, CSV_Template is in the same directory as the application
                    templatePath = Path.Combine(currentDirectory, "CSV_Template", templateFileName);
                }
                else if (currentDirectory.EndsWith("XQ360.DataMigration.Web"))
                {
                    // During development, navigate to the main migration project
                    templatePath = Path.Combine(currentDirectory, "..", "XQ360.DataMigration", "CSV_Template", templateFileName);
                }
                else
                {
                    // Fallback for other scenarios
                    templatePath = Path.Combine(currentDirectory, "CSV_Template", templateFileName);
                }

                if (!File.Exists(templatePath))
                {
                    _logger.LogError($"Template file not found: {templatePath}");
                    return new List<string>();
                }

                // Read the first line (header) from the template file
                using var fileStream = new FileStream(templatePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
                
                var headerLine = await streamReader.ReadLineAsync();
                if (string.IsNullOrWhiteSpace(headerLine))
                {
                    _logger.LogError($"Template file has no header: {templatePath}");
                    return new List<string>();
                }

                var headers = headerLine.Split(',').Select(h => h.Trim().Trim('"')).ToList();
                _logger.LogInformation($"Loaded {headers.Count} headers from template {templateFileName}");
                
                return headers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error reading template headers for {migrationType}");
                return new List<string>();
            }
        }

        private string GetTemplateFileName(string migrationType)
        {
            return migrationType switch
            {
                "spare-modules" => "SPARE_MODEL_IMPORT_TEMPLATE.csv",
                "preop-checklist" => "PREOP_CHECKLIST_IMPORT.csv",
                "vehicles" => "VEHICLE_IMPORT.csv",
                "persons" => "PERSON_IMPORT_TEMPLATE.csv",
                "cards" => "CARD_IMPORT.csv",
                "supervisor-access" => "SUPERVISOR_ACCESS_IMPORT.csv",
                "driver-blacklist" => "DRIVER_BLACKLIST_IMPORT.csv",
                "website-users" => "WEBSITE_USER_IMPORT.csv",
                "sync-vehicle-settings" => "VEHICLE_IMPORT.csv", // Uses same template
                _ => null
            };
        }


    }
}