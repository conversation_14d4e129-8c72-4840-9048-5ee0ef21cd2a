import { ApiBaseService } from './api-base'
import type { ImportSession, ImportSessionStatus } from '@/types/import-session'

export interface CreateImportSessionRequest {
  dealerId: string
  customerId: string
  driversCount: number
  vehiclesCount: number
  batchSize?: number
  description?: string
  options?: {
    skipValidation?: boolean
    dryRun?: boolean
    overwriteExisting?: boolean
  }
}

export interface ExecuteImportRequest {
  sessionId: string
  confirmExecution?: boolean
}

export interface ImportSessionResponse {
  session: ImportSession
  estimatedDuration?: string
  warnings: string[]
}

export interface ImportProgressResponse {
  sessionId: string
  status: ImportSessionStatus
  progress: {
    currentStep: number
    totalSteps: number
    percentage: number
    stage: string
    message: string
    estimatedTimeRemaining?: string
  }
  results?: {
    driversProcessed: number
    vehiclesProcessed: number
    errorsCount: number
    warningsCount: number
  }
  errors: string[]
  warnings: string[]
  startTime?: string
  endTime?: string
}

export interface ImportResultResponse {
  sessionId: string
  status: ImportSessionStatus
  summary: {
    totalDriversCreated: number
    totalVehiclesCreated: number
    totalErrors: number
    totalWarnings: number
    duration: string
  }
  details: {
    driversCreated: number
    vehiclesCreated: number
    driversSkipped: number
    vehiclesSkipped: number
    driversWithErrors: number
    vehiclesWithErrors: number
  }
  errors: Array<{
    type: 'driver' | 'vehicle' | 'system'
    message: string
    details?: string
    recordIndex?: number
  }>
  warnings: Array<{
    type: 'driver' | 'vehicle' | 'system'
    message: string
    details?: string
    recordIndex?: number
  }>
}

export class BulkImportService extends ApiBaseService {
  constructor() {
    super('/api')
  }

  /**
   * Create a new import session
   */
  async createSession(request: CreateImportSessionRequest): Promise<ImportSessionResponse> {
    try {
      return await this.post<ImportSessionResponse>('/bulk-import/sessions', request)
    } catch (error) {
      console.error('Failed to create import session:', error)
      throw error
    }
  }

  /**
   * Get import session by ID
   */
  async getSession(sessionId: string): Promise<ImportSession> {
    try {
      return await this.get<ImportSession>(`/bulk-import/sessions/${sessionId}`)
    } catch (error) {
      console.error(`Failed to fetch session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Execute import session
   */
  async executeSession(request: ExecuteImportRequest): Promise<ImportProgressResponse> {
    try {
      return await this.post<ImportProgressResponse>(
        `/bulk-import/sessions/${request.sessionId}/execute`,
        { confirmExecution: request.confirmExecution }
      )
    } catch (error) {
      console.error(`Failed to execute session ${request.sessionId}:`, error)
      throw error
    }
  }

  /**
   * Get import session progress
   */
  async getSessionProgress(sessionId: string): Promise<ImportProgressResponse> {
    try {
      return await this.get<ImportProgressResponse>(`/bulk-import/sessions/${sessionId}/progress`)
    } catch (error) {
      console.error(`Failed to get session progress ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Cancel import session
   */
  async cancelSession(sessionId: string): Promise<void> {
    try {
      await this.delete(`/bulk-import/sessions/${sessionId}`)
    } catch (error) {
      console.error(`Failed to cancel session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Get import session results
   */
  async getSessionResults(sessionId: string): Promise<ImportResultResponse> {
    try {
      return await this.get<ImportResultResponse>(`/bulk-import/sessions/${sessionId}/results`)
    } catch (error) {
      console.error(`Failed to get session results ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Get list of import sessions for a dealer
   */
  async getSessions(dealerId?: string, limit: number = 20, offset: number = 0): Promise<{
    sessions: ImportSession[]
    totalCount: number
    hasMore: boolean
  }> {
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString()
      })

      if (dealerId) {
        params.append('dealerId', dealerId)
      }

      return await this.get<any>(`/bulk-import/sessions?${params.toString()}`)
    } catch (error) {
      console.error('Failed to fetch import sessions:', error)
      throw error
    }
  }

  /**
   * Get active import sessions
   */
  async getActiveSessions(): Promise<ImportSession[]> {
    try {
      const response = await this.getSessions(undefined, 50, 0)
      return response.sessions.filter(session => 
        session.status === 'queued' || 
        session.status === 'processing'
      )
    } catch (error) {
      console.error('Failed to fetch active sessions:', error)
      return []
    }
  }

  /**
   * Retry failed import session
   */
  async retrySession(sessionId: string): Promise<ImportProgressResponse> {
    try {
      return await this.post<ImportProgressResponse>(`/bulk-import/sessions/${sessionId}/retry`)
    } catch (error) {
      console.error(`Failed to retry session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Download session results as file
   */
  async downloadSessionResults(sessionId: string, format: 'csv' | 'json' | 'excel' = 'csv'): Promise<Blob> {
    try {
      const response = await this.client.get(`/bulk-import/sessions/${sessionId}/download`, {
        params: { format },
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error(`Failed to download session results ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(sessionId: string): Promise<{
    totalRecords: number
    processedRecords: number
    successfulRecords: number
    failedRecords: number
    processingRate: number
    estimatedCompletion?: string
  }> {
    try {
      return await this.get<any>(`/bulk-import/sessions/${sessionId}/stats`)
    } catch (error) {
      console.error(`Failed to get session stats ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Validate import parameters before creating session
   */
  async validateImportParameters(request: Omit<CreateImportSessionRequest, 'description'>): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    estimatedDuration: string
    resourceRequirements: {
      memoryMB: number
      diskSpaceMB: number
      estimatedCpuTime: string
    }
  }> {
    try {
      return await this.post<any>('/bulk-import/validate', request)
    } catch (error) {
      console.error('Failed to validate import parameters:', error)
      throw error
    }
  }
}
