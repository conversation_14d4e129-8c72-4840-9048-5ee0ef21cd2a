# 🔐 XQ360 Data Migration - Permission Troubleshooting Guide

## 🚨 Current Issue: CSV_Input Directory Access Denied

**Error Message:** `Access to the path 'C:\inetpub\wwwroot\XQ360.DataMigration\CSV_Input' is denied.`

This is a common IIS deployment permission issue where the application pool identity doesn't have proper access to the CSV_Input directory.

## 🛠️ Quick Fix Solutions

### Solution 1: Run Permission Fix Script (Recommended)

```powershell
# Navigate to the deployment directory
cd C:\FleetXQ\XQ360.DataMigration

# Run the permission fix script as Administrator
.\deploy\fix-permissions.ps1
```

### Solution 2: Manual Permission Fix

If the script doesn't work, manually set permissions:

```powershell
# Run as Administrator
$deployPath = "C:\inetpub\wwwroot\XQ360Migration"
$appPoolName = "XQ360MigrationPool"

# Set permissions on CSV_Input directory
icacls "$deployPath\CSV_Input" /grant "IIS_IUSRS:(OI)(CI)(F)" /T
icacls "$deployPath\CSV_Input" /grant "IIS AppPool\$appPoolName:(OI)(CI)(F)" /T
icacls "$deployPath\CSV_Input" /grant "NETWORK SERVICE:(OI)(CI)(F)" /T

# Set permissions on Logs directory
icacls "$deployPath\Logs" /grant "IIS_IUSRS:(OI)(CI)(F)" /T
icacls "$deployPath\Logs" /grant "IIS AppPool\$appPoolName:(OI)(CI)(F)" /T
icacls "$deployPath\Logs" /grant "NETWORK SERVICE:(OI)(CI)(F)" /T

# Restart application pool
Import-Module WebAdministration
Restart-WebAppPool -Name $appPoolName
```

### Solution 3: IIS Manager Manual Fix

1. **Open IIS Manager**
2. **Navigate to Application Pools**
3. **Select your application pool** (e.g., `XQ360MigrationPool`)
4. **Right-click → Advanced Settings**
5. **Set Identity to:**
   - `ApplicationPoolIdentity` (default)
   - Or `LocalSystem` (for testing only)
6. **Click OK**
7. **Restart the application pool**

### Solution 4: File System Permissions via GUI

1. **Navigate to:** `C:\inetpub\wwwroot\XQ360Migration\CSV_Input`
2. **Right-click → Properties**
3. **Security tab → Edit**
4. **Add these users with Full Control:**
   - `IIS_IUSRS`
   - `IIS AppPool\XQ360MigrationPool`
   - `NETWORK SERVICE`
5. **Apply → OK**

## 🔍 Root Cause Analysis

### Why This Happens

1. **IIS Application Pool Identity**: By default, IIS uses `ApplicationPoolIdentity` which has limited permissions
2. **File System Security**: Windows restricts access to system directories
3. **Deployment Process**: Files copied during deployment may not inherit proper permissions

### Common Scenarios

| Scenario | Cause | Solution |
|----------|-------|----------|
| Fresh deployment | Files copied without proper permissions | Run permission fix script |
| Server restart | Application pool identity changed | Restart application pool |
| Security updates | Windows security tightened | Re-apply permissions |
| Different server | Different IIS configuration | Check application pool settings |

## 🧪 Testing the Fix

### Test 1: File Creation Test

```powershell
# Test if the application can create files
$testPath = "C:\inetpub\wwwroot\XQ360Migration\CSV_Input\test.txt"
"Test content" | Out-File -FilePath $testPath -Encoding UTF8
if (Test-Path $testPath) {
    Write-Host "✅ File creation test passed"
    Remove-Item $testPath -Force
} else {
    Write-Host "❌ File creation test failed"
}
```

### Test 2: Web Application Test

1. **Open your web application**
2. **Try to upload a CSV file**
3. **Check if the permission error is gone**
4. **Verify file appears in CSV_Input directory**

### Test 3: Application Pool Test

```powershell
# Check application pool status
Import-Module WebAdministration
$pool = Get-WebAppPool -Name "XQ360MigrationPool"
Write-Host "Application Pool Status: $($pool.State)"
Write-Host "Application Pool Identity: $($pool.ProcessModel.IdentityType)"
```

## 🚨 Emergency Fixes

### Emergency Fix 1: Temporary Everyone Permissions

```powershell
# WARNING: Only for testing, not for production
icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "Everyone:(OI)(CI)(F)" /T
```

### Emergency Fix 2: Change Application Pool Identity

```powershell
# WARNING: Only for testing, not for production
Import-Module WebAdministration
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.identityType" -Value "LocalSystem"
Restart-WebAppPool -Name "XQ360MigrationPool"
```

## 📋 Prevention Checklist

### Before Deployment

- [ ] Ensure deployment script runs as Administrator
- [ ] Verify IIS is properly configured
- [ ] Check that .NET Core Hosting Bundle is installed
- [ ] Confirm application pool settings

### After Deployment

- [ ] Run permission fix script
- [ ] Test file upload functionality
- [ ] Verify logs directory is writable
- [ ] Check application pool is running
- [ ] Test CSV file processing

### Production Considerations

- [ ] Use specific service accounts instead of Everyone
- [ ] Limit permissions to minimum required
- [ ] Document permission requirements
- [ ] Set up monitoring for permission issues

## 🔧 Advanced Troubleshooting

### Check IIS Logs

```powershell
# View recent IIS logs
Get-ChildItem "C:\inetpub\logs\LogFiles\W3SVC1" | Sort-Object LastWriteTime -Descending | Select-Object -First 5
```

### Check Application Logs

```powershell
# Check application logs
Get-ChildItem "C:\inetpub\wwwroot\XQ360Migration\Logs" | Sort-Object LastWriteTime -Descending | Select-Object -First 5
```

### Verify File Permissions

```powershell
# Check current permissions
icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input"
```

## 📞 Support

If the permission issue persists:

1. **Check the application logs** in `C:\inetpub\wwwroot\XQ360Migration\Logs`
2. **Review IIS logs** in `C:\inetpub\logs\LogFiles`
3. **Verify application pool configuration** in IIS Manager
4. **Test with a simple file creation** to isolate the issue
5. **Consider using a dedicated service account** for production

## 🎯 Quick Reference

| Command | Purpose |
|---------|---------|
| `.\deploy\fix-permissions.ps1` | Run automated permission fix |
| `Restart-WebAppPool -Name "XQ360MigrationPool"` | Restart application pool |
| `icacls "path" /grant "user:(OI)(CI)(F)" /T` | Grant full permissions |
| `Get-WebAppPool -Name "XQ360MigrationPool"` | Check application pool status |

---

**Remember:** Always run permission fixes as Administrator and test thoroughly before applying to production environments. 