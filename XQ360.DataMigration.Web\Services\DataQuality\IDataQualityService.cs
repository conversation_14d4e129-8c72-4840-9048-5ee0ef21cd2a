namespace XQ360.DataMigration.Web.Services.DataQuality
{
    /// <summary>
    /// Service for calculating data quality metrics and generating reports
    /// </summary>
    public interface IDataQualityService
    {
        /// <summary>
        /// Calculates comprehensive data quality score for a dataset
        /// </summary>
        Task<DataQualityScore> CalculateDataQualityScoreAsync(Guid sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Calculates data quality score for a specific entity type
        /// </summary>
        Task<DataQualityScore> CalculateEntityQualityScoreAsync<T>(IEnumerable<T> entities, CancellationToken cancellationToken = default);

        /// <summary>
        /// Detects anomalies in the dataset
        /// </summary>
        Task<AnomalyDetectionResult> DetectAnomaliesAsync(Guid sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Generates a comprehensive data quality report
        /// </summary>
        Task<DataQualityReport> GenerateQualityReportAsync(Guid sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates data completeness
        /// </summary>
        Task<CompletenessMetrics> ValidateCompletenessAsync(Guid sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates data uniqueness
        /// </summary>
        Task<UniquenessMetrics> ValidateUniquenessAsync(Guid sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates referential integrity
        /// </summary>
        Task<ReferentialIntegrityMetrics> ValidateReferentialIntegrityAsync(Guid sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets real-time quality metrics for dashboard
        /// </summary>
        Task<RealTimeQualityMetrics> GetRealTimeMetricsAsync(Guid sessionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Exports quality metrics to various formats
        /// </summary>
        Task<string> ExportQualityMetricsAsync(Guid sessionId, ExportFormat format, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Overall data quality score
    /// </summary>
    public class DataQualityScore
    {
        public double OverallScore { get; set; }
        public double CompletenessScore { get; set; }
        public double UniquenessScore { get; set; }
        public double ReferentialIntegrityScore { get; set; }
        public double BusinessRuleComplianceScore { get; set; }
        public double ConsistencyScore { get; set; }
        public Dictionary<string, double> EntityScores { get; set; } = new();
        public List<QualityIssue> Issues { get; set; } = new();
        public DateTime CalculatedAt { get; set; }
        public TimeSpan CalculationDuration { get; set; }
        public QualityGrade Grade { get; set; }
    }

    /// <summary>
    /// Quality grade enumeration
    /// </summary>
    public enum QualityGrade
    {
        Excellent,  // 90-100%
        Good,       // 80-89%
        Fair,       // 70-79%
        Poor,       // 60-69%
        Critical    // <60%
    }

    /// <summary>
    /// Individual quality issue
    /// </summary>
    public class QualityIssue
    {
        public string IssueId { get; set; } = string.Empty;
        public QualityIssueType Type { get; set; }
        public QualityIssueSeverity Severity { get; set; }
        public string EntityType { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int AffectedRecords { get; set; }
        public double ImpactScore { get; set; }
        public string Recommendation { get; set; } = string.Empty;
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// Quality issue types
    /// </summary>
    public enum QualityIssueType
    {
        Completeness,
        Uniqueness,
        ReferentialIntegrity,
        BusinessRule,
        Consistency,
        Accuracy,
        Validity
    }

    /// <summary>
    /// Quality issue severity levels
    /// </summary>
    public enum QualityIssueSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Anomaly detection result
    /// </summary>
    public class AnomalyDetectionResult
    {
        public List<DataAnomaly> Anomalies { get; set; } = new();
        public AnomalyStatistics Statistics { get; set; } = new();
        public DateTime DetectedAt { get; set; }
        public TimeSpan DetectionDuration { get; set; }
    }

    /// <summary>
    /// Individual data anomaly
    /// </summary>
    public class DataAnomaly
    {
        public string AnomalyId { get; set; } = string.Empty;
        public AnomalyType Type { get; set; }
        public string EntityType { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public object? ExpectedValue { get; set; }
        public object? ActualValue { get; set; }
        public double AnomalyScore { get; set; }
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// Anomaly types
    /// </summary>
    public enum AnomalyType
    {
        OutlierValue,
        UnexpectedPattern,
        MissingExpectedValue,
        InvalidFormat,
        InconsistentData,
        SuspiciousDuplicate
    }

    /// <summary>
    /// Anomaly detection statistics
    /// </summary>
    public class AnomalyStatistics
    {
        public int TotalAnomalies { get; set; }
        public Dictionary<AnomalyType, int> AnomaliesByType { get; set; } = new();
        public Dictionary<string, int> AnomaliesByEntity { get; set; } = new();
        public double AnomalyRate { get; set; }
        public int RecordsAnalyzed { get; set; }
    }

    /// <summary>
    /// Comprehensive data quality report
    /// </summary>
    public class DataQualityReport
    {
        public Guid SessionId { get; set; }
        public DataQualityScore QualityScore { get; set; } = new();
        public CompletenessMetrics Completeness { get; set; } = new();
        public UniquenessMetrics Uniqueness { get; set; } = new();
        public ReferentialIntegrityMetrics ReferentialIntegrity { get; set; } = new();
        public AnomalyDetectionResult Anomalies { get; set; } = new();
        public List<QualityRecommendation> Recommendations { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
        public TimeSpan GenerationDuration { get; set; }
        public string Summary { get; set; } = string.Empty;
    }

    /// <summary>
    /// Quality improvement recommendation
    /// </summary>
    public class QualityRecommendation
    {
        public string RecommendationId { get; set; } = string.Empty;
        public QualityRecommendationType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public QualityRecommendationPriority Priority { get; set; }
        public double EstimatedImpact { get; set; }
        public string Implementation { get; set; } = string.Empty;
        public List<string> AffectedEntities { get; set; } = new();
    }

    /// <summary>
    /// Quality recommendation types
    /// </summary>
    public enum QualityRecommendationType
    {
        DataCleaning,
        ValidationRule,
        ProcessImprovement,
        SystemConfiguration,
        Training
    }

    /// <summary>
    /// Quality recommendation priority levels
    /// </summary>
    public enum QualityRecommendationPriority
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Data completeness metrics
    /// </summary>
    public class CompletenessMetrics
    {
        public double OverallCompletenessRate { get; set; }
        public Dictionary<string, double> EntityCompletenessRates { get; set; } = new();
        public Dictionary<string, Dictionary<string, double>> FieldCompletenessRates { get; set; } = new();
        public List<CompletenessIssue> Issues { get; set; } = new();
        public int TotalRecords { get; set; }
        public int CompleteRecords { get; set; }
    }

    /// <summary>
    /// Completeness issue
    /// </summary>
    public class CompletenessIssue
    {
        public string EntityType { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public int MissingCount { get; set; }
        public int TotalCount { get; set; }
        public double MissingRate { get; set; }
        public bool IsRequired { get; set; }
    }

    /// <summary>
    /// Data uniqueness metrics
    /// </summary>
    public class UniquenessMetrics
    {
        public double OverallUniquenessRate { get; set; }
        public Dictionary<string, double> EntityUniquenessRates { get; set; } = new();
        public List<UniquenessViolation> Violations { get; set; } = new();
        public int TotalRecords { get; set; }
        public int UniqueRecords { get; set; }
        public int DuplicateRecords { get; set; }
    }

    /// <summary>
    /// Uniqueness violation
    /// </summary>
    public class UniquenessViolation
    {
        public string EntityType { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public object? DuplicateValue { get; set; }
        public int OccurrenceCount { get; set; }
        public List<int> AffectedRowNumbers { get; set; } = new();
    }

    /// <summary>
    /// Referential integrity metrics
    /// </summary>
    public class ReferentialIntegrityMetrics
    {
        public double OverallIntegrityRate { get; set; }
        public Dictionary<string, double> EntityIntegrityRates { get; set; } = new();
        public List<IntegrityViolation> Violations { get; set; } = new();
        public int TotalReferences { get; set; }
        public int ValidReferences { get; set; }
        public int BrokenReferences { get; set; }
    }

    /// <summary>
    /// Referential integrity violation
    /// </summary>
    public class IntegrityViolation
    {
        public string EntityType { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public string ReferencedTable { get; set; } = string.Empty;
        public object? InvalidValue { get; set; }
        public int ViolationCount { get; set; }
        public List<int> AffectedRowNumbers { get; set; } = new();
    }

    /// <summary>
    /// Real-time quality metrics for dashboard
    /// </summary>
    public class RealTimeQualityMetrics
    {
        public double CurrentQualityScore { get; set; }
        public QualityTrend Trend { get; set; }
        public Dictionary<string, double> MetricsByCategory { get; set; } = new();
        public List<QualityAlert> ActiveAlerts { get; set; } = new();
        public DateTime LastUpdated { get; set; }
        public TimeSpan UpdateFrequency { get; set; }
    }

    /// <summary>
    /// Quality trend information
    /// </summary>
    public class QualityTrend
    {
        public TrendDirection Direction { get; set; }
        public double ChangeRate { get; set; }
        public TimeSpan Period { get; set; }
        public List<QualityDataPoint> DataPoints { get; set; } = new();
    }

    /// <summary>
    /// Trend direction
    /// </summary>
    public enum TrendDirection
    {
        Improving,
        Stable,
        Declining
    }

    /// <summary>
    /// Quality data point for trend analysis
    /// </summary>
    public class QualityDataPoint
    {
        public DateTime Timestamp { get; set; }
        public double QualityScore { get; set; }
        public Dictionary<string, double> CategoryScores { get; set; } = new();
    }

    /// <summary>
    /// Quality alert
    /// </summary>
    public class QualityAlert
    {
        public string AlertId { get; set; } = string.Empty;
        public QualityAlertType Type { get; set; }
        public QualityAlertSeverity Severity { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime TriggeredAt { get; set; }
        public bool IsActive { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// Quality alert types
    /// </summary>
    public enum QualityAlertType
    {
        QualityDegradation,
        ThresholdBreach,
        AnomalyDetected,
        ValidationFailure
    }

    /// <summary>
    /// Quality alert severity levels
    /// </summary>
    public enum QualityAlertSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    /// <summary>
    /// Export format options
    /// </summary>
    public enum ExportFormat
    {
        Json,
        Csv,
        Excel,
        Pdf,
        Html
    }
}
