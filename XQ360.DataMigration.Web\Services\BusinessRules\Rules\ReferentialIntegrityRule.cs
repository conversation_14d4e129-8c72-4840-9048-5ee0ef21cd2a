using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;

namespace XQ360.DataMigration.Web.Services.BusinessRules.Rules
{
    /// <summary>
    /// Business rule for validating referential integrity constraints
    /// </summary>
    public class ReferentialIntegrityRule : BusinessRuleBase
    {
        private readonly ILogger<ReferentialIntegrityRule> _logger;

        public ReferentialIntegrityRule(ILogger<ReferentialIntegrityRule> logger)
        {
            _logger = logger;
        }

        public override string Id => "REFERENTIAL_INTEGRITY";
        public override string Name => "Referential Integrity Validation";
        public override string Description => "Validates that foreign key relationships maintain referential integrity across the database";
        public override string Category => "Data Integrity";
        public override string[] ApplicableEntityTypes => new[] { "Person", "Driver", "Vehicle", "Card", "Module", "Site", "Department", "Model" };

        public override async Task<ValidationResult> ValidateAsync(object entity, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var issues = new List<ValidationIssue>();

            try
            {
                using var connection = new SqlConnection(context.ConnectionString);
                await connection.OpenAsync(cancellationToken);

                var entityType = entity.GetType().Name;

                // Validate based on entity type
                switch (entityType)
                {
                    case "Person":
                    case "PersonImportModel":
                        await ValidatePersonReferencesAsync(entity, connection, issues, cancellationToken);
                        break;

                    case "Vehicle":
                    case "VehicleImportModel":
                        await ValidateVehicleReferencesAsync(entity, connection, issues, cancellationToken);
                        break;

                    case "Driver":
                        await ValidateDriverReferencesAsync(entity, connection, issues, cancellationToken);
                        break;

                    case "Card":
                    case "CardImportModel":
                        await ValidateCardReferencesAsync(entity, connection, issues, cancellationToken);
                        break;

                    case "Site":
                        await ValidateSiteReferencesAsync(entity, connection, issues, cancellationToken);
                        break;

                    case "Department":
                        await ValidateDepartmentReferencesAsync(entity, connection, issues, cancellationToken);
                        break;

                    case "Model":
                        await ValidateModelReferencesAsync(entity, connection, issues, cancellationToken);
                        break;

                    default:
                        // Generic validation for unknown entity types
                        await ValidateGenericReferencesAsync(entity, connection, issues, cancellationToken);
                        break;
                }

                return issues.Any(i => i.Severity == ValidationSeverity.Error)
                    ? CreateFailureResult(issues.ToArray())
                    : new ValidationResult { IsValid = true, Issues = issues };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating referential integrity for {EntityType}", entity.GetType().Name);

                issues.Add(CreateIssue(ValidationSeverity.Error,
                    $"Referential integrity validation error: {ex.Message}",
                    "System",
                    null,
                    "Contact system administrator"));

                return CreateFailureResult(issues.ToArray());
            }
        }

        private async Task ValidatePersonReferencesAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            // Validate Department reference
            var departmentName = GetPropertyValue(entity, "DepartmentName")?.ToString();
            var siteName = GetPropertyValue(entity, "Site")?.ToString();
            var customerName = GetPropertyValue(entity, "Customer")?.ToString();

            if (!string.IsNullOrEmpty(departmentName) && !string.IsNullOrEmpty(siteName) && !string.IsNullOrEmpty(customerName))
            {
                var departmentExists = await CheckDepartmentExistsAsync(departmentName, siteName, customerName, connection, cancellationToken);
                if (!departmentExists.Exists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Department '{departmentName}' not found in site '{siteName}' for customer '{customerName}'",
                        "DepartmentName",
                        departmentName,
                        "Ensure the department exists or create it first"));
                }
            }

            // Validate Driver reference if present
            var driverId = GetPropertyValue(entity, "DriverId");
            if (driverId is Guid driverGuid && driverGuid != Guid.Empty)
            {
                var driverExists = await CheckEntityExistsAsync("Driver", driverGuid, connection, cancellationToken);
                if (!driverExists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Driver with ID '{driverGuid}' not found",
                        "DriverId",
                        driverGuid,
                        "Ensure the driver exists or create it first"));
                }
            }
        }

        private async Task ValidateVehicleReferencesAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            // Validate Department reference
            var departmentName = GetPropertyValue(entity, "DepartmentName")?.ToString();
            var siteName = GetPropertyValue(entity, "Site")?.ToString();
            var customerName = GetPropertyValue(entity, "Customer")?.ToString();

            if (!string.IsNullOrEmpty(departmentName) && !string.IsNullOrEmpty(siteName) && !string.IsNullOrEmpty(customerName))
            {
                var departmentExists = await CheckDepartmentExistsAsync(departmentName, siteName, customerName, connection, cancellationToken);
                if (!departmentExists.Exists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Department '{departmentName}' not found in site '{siteName}' for customer '{customerName}'",
                        "DepartmentName",
                        departmentName,
                        "Ensure the department exists or create it first"));
                }
            }

            // Validate Model reference
            var modelName = GetPropertyValue(entity, "ModelName")?.ToString();
            var dealerName = GetPropertyValue(entity, "Dealer")?.ToString();

            if (!string.IsNullOrEmpty(modelName) && !string.IsNullOrEmpty(dealerName))
            {
                var modelExists = await CheckModelExistsAsync(modelName, dealerName, connection, cancellationToken);
                if (!modelExists.Exists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Model '{modelName}' not found for dealer '{dealerName}'",
                        "ModelName",
                        modelName,
                        "Ensure the model exists or create it first"));
                }
            }

            // Validate Module reference if present
            var moduleSerial = GetPropertyValue(entity, "ModuleSerial")?.ToString();
            if (!string.IsNullOrEmpty(moduleSerial))
            {
                var moduleExists = await CheckModuleExistsBySerialAsync(moduleSerial, connection, cancellationToken);
                if (!moduleExists.Exists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Module with serial '{moduleSerial}' not found",
                        "ModuleSerial",
                        moduleSerial,
                        "Ensure the module exists or create it first"));
                }
                else if (moduleExists.IsAllocated)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        $"Module with serial '{moduleSerial}' is already allocated",
                        "ModuleSerial",
                        moduleSerial,
                        "Consider using an unallocated module"));
                }
            }
        }

        private async Task ValidateDriverReferencesAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            // Validate Card reference if present
            var cardId = GetPropertyValue(entity, "CardDetailsId");
            if (cardId is Guid cardGuid && cardGuid != Guid.Empty)
            {
                var cardExists = await CheckEntityExistsAsync("Card", cardGuid, connection, cancellationToken);
                if (!cardExists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Card with ID '{cardGuid}' not found",
                        "CardDetailsId",
                        cardGuid,
                        "Ensure the card exists or create it first"));
                }
            }
        }

        private async Task ValidateCardReferencesAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            // Cards typically don't have foreign key references in the import model
            // But we can validate business logic constraints
            
            var weigandNumber = GetPropertyValue(entity, "WeigandNumber")?.ToString();
            if (string.IsNullOrEmpty(weigandNumber))
            {
                issues.Add(CreateIssue(ValidationSeverity.Warning,
                    "Card has no Weigand number specified",
                    "WeigandNumber",
                    null,
                    "Consider providing a Weigand number for proper card functionality"));
            }
        }

        private async Task ValidateSiteReferencesAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            // Validate Customer reference
            var customerId = GetPropertyValue(entity, "CustomerId");
            if (customerId is Guid customerGuid && customerGuid != Guid.Empty)
            {
                var customerExists = await CheckEntityExistsAsync("Customer", customerGuid, connection, cancellationToken);
                if (!customerExists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Customer with ID '{customerGuid}' not found",
                        "CustomerId",
                        customerGuid,
                        "Ensure the customer exists or create it first"));
                }
            }
        }

        private async Task ValidateDepartmentReferencesAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            // Validate Site reference
            var siteId = GetPropertyValue(entity, "SiteId");
            if (siteId is Guid siteGuid && siteGuid != Guid.Empty)
            {
                var siteExists = await CheckEntityExistsAsync("Site", siteGuid, connection, cancellationToken);
                if (!siteExists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Site with ID '{siteGuid}' not found",
                        "SiteId",
                        siteGuid,
                        "Ensure the site exists or create it first"));
                }
            }
        }

        private async Task ValidateModelReferencesAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            // Validate Dealer reference
            var dealerId = GetPropertyValue(entity, "DealerId");
            if (dealerId is Guid dealerGuid && dealerGuid != Guid.Empty)
            {
                var dealerExists = await CheckEntityExistsAsync("Dealer", dealerGuid, connection, cancellationToken);
                if (!dealerExists)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Dealer with ID '{dealerGuid}' not found",
                        "DealerId",
                        dealerGuid,
                        "Ensure the dealer exists or create it first"));
                }
            }
        }

        private async Task ValidateGenericReferencesAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            // Generic validation for entities with common foreign key patterns
            var properties = entity.GetType().GetProperties();
            
            foreach (var property in properties)
            {
                if (property.Name.EndsWith("Id") && property.PropertyType == typeof(Guid))
                {
                    var value = property.GetValue(entity);
                    if (value is Guid guid && guid != Guid.Empty)
                    {
                        // Try to determine the referenced table name
                        var referencedTable = property.Name.Replace("Id", "");
                        if (IsKnownTable(referencedTable))
                        {
                            var exists = await CheckEntityExistsAsync(referencedTable, guid, connection, cancellationToken);
                            if (!exists)
                            {
                                issues.Add(CreateIssue(ValidationSeverity.Warning,
                                    $"Referenced {referencedTable} with ID '{guid}' not found",
                                    property.Name,
                                    guid,
                                    $"Ensure the {referencedTable} exists or verify the reference"));
                            }
                        }
                    }
                }
            }
        }

        private async Task<(bool Exists, Guid? Id)> CheckDepartmentExistsAsync(string departmentName, string siteName, string customerName, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT d.Id
                FROM [dbo].[Department] d
                INNER JOIN [dbo].[Site] s ON d.SiteId = s.Id
                INNER JOIN [dbo].[Customer] c ON s.CustomerId = c.Id
                WHERE d.Name = @DepartmentName 
                AND s.Name = @SiteName 
                AND c.CompanyName = @CustomerName";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentName", departmentName);
            command.Parameters.AddWithValue("@SiteName", siteName);
            command.Parameters.AddWithValue("@CustomerName", customerName);

            var result = await command.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                return (true, (Guid)result);
            }

            return (false, null);
        }

        private async Task<(bool Exists, Guid? Id)> CheckModelExistsAsync(string modelName, string dealerName, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT m.Id
                FROM [dbo].[Model] m
                INNER JOIN [dbo].[Dealer] d ON m.DealerId = d.Id
                WHERE m.Name = @ModelName 
                AND d.Name = @DealerName";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@ModelName", modelName);
            command.Parameters.AddWithValue("@DealerName", dealerName);

            var result = await command.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                return (true, (Guid)result);
            }

            return (false, null);
        }

        private async Task<(bool Exists, bool IsAllocated, Guid? Id)> CheckModuleExistsBySerialAsync(string serialNumber, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT Id, IsAllocated
                FROM [dbo].[Module]
                WHERE SerialNumber = @SerialNumber";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@SerialNumber", serialNumber);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync(cancellationToken))
            {
                var id = reader.GetGuid(reader.GetOrdinal("Id"));
                var isAllocated = reader.GetBoolean(reader.GetOrdinal("IsAllocated"));
                return (true, isAllocated, id);
            }

            return (false, false, null);
        }

        private async Task<bool> CheckEntityExistsAsync(string tableName, Guid id, SqlConnection connection, CancellationToken cancellationToken)
        {
            var sql = $@"
                SELECT COUNT(*)
                FROM [dbo].[{tableName}]
                WHERE Id = @Id";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@Id", id);

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return count > 0;
        }

        private bool IsKnownTable(string tableName)
        {
            var knownTables = new[]
            {
                "Customer", "Dealer", "Site", "Department", "Model", "Vehicle", 
                "Person", "Driver", "Card", "Module", "Permission", "Canrule"
            };

            return knownTables.Contains(tableName, StringComparer.OrdinalIgnoreCase);
        }
    }
}
