<template>
  <div class="container-fluid py-4">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">Import Sessions History</h5>
          </div>
          <div class="card-body">
            <p class="text-muted">
              View and manage your bulk import sessions.
            </p>
            <div class="alert alert-light border">
              <i class="bi bi-info-circle me-2"></i>
              This feature will be implemented in a future update.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Sessions view component
</script>

<style scoped>
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>
