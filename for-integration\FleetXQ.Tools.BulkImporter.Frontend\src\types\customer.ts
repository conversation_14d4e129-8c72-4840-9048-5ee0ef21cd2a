export interface Customer {
    id: number
    dealerId: number
    name: string
    companyName: string
    contactName: string
    contactEmail: string
    contactPhone?: string
    address?: CustomerAddress
    isActive: boolean
    createdAt: Date
    updatedAt: Date
}

export interface CustomerAddress {
    street: string
    city: string
    state: string
    postalCode: string
    country: string
}

export interface CreateCustomerRequest {
    dealerId: string
    companyName: string
    countryId: string
    contactNumber?: string
    email?: string
    address?: string
    description?: string
    contractNumber?: string
    contractDate?: string
}

export interface CustomerSearchFilter {
    dealerId: number
    query?: string
    isActive?: boolean
    limit?: number
    offset?: number
}

export interface CustomerSearchResult {
    customers: Customer[]
    total: number
    totalCount: number
    pageNumber: number
    pageSize: number
    totalPages: number
    hasMore: boolean
}
