import type { ValidationRule } from '@/composables/useValidation'

// Common validation patterns
export const ValidationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithSpaces: /^[a-zA-Z0-9\s]+$/,
  numbersOnly: /^\d+$/,
  lettersOnly: /^[a-zA-Z]+$/,
  noSpecialChars: /^[a-zA-Z0-9\s\-_]+$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  macAddress: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  vin: /^[A-HJ-NPR-Z0-9]{17}$/,
  licensePlate: /^[A-Z0-9\-\s]{2,10}$/,
  postalCode: /^[A-Z0-9\s\-]{3,10}$/
}

// Validation rule factories
export class ValidationRuleFactory {
  static required(message = 'This field is required'): ValidationRule {
    return {
      validator: (value: any) => {
        if (Array.isArray(value)) return value.length > 0
        if (typeof value === 'string') return value.trim().length > 0
        return value !== null && value !== undefined && value !== ''
      },
      message,
      trigger: 'blur'
    }
  }

  static minLength(min: number, message?: string): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        return String(value).length >= min
      },
      message: message || `Must be at least ${min} characters`,
      trigger: 'blur'
    }
  }

  static maxLength(max: number, message?: string): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        return String(value).length <= max
      },
      message: message || `Must be no more than ${max} characters`,
      trigger: 'blur'
    }
  }

  static lengthRange(min: number, max: number, message?: string): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        const length = String(value).length
        return length >= min && length <= max
      },
      message: message || `Must be between ${min} and ${max} characters`,
      trigger: 'blur'
    }
  }

  static email(message = 'Must be a valid email address'): ValidationRule {
    return {
      validator: (value: string) => {
        if (!value) return true
        return ValidationPatterns.email.test(value)
      },
      message,
      trigger: 'blur'
    }
  }

  static phone(message = 'Must be a valid phone number'): ValidationRule {
    return {
      validator: (value: string) => {
        if (!value) return true
        return ValidationPatterns.phone.test(value.replace(/\s/g, ''))
      },
      message,
      trigger: 'blur'
    }
  }

  static numeric(message = 'Must be a number'): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        return !isNaN(Number(value))
      },
      message,
      trigger: 'change'
    }
  }

  static integer(message = 'Must be a whole number'): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        return Number.isInteger(Number(value))
      },
      message,
      trigger: 'change'
    }
  }

  static positiveNumber(message = 'Must be a positive number'): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        const num = Number(value)
        return !isNaN(num) && num > 0
      },
      message,
      trigger: 'change'
    }
  }

  static min(minValue: number, message?: string): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        return Number(value) >= minValue
      },
      message: message || `Must be at least ${minValue}`,
      trigger: 'change'
    }
  }

  static max(maxValue: number, message?: string): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        return Number(value) <= maxValue
      },
      message: message || `Must be no more than ${maxValue}`,
      trigger: 'change'
    }
  }

  static range(min: number, max: number, message?: string): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        const num = Number(value)
        return !isNaN(num) && num >= min && num <= max
      },
      message: message || `Must be between ${min} and ${max}`,
      trigger: 'change'
    }
  }

  static pattern(regex: RegExp, message: string): ValidationRule {
    return {
      validator: (value: string) => {
        if (!value) return true
        return regex.test(value)
      },
      message,
      trigger: 'blur'
    }
  }

  static oneOf(allowedValues: any[], message?: string): ValidationRule {
    return {
      validator: (value: any) => {
        if (!value) return true
        return allowedValues.includes(value)
      },
      message: message || `Must be one of: ${allowedValues.join(', ')}`,
      trigger: 'change'
    }
  }

  static url(message = 'Must be a valid URL'): ValidationRule {
    return {
      validator: (value: string) => {
        if (!value) return true
        return ValidationPatterns.url.test(value)
      },
      message,
      trigger: 'blur'
    }
  }

  static strongPassword(message = 'Password must contain at least 8 characters with uppercase, lowercase, number and special character'): ValidationRule {
    return {
      validator: (value: string) => {
        if (!value) return true
        return ValidationPatterns.strongPassword.test(value)
      },
      message,
      trigger: 'blur'
    }
  }

  static confirmPassword(passwordField: string, message = 'Passwords do not match'): ValidationRule {
    return {
      validator: (value: string, formData?: any) => {
        if (!value) return true
        return value === formData?.[passwordField]
      },
      message,
      trigger: 'blur'
    }
  }

  static async(asyncValidator: (value: any) => Promise<boolean>, message: string): ValidationRule {
    return {
      validator: asyncValidator,
      message,
      trigger: 'blur'
    }
  }

  static custom(validator: (value: any) => boolean, message: string): ValidationRule {
    return {
      validator,
      message,
      trigger: 'blur'
    }
  }
}

// Business-specific validation rules
export class BusinessValidationRules {
  static vehicleCount(min = 1, max = 10000): ValidationRule[] {
    return [
      ValidationRuleFactory.required('Vehicle count is required'),
      ValidationRuleFactory.integer('Vehicle count must be a whole number'),
      ValidationRuleFactory.range(min, max, `Vehicle count must be between ${min} and ${max}`)
    ]
  }

  static driverCount(min = 1, max = 5000): ValidationRule[] {
    return [
      ValidationRuleFactory.required('Driver count is required'),
      ValidationRuleFactory.integer('Driver count must be a whole number'),
      ValidationRuleFactory.range(min, max, `Driver count must be between ${min} and ${max}`)
    ]
  }

  static batchSize(min = 100, max = 1000): ValidationRule[] {
    return [
      ValidationRuleFactory.integer('Batch size must be a whole number'),
      ValidationRuleFactory.range(min, max, `Batch size must be between ${min} and ${max}`)
    ]
  }

  static dealerName(): ValidationRule[] {
    return [
      ValidationRuleFactory.required('Dealer name is required'),
      ValidationRuleFactory.minLength(2, 'Dealer name must be at least 2 characters'),
      ValidationRuleFactory.maxLength(100, 'Dealer name must be no more than 100 characters'),
      ValidationRuleFactory.pattern(ValidationPatterns.alphanumericWithSpaces, 'Dealer name can only contain letters, numbers and spaces')
    ]
  }

  static customerName(): ValidationRule[] {
    return [
      ValidationRuleFactory.required('Customer name is required'),
      ValidationRuleFactory.minLength(2, 'Customer name must be at least 2 characters'),
      ValidationRuleFactory.maxLength(100, 'Customer name must be no more than 100 characters'),
      ValidationRuleFactory.pattern(ValidationPatterns.alphanumericWithSpaces, 'Customer name can only contain letters, numbers and spaces')
    ]
  }

  static contactNumber(): ValidationRule[] {
    return [
      ValidationRuleFactory.phone('Must be a valid phone number'),
      ValidationRuleFactory.minLength(10, 'Phone number must be at least 10 digits'),
      ValidationRuleFactory.maxLength(15, 'Phone number must be no more than 15 digits')
    ]
  }

  static emailAddress(): ValidationRule[] {
    return [
      ValidationRuleFactory.email('Must be a valid email address'),
      ValidationRuleFactory.maxLength(254, 'Email address is too long')
    ]
  }

  static address(): ValidationRule[] {
    return [
      ValidationRuleFactory.minLength(5, 'Address must be at least 5 characters'),
      ValidationRuleFactory.maxLength(200, 'Address must be no more than 200 characters')
    ]
  }

  static description(): ValidationRule[] {
    return [
      ValidationRuleFactory.maxLength(500, 'Description must be no more than 500 characters')
    ]
  }

  static contractNumber(): ValidationRule[] {
    return [
      ValidationRuleFactory.pattern(ValidationPatterns.alphanumeric, 'Contract number can only contain letters and numbers'),
      ValidationRuleFactory.maxLength(50, 'Contract number must be no more than 50 characters')
    ]
  }
}

// Validation utilities
export const ValidationUtils = {
  /**
   * Combine multiple validation rules into a single array
   */
  combine(...ruleArrays: ValidationRule[][]): ValidationRule[] {
    return ruleArrays.flat()
  },

  /**
   * Create a conditional validation rule
   */
  conditional(condition: () => boolean, rules: ValidationRule[]): ValidationRule[] {
    return rules.map(rule => ({
      ...rule,
      validator: (value: any) => {
        if (!condition()) return true
        return rule.validator(value)
      }
    }))
  },

  /**
   * Create a validation rule that depends on another field
   */
  dependsOn(fieldName: string, rules: ValidationRule[]): ValidationRule[] {
    return rules.map(rule => ({
      ...rule,
      validator: (value: any, formData?: any) => {
        if (!formData?.[fieldName]) return true
        return rule.validator(value)
      }
    }))
  },

  /**
   * Debounce a validation rule
   */
  debounce(rule: ValidationRule, delay = 300): ValidationRule {
    let timeout: NodeJS.Timeout
    return {
      ...rule,
      validator: (value: any) => {
        return new Promise((resolve) => {
          clearTimeout(timeout)
          timeout = setTimeout(async () => {
            const result = await rule.validator(value)
            resolve(result)
          }, delay)
        })
      }
    }
  }
}
