import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Environment, EnvironmentValidation } from '@/types/environment'
import { getEnvironmentService } from '@/services'
import type { EnvironmentValidationRequest } from '@/services'

export const useEnvironmentStore = defineStore('environment', () => {
    // Services
    const environmentService = getEnvironmentService()

    // State
    const environments = ref<Environment[]>([])
    const currentEnvironment = ref<Environment | null>(null)
    const loading = ref(false)
    const error = ref<string | null>(null)
    const validationCache = ref<Map<string, EnvironmentValidation>>(new Map())
    const lastFetch = ref<Date | null>(null)

    // Getters
    const isProduction = computed(() =>
        currentEnvironment.value?.name.toLowerCase() === 'production'
    )

    const requiresApproval = computed(() =>
        currentEnvironment.value?.requiresApproval || false
    )

    const maxOperationSize = computed(() =>
        currentEnvironment.value?.maxOperationSize || 50000
    )

    const isInMaintenanceWindow = computed(() => {
        if (!currentEnvironment.value?.maintenanceWindows?.length) {
            return false
        }

        const now = new Date()
        const currentTime = now.getHours() * 60 + now.getMinutes()

        return currentEnvironment.value.maintenanceWindows.some(window => {
            const [startHour, startMin] = window.start.split(':').map(Number)
            const [endHour, endMin] = window.end.split(':').map(Number)
            const startTime = startHour * 60 + startMin
            const endTime = endHour * 60 + endMin

            return currentTime >= startTime && currentTime <= endTime
        })
    })

    const isDataStale = computed(() => {
        if (!lastFetch.value) return true
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
        return lastFetch.value < fiveMinutesAgo
    })

    // Actions
    const fetchEnvironments = async (force = false) => {
        if (!force && !isDataStale.value && environments.value.length > 0) {
            return
        }

        loading.value = true
        error.value = null

        try {
            const response = await environmentService.getEnvironments()
            environments.value = response.environments
            lastFetch.value = new Date()

            // Auto-select current environment if not already selected
            if (!currentEnvironment.value && response.currentEnvironment) {
                const current = environments.value.find(env => env.name === response.currentEnvironment)
                if (current) {
                    setCurrentEnvironment(current)
                }
            }
        } catch (err: any) {
            error.value = err.message || 'Failed to fetch environments'
            console.error('Error fetching environments:', err)
        } finally {
            loading.value = false
        }
    }

    const fetchCurrentEnvironment = async () => {
        loading.value = true
        error.value = null

        try {
            const environment = await environmentService.getCurrentEnvironment()
            setCurrentEnvironment(environment)
        } catch (err: any) {
            error.value = err.message || 'Failed to fetch current environment'
            console.error('Error fetching current environment:', err)
        } finally {
            loading.value = false
        }
    }

    const validateOperation = async (request: EnvironmentValidationRequest): Promise<EnvironmentValidation> => {
        const cacheKey = `${request.operationSize}-${request.operationType || 'default'}`

        // Check cache first
        if (validationCache.value.has(cacheKey)) {
            return validationCache.value.get(cacheKey)!
        }

        try {
            const response = await environmentService.validateOperation(request)
            const validation: EnvironmentValidation = {
                isValid: response.isValid,
                errorMessage: response.errorMessage,
                requiresApproval: response.requiresApproval,
                warnings: response.warnings,
                maxOperationSize: response.maxOperationSize
            }

            // Cache the result for 5 minutes
            validationCache.value.set(cacheKey, validation)
            setTimeout(() => {
                validationCache.value.delete(cacheKey)
            }, 5 * 60 * 1000)

            return validation
        } catch (err: any) {
            console.error('Error validating operation:', err)
            throw err
        }
    }

    const setCurrentEnvironment = (environment: Environment) => {
        currentEnvironment.value = environment
        localStorage.setItem('selectedEnvironment', JSON.stringify(environment))

        // Clear validation cache when environment changes
        validationCache.value.clear()
    }

    const loadSavedEnvironment = () => {
        const saved = localStorage.getItem('selectedEnvironment')
        if (saved) {
            try {
                currentEnvironment.value = JSON.parse(saved)
            } catch (err) {
                console.error('Error loading saved environment:', err)
                localStorage.removeItem('selectedEnvironment')
            }
        }
    }

    const clearEnvironment = () => {
        currentEnvironment.value = null
        localStorage.removeItem('selectedEnvironment')
        validationCache.value.clear()
    }

    const getOperationLimits = async () => {
        try {
            return await environmentService.getOperationLimits()
        } catch (err: any) {
            console.error('Error getting operation limits:', err)
            return {
                maxDrivers: 1000,
                maxVehicles: 1000,
                maxBatchSize: 500
            }
        }
    }

    const checkMaintenanceWindow = async () => {
        try {
            return await environmentService.isInMaintenanceWindow()
        } catch (err: any) {
            console.error('Error checking maintenance window:', err)
            return false
        }
    }

    return {
        // State
        environments,
        currentEnvironment,
        loading,
        error,
        validationCache,
        lastFetch,

        // Getters
        isProduction,
        requiresApproval,
        maxOperationSize,
        isInMaintenanceWindow,
        isDataStale,

        // Actions
        fetchEnvironments,
        fetchCurrentEnvironment,
        validateOperation,
        setCurrentEnvironment,
        loadSavedEnvironment,
        clearEnvironment,
        getOperationLimits,
        checkMaintenanceWindow
    }
})
