namespace XQ360.DataMigration.Web.Services.DuplicateDetection
{
    /// <summary>
    /// Service for detecting and resolving duplicate records
    /// </summary>
    public interface IDuplicateDetectionService
    {
        /// <summary>
        /// Detects duplicates in a collection of entities
        /// </summary>
        Task<DuplicateDetectionResult> DetectDuplicatesAsync<T>(IEnumerable<T> entities, DuplicateDetectionConfig config, CancellationToken cancellationToken = default);

        /// <summary>
        /// Detects duplicates for Person records with fuzzy matching
        /// </summary>
        Task<DuplicateDetectionResult> DetectPersonDuplicatesAsync(IEnumerable<object> persons, CancellationToken cancellationToken = default);

        /// <summary>
        /// Detects duplicates for Vehicle records
        /// </summary>
        Task<DuplicateDetectionResult> DetectVehicleDuplicatesAsync(IEnumerable<object> vehicles, CancellationToken cancellationToken = default);

        /// <summary>
        /// Resolves duplicates using specified strategy
        /// </summary>
        Task<DuplicateResolutionResult> ResolveDuplicatesAsync<T>(DuplicateDetectionResult duplicates, DuplicateResolutionStrategy strategy, CancellationToken cancellationToken = default);

        /// <summary>
        /// Calculates similarity score between two entities
        /// </summary>
        Task<double> CalculateSimilarityScoreAsync<T>(T entity1, T entity2, SimilarityConfig config, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets duplicate detection configuration for entity type
        /// </summary>
        DuplicateDetectionConfig GetDefaultConfig<T>();

        /// <summary>
        /// Validates duplicate resolution before applying
        /// </summary>
        Task<ValidationResult> ValidateResolutionAsync<T>(DuplicateGroup<T> duplicateGroup, DuplicateResolutionStrategy strategy, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Duplicate detection configuration
    /// </summary>
    public class DuplicateDetectionConfig
    {
        public string[] ExactMatchFields { get; set; } = Array.Empty<string>();
        public string[] FuzzyMatchFields { get; set; } = Array.Empty<string>();
        public double SimilarityThreshold { get; set; } = 0.8;
        public bool EnableFuzzyMatching { get; set; } = true;
        public bool CaseSensitive { get; set; } = false;
        public FuzzyMatchingAlgorithm Algorithm { get; set; } = FuzzyMatchingAlgorithm.Levenshtein;
        public Dictionary<string, double> FieldWeights { get; set; } = new();
        public bool IgnoreWhitespace { get; set; } = true;
        public bool IgnorePunctuation { get; set; } = true;
    }

    /// <summary>
    /// Fuzzy matching algorithms
    /// </summary>
    public enum FuzzyMatchingAlgorithm
    {
        Levenshtein,
        JaroWinkler,
        Soundex,
        Metaphone,
        Cosine
    }

    /// <summary>
    /// Similarity calculation configuration
    /// </summary>
    public class SimilarityConfig
    {
        public Dictionary<string, double> FieldWeights { get; set; } = new();
        public FuzzyMatchingAlgorithm Algorithm { get; set; } = FuzzyMatchingAlgorithm.Levenshtein;
        public bool NormalizeValues { get; set; } = true;
        public double MinimumScore { get; set; } = 0.0;
        public double MaximumScore { get; set; } = 1.0;
    }

    /// <summary>
    /// Duplicate detection result
    /// </summary>
    public class DuplicateDetectionResult
    {
        public List<DuplicateGroup<object>> DuplicateGroups { get; set; } = new();
        public DuplicateStatistics Statistics { get; set; } = new();
        public DateTime DetectedAt { get; set; }
        public TimeSpan DetectionDuration { get; set; }
        public DuplicateDetectionConfig ConfigUsed { get; set; } = new();
    }

    /// <summary>
    /// Generic duplicate group
    /// </summary>
    public class DuplicateGroup<T>
    {
        public string GroupId { get; set; } = string.Empty;
        public List<DuplicateCandidate<T>> Candidates { get; set; } = new();
        public double AverageSimilarity { get; set; }
        public double MaxSimilarity { get; set; }
        public double MinSimilarity { get; set; }
        public DuplicateConfidenceLevel ConfidenceLevel { get; set; }
        public string ReasonCode { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Individual duplicate candidate
    /// </summary>
    public class DuplicateCandidate<T>
    {
        public T Entity { get; set; } = default!;
        public int OriginalIndex { get; set; }
        public string EntityIdentifier { get; set; } = string.Empty;
        public double SimilarityScore { get; set; }
        public Dictionary<string, double> FieldSimilarities { get; set; } = new();
        public bool IsPrimaryCandidate { get; set; }
        public string Source { get; set; } = string.Empty;
    }

    /// <summary>
    /// Duplicate confidence levels
    /// </summary>
    public enum DuplicateConfidenceLevel
    {
        Low,        // 60-70% similarity
        Medium,     // 70-85% similarity
        High,       // 85-95% similarity
        VeryHigh    // 95%+ similarity
    }

    /// <summary>
    /// Duplicate detection statistics
    /// </summary>
    public class DuplicateStatistics
    {
        public int TotalEntities { get; set; }
        public int UniqueEntities { get; set; }
        public int DuplicateEntities { get; set; }
        public int DuplicateGroups { get; set; }
        public double DuplicationRate { get; set; }
        public Dictionary<DuplicateConfidenceLevel, int> GroupsByConfidence { get; set; } = new();
        public Dictionary<string, int> DuplicatesByField { get; set; } = new();
        public double AverageSimilarityScore { get; set; }
    }

    /// <summary>
    /// Duplicate resolution strategies
    /// </summary>
    public enum DuplicateResolutionStrategy
    {
        KeepFirst,          // Keep the first occurrence
        KeepLast,           // Keep the last occurrence
        KeepMostComplete,   // Keep the record with most complete data
        KeepHighestQuality, // Keep the record with highest quality score
        Merge,              // Merge all duplicates into one record
        MarkForReview,      // Mark duplicates for manual review
        Skip,               // Skip all duplicates
        Custom              // Use custom resolution logic
    }

    /// <summary>
    /// Duplicate resolution result
    /// </summary>
    public class DuplicateResolutionResult
    {
        public List<ResolvedDuplicateGroup> ResolvedGroups { get; set; } = new();
        public ResolutionStatistics Statistics { get; set; } = new();
        public DateTime ResolvedAt { get; set; }
        public TimeSpan ResolutionDuration { get; set; }
        public DuplicateResolutionStrategy StrategyUsed { get; set; }
        public List<ResolutionError> Errors { get; set; } = new();
    }

    /// <summary>
    /// Resolved duplicate group
    /// </summary>
    public class ResolvedDuplicateGroup
    {
        public string GroupId { get; set; } = string.Empty;
        public object? ResolvedEntity { get; set; }
        public List<object> DiscardedEntities { get; set; } = new();
        public ResolutionAction Action { get; set; }
        public string Reason { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Resolution actions
    /// </summary>
    public enum ResolutionAction
    {
        Kept,
        Merged,
        Discarded,
        MarkedForReview,
        Skipped,
        Error
    }

    /// <summary>
    /// Resolution statistics
    /// </summary>
    public class ResolutionStatistics
    {
        public int TotalGroups { get; set; }
        public int ResolvedGroups { get; set; }
        public int ErrorGroups { get; set; }
        public Dictionary<ResolutionAction, int> ActionCounts { get; set; } = new();
        public int EntitiesKept { get; set; }
        public int EntitiesDiscarded { get; set; }
        public int EntitiesMerged { get; set; }
    }

    /// <summary>
    /// Resolution error
    /// </summary>
    public class ResolutionError
    {
        public string GroupId { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public object? FailedEntity { get; set; }
    }

    /// <summary>
    /// Validation result for duplicate resolution
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<ValidationIssue> Issues { get; set; } = new();
        public List<ValidationWarning> Warnings { get; set; } = new();
    }

    /// <summary>
    /// Validation issue
    /// </summary>
    public class ValidationIssue
    {
        public string IssueType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string Suggestion { get; set; } = string.Empty;
    }

    /// <summary>
    /// Validation warning
    /// </summary>
    public class ValidationWarning
    {
        public string WarningType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Recommendation { get; set; } = string.Empty;
    }

    /// <summary>
    /// Person-specific duplicate detection configuration
    /// </summary>
    public class PersonDuplicateConfig : DuplicateDetectionConfig
    {
        public bool MatchOnNameOnly { get; set; } = false;
        public bool MatchOnNameAndDepartment { get; set; } = true;
        public bool IgnoreMiddleNames { get; set; } = true;
        public bool MatchNicknames { get; set; } = false;
        public double NameSimilarityThreshold { get; set; } = 0.85;
        public Dictionary<string, string[]> KnownNicknames { get; set; } = new();
    }

    /// <summary>
    /// Vehicle-specific duplicate detection configuration
    /// </summary>
    public class VehicleDuplicateConfig : DuplicateDetectionConfig
    {
        public bool MatchOnSerialOnly { get; set; } = true;
        public bool MatchOnModelAndYear { get; set; } = false;
        public bool IgnoreCase { get; set; } = true;
        public bool NormalizeSerialNumbers { get; set; } = true;
        public string[] SerialNumberFormats { get; set; } = Array.Empty<string>();
    }
}
