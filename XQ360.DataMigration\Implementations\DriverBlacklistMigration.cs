using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Implementations
{
    public class DriverBlacklistMigration
    {
        private readonly ILogger<DriverBlacklistMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly string _connectionString;
        private readonly MigrationReportingService _reportingService;

        // Cache for database lookups to avoid repeated queries
        private readonly Dictionary<string, Guid> _siteCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _dealerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _customerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _departmentCache = new Dictionary<string, Guid>();

        public DriverBlacklistMigration(
            ILogger<DriverBlacklistMigration> logger,
            IOptions<MigrationConfiguration> config,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _connectionString = _config.DatabaseConnection;
            _reportingService = reportingService;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Driver Blacklist migration - removing vehicle access rights");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Validate and execute blacklist removal
                var migrationResult = await ExecuteBlacklistRemovalAsync(data);

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.AccessRecordsRemoved; // Use access records removed as "inserted" for standard logging
                result.RecordsSkipped = migrationResult.RecordsSkipped;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;
                
                // CRITICAL: Transfer detailed errors and warnings for Full Migration Report
                result.DetailedErrors = migrationResult.DetailedErrors;
                result.DetailedWarnings = migrationResult.DetailedWarnings;

                var recordsProcessedSuccessfully = data.Count - migrationResult.RecordsSkipped;
                _logger.LogInformation($"Driver Blacklist migration completed: {migrationResult.AccessRecordsRemoved} access records removed from {recordsProcessedSuccessfully} drivers, {migrationResult.RecordsSkipped} skipped, Duration: {result.Duration}");

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Driver Blacklist Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Driver Blacklist migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private Task<List<DriverBlacklistImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Driver Blacklist CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var reader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<DriverBlacklistImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return Task.FromResult(records);
        }

        private async Task<BlacklistMigrationResult> ExecuteBlacklistRemovalAsync(List<DriverBlacklistImportModel> data)
        {
            _logger.LogInformation("Executing Driver Blacklist removal operations...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                var result = new BlacklistMigrationResult
                {
                    Success = true,
                    Errors = new List<string>(),
                    Warnings = new List<string>()
                };

                int accessRecordsRemoved = 0;
                int skippedRecords = 0;

                foreach (var record in data)
                {
                    try
                    {
                        _logger.LogDebug($"Processing blacklist for {record.FirstName} {record.LastName} - Vehicle: {record.GMTPID}");

                        // Step 1: Find the driver using Person details and Weigand
                        var driverInfo = await FindDriverInfoAsync(record, connection, transaction);
                        if (driverInfo == null)
                        {
                            var error = $"Driver not found: {record.FirstName} {record.LastName} (Weigand: {record.Weigand}) in {record.PersonDepartment}, {record.PersonSite}";
                            result.Warnings.Add(error);
                            _reportingService.AddDetailedWarning(result, skippedRecords + 1, WarningTypes.MISSING_DEPENDENCY, error,
                                "Driver", $"{record.FirstName} {record.LastName} (Weigand: {record.Weigand})",
                                "Ensure driver exists with isDriver=true and has a card assigned",
                                new Dictionary<string, string> 
                                {
                                    { "Department", record.PersonDepartment ?? "Unknown" },
                                    { "Site", record.PersonSite ?? "Unknown" },
                                    { "Weigand", record.Weigand ?? "Unknown" }
                                });
                            skippedRecords++;
                            continue;
                        }

                        // Step 2: Find the vehicle using GMTP ID
                        var vehicleInfo = await FindVehicleInfoAsync(record.GMTPID ?? string.Empty, connection, transaction);
                        if (vehicleInfo == null)
                        {
                            var error = $"Vehicle not found with GMTP ID: {record.GMTPID}";
                            result.Warnings.Add(error);
                            _reportingService.AddDetailedWarning(result, skippedRecords + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT, error,
                                "Vehicle", record.GMTPID,
                                "Ensure vehicle exists in the database",
                                new Dictionary<string, string> 
                                {
                                    { "GMTP ID", record.GMTPID ?? "Unknown" },
                                    { "Hire No", record.HireNo ?? "Unknown" },
                                    { "Serial NO", record.SerialNO ?? "Unknown" }
                                });
                            skippedRecords++;
                            continue;
                        }

                        _logger.LogDebug($"Found driver {driverInfo.DriverId} and vehicle {vehicleInfo.VehicleId} for blacklist removal");

                        // Step 3: Remove PerVehicleNormalCardAccess record
                        var perVehicleRemoved = await RemovePerVehicleAccessAsync(driverInfo.CardId, vehicleInfo.VehicleId, connection, transaction);
                        accessRecordsRemoved += perVehicleRemoved;

                        // Step 4: Check if driver has other vehicles of the same model, if not remove ModelVehicleNormalCardAccess
                        var modelAccessRemoved = await CheckAndRemoveModelAccessAsync(driverInfo.CardId, vehicleInfo.ModelId, vehicleInfo.DepartmentId, connection, transaction);
                        accessRecordsRemoved += modelAccessRemoved;

                        _logger.LogDebug($"Removed {perVehicleRemoved} vehicle access and {modelAccessRemoved} model access records for {record.FirstName} {record.LastName}");
                    }
                    catch (Exception ex)
                    {
                        var error = $"Failed to process blacklist for {record.FirstName} {record.LastName}: {ex.Message}";
                        result.Warnings.Add(error);
                        _logger.LogWarning($"Error processing record: {error}");
                        skippedRecords++;
                    }
                }

                await transaction.CommitAsync();

                result.AccessRecordsRemoved = accessRecordsRemoved;
                result.RecordsSkipped = skippedRecords;

                _logger.LogInformation($"Successfully processed blacklist: {accessRecordsRemoved} access records removed, {skippedRecords} records skipped");

                return result;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Driver Blacklist removal failed");
                return new BlacklistMigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<DriverInfo?> FindDriverInfoAsync(DriverBlacklistImportModel record, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                SELECT 
                    p.DriverId,
                    d.CardDetailsId,
                    p.Id as PersonId
                FROM [dbo].[Person] p
                INNER JOIN [dbo].[Driver] d ON p.DriverId = d.Id
                INNER JOIN [dbo].[Department] dept ON p.DepartmentId = dept.Id
                INNER JOIN [dbo].[Site] s ON dept.SiteId = s.Id
                INNER JOIN [dbo].[Customer] c ON s.CustomerId = c.Id
                INNER JOIN [dbo].[Dealer] dealer ON c.DealerId = dealer.Id
                WHERE p.FirstName = @FirstName 
                AND p.LastName = @LastName
                AND p.IsDriver = 1
                AND d.CardDetailsId IS NOT NULL
                AND dept.Name = @DepartmentName
                AND s.Name = @SiteName
                AND c.CompanyName = @CustomerName
                AND dealer.Name = @DealerName
                AND EXISTS (
                    SELECT 1 FROM [dbo].[Card] c 
                    WHERE c.Id = d.CardDetailsId 
                    AND c.Weigand = @Weigand
                )";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@FirstName", record.FirstName);
            cmd.Parameters.AddWithValue("@LastName", record.LastName);
            cmd.Parameters.AddWithValue("@DepartmentName", record.PersonDepartment);
            cmd.Parameters.AddWithValue("@SiteName", record.PersonSite);
            cmd.Parameters.AddWithValue("@CustomerName", record.PersonCustomer);
            cmd.Parameters.AddWithValue("@DealerName", record.PersonDealer);
            cmd.Parameters.AddWithValue("@Weigand", record.Weigand);

            using var reader = await cmd.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new DriverInfo
                {
                    DriverId = reader.GetGuid(0),     // DriverId - first column
                    CardId = reader.GetGuid(1),       // CardDetailsId - second column  
                    PersonId = reader.GetGuid(2)      // PersonId - third column
                };
            }

            return null;
        }

        private async Task<VehicleInfo?> FindVehicleInfoAsync(string gmtpId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                SELECT 
                    v.Id as VehicleId,
                    v.ModelId,
                    v.DepartmentId
                FROM [dbo].[Vehicle] v
                INNER JOIN [dbo].[Module] m ON v.ModuleId1 = m.Id
                WHERE m.IoTDevice = @GMTPID";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@GMTPID", gmtpId);

            using var reader = await cmd.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new VehicleInfo
                {
                    VehicleId = reader.GetGuid(0),      // VehicleId - first column
                    ModelId = reader.GetGuid(1),        // ModelId - second column
                    DepartmentId = reader.GetGuid(2)    // DepartmentId - third column
                };
            }

            return null;
        }

        private async Task<int> RemovePerVehicleAccessAsync(Guid cardId, Guid vehicleId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                DELETE FROM [dbo].[PerVehicleNormalCardAccess]
                WHERE CardId = @CardId
                AND VehicleId = @VehicleId";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CardId", cardId);
            cmd.Parameters.AddWithValue("@VehicleId", vehicleId);

            return await cmd.ExecuteNonQueryAsync();
        }

        private async Task<int> CheckAndRemoveModelAccessAsync(Guid cardId, Guid modelId, Guid departmentId, SqlConnection connection, SqlTransaction transaction)
        {
            // Check if driver has any other vehicles of the same model
            var checkSql = @"
                SELECT COUNT(*)
                FROM [dbo].[PerVehicleNormalCardAccess] pvnca
                INNER JOIN [dbo].[Vehicle] v ON pvnca.VehicleId = v.Id
                WHERE pvnca.CardId = @CardId
                AND v.ModelId = @ModelId";

            using var checkCmd = new SqlCommand(checkSql, connection, transaction);
            checkCmd.Parameters.AddWithValue("@CardId", cardId);
            checkCmd.Parameters.AddWithValue("@ModelId", modelId);

            var otherVehicleCount = (int)(await checkCmd.ExecuteScalarAsync() ?? 0);

            // If no other vehicles of the same model, remove ModelVehicleNormalCardAccess
            if (otherVehicleCount == 0)
            {
                var deleteSql = @"
                    DELETE FROM [dbo].[ModelVehicleNormalCardAccess]
                    WHERE CardId = @CardId
                    AND ModelId = @ModelId
                    AND DepartmentId = @DepartmentId";

                using var deleteCmd = new SqlCommand(deleteSql, connection, transaction);
                deleteCmd.Parameters.AddWithValue("@CardId", cardId);
                deleteCmd.Parameters.AddWithValue("@ModelId", modelId);
                deleteCmd.Parameters.AddWithValue("@DepartmentId", departmentId);

                return await deleteCmd.ExecuteNonQueryAsync();
            }

            return 0; // No model access records removed
        }
    }

    // Helper classes for the blacklist migration
    public class DriverInfo
    {
        public Guid DriverId { get; set; }
        public Guid CardId { get; set; }
        public Guid PersonId { get; set; }
    }

    public class VehicleInfo
    {
        public Guid VehicleId { get; set; }
        public Guid ModelId { get; set; }
        public Guid DepartmentId { get; set; }
    }

    public class BlacklistMigrationResult : MigrationResult
    {
        public int AccessRecordsRemoved { get; set; }
    }
} 