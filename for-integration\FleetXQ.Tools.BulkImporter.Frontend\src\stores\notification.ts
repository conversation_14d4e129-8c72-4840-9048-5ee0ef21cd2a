import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
    Notification,
    CreateNotificationRequest,
    NotificationFilter,
    NotificationSearchResult,
    NotificationType
} from '@/types/notification'

export const useNotificationStore = defineStore('notification', () => {
    // State
    const notifications = ref<Notification[]>([])
    const searchResults = ref<NotificationSearchResult | null>(null)
    const loading = ref(false)
    const error = ref<string | null>(null)

    // Getters
    const unreadNotifications = computed(() =>
        notifications.value.filter(notification => !notification.isRead)
    )

    const unreadCount = computed(() =>
        unreadNotifications.value.length
    )

    const persistentNotifications = computed(() =>
        notifications.value.filter(notification => notification.isPersistent && !notification.isRead)
    )

    const recentNotifications = computed(() =>
        [...notifications.value]
            .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
            .slice(0, 10)
    )

    const notificationsByType = computed(() => (type: NotificationType) =>
        notifications.value.filter(notification => notification.type === type)
    )

    const hasUnreadNotifications = computed(() =>
        unreadCount.value > 0
    )

    const notificationById = computed(() => (id: string) =>
        notifications.value.find(notification => notification.id === id)
    )

    // Actions
    const fetchNotifications = async (filter?: NotificationFilter) => {
        loading.value = true
        error.value = null

        try {
            const queryParams = new URLSearchParams()
            if (filter?.type?.length) queryParams.append('type', filter.type.join(','))
            if (filter?.isRead !== undefined) queryParams.append('isRead', filter.isRead.toString())
            if (filter?.fromDate) queryParams.append('fromDate', filter.fromDate.toISOString())
            if (filter?.toDate) queryParams.append('toDate', filter.toDate.toISOString())
            if (filter?.limit) queryParams.append('limit', filter.limit.toString())
            if (filter?.offset) queryParams.append('offset', filter.offset.toString())

            const response = await fetch(`/api/notifications?${queryParams}`)
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const result: NotificationSearchResult = await response.json()
            searchResults.value = result

            // Update notifications array with unique entries
            const existingIds = new Set(notifications.value.map(n => n.id))
            const newNotifications = result.notifications.filter(n => !existingIds.has(n.id))
            notifications.value = [...notifications.value, ...newNotifications]

        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Failed to fetch notifications'
            console.error('Error fetching notifications:', err)
        } finally {
            loading.value = false
        }
    }

    const createNotification = (request: CreateNotificationRequest): Notification => {
        const notification: Notification = {
            id: generateId(),
            type: request.type,
            title: request.title,
            message: request.message,
            timestamp: new Date(),
            isRead: false,
            isPersistent: request.isPersistent || false,
            actionUrl: request.actionUrl,
            actionText: request.actionText,
            autoHideDelay: request.autoHideDelay || request.duration,
            data: request.data
        }

        notifications.value.unshift(notification)

        // Auto-hide notification if specified
        if (notification.autoHideDelay && !notification.isPersistent) {
            setTimeout(() => {
                removeNotification(notification.id)
            }, notification.autoHideDelay)
        }

        return notification
    }

    const markAsRead = async (notificationId: string) => {
        const notification = notificationById.value(notificationId)
        if (!notification || notification.isRead) {
            return
        }

        try {
            const response = await fetch(`/api/notifications/${notificationId}/read`, {
                method: 'PATCH'
            })

            if (response.ok) {
                const index = notifications.value.findIndex(n => n.id === notificationId)
                if (index >= 0) {
                    notifications.value[index].isRead = true
                }
            }
        } catch (err) {
            console.error('Error marking notification as read:', err)
            // Mark as read locally even if API call fails
            const index = notifications.value.findIndex(n => n.id === notificationId)
            if (index >= 0) {
                notifications.value[index].isRead = true
            }
        }
    }

    const markAllAsRead = async () => {
        try {
            const response = await fetch('/api/notifications/read-all', {
                method: 'PATCH'
            })

            if (response.ok) {
                notifications.value.forEach(notification => {
                    notification.isRead = true
                })
            }
        } catch (err) {
            console.error('Error marking all notifications as read:', err)
            // Mark as read locally even if API call fails
            notifications.value.forEach(notification => {
                notification.isRead = true
            })
        }
    }

    const removeNotification = (notificationId: string) => {
        const index = notifications.value.findIndex(n => n.id === notificationId)
        if (index >= 0) {
            notifications.value.splice(index, 1)
        }
    }

    const clearNotifications = () => {
        notifications.value = []
        searchResults.value = null
        error.value = null
        saveNotificationsToStorage()
    }

    const clearReadNotifications = () => {
        notifications.value = notifications.value.filter(n => !n.isRead)
        saveNotificationsToStorage()
    }

    const saveNotificationsToStorage = () => {
        try {
            // Only save persistent notifications to localStorage
            const persistentNotifs = notifications.value.filter(n => n.isPersistent && !n.isRead)
            localStorage.setItem('notifications', JSON.stringify(persistentNotifs))
        } catch (err) {
            console.error('Error saving notifications to storage:', err)
        }
    }

    const loadNotificationsFromStorage = () => {
        try {
            const stored = localStorage.getItem('notifications')
            if (stored) {
                const persistentNotifs = JSON.parse(stored)
                notifications.value = [...persistentNotifs, ...notifications.value]
            }
        } catch (err) {
            console.error('Error loading notifications from storage:', err)
        }
    }

    // Convenience methods for creating specific notification types
    const addNotification = (request: CreateNotificationRequest) => {
        return createNotification(request)
    }

    const showSuccess = (title: string, message: string, options?: Partial<CreateNotificationRequest>) => {
        return createNotification({
            type: 'success',
            title,
            message,
            autoHideDelay: 5000,
            ...options
        })
    }

    const showError = (title: string, message: string, options?: Partial<CreateNotificationRequest>) => {
        return createNotification({
            type: 'error',
            title,
            message,
            isPersistent: true,
            ...options
        })
    }

    const showWarning = (title: string, message: string, options?: Partial<CreateNotificationRequest>) => {
        return createNotification({
            type: 'warning',
            title,
            message,
            autoHideDelay: 8000,
            ...options
        })
    }

    const showInfo = (title: string, message: string, options?: Partial<CreateNotificationRequest>) => {
        return createNotification({
            type: 'info',
            title,
            message,
            autoHideDelay: 6000,
            ...options
        })
    }

    // Helper function to generate unique IDs
    const generateId = (): string => {
        return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    return {
        // State
        notifications,
        searchResults,
        loading,
        error,

        // Getters
        unreadNotifications,
        unreadCount,
        persistentNotifications,
        recentNotifications,
        notificationsByType,
        hasUnreadNotifications,
        notificationById,

        // Actions
        fetchNotifications,
        createNotification,
        markAsRead,
        markAllAsRead,
        removeNotification,
        clearNotifications,
        clearReadNotifications,
        saveNotificationsToStorage,
        loadNotificationsFromStorage,

        // Convenience methods
        addNotification,
        showSuccess,
        showError,
        showWarning,
        showInfo
    }
})
