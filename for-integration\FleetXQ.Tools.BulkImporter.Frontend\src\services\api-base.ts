import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'

export interface ApiError {
    message: string
    details?: string
    status?: number
    code?: string
    validationErrors?: Record<string, string[]>
}

export interface ApiResponse<T = any> {
    data: T
    success: boolean
    message?: string
    errors?: string[]
    warnings?: string[]
}

export interface RetryConfig {
    maxRetries: number
    retryDelay: number
    retryCondition?: (error: AxiosError) => boolean
}

export class ApiBaseService {
    protected client: AxiosInstance
    private retryConfig: RetryConfig

    constructor(baseURL: string = '/api', retryConfig?: Partial<RetryConfig>) {
        this.retryConfig = {
            maxRetries: 3,
            retryDelay: 1000,
            retryCondition: this.defaultRetryCondition,
            ...retryConfig
        }

        this.client = axios.create({
            baseURL,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })

        this.setupInterceptors()
    }

    private setupInterceptors(): void {
        // Request interceptor for authentication and logging
        this.client.interceptors.request.use(
            (config) => {
                // Add authentication token if available
                const token = this.getAuthToken()
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`
                }

                // Add correlation ID for request tracking
                config.headers['X-Correlation-ID'] = this.generateCorrelationId()

                // Log request in development
                if (import.meta.env.DEV) {
                    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
                        params: config.params,
                        data: config.data
                    })
                }

                return config
            },
            (error) => {
                console.error('[API Request Error]', error)
                return Promise.reject(error)
            }
        )

        // Response interceptor for error handling and logging
        this.client.interceptors.response.use(
            (response) => {
                // Log response in development
                if (import.meta.env.DEV) {
                    console.log(`[API Response] ${response.status} ${response.config.url}`, response.data)
                }

                return response
            },
            async (error: AxiosError) => {
                // Log error
                console.error('[API Response Error]', {
                    url: error.config?.url,
                    method: error.config?.method,
                    status: error.response?.status,
                    data: error.response?.data
                })

                // Handle retry logic
                if (this.shouldRetry(error)) {
                    return this.retryRequest(error)
                }

                // Transform error to our standard format
                const apiError = this.transformError(error)
                return Promise.reject(apiError)
            }
        )
    }

    private getAuthToken(): string | null {
        // Get token from localStorage, sessionStorage, or store
        return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
    }

    private generateCorrelationId(): string {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }

    private defaultRetryCondition(error: AxiosError): boolean {
        // Retry on network errors or 5xx server errors
        return !error.response || (error.response.status >= 500 && error.response.status < 600)
    }

    private shouldRetry(error: AxiosError): boolean {
        const config = error.config as any
        if (!config || config.__retryCount >= this.retryConfig.maxRetries) {
            return false
        }

        return this.retryConfig.retryCondition ? this.retryConfig.retryCondition(error) : false
    }

    private async retryRequest(error: AxiosError): Promise<AxiosResponse> {
        const config = error.config as any
        config.__retryCount = config.__retryCount || 0
        config.__retryCount += 1

        // Calculate delay with exponential backoff
        const delay = this.retryConfig.retryDelay * Math.pow(2, config.__retryCount - 1)

        console.log(`[API Retry] Attempt ${config.__retryCount}/${this.retryConfig.maxRetries} after ${delay}ms`)

        await new Promise(resolve => setTimeout(resolve, delay))
        return this.client.request(config)
    }

    private transformError(error: AxiosError): ApiError {
        const apiError: ApiError = {
            message: 'An unexpected error occurred',
            status: error.response?.status
        }

        if (error.response?.data) {
            const responseData = error.response.data as any

            // Handle ASP.NET Core ProblemDetails format
            if (responseData.title || responseData.detail) {
                apiError.message = responseData.title || responseData.detail || apiError.message
                apiError.details = responseData.detail
                apiError.code = responseData.type
            }
            // Handle custom API response format
            else if (responseData.message) {
                apiError.message = responseData.message
                apiError.details = responseData.details
                apiError.validationErrors = responseData.validationErrors
            }
            // Handle validation errors
            else if (responseData.errors) {
                apiError.message = 'Validation failed'
                apiError.validationErrors = responseData.errors
            }
        } else if (error.message) {
            apiError.message = error.message
        }

        return apiError
    }

    // Protected helper methods for derived services
    protected async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
        const response = await this.client.get<T>(url, config)
        return response.data
    }

    protected async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
        const response = await this.client.post<T>(url, data, config)
        return response.data
    }

    protected async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
        const response = await this.client.put<T>(url, data, config)
        return response.data
    }

    protected async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
        const response = await this.client.patch<T>(url, data, config)
        return response.data
    }

    protected async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
        const response = await this.client.delete<T>(url, config)
        return response.data
    }

    // Public utility methods
    public setAuthToken(token: string): void {
        localStorage.setItem('auth_token', token)
    }

    public clearAuthToken(): void {
        localStorage.removeItem('auth_token')
        sessionStorage.removeItem('auth_token')
    }

    public updateBaseURL(baseURL: string): void {
        this.client.defaults.baseURL = baseURL
    }
}
