using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading.Tasks.Dataflow;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Parallel processing service for Phase 3.1.1: Parallel Processing Architecture
/// Implements TPL-based parallel processing with degree of parallelism = CPU cores - 1
/// </summary>
public class ParallelProcessingService : IParallelProcessingService
{
    private readonly ILogger<ParallelProcessingService> _logger;
    private readonly BulkSeederConfiguration _config;
    private readonly int _degreeOfParallelism;

    public ParallelProcessingService(
        ILogger<ParallelProcessingService> logger,
        IOptions<BulkSeederConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        
        // Set degree of parallelism to CPU cores - 1 (as per Phase 3 specification)
        _degreeOfParallelism = Math.Max(1, Environment.ProcessorCount - 1);
        
        _logger.LogInformation("Parallel processing service initialized with degree of parallelism: {DegreeOfParallelism}", 
            _degreeOfParallelism);
    }

    public async Task<ParallelProcessingResult<TResult>> ProcessBatchInParallelAsync<TInput, TResult>(
        IEnumerable<TInput> inputItems,
        Func<TInput, CancellationToken, Task<TResult>> processor,
        ParallelProcessingOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var items = inputItems.ToList();
        var result = new ParallelProcessingResult<TResult>
        {
            TotalItems = items.Count,
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting parallel processing of {TotalItems} items with {DegreeOfParallelism} threads", 
            items.Count, _degreeOfParallelism);

        try
        {
            var parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = _degreeOfParallelism,
                CancellationToken = cancellationToken
            };

            var exceptions = new ConcurrentBag<Exception>();
            var results = new ConcurrentBag<TResult>();
            var progressTracker = new ParallelProgressTracker(items.Count, options.ProgressCallback);

            // Process items in parallel with memory management
            await Parallel.ForEachAsync(items, parallelOptions, async (item, ct) =>
            {
                try
                {
                    var itemResult = await processor(item, ct).ConfigureAwait(false);
                    results.Add(itemResult);
                    
                    await progressTracker.IncrementSuccessfulAsync().ConfigureAwait(false);
                    
                    // Trigger GC collection if memory threshold exceeded
                    if (options.EnableMemoryManagement && 
                        progressTracker.ProcessedCount % options.GcCollectionInterval == 0)
                    {
                        await TriggerMemoryOptimizationAsync().ConfigureAwait(false);
                    }
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                    await progressTracker.IncrementFailedAsync().ConfigureAwait(false);
                    
                    _logger.LogError(ex, "Error processing item in parallel batch");
                }
            });

            result.ProcessedItems = progressTracker.ProcessedCount;
            result.SuccessfulItems = progressTracker.SuccessfulCount;
            result.FailedItems = progressTracker.FailedCount;
            result.Results = results.ToList();
            result.Exceptions = exceptions.ToList();
            result.Duration = stopwatch.Elapsed;
            result.Success = exceptions.IsEmpty;

            _logger.LogInformation("Parallel processing completed: {Successful}/{Total} successful in {Duration}ms, Peak memory: {PeakMemoryMB}MB",
                result.SuccessfulItems, result.TotalItems, result.Duration.TotalMilliseconds, result.PeakMemoryUsageMB);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Parallel processing failed");
            result.Duration = stopwatch.Elapsed;
            result.Success = false;
            result.Exceptions = new List<Exception> { ex };
            return result;
        }
    }

    public async Task<ParallelProcessingResult<TResult>> ProcessBatchWithDataflowAsync<TInput, TResult>(
        IAsyncEnumerable<TInput> inputStream,
        Func<TInput, CancellationToken, Task<TResult>> processor,
        ParallelProcessingOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ParallelProcessingResult<TResult>
        {
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting dataflow parallel processing with {DegreeOfParallelism} threads", 
            _degreeOfParallelism);

        try
        {
            var results = new ConcurrentBag<TResult>();
            var exceptions = new ConcurrentBag<Exception>();
            var progressTracker = new ParallelProgressTracker(0, options.ProgressCallback);

            // Create dataflow pipeline for memory-efficient processing
            var transformBlock = new TransformBlock<TInput, TResult>(
                async input =>
                {
                    try
                    {
                        var itemResult = await processor(input, cancellationToken).ConfigureAwait(false);
                        await progressTracker.IncrementSuccessfulAsync().ConfigureAwait(false);
                        return itemResult;
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                        await progressTracker.IncrementFailedAsync().ConfigureAwait(false);
                        _logger.LogError(ex, "Error processing item in dataflow pipeline");
                        throw;
                    }
                },
                new ExecutionDataflowBlockOptions
                {
                    MaxDegreeOfParallelism = _degreeOfParallelism,
                    BoundedCapacity = options.MaxBoundedCapacity,
                    CancellationToken = cancellationToken
                });

            var actionBlock = new ActionBlock<TResult>(
                itemResult =>
                {
                    results.Add(itemResult);
                    return Task.CompletedTask;
                },
                new ExecutionDataflowBlockOptions
                {
                    BoundedCapacity = options.MaxBoundedCapacity,
                    CancellationToken = cancellationToken
                });

            transformBlock.LinkTo(actionBlock, new DataflowLinkOptions { PropagateCompletion = true });

            // Feed input stream to pipeline
            await foreach (var item in inputStream.WithCancellation(cancellationToken))
            {
                await transformBlock.SendAsync(item, cancellationToken).ConfigureAwait(false);
                result.TotalItems++;
                
                // Memory management for streaming
                if (options.EnableMemoryManagement && 
                    result.TotalItems % options.GcCollectionInterval == 0)
                {
                    await TriggerMemoryOptimizationAsync().ConfigureAwait(false);
                }
            }

            transformBlock.Complete();
            await actionBlock.Completion.ConfigureAwait(false);

            result.ProcessedItems = progressTracker.ProcessedCount;
            result.SuccessfulItems = progressTracker.SuccessfulCount;
            result.FailedItems = progressTracker.FailedCount;
            result.Results = results.ToList();
            result.Exceptions = exceptions.ToList();
            result.Duration = stopwatch.Elapsed;
            result.Success = exceptions.IsEmpty;

            _logger.LogInformation("Dataflow parallel processing completed: {Successful}/{Total} successful in {Duration}ms",
                result.SuccessfulItems, result.TotalItems, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dataflow parallel processing failed");
            result.Duration = stopwatch.Elapsed;
            result.Success = false;
            result.Exceptions = new List<Exception> { ex };
            return result;
        }
    }

    public async Task<ParallelProcessingResult<TResult>> ProcessBatchWithPartitioningAsync<TInput, TResult>(
        IEnumerable<TInput> inputItems,
        Func<IEnumerable<TInput>, CancellationToken, Task<IEnumerable<TResult>>> batchProcessor,
        int partitionSize,
        ParallelProcessingOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var items = inputItems.ToList();
        var result = new ParallelProcessingResult<TResult>
        {
            TotalItems = items.Count,
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting partitioned parallel processing of {TotalItems} items with partition size {PartitionSize}", 
            items.Count, partitionSize);

        try
        {
            // Create partitions
            var partitions = items
                .Select((item, index) => new { Item = item, Index = index })
                .GroupBy(x => x.Index / partitionSize)
                .Select(g => g.Select(x => x.Item).ToList())
                .ToList();

            var parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = _degreeOfParallelism,
                CancellationToken = cancellationToken
            };

            var allResults = new ConcurrentBag<TResult>();
            var exceptions = new ConcurrentBag<Exception>();
            var progressTracker = new ParallelProgressTracker(partitions.Count, options.ProgressCallback);

            // Process partitions in parallel
            await Parallel.ForEachAsync(partitions, parallelOptions, async (partition, ct) =>
            {
                try
                {
                    var partitionResults = await batchProcessor(partition, ct).ConfigureAwait(false);
                    
                    foreach (var partitionResult in partitionResults)
                    {
                        allResults.Add(partitionResult);
                    }
                    
                    await progressTracker.IncrementSuccessfulAsync().ConfigureAwait(false);
                    
                    // Memory optimization for large partitions
                    if (options.EnableMemoryManagement)
                    {
                        await TriggerMemoryOptimizationAsync().ConfigureAwait(false);
                    }
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                    await progressTracker.IncrementFailedAsync().ConfigureAwait(false);
                    
                    _logger.LogError(ex, "Error processing partition in parallel batch");
                }
            });

            result.ProcessedItems = progressTracker.ProcessedCount * partitionSize;
            result.SuccessfulItems = progressTracker.SuccessfulCount * partitionSize;
            result.FailedItems = progressTracker.FailedCount * partitionSize;
            result.Results = allResults.ToList();
            result.Exceptions = exceptions.ToList();
            result.Duration = stopwatch.Elapsed;
            result.Success = exceptions.IsEmpty;

            _logger.LogInformation("Partitioned parallel processing completed: {Successful}/{Total} partitions successful in {Duration}ms",
                progressTracker.SuccessfulCount, partitions.Count, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Partitioned parallel processing failed");
            result.Duration = stopwatch.Elapsed;
            result.Success = false;
            result.Exceptions = new List<Exception> { ex };
            return result;
        }
    }

    public ParallelProcessingMetrics GetPerformanceMetrics()
    {
        var process = Process.GetCurrentProcess();
        
        return new ParallelProcessingMetrics
        {
            AvailableProcessors = Environment.ProcessorCount,
            ConfiguredDegreeOfParallelism = _degreeOfParallelism,
            CurrentMemoryUsageMB = GC.GetTotalMemory(false) / (1024 * 1024),
            WorkingSetMB = process.WorkingSet64 / (1024 * 1024),
            ThreadPoolThreads = ThreadPool.ThreadCount,
            AvailableThreadPoolThreads = GetAvailableThreadPoolThreads()
        };
    }

    public async Task OptimizeThreadPoolAsync()
    {
        try
        {
            // Optimize thread pool for high-throughput scenarios
            var minWorkerThreads = Math.Max(Environment.ProcessorCount, _degreeOfParallelism * 2);
            var maxWorkerThreads = Environment.ProcessorCount * 4;
            var minCompletionPortThreads = Environment.ProcessorCount;
            var maxCompletionPortThreads = Environment.ProcessorCount * 2;

            ThreadPool.SetMinThreads(minWorkerThreads, minCompletionPortThreads);
            ThreadPool.SetMaxThreads(maxWorkerThreads, maxCompletionPortThreads);

            _logger.LogInformation("Thread pool optimized: Min worker threads: {MinWorker}, Max worker threads: {MaxWorker}, Min completion port threads: {MinCompletion}, Max completion port threads: {MaxCompletion}",
                minWorkerThreads, maxWorkerThreads, minCompletionPortThreads, maxCompletionPortThreads);

            await Task.CompletedTask.ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to optimize thread pool");
        }
    }

    private async Task TriggerMemoryOptimizationAsync()
    {
        try
        {
            // Force garbage collection to manage memory
            GC.Collect(2, GCCollectionMode.Optimized);
            GC.WaitForPendingFinalizers();
            GC.Collect(2, GCCollectionMode.Optimized);

            await Task.Delay(1).ConfigureAwait(false); // Yield control
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Memory optimization failed");
        }
    }

    private int GetAvailableThreadPoolThreads()
    {
        ThreadPool.GetAvailableThreads(out var workerThreads, out var completionPortThreads);
        return workerThreads;
    }
}

/// <summary>
/// Progress tracker for parallel operations with thread-safe counters
/// </summary>
internal class ParallelProgressTracker
{
    private int _successfulCount;
    private int _failedCount;
    private readonly int _totalCount;
    private readonly Func<int, int, int, Task>? _progressCallback;

    public ParallelProgressTracker(int totalCount, Func<int, int, int, Task>? progressCallback)
    {
        _totalCount = totalCount;
        _progressCallback = progressCallback;
    }

    public int SuccessfulCount => _successfulCount;
    public int FailedCount => _failedCount;
    public int ProcessedCount => _successfulCount + _failedCount;

    public async Task IncrementSuccessfulAsync()
    {
        var successful = Interlocked.Increment(ref _successfulCount);
        await NotifyProgressAsync(successful, _failedCount).ConfigureAwait(false);
    }

    public async Task IncrementFailedAsync()
    {
        var failed = Interlocked.Increment(ref _failedCount);
        await NotifyProgressAsync(_successfulCount, failed).ConfigureAwait(false);
    }

    private async Task NotifyProgressAsync(int successful, int failed)
    {
        if (_progressCallback != null)
        {
            try
            {
                await _progressCallback(successful, failed, _totalCount).ConfigureAwait(false);
            }
            catch (Exception)
            {
                // Ignore progress callback errors to avoid breaking main processing
            }
        }
    }
}
