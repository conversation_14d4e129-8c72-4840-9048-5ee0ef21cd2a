using Microsoft.AspNetCore.SignalR;

namespace FleetXQ.Tools.BulkImporter.WebApi.Hubs;

/// <summary>
/// SignalR hub for real-time import progress updates
/// </summary>
public class ImportProgressHub : Hub
{
    private readonly ILogger<ImportProgressHub> _logger;

    public ImportProgressHub(ILogger<ImportProgressHub> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Called when a client connects to the hub
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Client {ConnectionId} connected to ImportProgressHub", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Called when a client disconnects from the hub
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Client {ConnectionId} disconnected from ImportProgressHub", Context.ConnectionId);
        
        if (exception != null)
        {
            _logger.LogWarning(exception, "Client {ConnectionId} disconnected with exception", Context.ConnectionId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Join a specific import session group for targeted updates
    /// </summary>
    /// <param name="sessionId">Import session identifier</param>
    public async Task JoinSessionGroup(string sessionId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"session_{sessionId}");
        _logger.LogInformation("Client {ConnectionId} joined session group {SessionId}", Context.ConnectionId, sessionId);
    }

    /// <summary>
    /// Leave a specific import session group
    /// </summary>
    /// <param name="sessionId">Import session identifier</param>
    public async Task LeaveSessionGroup(string sessionId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"session_{sessionId}");
        _logger.LogInformation("Client {ConnectionId} left session group {SessionId}", Context.ConnectionId, sessionId);
    }
}

/// <summary>
/// Interface for sending progress updates to clients
/// </summary>
public interface IImportProgressClient
{
    /// <summary>
    /// Send progress update to client
    /// </summary>
    Task ProgressUpdate(ImportProgressUpdate update);

    /// <summary>
    /// Send status change notification to client
    /// </summary>
    Task StatusChanged(ImportStatusUpdate update);

    /// <summary>
    /// Send error notification to client
    /// </summary>
    Task ErrorOccurred(ImportErrorUpdate update);

    /// <summary>
    /// Send completion notification to client
    /// </summary>
    Task ImportCompleted(ImportCompletionUpdate update);
}

/// <summary>
/// Progress update model
/// </summary>
public class ImportProgressUpdate
{
    public string SessionId { get; set; } = string.Empty;
    public int TotalRows { get; set; }
    public int ProcessedRows { get; set; }
    public int SuccessfulRows { get; set; }
    public int FailedRows { get; set; }
    public double PercentageComplete { get; set; }
    public string CurrentOperation { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public TimeSpan? EstimatedTimeRemaining { get; set; }
}

/// <summary>
/// Status update model
/// </summary>
public class ImportStatusUpdate
{
    public string SessionId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Error update model
/// </summary>
public class ImportErrorUpdate
{
    public string SessionId { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string ErrorType { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object>? Details { get; set; }
}

/// <summary>
/// Completion update model
/// </summary>
public class ImportCompletionUpdate
{
    public string SessionId { get; set; } = string.Empty;
    public bool Success { get; set; }
    public int TotalRows { get; set; }
    public int SuccessfulRows { get; set; }
    public int FailedRows { get; set; }
    public TimeSpan Duration { get; set; }
    public string Summary { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
