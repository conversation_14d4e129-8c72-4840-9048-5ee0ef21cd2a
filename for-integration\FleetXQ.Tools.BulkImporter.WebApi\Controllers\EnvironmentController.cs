using Microsoft.AspNetCore.Mvc;
using FleetXQ.Tools.BulkImporter.Core.Services;
using FleetXQ.Tools.BulkImporter.Core.Configuration;

namespace FleetXQ.Tools.BulkImporter.WebApi.Controllers;

/// <summary>
/// Controller for managing environment-specific operations and validations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class EnvironmentController : ControllerBase
{
    private readonly IEnvironmentService _environmentService;
    private readonly ILogger<EnvironmentController> _logger;

    public EnvironmentController(
        IEnvironmentService environmentService,
        ILogger<EnvironmentController> logger)
    {
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Gets the list of available environments
    /// </summary>
    /// <returns>List of available environments with their configurations</returns>
    /// <response code="200">Returns the list of available environments</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<EnvironmentInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public ActionResult<IEnumerable<EnvironmentInfo>> GetEnvironments()
    {
        try
        {
            _logger.LogDebug("Getting available environments");

            // For now, return the current environment
            // In a full implementation, this would return all configured environments
            var currentEnvironment = _environmentService.Environment;
            var environments = new List<EnvironmentInfo>
            {
                new EnvironmentInfo
                {
                    Name = currentEnvironment.Name,
                    Description = currentEnvironment.Description,
                    DisplayName = _environmentService.GetEnvironmentDisplayName(),
                    RequiresApproval = currentEnvironment.RequiresApproval,
                    MaxOperationSize = currentEnvironment.MaxOperationSize,
                    IsInMaintenanceWindow = _environmentService.IsInMaintenanceWindow(),
                    MaintenanceWindows = currentEnvironment.MaintenanceWindows?.Select(mw => new MaintenanceWindowInfo
                    {
                        Start = mw.Start,
                        End = mw.End,
                        TimeZone = mw.TimeZone,
                        Description = mw.Description
                    }).ToList() ?? new List<MaintenanceWindowInfo>()
                }
            };

            _logger.LogDebug("Retrieved {Count} environments", environments.Count);
            return Ok(environments);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving environments");
            return Problem(
                title: "Error retrieving environments",
                detail: "An error occurred while retrieving the list of available environments",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets the current environment information
    /// </summary>
    /// <returns>Current environment information</returns>
    /// <response code="200">Returns the current environment information</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("current")]
    [ProducesResponseType(typeof(EnvironmentInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public ActionResult<EnvironmentInfo> GetCurrentEnvironment()
    {
        try
        {
            _logger.LogDebug("Getting current environment information");

            var currentEnvironment = _environmentService.Environment;
            var environmentInfo = new EnvironmentInfo
            {
                Name = currentEnvironment.Name,
                Description = currentEnvironment.Description,
                DisplayName = _environmentService.GetEnvironmentDisplayName(),
                RequiresApproval = currentEnvironment.RequiresApproval,
                MaxOperationSize = currentEnvironment.MaxOperationSize,
                IsInMaintenanceWindow = _environmentService.IsInMaintenanceWindow(),
                MaintenanceWindows = currentEnvironment.MaintenanceWindows?.Select(mw => new MaintenanceWindowInfo
                {
                    Start = mw.Start,
                    End = mw.End,
                    TimeZone = mw.TimeZone,
                    Description = mw.Description
                }).ToList() ?? new List<MaintenanceWindowInfo>()
            };

            _logger.LogDebug("Retrieved current environment: {Environment}", environmentInfo.Name);
            return Ok(environmentInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving current environment");
            return Problem(
                title: "Error retrieving current environment",
                detail: "An error occurred while retrieving the current environment information",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Validates if an operation is allowed in the current environment
    /// </summary>
    /// <param name="operationSize">Size of the operation (number of records)</param>
    /// <returns>Validation result indicating if the operation is allowed</returns>
    /// <response code="200">Returns the validation result</response>
    /// <response code="400">If the operation size is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("validate")]
    [ProducesResponseType(typeof(EnvironmentValidationResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public ActionResult<EnvironmentValidationResponse> ValidateOperation([FromQuery] int operationSize)
    {
        try
        {
            if (operationSize <= 0)
            {
                _logger.LogWarning("Invalid operation size provided: {OperationSize}", operationSize);
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid operation size",
                    Detail = "Operation size must be greater than 0",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Validating operation with size: {OperationSize}", operationSize);

            var validationResult = _environmentService.ValidateOperation(operationSize);
            var response = new EnvironmentValidationResponse
            {
                IsValid = validationResult.IsValid,
                ErrorMessage = validationResult.ErrorMessage,
                RequiresApproval = validationResult.RequiresApproval,
                Warnings = validationResult.Warnings,
                EnvironmentName = _environmentService.Environment.Name,
                MaxOperationSize = _environmentService.Environment.MaxOperationSize,
                IsInMaintenanceWindow = _environmentService.IsInMaintenanceWindow()
            };

            _logger.LogDebug("Operation validation result: {IsValid}", response.IsValid);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating operation with size: {OperationSize}", operationSize);
            return Problem(
                title: "Error validating operation",
                detail: "An error occurred while validating the operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }
}

/// <summary>
/// Environment information for API responses
/// </summary>
public class EnvironmentInfo
{
    /// <summary>
    /// Environment name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Environment description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Display name for the environment
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Whether operations require approval in this environment
    /// </summary>
    public bool RequiresApproval { get; set; }

    /// <summary>
    /// Maximum operation size allowed in this environment
    /// </summary>
    public int MaxOperationSize { get; set; }

    /// <summary>
    /// Whether the environment is currently in a maintenance window
    /// </summary>
    public bool IsInMaintenanceWindow { get; set; }

    /// <summary>
    /// Maintenance windows for this environment
    /// </summary>
    public List<MaintenanceWindowInfo> MaintenanceWindows { get; set; } = new();
}

/// <summary>
/// Maintenance window information for API responses
/// </summary>
public class MaintenanceWindowInfo
{
    /// <summary>
    /// Start time (HH:mm format)
    /// </summary>
    public string Start { get; set; } = string.Empty;

    /// <summary>
    /// End time (HH:mm format)
    /// </summary>
    public string End { get; set; } = string.Empty;

    /// <summary>
    /// Time zone for the maintenance window
    /// </summary>
    public string TimeZone { get; set; } = string.Empty;

    /// <summary>
    /// Description of the maintenance window
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Environment validation response
/// </summary>
public class EnvironmentValidationResponse
{
    /// <summary>
    /// Whether the operation is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Error message if validation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether the operation requires approval
    /// </summary>
    public bool RequiresApproval { get; set; }

    /// <summary>
    /// List of warnings for the operation
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Name of the environment being validated against
    /// </summary>
    public string EnvironmentName { get; set; } = string.Empty;

    /// <summary>
    /// Maximum operation size for the environment
    /// </summary>
    public int MaxOperationSize { get; set; }

    /// <summary>
    /// Whether the environment is in a maintenance window
    /// </summary>
    public bool IsInMaintenanceWindow { get; set; }
}
