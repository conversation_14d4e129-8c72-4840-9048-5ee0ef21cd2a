using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Data.SqlClient;
using System.Diagnostics;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;
using System.Data;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Complex entity creation service following migration patterns
/// Implementation of Phase 2.2: Complex Entity Creation Sequences
/// </summary>
public class ComplexEntityCreationService : IComplexEntityCreationService
{
    private readonly ILogger<ComplexEntityCreationService> _logger;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly IBulkInsertOptimizationService _bulkInsertService;
    private readonly IForeignKeyLookupCacheService _cacheService;

    public ComplexEntityCreationService(
        ILogger<ComplexEntityCreationService> logger,
        IEnvironmentConfigurationService environmentService,
        IBulkInsertOptimizationService bulkInsertService,
        IForeignKeyLookupCacheService cacheService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _bulkInsertService = bulkInsertService ?? throw new ArgumentNullException(nameof(bulkInsertService));
        _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
    }

    public async Task<ComplexEntityResult> CreateVehicleBatchAsync(
        Guid sessionId,
        IEnumerable<VehicleCreateRequest> vehicleRequests,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var requests = vehicleRequests.ToList();
        var result = new ComplexEntityResult
        {
            TotalRequests = requests.Count
        };

        _logger.LogInformation("Starting Vehicle batch creation: {TotalRequests} requests", result.TotalRequests);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            using var transaction = connection.BeginTransaction();

            try
            {
                foreach (var request in requests)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    await CreateSingleVehicleAsync(sessionId, request, connection, transaction, result);
                }

                await transaction.CommitAsync(cancellationToken);
                
                result.Success = result.FailedRequests == 0;
                result.Duration = stopwatch.Elapsed;

                _logger.LogInformation("Vehicle batch creation completed: {Successful}/{Total} successful in {Duration}ms",
                    result.SuccessfulRequests, result.TotalRequests, result.Duration.TotalMilliseconds);

                return result;
            }
            catch (Exception)
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Vehicle batch creation failed");
            result.Errors.Add($"Batch operation failed: {ex.Message}");
            result.Duration = stopwatch.Elapsed;
            return result;
        }
    }

    private async Task CreateSingleVehicleAsync(
        Guid sessionId,
        VehicleCreateRequest request,
        SqlConnection connection,
        SqlTransaction transaction,
        ComplexEntityResult result)
    {
        var vehicleKey = $"{request.SerialNo}_{request.HireNo}";
        
        try
        {
            _logger.LogDebug("Creating vehicle: {SerialNo} ({HireNo})", request.SerialNo, request.HireNo);

            // Step 1: Check if vehicle already exists
            var existingVehicleId = await CheckVehicleExistsAsync(request.SerialNo, connection, transaction);
            if (existingVehicleId != null)
            {
                result.Warnings.Add($"Vehicle {request.SerialNo} already exists");
                result.Results[vehicleKey] = new { VehicleId = existingVehicleId, Created = false };
                result.SuccessfulRequests++;
                return;
            }

            // Step 2: Validate and get module ID (following VehicleMigration pattern)
            Guid? moduleId = null;
            if (!string.IsNullOrEmpty(request.ModuleIoTDevice))
            {
                moduleId = await GetModuleIdAsync(request.ModuleIoTDevice, connection, transaction);
                if (moduleId == null)
                {
                    result.Errors.Add($"Module {request.ModuleIoTDevice} not found for vehicle {request.SerialNo}");
                    result.FailedRequests++;
                    return;
                }

                // Check if module is already allocated
                var isModuleAllocated = await IsModuleAllocatedAsync(moduleId.Value, connection, transaction);
                if (isModuleAllocated)
                {
                    result.Errors.Add($"Module {request.ModuleIoTDevice} is already allocated to another vehicle");
                    result.FailedRequests++;
                    return;
                }
            }

            // Step 3: Get Canrule ID (following VehicleMigration pattern)
            var canruleId = await GetCanruleIdAsync(connection, transaction);

            // Step 4: Create ChecklistSettings (following VehicleMigration pattern)
            var checklistId = await CreateChecklistSettingsAsync(request, connection, transaction);

            // Step 5: Create VehicleOtherSettings (following VehicleMigration pattern)
            var otherSettingsId = await CreateVehicleOtherSettingsAsync(connection, transaction);

            // Step 6: Create Vehicle record
            var vehicleId = await CreateVehicleRecordAsync(
                request, checklistId, otherSettingsId, canruleId, moduleId, connection, transaction);

            // Step 7: Update module allocation if module was assigned
            if (moduleId.HasValue)
            {
                await UpdateModuleAllocationAsync(moduleId.Value, vehicleId, connection, transaction);
            }

            result.Results[vehicleKey] = new 
            { 
                VehicleId = vehicleId, 
                ChecklistId = checklistId,
                OtherSettingsId = otherSettingsId,
                ModuleId = moduleId,
                Created = true 
            };
            
            result.SuccessfulRequests++;

            _logger.LogDebug("Successfully created vehicle: {SerialNo} with ID {VehicleId}", 
                request.SerialNo, vehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Vehicle creation failed for {SerialNo}", request.SerialNo);
            result.Errors.Add($"Vehicle creation failed for {request.SerialNo}: {ex.Message}");
            result.FailedRequests++;
        }
    }

    private async Task<Guid?> CheckVehicleExistsAsync(string serialNo, SqlConnection connection, SqlTransaction transaction)
    {
        const string sql = "SELECT Id FROM dbo.Vehicle WHERE SerialNo = @SerialNo";
        using var cmd = new SqlCommand(sql, connection, transaction);
        cmd.Parameters.AddWithValue("@SerialNo", serialNo);

        var result = await cmd.ExecuteScalarAsync();
        return result as Guid?;
    }

    private async Task<Guid?> GetModuleIdAsync(string iotDevice, SqlConnection connection, SqlTransaction transaction)
    {
        const string sql = "SELECT Id FROM dbo.Module WHERE IoTDevice = @IoTDevice";
        using var cmd = new SqlCommand(sql, connection, transaction);
        cmd.Parameters.AddWithValue("@IoTDevice", iotDevice);

        var result = await cmd.ExecuteScalarAsync();
        return result as Guid?;
    }

    private async Task<bool> IsModuleAllocatedAsync(Guid moduleId, SqlConnection connection, SqlTransaction transaction)
    {
        const string sql = "SELECT COUNT(1) FROM dbo.Vehicle WHERE ModuleId1 = @ModuleId";
        using var cmd = new SqlCommand(sql, connection, transaction);
        cmd.Parameters.AddWithValue("@ModuleId", moduleId);

        var count = (int)(await cmd.ExecuteScalarAsync() ?? 0);
        return count > 0;
    }

    private async Task<Guid> GetCanruleIdAsync(SqlConnection connection, SqlTransaction transaction)
    {
        const string sql = "SELECT TOP 1 Id FROM dbo.Canrule ORDER BY Id";
        using var cmd = new SqlCommand(sql, connection, transaction);
        
        var result = await cmd.ExecuteScalarAsync();
        if (result == null)
            throw new InvalidOperationException("No Canrule found in database");
        
        return (Guid)result;
    }

    private async Task<Guid> CreateChecklistSettingsAsync(
        VehicleCreateRequest request, 
        SqlConnection connection, 
        SqlTransaction transaction)
    {
        var checklistId = Guid.NewGuid();
        
        // Get department checklist configuration (following VehicleMigration pattern)
        var departmentChecklistSql = @"
            SELECT TOP 1 
                CASE WHEN @ChecklistType = 0 THEN TimeBasedChecklistId ELSE DriverBasedChecklistId END as ChecklistId
            FROM dbo.Department 
            WHERE Id = @DepartmentId";

        using var deptCmd = new SqlCommand(departmentChecklistSql, connection, transaction);
        deptCmd.Parameters.AddWithValue("@DepartmentId", request.DepartmentId);
        deptCmd.Parameters.AddWithValue("@ChecklistType", (int)request.ChecklistType);
        
        var departmentChecklistId = await deptCmd.ExecuteScalarAsync() as Guid?;

        // Create ChecklistSettings record
        const string insertChecklistSql = @"
            INSERT INTO dbo.ChecklistSettings (Id, ChecklistId, Created, Updated, UpdatedBy, CreatedBy)
            VALUES (@Id, @ChecklistId, @Created, @Updated, @UpdatedBy, @CreatedBy)";

        using var checklistCmd = new SqlCommand(insertChecklistSql, connection, transaction);
        checklistCmd.Parameters.AddWithValue("@Id", checklistId);
        checklistCmd.Parameters.AddWithValue("@ChecklistId", departmentChecklistId ?? (object)DBNull.Value);
        checklistCmd.Parameters.AddWithValue("@Created", DateTime.UtcNow);
        checklistCmd.Parameters.AddWithValue("@Updated", DateTime.UtcNow);
        checklistCmd.Parameters.AddWithValue("@UpdatedBy", "BulkSeeder");
        checklistCmd.Parameters.AddWithValue("@CreatedBy", "BulkSeeder");

        await checklistCmd.ExecuteNonQueryAsync();
        return checklistId;
    }

    private async Task<Guid> CreateVehicleOtherSettingsAsync(SqlConnection connection, SqlTransaction transaction)
    {
        var otherSettingsId = Guid.NewGuid();

        const string insertOtherSettingsSql = @"
            INSERT INTO dbo.VehicleOtherSettings (
                Id, CheckPINEnabled, CheckVehicleInSameDepartment, IsVehicleIDRequired, 
                VINCodeVisible, VINCodeRequired, IgnoreMissingHours, Created, Updated, 
                UpdatedBy, CreatedBy
            ) VALUES (
                @Id, 0, 0, 0, 0, 0, 0, @Created, @Updated, @UpdatedBy, @CreatedBy
            )";

        using var otherSettingsCmd = new SqlCommand(insertOtherSettingsSql, connection, transaction);
        otherSettingsCmd.Parameters.AddWithValue("@Id", otherSettingsId);
        otherSettingsCmd.Parameters.AddWithValue("@Created", DateTime.UtcNow);
        otherSettingsCmd.Parameters.AddWithValue("@Updated", DateTime.UtcNow);
        otherSettingsCmd.Parameters.AddWithValue("@UpdatedBy", "BulkSeeder");
        otherSettingsCmd.Parameters.AddWithValue("@CreatedBy", "BulkSeeder");

        await otherSettingsCmd.ExecuteNonQueryAsync();
        return otherSettingsId;
    }

    private async Task<Guid> CreateVehicleRecordAsync(
        VehicleCreateRequest request,
        Guid checklistId,
        Guid otherSettingsId,
        Guid canruleId,
        Guid? moduleId,
        SqlConnection connection,
        SqlTransaction transaction)
    {
        var vehicleId = Guid.NewGuid();

        const string insertVehicleSql = @"
            INSERT INTO dbo.Vehicle (
                Id, SerialNo, HireNo, IDLETimer, ModuleIsConnected, OnHire, ImpactLockout,
                TimeoutEnabled, IsCanbus, HireTime, ModelId, SiteId, DepartmentId,
                ModuleId1, CustomerId, ChecklistSettingsId, VehicleOtherSettingsId, CanruleId,
                Created, Updated, UpdatedBy, CreatedBy
            ) VALUES (
                @Id, @SerialNo, @HireNo, @IDLETimer, 0, @OnHire, @ImpactLockout,
                @TimeoutEnabled, @IsCanbus, @HireTime, @ModelId, @SiteId, @DepartmentId,
                @ModuleId1, @CustomerId, @ChecklistSettingsId, @VehicleOtherSettingsId, @CanruleId,
                @Created, @Updated, @UpdatedBy, @CreatedBy
            )";

        using var vehicleCmd = new SqlCommand(insertVehicleSql, connection, transaction);
        vehicleCmd.Parameters.AddWithValue("@Id", vehicleId);
        vehicleCmd.Parameters.AddWithValue("@SerialNo", request.SerialNo);
        vehicleCmd.Parameters.AddWithValue("@HireNo", request.HireNo);
        vehicleCmd.Parameters.AddWithValue("@IDLETimer", request.IdleTimer ?? (object)DBNull.Value);
        vehicleCmd.Parameters.AddWithValue("@OnHire", request.OnHire);
        vehicleCmd.Parameters.AddWithValue("@ImpactLockout", request.ImpactLockout);
        vehicleCmd.Parameters.AddWithValue("@TimeoutEnabled", request.TimeoutEnabled);
        vehicleCmd.Parameters.AddWithValue("@IsCanbus", request.IsCanbus);
        vehicleCmd.Parameters.AddWithValue("@HireTime", DateTime.UtcNow);
        vehicleCmd.Parameters.AddWithValue("@ModelId", request.ModelId);
        vehicleCmd.Parameters.AddWithValue("@SiteId", request.SiteId);
        vehicleCmd.Parameters.AddWithValue("@DepartmentId", request.DepartmentId);
        vehicleCmd.Parameters.AddWithValue("@ModuleId1", moduleId ?? (object)DBNull.Value);
        vehicleCmd.Parameters.AddWithValue("@CustomerId", request.CustomerId);
        vehicleCmd.Parameters.AddWithValue("@ChecklistSettingsId", checklistId);
        vehicleCmd.Parameters.AddWithValue("@VehicleOtherSettingsId", otherSettingsId);
        vehicleCmd.Parameters.AddWithValue("@CanruleId", canruleId);
        vehicleCmd.Parameters.AddWithValue("@Created", DateTime.UtcNow);
        vehicleCmd.Parameters.AddWithValue("@Updated", DateTime.UtcNow);
        vehicleCmd.Parameters.AddWithValue("@UpdatedBy", "BulkSeeder");
        vehicleCmd.Parameters.AddWithValue("@CreatedBy", "BulkSeeder");

        await vehicleCmd.ExecuteNonQueryAsync();
        return vehicleId;
    }

    private async Task UpdateModuleAllocationAsync(Guid moduleId, Guid vehicleId, SqlConnection connection, SqlTransaction transaction)
    {
        const string updateModuleSql = @"
            UPDATE dbo.Module 
            SET VehicleId = @VehicleId, Updated = @Updated, UpdatedBy = @UpdatedBy
            WHERE Id = @ModuleId";

        using var cmd = new SqlCommand(updateModuleSql, connection, transaction);
        cmd.Parameters.AddWithValue("@VehicleId", vehicleId);
        cmd.Parameters.AddWithValue("@ModuleId", moduleId);
        cmd.Parameters.AddWithValue("@Updated", DateTime.UtcNow);
        cmd.Parameters.AddWithValue("@UpdatedBy", "BulkSeeder");

        await cmd.ExecuteNonQueryAsync();
    }

    public async Task<ComplexEntityResult> CreateCardAccessPermissionsBatchAsync(
        Guid sessionId,
        IEnumerable<CardAccessCreateRequest> accessRequests,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var requests = accessRequests.ToList();
        var result = new ComplexEntityResult
        {
            TotalRequests = requests.Count
        };

        _logger.LogInformation("Starting Card Access batch creation: {TotalRequests} requests", result.TotalRequests);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            using var transaction = connection.BeginTransaction();

            try
            {
                // Get normal driver permission ID (following VehicleAccessMigration pattern)
                var normalDriverPermissionId = await GetNormalDriverPermissionIdAsync(connection, transaction);

                foreach (var request in requests)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    await CreateCardAccessPermissionsAsync(sessionId, request, normalDriverPermissionId, connection, transaction, result);
                }

                await transaction.CommitAsync(cancellationToken);
                
                result.Success = result.FailedRequests == 0;
                result.Duration = stopwatch.Elapsed;

                _logger.LogInformation("Card Access batch creation completed: {Successful}/{Total} successful in {Duration}ms",
                    result.SuccessfulRequests, result.TotalRequests, result.Duration.TotalMilliseconds);

                return result;
            }
            catch (Exception)
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Card Access batch creation failed");
            result.Errors.Add($"Batch operation failed: {ex.Message}");
            result.Duration = stopwatch.Elapsed;
            return result;
        }
    }

    private async Task<Guid> GetNormalDriverPermissionIdAsync(SqlConnection connection, SqlTransaction transaction)
    {
        const string sql = "SELECT Id FROM dbo.Permission WHERE Name = 'Normal Driver'";
        using var cmd = new SqlCommand(sql, connection, transaction);
        
        var result = await cmd.ExecuteScalarAsync();
        if (result == null)
            throw new InvalidOperationException("Normal Driver permission not found");
        
        return (Guid)result;
    }

    private async Task CreateCardAccessPermissionsAsync(
        Guid sessionId,
        CardAccessCreateRequest request,
        Guid normalDriverPermissionId,
        SqlConnection connection,
        SqlTransaction transaction,
        ComplexEntityResult result)
    {
        var cardKey = $"{request.DriverId}_{request.WeigandNumber}";
        
        try
        {
            // Step 1: Create Card record
            var cardId = await CreateCardAsync(request, connection, transaction);

            // Step 2: Update Driver with Card reference
            await UpdateDriverCardAsync(request.DriverId, cardId, connection, transaction);

            // Step 3: Create access permissions based on access level
            var permissionCount = await CreateAccessPermissionsByLevelAsync(
                request, cardId, normalDriverPermissionId, connection, transaction);

            result.Results[cardKey] = new 
            { 
                CardId = cardId,
                DriverId = request.DriverId,
                PermissionsCreated = permissionCount,
                AccessLevel = request.AccessLevel.ToString()
            };
            
            result.SuccessfulRequests++;

            _logger.LogDebug("Successfully created card and {PermissionCount} access permissions for driver {DriverId}", 
                permissionCount, request.DriverId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Card access creation failed for driver {DriverId}", request.DriverId);
            result.Errors.Add($"Card access creation failed for driver {request.DriverId}: {ex.Message}");
            result.FailedRequests++;
        }
    }

    private async Task<Guid> CreateCardAsync(CardAccessCreateRequest request, SqlConnection connection, SqlTransaction transaction)
    {
        var cardId = Guid.NewGuid();

        const string insertCardSql = @"
            INSERT INTO dbo.Card (
                Id, Weigand, WeigandIssue, WeigandFacility, Created, Updated, UpdatedBy, CreatedBy
            ) VALUES (
                @Id, @Weigand, 0, 0, @Created, @Updated, @UpdatedBy, @CreatedBy
            )";

        using var cmd = new SqlCommand(insertCardSql, connection, transaction);
        cmd.Parameters.AddWithValue("@Id", cardId);
        cmd.Parameters.AddWithValue("@Weigand", request.WeigandNumber);
        cmd.Parameters.AddWithValue("@Created", DateTime.UtcNow);
        cmd.Parameters.AddWithValue("@Updated", DateTime.UtcNow);
        cmd.Parameters.AddWithValue("@UpdatedBy", "BulkSeeder");
        cmd.Parameters.AddWithValue("@CreatedBy", "BulkSeeder");

        await cmd.ExecuteNonQueryAsync();
        return cardId;
    }

    private async Task UpdateDriverCardAsync(Guid driverId, Guid cardId, SqlConnection connection, SqlTransaction transaction)
    {
        const string updateDriverSql = @"
            UPDATE dbo.Driver 
            SET CardDetailsId = @CardId, Updated = @Updated, UpdatedBy = @UpdatedBy
            WHERE Id = @DriverId";

        using var cmd = new SqlCommand(updateDriverSql, connection, transaction);
        cmd.Parameters.AddWithValue("@CardId", cardId);
        cmd.Parameters.AddWithValue("@DriverId", driverId);
        cmd.Parameters.AddWithValue("@Updated", DateTime.UtcNow);
        cmd.Parameters.AddWithValue("@UpdatedBy", "BulkSeeder");

        await cmd.ExecuteNonQueryAsync();
    }

    private async Task<int> CreateAccessPermissionsByLevelAsync(
        CardAccessCreateRequest request,
        Guid cardId,
        Guid normalDriverPermissionId,
        SqlConnection connection,
        SqlTransaction transaction)
    {
        var permissionCount = 0;

        switch (request.AccessLevel)
        {
            case AccessLevel.Site:
                permissionCount += await CreateSiteAccessPermissionsAsync(request, cardId, normalDriverPermissionId, connection, transaction);
                break;
            case AccessLevel.Department:
                permissionCount += await CreateDepartmentAccessPermissionsAsync(request, cardId, normalDriverPermissionId, connection, transaction);
                break;
            case AccessLevel.Model:
                permissionCount += await CreateModelAccessPermissionsAsync(request, cardId, normalDriverPermissionId, connection, transaction);
                break;
            case AccessLevel.Vehicle:
                permissionCount += await CreateVehicleAccessPermissionsAsync(request, cardId, normalDriverPermissionId, connection, transaction);
                break;
        }

        return permissionCount;
    }

    private async Task<int> CreateSiteAccessPermissionsAsync(
        CardAccessCreateRequest request,
        Guid cardId,
        Guid normalDriverPermissionId,
        SqlConnection connection,
        SqlTransaction transaction)
    {
        // Create Site access (following VehicleAccessMigration pattern)
        const string siteAccessSql = @"
            INSERT INTO dbo.SiteVehicleNormalCardAccess (Id, SiteId, PermissionId, CardId)
            SELECT NEWID(), @SiteId, @PermissionId, @CardId
            WHERE NOT EXISTS (
                SELECT 1 FROM dbo.SiteVehicleNormalCardAccess 
                WHERE SiteId = @SiteId AND CardId = @CardId AND PermissionId = @PermissionId
            )";

        using var cmd = new SqlCommand(siteAccessSql, connection, transaction);
        cmd.Parameters.AddWithValue("@SiteId", request.SiteId);
        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
        cmd.Parameters.AddWithValue("@CardId", cardId);

        return await cmd.ExecuteNonQueryAsync();
    }

    private async Task<int> CreateDepartmentAccessPermissionsAsync(
        CardAccessCreateRequest request,
        Guid cardId,
        Guid normalDriverPermissionId,
        SqlConnection connection,
        SqlTransaction transaction)
    {
        const string departmentAccessSql = @"
            INSERT INTO dbo.DepartmentVehicleNormalCardAccess (Id, DepartmentId, PermissionId, CardId)
            SELECT NEWID(), @DepartmentId, @PermissionId, @CardId
            WHERE NOT EXISTS (
                SELECT 1 FROM dbo.DepartmentVehicleNormalCardAccess 
                WHERE DepartmentId = @DepartmentId AND CardId = @CardId AND PermissionId = @PermissionId
            )";

        using var cmd = new SqlCommand(departmentAccessSql, connection, transaction);
        cmd.Parameters.AddWithValue("@DepartmentId", request.DepartmentId);
        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
        cmd.Parameters.AddWithValue("@CardId", cardId);

        return await cmd.ExecuteNonQueryAsync();
    }

    private async Task<int> CreateModelAccessPermissionsAsync(
        CardAccessCreateRequest request,
        Guid cardId,
        Guid normalDriverPermissionId,
        SqlConnection connection,
        SqlTransaction transaction)
    {
        var permissionCount = 0;

        foreach (var modelId in request.ModelIds)
        {
            const string modelAccessSql = @"
                INSERT INTO dbo.ModelVehicleNormalCardAccess (Id, ModelId, PermissionId, CardId, DepartmentId)
                SELECT NEWID(), @ModelId, @PermissionId, @CardId, @DepartmentId
                WHERE NOT EXISTS (
                    SELECT 1 FROM dbo.ModelVehicleNormalCardAccess 
                    WHERE ModelId = @ModelId AND CardId = @CardId AND DepartmentId = @DepartmentId AND PermissionId = @PermissionId
                )";

            using var cmd = new SqlCommand(modelAccessSql, connection, transaction);
            cmd.Parameters.AddWithValue("@ModelId", modelId);
            cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
            cmd.Parameters.AddWithValue("@CardId", cardId);
            cmd.Parameters.AddWithValue("@DepartmentId", request.DepartmentId);

            permissionCount += await cmd.ExecuteNonQueryAsync();
        }

        return permissionCount;
    }

    private async Task<int> CreateVehicleAccessPermissionsAsync(
        CardAccessCreateRequest request,
        Guid cardId,
        Guid normalDriverPermissionId,
        SqlConnection connection,
        SqlTransaction transaction)
    {
        var permissionCount = 0;

        foreach (var vehicleId in request.VehicleIds)
        {
            const string vehicleAccessSql = @"
                INSERT INTO dbo.PerVehicleNormalCardAccess (Id, VehicleId, PermissionId, CardId)
                SELECT NEWID(), @VehicleId, @PermissionId, @CardId
                WHERE NOT EXISTS (
                    SELECT 1 FROM dbo.PerVehicleNormalCardAccess 
                    WHERE VehicleId = @VehicleId AND CardId = @CardId AND PermissionId = @PermissionId
                )";

            using var cmd = new SqlCommand(vehicleAccessSql, connection, transaction);
            cmd.Parameters.AddWithValue("@VehicleId", vehicleId);
            cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
            cmd.Parameters.AddWithValue("@CardId", cardId);

            permissionCount += await cmd.ExecuteNonQueryAsync();
        }

        return permissionCount;
    }

    public async Task<ComplexEntityResult> AllocateModulesBatchAsync(
        Guid sessionId,
        IEnumerable<ModuleAllocationRequest> allocationRequests,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var requests = allocationRequests.ToList();
        var result = new ComplexEntityResult
        {
            TotalRequests = requests.Count
        };

        _logger.LogInformation("Starting Module allocation: {TotalRequests} requests", result.TotalRequests);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            using var transaction = connection.BeginTransaction();

            try
            {
                foreach (var request in requests)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    await AllocateSingleModuleAsync(sessionId, request, connection, transaction, result);
                }

                await transaction.CommitAsync(cancellationToken);
                
                result.Success = result.FailedRequests == 0;
                result.Duration = stopwatch.Elapsed;

                _logger.LogInformation("Module allocation completed: {Successful}/{Total} successful in {Duration}ms",
                    result.SuccessfulRequests, result.TotalRequests, result.Duration.TotalMilliseconds);

                return result;
            }
            catch (Exception)
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Module allocation failed");
            result.Errors.Add($"Batch operation failed: {ex.Message}");
            result.Duration = stopwatch.Elapsed;
            return result;
        }
    }

    private async Task AllocateSingleModuleAsync(
        Guid sessionId,
        ModuleAllocationRequest request,
        SqlConnection connection,
        SqlTransaction transaction,
        ComplexEntityResult result)
    {
        var moduleKey = $"{request.IoTDevice}_{request.VehicleId}";
        
        try
        {
            // Step 1: Validate module exists
            var moduleId = await GetModuleIdAsync(request.IoTDevice, connection, transaction);
            if (moduleId == null)
            {
                result.Errors.Add($"Module {request.IoTDevice} not found");
                result.FailedRequests++;
                return;
            }

            // Step 2: Check if module is already allocated (if validation requested)
            if (request.ValidateAvailability)
            {
                var isAllocated = await IsModuleAllocatedAsync(moduleId.Value, connection, transaction);
                if (isAllocated)
                {
                    result.Errors.Add($"Module {request.IoTDevice} is already allocated");
                    result.FailedRequests++;
                    return;
                }
            }

            // Step 3: Update module allocation
            await UpdateModuleAllocationAsync(moduleId.Value, request.VehicleId, connection, transaction);

            result.Results[moduleKey] = new 
            { 
                ModuleId = moduleId.Value,
                VehicleId = request.VehicleId,
                IoTDevice = request.IoTDevice,
                Allocated = true
            };
            
            result.SuccessfulRequests++;

            _logger.LogDebug("Successfully allocated module {IoTDevice} to vehicle {VehicleId}", 
                request.IoTDevice, request.VehicleId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Module allocation failed for {IoTDevice}", request.IoTDevice);
            result.Errors.Add($"Module allocation failed for {request.IoTDevice}: {ex.Message}");
            result.FailedRequests++;
        }
    }
}
