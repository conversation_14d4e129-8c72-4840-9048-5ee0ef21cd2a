using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Tests to discover what's actually in your database
    /// Run this to see your real schema before writing validation tests
    /// </summary>
    public class SchemaDiscoveryTests
    {
        private readonly ITestOutputHelper _output;
        private readonly DatabaseSchemaInspector _inspector;

        public SchemaDiscoveryTests(ITestOutputHelper output)
        {
            _output = output;
            _inspector = new DatabaseSchemaInspector();
        }

        [Fact]
        public async Task ShowAllTablesInDatabase()
        {
            _output.WriteLine("=== DISCOVERING ALL TABLES ===");
            
            var tables = await _inspector.GetAllTablesAsync();
            
            _output.WriteLine($"Found {tables.Count} tables in database:");
            foreach (var table in tables)
            {
                _output.WriteLine($"  • {table}");
            }
            
            // This test always passes - it's just for discovery
            Assert.True(tables.Count >= 0);
        }

        [Fact]
        public async Task ShowVehicleTableColumns()
        {
            _output.WriteLine("=== VEHICLE TABLE COLUMNS ===");
            
            var columns = await _inspector.GetTableColumnsAsync("Vehicle");
            
            _output.WriteLine($"Vehicle table has {columns.Count} columns:");
            foreach (var column in columns)
            {
                _output.WriteLine($"  • {column}");
            }
            
            // This test always passes - it's just for discovery
            Assert.True(columns.Count >= 0);
        }

        [Fact]
        public async Task ShowPersonTableColumns()
        {
            _output.WriteLine("=== PERSON TABLE COLUMNS ===");
            
            var columns = await _inspector.GetTableColumnsAsync("Person");
            
            _output.WriteLine($"Person table has {columns.Count} columns:");
            foreach (var column in columns)
            {
                _output.WriteLine($"  • {column}");
            }
            
            // This test always passes - it's just for discovery
            Assert.True(columns.Count >= 0);
        }

        [Fact]
        public async Task ShowCardTableColumns()
        {
            _output.WriteLine("=== CARD TABLE COLUMNS ===");
            
            var columns = await _inspector.GetTableColumnsAsync("Card");
            
            _output.WriteLine($"Card table has {columns.Count} columns:");
            foreach (var column in columns)
            {
                _output.WriteLine($"  • {column}");
            }
            
            // This test always passes - it's just for discovery
            Assert.True(columns.Count >= 0);
        }
    }
} 