<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <!-- Security Headers -->
      <httpProtocol>
        <customHeaders>
          <add name="X-Content-Type-Options" value="nosniff" />
          <add name="X-Frame-Options" value="SAMEORIGIN" />
          <add name="X-XSS-Protection" value="1; mode=block" />
          <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
          <add name="Content-Security-Policy" value="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: ws:;" />
        </customHeaders>
      </httpProtocol>
      
      <!-- Compression -->
      <urlCompression doStaticCompression="true" doDynamicCompression="true" />
      <httpCompression>
        <dynamicTypes>
          <add mimeType="text/*" enabled="true" />
          <add mimeType="message/*" enabled="true" />
          <add mimeType="application/javascript" enabled="true" />
          <add mimeType="application/json" enabled="true" />
          <add mimeType="*/*" enabled="false" />
        </dynamicTypes>
        <staticTypes>
          <add mimeType="text/*" enabled="true" />
          <add mimeType="message/*" enabled="true" />
          <add mimeType="application/javascript" enabled="true" />
          <add mimeType="application/json" enabled="true" />
          <add mimeType="*/*" enabled="false" />
        </staticTypes>
      </httpCompression>
      
      <!-- Request Filtering -->
      <security>
        <requestFiltering>
          <requestLimits maxAllowedContentLength="52428800" /> <!-- 50MB -->
          <fileExtensions allowUnlisted="false">
            <add fileExtension=".csv" allowed="true" />
            <add fileExtension=".txt" allowed="true" />
          </fileExtensions>
        </requestFiltering>
      </security>
      
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      
      <!-- CRITICAL FIX: Enable logging and use outofprocess hosting model for SignalR -->
      <aspNetCore processPath="dotnet" 
                  arguments=".\XQ360.DataMigration.Web.dll" 
                  stdoutLogEnabled="true" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="outofprocess"
                  forwardWindowsAuthToken="false">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
          <environmentVariable name="ASPNETCORE_URLS" value="http://localhost:8080" />
          <environmentVariable name="DOTNET_ENVIRONMENT" value="Production" />
          <!-- Enable detailed logging for debugging -->
          <environmentVariable name="ASPNETCORE_LOGGING__CONSOLE__DISABLECOLORS" value="true" />
          <environmentVariable name="ASPNETCORE_LOGGING__CONSOLE__FORMAT" value="json" />
        </environmentVariables>
      </aspNetCore>
      
      <!-- Enable WebSockets for SignalR -->
      <webSocket enabled="true" />
      
      <!-- Configure static content MIME types -->
      <staticContent>
        <mimeMap fileExtension=".css" mimeType="text/css" />
        <mimeMap fileExtension=".js" mimeType="application/javascript" />
        <mimeMap fileExtension=".ico" mimeType="image/x-icon" />
        <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
        <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
        <mimeMap fileExtension=".ttf" mimeType="application/font-ttf" />
        <mimeMap fileExtension=".eot" mimeType="application/vnd.ms-fontobject" />
        <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
        <mimeMap fileExtension=".json" mimeType="application/json" />
        <mimeMap fileExtension=".map" mimeType="application/json" />
        <mimeMap fileExtension=".csv" mimeType="text/csv" />
      </staticContent>
      
      <!-- Caching for static content -->
      <staticContent>
        <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="7.00:00:00" />
      </staticContent>
      
      <!-- Error Pages -->
      <httpErrors errorMode="Custom" existingResponse="Replace">
        <remove statusCode="404" subStatusCode="-1" />
        <error statusCode="404" path="/Error" responseMode="ExecuteURL" />
        <remove statusCode="500" subStatusCode="-1" />
        <error statusCode="500" path="/Error" responseMode="ExecuteURL" />
      </httpErrors>
    </system.webServer>
  </location>
</configuration> 