# Fix script for ObjectDisposedException in MigrationService
# This script will update the code to use IServiceScopeFactory instead of IServiceProvider

Write-Host "=== Fixing ObjectDisposedException in MigrationService ===" -ForegroundColor Cyan
Write-Host ""

# Define the file path
$migrationServiceFile = "C:\inetpub\wwwroot\XQ360Migration\XQ360.DataMigration.Web.dll"
$sourceFile = "C:\files\XQ360DataMigration\XQ360.DataMigration.Web\Services\IMigrationService.cs"

Write-Host "1. Checking if source file exists..." -ForegroundColor Yellow
if (Test-Path $sourceFile) {
    Write-Host "   ✓ Source file found" -ForegroundColor Green
} else {
    Write-Host "   ❌ Source file not found at: $sourceFile" -ForegroundColor Red
    Write-Host "   💡 You'll need to manually update the code" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "   MANUAL FIX REQUIRED:" -ForegroundColor Red
    Write-Host "   1. Open IMigrationService.cs" -ForegroundColor Gray
    Write-Host "   2. Change line 21: IServiceProvider _serviceProvider;" -ForegroundColor Gray
    Write-Host "   3. Change to: IServiceScopeFactory _serviceScopeFactory;" -ForegroundColor Gray
    Write-Host "   4. Update constructor parameter" -ForegroundColor Gray
    Write-Host "   5. Change line 138: using var scope = _serviceProvider.CreateScope();" -ForegroundColor Gray
    Write-Host "   6. Change to: using var scope = _serviceScopeFactory.CreateScope();" -ForegroundColor Gray
    Write-Host "   7. Rebuild and redeploy the application" -ForegroundColor Gray
    exit 1
}

Write-Host ""

Write-Host "2. Creating backup of current file..." -ForegroundColor Yellow
$backupFile = "$sourceFile.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Copy-Item $sourceFile $backupFile
Write-Host "   ✓ Backup created: $backupFile" -ForegroundColor Green

Write-Host ""

Write-Host "3. Reading current file content..." -ForegroundColor Yellow
$content = Get-Content $sourceFile -Raw

Write-Host ""

Write-Host "4. Applying fixes..." -ForegroundColor Yellow

# Fix 1: Change IServiceProvider to IServiceScopeFactory in field declaration
$content = $content -replace 'private readonly IServiceProvider _serviceProvider;', 'private readonly IServiceScopeFactory _serviceScopeFactory;'

# Fix 2: Change constructor parameter
$content = $content -replace 'IServiceProvider serviceProvider,', 'IServiceScopeFactory serviceScopeFactory,'

# Fix 3: Change constructor assignment
$content = $content -replace '_serviceProvider = serviceProvider;', '_serviceScopeFactory = serviceScopeFactory;'

# Fix 4: Change the CreateScope call
$content = $content -replace 'using var scope = _serviceProvider\.CreateScope\(\);', 'using var scope = _serviceScopeFactory.CreateScope();'

Write-Host "   ✓ Applied all fixes" -ForegroundColor Green

Write-Host ""

Write-Host "5. Writing updated content..." -ForegroundColor Yellow
$content | Out-File -FilePath $sourceFile -Encoding UTF8
Write-Host "   ✓ File updated successfully" -ForegroundColor Green

Write-Host ""

Write-Host "6. Verifying changes..." -ForegroundColor Yellow
$updatedContent = Get-Content $sourceFile -Raw

# Check if fixes were applied
$hasServiceScopeFactory = $updatedContent -match 'IServiceScopeFactory'
$hasCreateScopeCall = $updatedContent -match '_serviceScopeFactory\.CreateScope\(\)'

if ($hasServiceScopeFactory -and $hasCreateScopeCall) {
    Write-Host "   ✓ All fixes verified successfully" -ForegroundColor Green
} else {
    Write-Host "   ❌ Some fixes may not have been applied correctly" -ForegroundColor Red
}

Write-Host ""

Write-Host "=== FIX APPLIED ===" -ForegroundColor Green
Write-Host "The ObjectDisposedException fix has been applied to the source code." -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Rebuild the application: dotnet build" -ForegroundColor Gray
Write-Host "2. Publish the updated application" -ForegroundColor Gray
Write-Host "3. Deploy to the remote server" -ForegroundColor Gray
Write-Host "4. Restart the IIS application pool" -ForegroundColor Gray
Write-Host "5. Test the migration functionality" -ForegroundColor Gray
Write-Host ""
Write-Host "The fix changes:" -ForegroundColor Cyan
Write-Host "- IServiceProvider → IServiceScopeFactory" -ForegroundColor Gray
Write-Host "- _serviceProvider.CreateScope() → _serviceScopeFactory.CreateScope()" -ForegroundColor Gray
Write-Host "- This prevents the ObjectDisposedException in background tasks" -ForegroundColor Gray 