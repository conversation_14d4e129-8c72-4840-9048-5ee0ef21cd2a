using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Core.Configuration;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace FleetXQ.Tools.BulkImporter.Core.Services;

/// <summary>
/// Service for managing environment-specific operations and validations
/// </summary>
public class EnvironmentService : IEnvironmentService
{
    private readonly ILogger<EnvironmentService> _logger;
    private readonly EnvironmentOptions _environmentOptions;
    private readonly IHostEnvironment _hostEnvironment;
    private readonly HttpClient _httpClient;

    public EnvironmentService(
        ILogger<EnvironmentService> logger,
        IOptions<EnvironmentOptions> environmentOptions,
        IHostEnvironment hostEnvironment,
        HttpClient httpClient)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentOptions = environmentOptions?.Value ?? throw new ArgumentNullException(nameof(environmentOptions));
        _hostEnvironment = hostEnvironment ?? throw new ArgumentNullException(nameof(hostEnvironment));
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
    }

    public EnvironmentOptions Environment => _environmentOptions;

    public EnvironmentValidationResult ValidateOperation(int operationSize)
    {
        var result = new EnvironmentValidationResult
        {
            RequiresApproval = _environmentOptions.RequiresApproval
        };

        // Check operation size limits
        if (operationSize > _environmentOptions.MaxOperationSize)
        {
            result.IsValid = false;
            result.ErrorMessage = $"Operation size ({operationSize:N0}) exceeds maximum allowed for {_environmentOptions.Name} environment ({_environmentOptions.MaxOperationSize:N0})";
            return result;
        }

        // Check if in maintenance window
        if (IsInMaintenanceWindow())
        {
            result.Warnings.Add("Operation is being performed during a scheduled maintenance window");
        }

        // Environment-specific warnings
        switch (_environmentOptions.Name.ToLowerInvariant())
        {
            case "production":
                if (operationSize > 10000)
                {
                    result.Warnings.Add("Large operation detected in production environment - extra caution advised");
                }
                break;
            case "pilot":
                if (operationSize > 5000)
                {
                    result.Warnings.Add("Large operation detected in pilot environment - consider testing in staging first");
                }
                break;
        }

        result.IsValid = true;
        return result;
    }

    public bool IsInMaintenanceWindow()
    {
        if (!_environmentOptions.MaintenanceWindows.Any())
            return false;

        var now = DateTime.UtcNow;

        foreach (var window in _environmentOptions.MaintenanceWindows)
        {
            if (IsTimeInWindow(now, window))
            {
                _logger.LogWarning("Current time is within maintenance window: {Description} ({Start} - {End} {TimeZone})",
                    window.Description, window.Start, window.End, window.TimeZone);
                return true;
            }
        }

        return false;
    }

    public string ResolveFilePath(string relativePath)
    {
        if (string.IsNullOrEmpty(relativePath))
            return string.Empty;

        // If path is already absolute, return as-is
        if (Path.IsPathRooted(relativePath))
            return relativePath;

        // For containerized environments, use absolute paths
        if (_environmentOptions.Name.ToLowerInvariant() is "production" or "staging" or "pilot")
        {
            // Assume containerized deployment with absolute paths
            return relativePath.StartsWith("/") ? relativePath : Path.Combine("/app", relativePath);
        }

        // For development, use relative to application directory
        var baseDirectory = AppContext.BaseDirectory;
        var resolvedPath = Path.Combine(baseDirectory, relativePath);

        // Ensure directory exists
        var directory = Path.GetDirectoryName(resolvedPath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
            _logger.LogDebug("Created directory: {Directory}", directory);
        }

        return resolvedPath;
    }

    public async Task SendNotificationAsync(string message, NotificationLevel level = NotificationLevel.Info, CancellationToken cancellationToken = default)
    {
        if (!_environmentOptions.NotificationWebhooks.Any())
        {
            _logger.LogDebug("No notification webhooks configured for environment {Environment}", _environmentOptions.Name);
            return;
        }

        var notification = new
        {
            Environment = _environmentOptions.Name,
            Level = level.ToString(),
            Message = message,
            Timestamp = DateTime.UtcNow,
            Application = "FleetXQ.BulkImporter",
            Hostname = System.Environment.MachineName
        };

        var json = JsonSerializer.Serialize(notification, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        var content = new StringContent(json, Encoding.UTF8, "application/json");

        foreach (var webhook in _environmentOptions.NotificationWebhooks)
        {
            try
            {
                var response = await _httpClient.PostAsync(webhook, content, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogDebug("Notification sent successfully to {Webhook}", webhook);
                }
                else
                {
                    _logger.LogWarning("Failed to send notification to {Webhook}. Status: {StatusCode}",
                        webhook, response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending notification to webhook {Webhook}", webhook);
            }
        }
    }

    public string GetEnvironmentDisplayName()
    {
        var displayName = _environmentOptions.Name;

        if (!string.IsNullOrEmpty(_environmentOptions.Description))
        {
            displayName += $" ({_environmentOptions.Description})";
        }

        return displayName;
    }

    private static bool IsTimeInWindow(DateTime currentTime, MaintenanceWindow window)
    {
        // Parse window times
        if (!TimeSpan.TryParse(window.Start, out var startTime) ||
            !TimeSpan.TryParse(window.End, out var endTime))
        {
            return false;
        }

        // Convert current time to specified timezone (simplified - assumes UTC for now)
        var currentTimeOfDay = currentTime.TimeOfDay;

        // Handle windows that cross midnight
        if (startTime <= endTime)
        {
            return currentTimeOfDay >= startTime && currentTimeOfDay <= endTime;
        }
        else
        {
            return currentTimeOfDay >= startTime || currentTimeOfDay <= endTime;
        }
    }
}
