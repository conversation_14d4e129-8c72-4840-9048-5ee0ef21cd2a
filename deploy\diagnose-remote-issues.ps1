# Diagnostic script for remote server issues
# Run this on your remote server to identify problems

Write-Host "=== XQ360 Migration Remote Server Diagnostics ===" -ForegroundColor Cyan
Write-Host ""

# 1. Check if CSV_Input directory exists
Write-Host "1. Checking CSV_Input directory..." -ForegroundColor Yellow
$csvInputPath = "C:\inetpub\wwwroot\XQ360Migration\CSV_Input"
if (Test-Path $csvInputPath) {
    Write-Host "   ✓ CSV_Input directory exists" -ForegroundColor Green
    $csvFiles = Get-ChildItem $csvInputPath -Filter "*.csv"
    Write-Host "   📁 Found $($csvFiles.Count) CSV files:" -ForegroundColor Green
    foreach ($file in $csvFiles) {
        Write-Host "      - $($file.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "   ❌ CSV_Input directory does NOT exist!" -ForegroundColor Red
    Write-Host "   This is causing DirectoryNotFoundException errors" -ForegroundColor Red
}

Write-Host ""

# 2. Check if CSV_Template directory exists (for comparison)
Write-Host "2. Checking CSV_Template directory..." -ForegroundColor Yellow
$csvTemplatePath = "C:\inetpub\wwwroot\XQ360Migration\CSV_Template"
if (Test-Path $csvTemplatePath) {
    Write-Host "   ✓ CSV_Template directory exists" -ForegroundColor Green
    $templateFiles = Get-ChildItem $csvTemplatePath -Filter "*.csv"
    Write-Host "   📁 Found $($templateFiles.Count) template files:" -ForegroundColor Green
    foreach ($file in $templateFiles) {
        Write-Host "      - $($file.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "   ❌ CSV_Template directory does NOT exist!" -ForegroundColor Red
}

Write-Host ""

# 3. Check application logs for recent errors
Write-Host "3. Checking recent application logs..." -ForegroundColor Yellow
$logsPath = "C:\inetpub\wwwroot\XQ360Migration\Logs"
if (Test-Path $logsPath) {
    $logFiles = Get-ChildItem $logsPath -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 3
    if ($logFiles) {
        Write-Host "   📄 Recent log files found:" -ForegroundColor Green
        foreach ($log in $logFiles) {
            Write-Host "      - $($log.Name) (Last modified: $($log.LastWriteTime))" -ForegroundColor Gray
        }
        
        # Check for specific errors in the most recent log
        $latestLog = $logFiles[0].FullName
        Write-Host "   🔍 Checking latest log for errors..." -ForegroundColor Yellow
        
        $errorLines = Get-Content $latestLog | Select-String -Pattern "ERR|Exception|Error" | Select-Object -Last 10
        if ($errorLines) {
            Write-Host "   ❌ Found errors in latest log:" -ForegroundColor Red
            foreach ($line in $errorLines) {
                Write-Host "      $($line)" -ForegroundColor Red
            }
        } else {
            Write-Host "   ✓ No recent errors found in logs" -ForegroundColor Green
        }
    } else {
        Write-Host "   ❌ No log files found in Logs directory" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ Logs directory does NOT exist!" -ForegroundColor Red
}

Write-Host ""

# 4. Check IIS application pool status
Write-Host "4. Checking IIS Application Pool..." -ForegroundColor Yellow
try {
    $appPool = Get-IISAppPool -Name "XQ360Migration" -ErrorAction SilentlyContinue
    if ($appPool) {
        Write-Host "   ✓ Application pool 'XQ360Migration' exists" -ForegroundColor Green
        Write-Host "   📊 Status: $($appPool.State)" -ForegroundColor Gray
        Write-Host "   🔧 Managed Runtime Version: $($appPool.ManagedRuntimeVersion)" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ Application pool 'XQ360Migration' does NOT exist!" -ForegroundColor Red
        Write-Host "   💡 You may need to create it or check the correct name" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ Error checking application pool: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 5. Check if web.config exists and is valid
Write-Host "5. Checking web.config..." -ForegroundColor Yellow
$webConfigPath = "C:\inetpub\wwwroot\XQ360Migration\web.config"
if (Test-Path $webConfigPath) {
    Write-Host "   ✓ web.config exists" -ForegroundColor Green
    
    # Check for key settings
    $webConfig = [xml](Get-Content $webConfigPath)
    $aspNetCore = $webConfig.SelectSingleNode("//aspNetCore")
    if ($aspNetCore) {
        $stdoutLogEnabled = $aspNetCore.GetAttribute("stdoutLogEnabled")
        $hostingModel = $aspNetCore.GetAttribute("hostingModel")
        
        Write-Host "   📋 stdoutLogEnabled: $stdoutLogEnabled" -ForegroundColor Gray
        Write-Host "   📋 hostingModel: $hostingModel" -ForegroundColor Gray
        
        if ($stdoutLogEnabled -eq "true" -and $hostingModel -eq "outofprocess") {
            Write-Host "   ✓ web.config settings look correct for SignalR" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️ web.config may need updates for SignalR" -ForegroundColor Yellow
        }
    } else {
        Write-Host "   ❌ aspNetCore section not found in web.config" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ web.config does NOT exist!" -ForegroundColor Red
}

Write-Host ""

# 6. Check for running processes
Write-Host "6. Checking running processes..." -ForegroundColor Yellow
$dotnetProcesses = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue
if ($dotnetProcesses) {
    Write-Host "   ✓ Found $($dotnetProcesses.Count) dotnet processes running" -ForegroundColor Green
    foreach ($proc in $dotnetProcesses) {
        Write-Host "      - PID: $($proc.Id), CPU: $($proc.CPU), Memory: $([math]::Round($proc.WorkingSet64/1MB, 2)) MB" -ForegroundColor Gray
    }
} else {
    Write-Host "   ❌ No dotnet processes found running" -ForegroundColor Red
}

Write-Host ""

# 7. Check SignalR hub accessibility
Write-Host "7. Testing SignalR hub accessibility..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost/migrationHub/negotiate?negotiateVersion=1" -Method POST -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✓ SignalR hub is accessible" -ForegroundColor Green
    } else {
        Write-Host "   ❌ SignalR hub returned status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ SignalR hub test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 8. Summary and recommendations
Write-Host "=== SUMMARY AND RECOMMENDATIONS ===" -ForegroundColor Cyan
Write-Host ""

if (-not (Test-Path $csvInputPath)) {
    Write-Host "🔧 IMMEDIATE ACTION REQUIRED:" -ForegroundColor Red
    Write-Host "   1. Create CSV_Input directory:" -ForegroundColor Yellow
    Write-Host "      New-Item -ItemType Directory -Path '$csvInputPath' -Force" -ForegroundColor Gray
    Write-Host "   2. Copy CSV files from CSV_Template to CSV_Input:" -ForegroundColor Yellow
    Write-Host "      Copy-Item '$csvTemplatePath\*.csv' '$csvInputPath\'" -ForegroundColor Gray
    Write-Host "   3. Set proper permissions:" -ForegroundColor Yellow
    Write-Host "      icacls '$csvInputPath' /grant 'IIS_IUSRS:(OI)(CI)F'" -ForegroundColor Gray
}

Write-Host ""
Write-Host "📋 DIAGNOSTIC COMPLETE" -ForegroundColor Green
Write-Host "Run this script again after making changes to verify fixes." -ForegroundColor Cyan 