# XQ360 Data Migration Web — User Manual

A concise, step-by-step guide for running CSV-based migrations using the web app.

## Quick Start

1. Open the migration site and go to `Home/Index` (the main dashboard).
2. Select the correct Environment from the dropdown (e.g., `Development`, `US`, `UK`, `AU`, `Pilot`).
3. Check the migration types you need (e.g., Persons, Vehicles, Cards).
4. Download CSV templates for each selected type and prepare your files.
5. Upload the CSV for each selected type.
6. (Recommended) Keep "Run Database Checking" enabled to validate setup and configuration.
7. Click Start Migration and monitor live progress.
8. View/Download the migration report when finished.

## Preparing Your CSV Files

- **Templates**: Use the Template button next to each migration to download the exact CSV format.
- **File format**: Save as CSV (.csv). Header names must match the template; order can differ.
- **Booleans**: Use `true`/`false` (not yes/no).
- **Uniqueness**: Weigand (cards) and Device ID (vehicles) must be unique.
- **Names**: First/Last names must match between `PERSON_IMPORT_TEMPLATE.csv` and card-related files.

Required filenames expected by the system:

- Persons: `PERSON_IMPORT_TEMPLATE.csv`
- Vehicles: `VEHICLE_IMPORT.csv`
- Cards & Access: `CARD_IMPORT.csv`
- Pre-Op Checklist: `PREOP_CHECKLIST_IMPORT.csv`
- Spare Modules: `SPARE_MODEL_IMPORT_TEMPLATE.csv`
- Supervisor Access: `SUPERVISOR_ACCESS_IMPORT.csv`
- Driver Blacklist: `DRIVER_BLACKLIST_IMPORT.csv`
- Website Users: `WEBSITE_USER_IMPORT.csv`

Tip: You can preview template headers via `api/template/view/{migrationType}` and download via `api/template/download/{migrationType}`.

## Running a Migration

### 1) Select Environment

- Choose from configured environments (see `appsettings.json` under `Migration.Environments`).
- Use `Development` or `Pilot` for testing; use production (e.g., `US`, `UK`, `AU`) only for real migrations.

### 2) Choose Migration Types

- Tick the boxes for the data you plan to import. Each type will show an upload field and a Template button.

### 3) Download Templates (optional but recommended)

- Click Template beside each selected type to download the exact CSV structure.
- Fill in your data and save as CSV.

### 4) Upload CSV Files

- For each selected type, choose your prepared CSV file.
- The page validates extensions and empty files immediately and shows clear messages.
- Header validation happens server-side before execution; warnings are surfaced in progress.

### 5) Run Database Checking (recommended)

- Leave "Run Database Checking" ON to validate your environment before running the migration.
- You’ll see pass/fail results for Database, API configuration, and migration services.
- If any checks fail, fix the configuration first; do not proceed.
- You can uncheck this option to skip checks and run anyway (not recommended).

### 6) Start Migration

- Click Start Migration. The app will:
  - Save and validate your CSVs
  - Run migrations in the correct order
  - Stream real-time updates via SignalR to the page
  - Generate detailed text reports

### 7) Monitor Progress

- Live status, step-by-step summaries, counts (processed/inserted/skipped/errors) display in real time.
- If a WebSocket issue occurs, the UI falls back to periodic status checks automatically.

### 8) View Reports

- From the page, click View/Download Report to open or save the latest report.
- Reports are kept in `Reports/` (web app) or fallback to the main migration project `XQ360.DataMigration/Reports`.

## Migration Order and Dependencies

The app runs selected migrations with dependency-aware ordering:

1) Spare Modules → 2) Pre-Op Checklist → 3) Vehicles → 4) Persons → 5) Cards & Vehicle Access → 6) Supervisor Access → 7) Driver Blacklist → 8) Website Users → 9) Vehicle Settings Sync

Notes:

- Cards require Persons and Vehicles to exist first.
- Supervisor and blacklist updates depend on existing persons/cards.
- Vehicle Settings Sync runs automatically at the end if any selected type affects vehicles or access. It is skipped if you only import Spare Modules or Website Users.

## Vehicle Settings Sync

- Automatically triggered after all other selected types complete (except when only Spare Modules/Website Users were selected).
- Uses Device IDs to synchronize IoT device configuration with the FleetXQ system.
- Devices offline at the time will be reported as skipped/unsynced; re-run later when online.

## Safe Re-runs

- The migration is designed to be safe to run multiple times:
  - Existing records are skipped
  - Reports indicate inserted, skipped, and failed rows
  - Fix CSV issues and re-run specific migration types as needed

## Where Files Live (for reference)

- Uploaded files are saved to `CSV_Input` next to the app.
- When deployed to IIS under an app folder like `XQ360Migration`, paths resolve automatically:
  - Templates: `XQ360Migration/CSV_Template`
  - Uploads: `XQ360Migration/CSV_Input`
  - Reports: `XQ360Migration/Reports`

## Troubleshooting

- CSV template/header errors
  - The app surfaces format warnings before execution; review messages in the progress panel
  - Ensure filenames and headers match templates exactly

- Environment checks fail (Database Checking)
  - Verify selected Environment credentials and connection strings (`appsettings.json`)
  - Confirm API base URL and database are reachable from the server

- SignalR (live updates) not working
  - Use the `Home/SignalRTest` page to verify connection
  - Ensure IIS WebSockets are enabled and proxies allow WebSocket traffic
  - The UI will still poll periodically if SignalR is temporarily unavailable

- No reports found
  - The app looks in `Reports/` (web) then falls back to `XQ360.DataMigration/Reports`
  - If neither exists, the page will display a helpful message

- Partial success
  - Use the report’s Failed section to correct CSV rows and re-run the affected migration type(s)

## Reference

- Main UI: `Home/Index`
- Live progress (automatic navigation after start): `Home/Progress/{migrationId}`
- SignalR hub (internal): `/migrationHub`
- Template APIs:
  - View headers/sample rows: `api/template/view/{migrationType}`
  - Download file: `api/template/download/{migrationType}`

Supported `migrationType` values:

- `spare-modules`, `preop-checklist`, `vehicles`, `persons`, `cards`, `supervisor-access`, `driver-blacklist`, `website-users`, `sync-vehicle-settings`

## Tips for Clean Runs

- Prepare all CSVs first and validate headers using Template → View.
- Start with `Development` or `Pilot`, then repeat in production once verified.
- Prefer running all required types together so dependencies are automatically respected and Vehicle Sync runs once at the end.


