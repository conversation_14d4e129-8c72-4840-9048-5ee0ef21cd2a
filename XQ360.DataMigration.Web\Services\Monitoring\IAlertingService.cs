using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace XQ360.DataMigration.Web.Services.Monitoring
{
    public interface IAlertingService
    {
        Task CreateAlertAsync(Alert alert, CancellationToken cancellationToken = default);
        Task<List<Alert>> GetActiveAlertsAsync(AlertFilter? filter = null, CancellationToken cancellationToken = default);
        Task<Alert?> GetAlertAsync(Guid alertId, CancellationToken cancellationToken = default);
        Task ResolveAlertAsync(Guid alertId, string resolvedBy, string resolution, CancellationToken cancellationToken = default);
        Task AcknowledgeAlertAsync(Guid alertId, string acknowledgedBy, CancellationToken cancellationToken = default);
        Task EscalateAlertAsync(Guid alertId, AlertSeverity newSeverity, string escalatedBy, string reason, CancellationToken cancellationToken = default);
        Task<AlertStatistics> GetAlertStatisticsAsync(TimeSpan period, CancellationToken cancellationToken = default);
        Task SendNotificationAsync(Notification notification, CancellationToken cancellationToken = default);
        Task<List<NotificationChannel>> GetNotificationChannelsAsync(CancellationToken cancellationToken = default);
        Task ConfigureNotificationRulesAsync(List<NotificationRule> rules, CancellationToken cancellationToken = default);
        Task TestNotificationChannelAsync(string channelId, CancellationToken cancellationToken = default);
    }

    public class Alert
    {
        public Guid AlertId { get; set; }
        public string AlertType { get; set; } = string.Empty;
        public AlertSeverity Severity { get; set; }
        public AlertStatus Status { get; set; } = AlertStatus.Open;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Component { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? AcknowledgedAt { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public string? AcknowledgedBy { get; set; }
        public string? ResolvedBy { get; set; }
        public string? Resolution { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
        public List<AlertAction> Actions { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public int EscalationLevel { get; set; } = 0;
        public DateTime? LastEscalated { get; set; }
        public string? EscalationReason { get; set; }
        public TimeSpan? Ttl { get; set; }
        public bool SuppressNotifications { get; set; } = false;
    }

    public enum AlertStatus
    {
        Open,
        Acknowledged,
        InProgress,
        Resolved,
        Closed,
        Suppressed
    }

    public class AlertAction
    {
        public Guid ActionId { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public AlertActionType ActionType { get; set; }
        public string PerformedBy { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> Details { get; set; } = new();
    }

    public enum AlertActionType
    {
        Created,
        Acknowledged,
        Escalated,
        Resolved,
        Reopened,
        NotificationSent,
        CommentAdded,
        AssignmentChanged
    }

    public class AlertFilter
    {
        public List<AlertSeverity>? Severities { get; set; }
        public List<AlertStatus>? Statuses { get; set; }
        public List<string>? AlertTypes { get; set; }
        public List<string>? Sources { get; set; }
        public List<string>? Components { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public List<string>? Tags { get; set; }
        public int Skip { get; set; } = 0;
        public int Take { get; set; } = 100;
        public string OrderBy { get; set; } = "CreatedAt";
        public bool OrderDescending { get; set; } = true;
    }

    public class AlertStatistics
    {
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan Period { get; set; }
        public int TotalAlerts { get; set; }
        public int OpenAlerts { get; set; }
        public int AcknowledgedAlerts { get; set; }
        public int ResolvedAlerts { get; set; }
        public Dictionary<AlertSeverity, int> AlertsBySeverity { get; set; } = new();
        public Dictionary<string, int> AlertsByType { get; set; } = new();
        public Dictionary<string, int> AlertsByComponent { get; set; } = new();
        public TimeSpan AverageResolutionTime { get; set; }
        public TimeSpan AverageAcknowledgmentTime { get; set; }
        public double ResolutionRate => TotalAlerts > 0 ? (double)ResolvedAlerts / TotalAlerts * 100 : 0;
        public List<TopAlertSource> TopSources { get; set; } = new();
        public List<AlertTrend> Trends { get; set; } = new();
    }

    public class TopAlertSource
    {
        public string Source { get; set; } = string.Empty;
        public int AlertCount { get; set; }
        public Dictionary<AlertSeverity, int> SeverityBreakdown { get; set; } = new();
    }

    public class AlertTrend
    {
        public DateTime TimeSlot { get; set; }
        public int AlertCount { get; set; }
        public Dictionary<AlertSeverity, int> SeverityBreakdown { get; set; } = new();
    }

    public class Notification
    {
        public Guid NotificationId { get; set; }
        public NotificationType Type { get; set; }
        public NotificationPriority Priority { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public List<string> Recipients { get; set; } = new();
        public List<string> Channels { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ScheduledFor { get; set; }
        public TimeSpan? Ttl { get; set; }
        public int MaxRetries { get; set; } = 3;
        public List<NotificationDelivery> Deliveries { get; set; } = new();
        public Guid? RelatedAlertId { get; set; }
        public string? Template { get; set; }
        public Dictionary<string, string> TemplateParameters { get; set; } = new();
    }

    public enum NotificationType
    {
        Alert,
        Information,
        Warning,
        Success,
        Reminder,
        Escalation
    }

    public enum NotificationPriority
    {
        Low,
        Normal,
        High,
        Urgent
    }

    public class NotificationDelivery
    {
        public Guid DeliveryId { get; set; }
        public string Channel { get; set; } = string.Empty;
        public string Recipient { get; set; } = string.Empty;
        public NotificationDeliveryStatus Status { get; set; }
        public DateTime AttemptedAt { get; set; }
        public DateTime? DeliveredAt { get; set; }
        public string? ErrorMessage { get; set; }
        public int AttemptNumber { get; set; }
        public Dictionary<string, object> DeliveryMetadata { get; set; } = new();
    }

    public enum NotificationDeliveryStatus
    {
        Pending,
        Sent,
        Delivered,
        Failed,
        Bounced,
        Suppressed
    }

    public class NotificationChannel
    {
        public string ChannelId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public NotificationChannelType Type { get; set; }
        public bool IsEnabled { get; set; } = true;
        public Dictionary<string, string> Configuration { get; set; } = new();
        public List<string> SupportedNotificationTypes { get; set; } = new();
        public NotificationChannelHealthStatus HealthStatus { get; set; } = NotificationChannelHealthStatus.Unknown;
        public DateTime? LastHealthCheck { get; set; }
        public string? LastError { get; set; }
        public int Priority { get; set; } = 1;
        public bool RequiresAuthentication { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public enum NotificationChannelType
    {
        Email,
        Sms,
        Slack,
        Teams,
        Webhook,
        Database,
        File,
        SignalR,
        Push
    }

    public enum NotificationChannelHealthStatus
    {
        Healthy,
        Degraded,
        Unhealthy,
        Unknown
    }

    public class NotificationRule
    {
        public Guid RuleId { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsEnabled { get; set; } = true;
        public int Priority { get; set; } = 1;
        public NotificationRuleCondition Condition { get; set; } = new();
        public List<string> Channels { get; set; } = new();
        public List<string> Recipients { get; set; } = new();
        public string? MessageTemplate { get; set; }
        public NotificationPriority NotificationPriority { get; set; } = NotificationPriority.Normal;
        public TimeSpan? Throttle { get; set; }
        public Dictionary<string, object> Configuration { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class NotificationRuleCondition
    {
        public List<AlertSeverity>? AlertSeverities { get; set; }
        public List<string>? AlertTypes { get; set; }
        public List<string>? Sources { get; set; }
        public List<string>? Components { get; set; }
        public List<string>? Tags { get; set; }
        public TimeSpan? MinDuration { get; set; }
        public int? MaxOccurrences { get; set; }
        public Dictionary<string, object> CustomConditions { get; set; } = new();
    }

    public class AlertConfiguration
    {
        public TimeSpan DefaultTtl { get; set; } = TimeSpan.FromDays(7);
        public bool EnableAutoResolution { get; set; } = true;
        public TimeSpan AutoResolutionTimeout { get; set; } = TimeSpan.FromHours(24);
        public bool EnableEscalation { get; set; } = true;
        public TimeSpan EscalationTimeout { get; set; } = TimeSpan.FromMinutes(30);
        public int MaxEscalationLevel { get; set; } = 3;
        public bool EnableDeduplication { get; set; } = true;
        public TimeSpan DeduplicationWindow { get; set; } = TimeSpan.FromMinutes(5);
        public List<string> DeduplicationFields { get; set; } = new() { "AlertType", "Component", "Source" };
        public Dictionary<AlertSeverity, AlertSeverityConfig> SeverityConfigurations { get; set; } = new();
    }

    public class AlertSeverityConfig
    {
        public AlertSeverity Severity { get; set; }
        public bool RequiresAcknowledgment { get; set; }
        public TimeSpan? AcknowledgmentTimeout { get; set; }
        public bool AutoEscalate { get; set; }
        public TimeSpan? EscalationDelay { get; set; }
        public List<string> DefaultRecipients { get; set; } = new();
        public List<string> DefaultChannels { get; set; } = new();
        public string? NotificationTemplate { get; set; }
    }
}
