-- =============================================
-- FleetXQ Bulk Importer - Merge Procedures
-- Creates stored procedures for merging staging data to production
-- =============================================

-- =============================================
-- Procedure: usp_MergeDriversToProduction
-- Merges validated drivers from staging to production tables
-- =============================================
IF OBJECT_ID('[Staging].[usp_MergeDriversToProduction]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[usp_MergeDriversToProduction]
GO

CREATE PROCEDURE [Staging].[usp_MergeDriversToProduction]
    @ImportSessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @ProcessedCount INT = 0;
    DECLARE @SuccessCount INT = 0;
    DECLARE @ErrorCount INT = 0;
    DECLARE @BatchStart BIGINT = 1;
    DECLARE @BatchEnd BIGINT;
    DECLARE @MaxId BIGINT;
    
    BEGIN TRY
        -- Get the range of IDs to process
        SELECT @MaxId = MAX([Id])
        FROM [Staging].[DriverImport]
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Valid'
            AND [ProcessingAction] IN ('Insert', 'Update');
        
        IF @MaxId IS NULL
        BEGIN
            PRINT 'No valid driver records to process';
            RETURN;
        END
        
        PRINT CONCAT('Starting driver merge for session: ', @ImportSessionId);
        PRINT CONCAT('Processing up to ID: ', @MaxId, ' in batches of ', @BatchSize);
        
        -- Process in batches to avoid lock escalation
        WHILE @BatchStart <= @MaxId
        BEGIN
            SET @BatchEnd = @BatchStart + @BatchSize - 1;
            
            BEGIN TRANSACTION;
            
            -- Step 1: MERGE Person records
            WITH PersonData AS (
                SELECT 
                    di.[Id],
                    COALESCE(di.[ExistingPersonId], NEWID()) as [PersonId],
                    di.[PersonFirstName],
                    di.[PersonLastName],
                    di.[PersonEmail],
                    di.[PersonPhone],
                    di.[Notes],
                    di.[CustomerId],
                    di.[SiteId],
                    di.[DepartmentId],
                    COALESCE(di.[PersonHasLicense], 1) as [HasLicense],
                    COALESCE(di.[PersonIsActiveDriver], 1) as [IsActiveDriver],
                    COALESCE(di.[PersonIsActiveDriver], 1) as [IsDriver],
                    COALESCE(di.[PersonVehicleAccess], 1) as [VehicleAccess],
                    COALESCE(di.[PersonLicenseActive], 1) as [LicenseActive],
                    COALESCE(di.[PersonCanUnlockVehicle], 0) as [CanUnlockVehicle],
                    COALESCE(di.[PersonNormalDriverAccess], 1) as [NormalDriverAccess],
                    0 as [OnDemand], -- Default values for required fields
                    0 as [VORActivateDeactivate],
                    0 as [MaintenanceMode],
                    di.[ProcessingAction]
                FROM [Staging].[DriverImport] di
                WHERE di.[ImportSessionId] = @ImportSessionId
                    AND di.[ValidationStatus] = 'Valid'
                    AND di.[ProcessingAction] IN ('Insert', 'Update')
                    AND di.[Id] BETWEEN @BatchStart AND @BatchEnd
            )
            MERGE [dbo].[Person] AS target
            USING PersonData AS source ON target.[Id] = source.[PersonId]
            WHEN MATCHED THEN
                UPDATE SET
                    [FirstName] = source.[PersonFirstName],
                    [LastName] = source.[PersonLastName],
                    [Email] = source.[PersonEmail],
                    [Phone] = source.[PersonPhone],
                    [Notes] = source.[Notes],
                    [CustomerId] = source.[CustomerId],
                    [SiteId] = source.[SiteId],
                    [DepartmentId] = source.[DepartmentId],
                    [HasLicense] = source.[HasLicense],
                    [IsActiveDriver] = source.[IsActiveDriver],
                    [IsDriver] = source.[IsDriver],
                    [VehicleAccess] = source.[VehicleAccess],
                    [LicenseActive] = source.[LicenseActive],
                    [CanUnlockVehicle] = source.[CanUnlockVehicle],
                    [NormalDriverAccess] = source.[NormalDriverAccess]
            WHEN NOT MATCHED THEN
                INSERT ([Id], [FirstName], [LastName], [Email], [Phone], [Notes],
                       [CustomerId], [SiteId], [DepartmentId], [HasLicense], [IsActiveDriver],
                       [IsDriver], [VehicleAccess], [LicenseActive], [CanUnlockVehicle],
                       [NormalDriverAccess], [OnDemand], [VORActivateDeactivate], [MaintenanceMode])
                VALUES (source.[PersonId], source.[PersonFirstName], source.[PersonLastName],
                       source.[PersonEmail], source.[PersonPhone], source.[Notes],
                       source.[CustomerId], source.[SiteId], source.[DepartmentId],
                       source.[HasLicense], source.[IsActiveDriver], source.[IsDriver],
                       source.[VehicleAccess], source.[LicenseActive], source.[CanUnlockVehicle],
                       source.[NormalDriverAccess], source.[OnDemand], source.[VORActivateDeactivate],
                       source.[MaintenanceMode]);
            
            -- Step 2: MERGE Driver records
            WITH DriverData AS (
                SELECT 
                    di.[Id],
                    COALESCE(di.[ExistingDriverId], NEWID()) as [DriverId],
                    COALESCE(di.[ExistingPersonId], NEWID()) as [PersonId], -- This should match PersonData
                    COALESCE(di.[DriverActive], 1) as [Active],
                    COALESCE(di.[DriverLicenseMode], 0) as [LicenseMode],
                    di.[DriverVehicleAccess] as [VehicleAccess],
                    di.[CustomerId],
                    di.[SiteId],
                    di.[DepartmentId]
                FROM [Staging].[DriverImport] di
                WHERE di.[ImportSessionId] = @ImportSessionId
                    AND di.[ValidationStatus] = 'Valid'
                    AND di.[ProcessingAction] IN ('Insert', 'Update')
                    AND di.[Id] BETWEEN @BatchStart AND @BatchEnd
            )
            MERGE [dbo].[Driver] AS target
            USING DriverData AS source ON target.[Id] = source.[DriverId]
            WHEN MATCHED THEN
                UPDATE SET
                    [Active] = source.[Active],
                    [LicenseMode] = source.[LicenseMode],
                    [VehicleAccess] = source.[VehicleAccess],
                    [CustomerId] = source.[CustomerId],
                    [SiteId] = source.[SiteId],
                    [DepartmentId] = source.[DepartmentId]
            WHEN NOT MATCHED THEN
                INSERT ([Id], [Active], [LicenseMode], [VehicleAccess], [CustomerId], [SiteId], [DepartmentId])
                VALUES (source.[DriverId], source.[Active], source.[LicenseMode], source.[VehicleAccess],
                       source.[CustomerId], source.[SiteId], source.[DepartmentId]);
            
            -- Step 3: Update Person.DriverId relationships
            UPDATE p
            SET [DriverId] = d.[Id]
            FROM [dbo].[Person] p
            INNER JOIN [Staging].[DriverImport] di ON p.[Id] = COALESCE(di.[ExistingPersonId], NEWID())
            INNER JOIN [dbo].[Driver] d ON d.[Id] = COALESCE(di.[ExistingDriverId], NEWID())
            WHERE di.[ImportSessionId] = @ImportSessionId
                AND di.[ValidationStatus] = 'Valid'
                AND di.[ProcessingAction] IN ('Insert', 'Update')
                AND di.[Id] BETWEEN @BatchStart AND @BatchEnd;
            
            -- Step 4: Update staging with processing results
            UPDATE [Staging].[DriverImport]
            SET [ProcessingAction] = 'Processed',
                [ProcessedAt] = GETUTCDATE(),
                [ValidationStatus] = 'Processed'
            WHERE [ImportSessionId] = @ImportSessionId
                AND [ValidationStatus] = 'Valid'
                AND [Id] BETWEEN @BatchStart AND @BatchEnd;
            
            SET @ProcessedCount = @ProcessedCount + @@ROWCOUNT;
            SET @SuccessCount = @SuccessCount + @@ROWCOUNT;
            
            COMMIT TRANSACTION;
            
            PRINT CONCAT('Processed batch: ', @BatchStart, ' to ', @BatchEnd, ' (', @@ROWCOUNT, ' rows)');
            
            SET @BatchStart = @BatchEnd + 1;
        END
        
        -- Update session statistics
        UPDATE [Staging].[ImportSession]
        SET [ProcessedRows] = [ProcessedRows] + @ProcessedCount,
            [SuccessfulRows] = [SuccessfulRows] + @SuccessCount
        WHERE [Id] = @ImportSessionId;
        
        PRINT CONCAT('Driver merge completed. Processed: ', @ProcessedCount, ', Successful: ', @SuccessCount);
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Update staging with error information
        UPDATE [Staging].[DriverImport]
        SET [ProcessingErrors] = ERROR_MESSAGE(),
            [ValidationStatus] = 'Error'
        WHERE [ImportSessionId] = @ImportSessionId
            AND [Id] BETWEEN @BatchStart AND @BatchEnd;
        
        SET @ErrorMessage = CONCAT('Driver merge failed at batch ', @BatchStart, ': ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_MergeVehiclesToProduction
-- Merges validated vehicles from staging to production tables
-- =============================================
IF OBJECT_ID('[Staging].[usp_MergeVehiclesToProduction]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[usp_MergeVehiclesToProduction]
GO

CREATE PROCEDURE [Staging].[usp_MergeVehiclesToProduction]
    @ImportSessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @ProcessedCount INT = 0;
    
    BEGIN TRY
        -- Simplified vehicle merge (detailed implementation would follow similar pattern to drivers)
        PRINT CONCAT('Starting vehicle merge for session: ', @ImportSessionId);
        
        -- MERGE Vehicle records in batches
        WITH VehicleData AS (
            SELECT 
                COALESCE(vi.[ExistingVehicleId], NEWID()) as [VehicleId],
                vi.[HireNo],
                vi.[SerialNo],
                vi.[Description],
                COALESCE(vi.[OnHire], 1) as [OnHire],
                COALESCE(vi.[ImpactLockout], 0) as [ImpactLockout],
                COALESCE(vi.[IsCanbus], 0) as [IsCanbus],
                COALESCE(vi.[TimeoutEnabled], 1) as [TimeoutEnabled],
                COALESCE(vi.[ModuleIsConnected], 0) as [ModuleIsConnected],
                vi.[IDLETimer],
                vi.[CustomerId],
                vi.[SiteId],
                vi.[DepartmentId],
                vi.[ModelId],
                vi.[ModuleId],
                vi.[AssignedDriverId],
                vi.[AssignedPersonId]
            FROM [Staging].[VehicleImport] vi
            WHERE vi.[ImportSessionId] = @ImportSessionId
                AND vi.[ValidationStatus] = 'Valid'
                AND vi.[ProcessingAction] IN ('Insert', 'Update')
        )
        MERGE [dbo].[Vehicle] AS target
        USING VehicleData AS source ON target.[Id] = source.[VehicleId]
        WHEN MATCHED THEN
            UPDATE SET
                [HireNo] = source.[HireNo],
                [SerialNo] = source.[SerialNo],
                [Description] = source.[Description],
                [OnHire] = source.[OnHire],
                [ImpactLockout] = source.[ImpactLockout],
                [IsCanbus] = source.[IsCanbus],
                [TimeoutEnabled] = source.[TimeoutEnabled],
                [ModuleIsConnected] = source.[ModuleIsConnected],
                [IDLETimer] = source.[IDLETimer],
                [DriverId] = source.[AssignedDriverId],
                [PersonId] = source.[AssignedPersonId]
        WHEN NOT MATCHED THEN
            INSERT ([Id], [HireNo], [SerialNo], [Description], [OnHire], [ImpactLockout],
                   [IsCanbus], [TimeoutEnabled], [ModuleIsConnected], [IDLETimer],
                   [CustomerId], [SiteId], [DepartmentId], [ModelId], [ModuleId1],
                   [DriverId], [PersonId])
            VALUES (source.[VehicleId], source.[HireNo], source.[SerialNo], source.[Description],
                   source.[OnHire], source.[ImpactLockout], source.[IsCanbus], source.[TimeoutEnabled],
                   source.[ModuleIsConnected], source.[IDLETimer], source.[CustomerId],
                   source.[SiteId], source.[DepartmentId], source.[ModelId], source.[ModuleId],
                   source.[AssignedDriverId], source.[AssignedPersonId]);
        
        -- Update module status and allocation history for new assignments
        UPDATE m
        SET [ModuleStatus] = 'Assigned',
            [IsAllocatedToVehicle] = 1
        FROM [dbo].[Module] m
        INNER JOIN VehicleData vd ON m.[Id] = vd.[ModuleId]
        WHERE NOT EXISTS (
            SELECT 1 FROM [dbo].[Vehicle] v2 WHERE v2.[ModuleId1] = m.[Id] AND v2.[Id] != vd.[VehicleId]
        );
        
        -- Create allocation history entries for new vehicle-module assignments
        INSERT INTO [dbo].[ModuleAllocationHistory] 
            ([ModuleId], [VehicleId], [AllocationDate], [AllocationType], [RequestedBy], [Notes], [DealerId], [ImportSessionId])
        SELECT DISTINCT
            vd.[ModuleId],
            vd.[VehicleId],
            GETUTCDATE(),
            'Assignment',
            'Bulk Import',
            CONCAT('Vehicle assignment via bulk import session: ', @ImportSessionId),
            (SELECT TOP 1 iss.[SelectedDealerId] FROM [Staging].[ImportSession] iss WHERE iss.[Id] = @ImportSessionId),
            @ImportSessionId
        FROM VehicleData vd
        WHERE NOT EXISTS (
            SELECT 1 FROM [dbo].[ModuleAllocationHistory] mah 
            WHERE mah.[ModuleId] = vd.[ModuleId] 
                AND mah.[VehicleId] = vd.[VehicleId] 
                AND mah.[DeallocationDate] IS NULL
        );
        
        SET @ProcessedCount = @@ROWCOUNT;
        
        -- Update staging with processing results
        UPDATE [Staging].[VehicleImport]
        SET [ProcessingAction] = 'Processed',
            [ProcessedAt] = GETUTCDATE(),
            [ValidationStatus] = 'Processed'
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Valid';
        
        PRINT CONCAT('Vehicle merge completed. Processed: ', @ProcessedCount, ' rows');
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Vehicle merge failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

PRINT 'Merge procedures created successfully'