# Quick Deployment Guide - Remote Windows Server

## 🚀 **Fast Deployment Steps**

### **Prerequisites on Remote Server:**
- ✅ Windows Server with IIS
- ✅ .NET 9.0 Runtime
- ✅ ASP.NET Core Hosting Bundle
- ✅ Network access to target databases

### **Step 1: Copy Files to Remote Server**
```powershell
# Copy the entire project to remote server
# Or use the deployment script
```

### **Step 2: Run Deployment Script**
```powershell
# On remote server, run:
.\deploy\deploy-remote.ps1 -ServerName "YOUR_SERVER_IP" -Port 80
```

### **Step 3: Update Production Configuration**
Edit `deploy\production-appsettings.json` with real credentials:
- Replace `YOUR_US_PASSWORD` with actual US database password
- Replace `YOUR_UK_PASSWORD` with actual UK database password  
- Replace `YOUR_AU_PASSWORD` with actual AU database password
- Replace `YOUR_PILOT_PASSWORD` with actual Pilot database password
- Update `YOUR_SERVER_SQL` with your server's SQL instance

### **Step 4: Test Deployment**
1. Open browser to `http://YOUR_SERVER_IP`
2. Select environment (e.g., "Development")
3. Check "Run Database Checking"
4. Click "Start Migration"
5. Verify tests run successfully

## 🎯 **What Gets Deployed:**

### **Web Application (IIS):**
- ✅ **UI Access**: `http://YOUR_SERVER_IP`
- ✅ **Environment Selection**: US, UK, AU, Pilot, Development
- ✅ **Database Checking**: Tests run against selected environment
- ✅ **Migration Operations**: Full migration functionality

### **Main Application (Console):**
- ✅ **CLI Operations**: Available at `C:\inetpub\wwwroot\XQ360Migration\bin`
- ✅ **Command Line**: Can run migrations via command line
- ✅ **Batch Processing**: Automated migration capabilities

### **Test Application:**
- ✅ **Database Checking**: Integrated with web UI
- ✅ **Environment Testing**: Tests connectivity to all environments
- ✅ **Validation**: Pre-migration validation

## 🔧 **Configuration:**

### **Production Settings:**
- **File**: `deploy\production-appsettings.json`
- **Environments**: US, UK, AU, Pilot, Development (no Local)
- **Logs**: `C:\inetpub\wwwroot\XQ360Migration\Logs\`
- **Reports**: `C:\inetpub\wwwroot\XQ360Migration\Reports\`

### **IIS Configuration:**
- **Application Pool**: `XQ360MigrationPool`
- **Website**: `XQ360Migration`
- **Port**: 80 (configurable)
- **Path**: `C:\inetpub\wwwroot\XQ360Migration`

## 🌐 **Access URLs:**

### **Local Access:**
- `http://localhost`

### **Remote Access:**
- `http://YOUR_SERVER_IP`
- `http://your-domain.com` (if DNS configured)

### **SSL/HTTPS:**
- Configure SSL certificate for `https://YOUR_SERVER_IP`

## 🚨 **Troubleshooting:**

### **Common Issues:**
1. **Website won't start**: Check Application Pool identity
2. **Permission errors**: Run as Administrator
3. **Network access**: Check firewall rules
4. **Database connection**: Verify credentials in production-appsettings.json

### **Logs Location:**
- **Application Logs**: `C:\inetpub\wwwroot\XQ360Migration\Logs\`
- **IIS Logs**: `C:\inetpub\logs\LogFiles\W3SVC1\`

## ✅ **Verification Checklist:**

- [ ] Website starts successfully
- [ ] Web UI loads remotely
- [ ] Environment selection works
- [ ] Database checking functionality works
- [ ] Migration operations work
- [ ] Logs are being written
- [ ] Network connectivity to target databases
- [ ] API connectivity to target endpoints

## 📞 **Support:**

For detailed instructions, see: `deploy\README-REMOTE-DEPLOYMENT.md` 