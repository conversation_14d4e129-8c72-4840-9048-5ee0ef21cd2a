using Serilog.Core;
using Serilog.Events;

namespace FleetXQ.Tools.BulkImporter.Logging;

/// <summary>
/// Serilog enricher that adds correlation and operation IDs to log events
/// </summary>
public class CorrelationEnricher : ILogEventEnricher
{
    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        var correlationId = CorrelationContext.CorrelationId;
        if (!string.IsNullOrEmpty(correlationId))
        {
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("CorrelationId", correlationId));
        }

        var operationId = CorrelationContext.OperationId;
        if (!string.IsNullOrEmpty(operationId))
        {
            logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("OperationId", operationId));
        }
    }
}