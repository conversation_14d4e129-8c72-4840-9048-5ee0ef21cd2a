# XQ360 Data Migration Web Application - Manual IIS Deployment Script
# This script helps prepare the application for manual IIS deployment

param(
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [switch]$BuildOnly = $false
)

Write-Host "🚀 XQ360 Migration Web Application - Manual IIS Deployment Helper" -ForegroundColor Green
Write-Host "This script prepares the application for manual IIS deployment" -ForegroundColor Yellow

# Step 1: Build the application
Write-Host "📦 Building web application..." -ForegroundColor Yellow
dotnet build XQ360.DataMigration.Web -c Release

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build completed successfully!" -ForegroundColor Green

if ($BuildOnly) {
    Write-Host "📁 Built files are available in: XQ360.DataMigration.Web\bin\Release\net9.0\" -ForegroundColor Cyan
    Write-Host "📋 Production config template: deploy\production-appsettings.json" -ForegroundColor Cyan
    exit 0
}

# Step 2: Create deployment directory
Write-Host "📁 Creating deployment directory..." -ForegroundColor Yellow
if (Test-Path $DeployPath) {
    Write-Host "⚠️ Directory already exists. Backing up..." -ForegroundColor Yellow
    $backupPath = "$DeployPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    Rename-Item $DeployPath $backupPath
}
New-Item -ItemType Directory -Path $DeployPath -Force

# Step 3: Copy built files
Write-Host "📋 Copying application files..." -ForegroundColor Yellow
Copy-Item "XQ360.DataMigration.Web\bin\Release\net9.0\*" -Destination $DeployPath -Recurse -Force

# Step 4: Copy production configuration
Write-Host "⚙️ Copying production configuration..." -ForegroundColor Yellow
Copy-Item "deploy\production-appsettings.json" -Destination "$DeployPath\appsettings.json" -Force

# Step 5: Set basic permissions
Write-Host "🔐 Setting basic permissions..." -ForegroundColor Yellow
icacls $DeployPath /grant "IIS_IUSRS:(OI)(CI)(RX)" /T

Write-Host "✅ Files prepared for IIS deployment!" -ForegroundColor Green
Write-Host "📁 Application files copied to: $DeployPath" -ForegroundColor Cyan
Write-Host "" -ForegroundColor White
Write-Host "🔧 Next Steps (Manual):" -ForegroundColor Yellow
Write-Host "1. Open IIS Manager" -ForegroundColor White
Write-Host "2. Create Application Pool: XQ360MigrationPool (No Managed Code)" -ForegroundColor White
Write-Host "3. Create Website: XQ360Migration pointing to $DeployPath" -ForegroundColor White
Write-Host "4. Set port (e.g., 8080) and start the website" -ForegroundColor White
Write-Host "5. Access at: http://localhost:8080" -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host "📖 See deploy\README-IIS-DEPLOYMENT.md for detailed instructions" -ForegroundColor Cyan 