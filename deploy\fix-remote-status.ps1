# XQ360 Data Migration - Fix Remote Server Status Issues
# This script fixes the status update problems on remote servers

param(
    [string]$ServerName = "localhost",
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [string]$AppPoolName = "XQ360MigrationPool",
    [switch]$TestOnly = $false
)

Write-Host "XQ360 Data Migration - Fix Remote Server Status Issues" -ForegroundColor Green
Write-Host "Target Server: $ServerName" -ForegroundColor Yellow
Write-Host "Deploy Path: $DeployPath" -ForegroundColor Yellow

# Step 1: Create logs directory if it doesn't exist
Write-Host "Creating logs directory..." -ForegroundColor Yellow
$logsPath = Join-Path $DeployPath "logs"
if (-not (Test-Path $logsPath)) {
    New-Item -ItemType Directory -Path $logsPath -Force
    Write-Host "✅ Created logs directory: $logsPath" -ForegroundColor Green
} else {
    Write-Host "✅ Logs directory already exists: $logsPath" -ForegroundColor Green
}

# Step 2: Backup current web.config
Write-Host "Backing up current web.config..." -ForegroundColor Yellow
$webConfigPath = Join-Path $DeployPath "web.config"
$backupPath = Join-Path $DeployPath "web.config.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"

if (Test-Path $webConfigPath) {
    Copy-Item $webConfigPath $backupPath
    Write-Host "✅ Backed up web.config to: $backupPath" -ForegroundColor Green
} else {
    Write-Host "⚠️  No existing web.config found at: $webConfigPath" -ForegroundColor Yellow
}

# Step 3: Copy the corrected web.config
Write-Host "Updating web.config with SignalR and logging fixes..." -ForegroundColor Yellow
$correctedWebConfig = Join-Path $PSScriptRoot "remote-server-web.config"

if (Test-Path $correctedWebConfig) {
    Copy-Item $correctedWebConfig $webConfigPath -Force
    Write-Host "✅ Updated web.config with SignalR and logging fixes" -ForegroundColor Green
} else {
    Write-Host "❌ Corrected web.config not found at: $correctedWebConfig" -ForegroundColor Red
    Write-Host "Please ensure the remote-server-web.config file exists in the deploy directory." -ForegroundColor Yellow
    exit 1
}

# Step 4: Set proper permissions on logs directory
Write-Host "Setting permissions on logs directory..." -ForegroundColor Yellow
try {
    $acl = Get-Acl $logsPath
    $rule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($rule)
    Set-Acl $logsPath $acl
    Write-Host "✅ Set permissions on logs directory" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not set permissions on logs directory: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 5: Restart Application Pool
Write-Host "Restarting application pool..." -ForegroundColor Yellow
try {
    Import-Module WebAdministration
    Restart-WebAppPool -Name $AppPoolName
    Write-Host "✅ Application pool '$AppPoolName' restarted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restart application pool: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please restart the application pool manually in IIS Manager." -ForegroundColor Yellow
}

# Step 6: Test SignalR connection
Write-Host "Testing SignalR connection..." -ForegroundColor Yellow
Start-Sleep -Seconds 5  # Wait for app pool to restart

try {
    $testUrl = "http://$ServerName/migrationHub"
    $response = Invoke-WebRequest -Uri $testUrl -Method GET -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ SignalR hub is accessible" -ForegroundColor Green
} catch {
    Write-Host "⚠️  SignalR hub test failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "This is normal if the hub requires authentication." -ForegroundColor Yellow
}

# Step 7: Check logs directory permissions
Write-Host "Verifying logs directory permissions..." -ForegroundColor Yellow
if (Test-Path $logsPath) {
    try {
        $testFile = Join-Path $logsPath "test-write.tmp"
        "Test write access" | Out-File -FilePath $testFile -Encoding UTF8
        Remove-Item $testFile -Force
        Write-Host "✅ Logs directory is writable" -ForegroundColor Green
    } catch {
        Write-Host "❌ Logs directory is not writable: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please check IIS_IUSRS permissions on: $logsPath" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Logs directory does not exist: $logsPath" -ForegroundColor Red
}

# Step 8: Display summary
Write-Host "`n=== FIX SUMMARY ===" -ForegroundColor Cyan
Write-Host "✅ Updated web.config with SignalR and logging fixes" -ForegroundColor Green
Write-Host "✅ Created logs directory with proper permissions" -ForegroundColor Green
Write-Host "✅ Restarted application pool" -ForegroundColor Green
Write-Host "`nKey changes made:" -ForegroundColor Yellow
Write-Host "  • Enabled stdoutLogEnabled='true'" -ForegroundColor White
Write-Host "  • Changed hostingModel to 'outofprocess'" -ForegroundColor White
Write-Host "  • Enabled WebSocket support for SignalR" -ForegroundColor White
Write-Host "  • Added detailed logging environment variables" -ForegroundColor White
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test the migration status updates in the web interface" -ForegroundColor White
Write-Host "2. Check the logs directory for any error messages" -ForegroundColor White
Write-Host "3. Monitor the application for proper SignalR communication" -ForegroundColor White

Write-Host "`n✅ Remote server status fix completed!" -ForegroundColor Green 