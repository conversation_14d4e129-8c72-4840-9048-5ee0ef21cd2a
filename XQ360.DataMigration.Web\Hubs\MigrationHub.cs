using Microsoft.AspNetCore.SignalR;

namespace XQ360.DataMigration.Web.Hubs;

public class MigrationHub : Hub
{
    public async Task JoinMigrationGroup(string migrationId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, migrationId);
        await Clients.Caller.SendAsync("JoinedGroup", migrationId);
    }

    public async Task LeaveMigrationGroup(string migrationId)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, migrationId);
        await Clients.Caller.SendAsync("LeftGroup", migrationId);
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        await base.OnDisconnectedAsync(exception);
    }
} 