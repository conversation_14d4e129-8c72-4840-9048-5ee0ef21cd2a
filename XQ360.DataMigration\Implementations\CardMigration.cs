using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Interfaces;

namespace XQ360.DataMigration.Implementations
{
    public enum CardTypeEnum
    {
        Card = 0,
        Pin = 1
    }

    public enum KeypadReaderEnum
    {
        Rosslare = 0,
        Smart = 1,
        _48Bit = 4
    }

    public class CardMigration : ICoordinatedMigration
    {
        private readonly ILogger<CardMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly string _connectionString;
        private readonly MigrationReportingService _reportingService;

        // Thread-safe cache for database lookups to avoid repeated queries
        private readonly ConcurrentDictionary<string, Guid> _siteCache = new ConcurrentDictionary<string, Guid>();
        private readonly ConcurrentDictionary<string, Guid> _dealerCache = new ConcurrentDictionary<string, Guid>();
        private readonly ConcurrentDictionary<string, Guid> _customerCache = new ConcurrentDictionary<string, Guid>();

        public CardMigration(
            ILogger<CardMigration> logger,
            IOptions<MigrationConfiguration> config,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _connectionString = _config.DatabaseConnection;
            _reportingService = reportingService;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Card migration using direct SQL approach");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Execute SQL migration
                var migrationResult = await ExecuteCardSqlAsync(data);

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;

                // Merge detailed results
                result.DetailedErrors.AddRange(migrationResult.DetailedErrors);
                result.DetailedWarnings.AddRange(migrationResult.DetailedWarnings);

                // Add success information
                _reportingService.AddSuccessInfo(result, "Cards Created", result.RecordsInserted);
                if (result.RecordsSkipped > 0)
                {
                    _reportingService.AddSuccessInfo(result, "Records Skipped", result.RecordsSkipped);
                    
                    // Add specific skip reasons from warnings
                    if (result.Warnings.Any(w => w.Contains("failed driver lookup")))
                    {
                        _reportingService.AddSkipReason(result, "Some records skipped due to missing persons (run Person migration first)");
                    }
                    if (result.Warnings.Any(w => w.Contains("already have cards")))
                    {
                        _reportingService.AddSkipReason(result, "Some records skipped because drivers already have cards assigned");
                    }
                }

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Card Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Card migration failed");
                var failedResult = new MigrationResult
                {
                    Success = false,
                    RecordsProcessed = result.RecordsProcessed,
                    Duration = DateTime.UtcNow - startTime
                };
                
                _reportingService.AddDetailedError(failedResult, 0, ErrorTypes.DATABASE_ERROR, ex.Message, 
                    suggestion: "Check database connection and permissions");
                
                _reportingService.GenerateMigrationReport(failedResult, "Card Migration");
                return failedResult;
            }
        }

        /// <summary>
        /// Execute Card migration using an external transaction for coordination with other migrations
        /// </summary>
        public async Task<MigrationResult> ExecuteWithTransactionAsync(string csvFilePath, SqlConnection connection, SqlTransaction transaction)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Card migration using coordinated transaction");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Execute SQL migration with external transaction
                var migrationResult = await ExecuteCardSqlAsync(data, connection, transaction);

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;

                // Merge detailed results
                result.DetailedErrors.AddRange(migrationResult.DetailedErrors);
                result.DetailedWarnings.AddRange(migrationResult.DetailedWarnings);

                // Add success information
                _reportingService.AddSuccessInfo(result, "Cards Created", result.RecordsInserted);
                if (result.RecordsSkipped > 0)
                {
                    _reportingService.AddSuccessInfo(result, "Records Skipped", result.RecordsSkipped);
                    
                    // Add specific skip reasons from warnings
                    if (result.Warnings.Any(w => w.Contains("failed driver lookup")))
                    {
                        _reportingService.AddSkipReason(result, "Some records skipped due to missing persons (run Person migration first)");
                    }
                    if (result.Warnings.Any(w => w.Contains("already have cards")))
                    {
                        _reportingService.AddSkipReason(result, "Some records skipped because drivers already have cards assigned");
                    }
                }

                // Note: Don't generate final report here as this is coordinated - parent will handle reporting

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Coordinated Card migration failed");
                var failedResult = new MigrationResult
                {
                    Success = false,
                    RecordsProcessed = result.RecordsProcessed,
                    Duration = DateTime.UtcNow - startTime
                };
                
                _reportingService.AddDetailedError(failedResult, 0, ErrorTypes.DATABASE_ERROR, ex.Message, 
                    suggestion: "Check database connection and permissions");
                
                return failedResult;
            }
        }

        private Task<List<CardImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Card CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<CardImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return Task.FromResult(records);
        }

        private async Task<MigrationResult> ExecuteCardSqlAsync(List<CardImportModel> data)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var transaction = connection.BeginTransaction();

            try
            {
                var result = await ExecuteCardSqlAsync(data, connection, transaction);
                await transaction.CommitAsync();
                return result;
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        private async Task<MigrationResult> ExecuteCardSqlAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction)
        {
            _logger.LogInformation("Executing Card SQL migration with Weigand uniqueness validation...");
            _logger.LogInformation("✅ Weigand validation enabled: Cards with duplicate Weigand numbers within the same site will be skipped (CSV batch + database, excluding same person)");

            try
            {
                // Step 1: Create temporary table with card data
                var createTempTableSql = @"
                    CREATE TABLE #CardData (
                        Site NVARCHAR(100),
                        Department NVARCHAR(100),
                        FirstName NVARCHAR(100),
                        LastName NVARCHAR(100),
                        FacilityCode NVARCHAR(3),
                        Type INT,
                        CardNo NVARCHAR(50),
                        KeypadReader INT,
                        Weigand NVARCHAR(50),
                        AccessLevel NVARCHAR(50),
                        CardId UNIQUEIDENTIFIER,
                        SiteId UNIQUEIDENTIFIER,
                        DriverId UNIQUEIDENTIFIER NULL,
                        HasExistingCard BIT DEFAULT 0
                    );";

                using (var cmd = new SqlCommand(createTempTableSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Step 2: Insert card data with converted values and site lookups (with Weigand validation)
                var insertTempSql = @"
                    INSERT INTO #CardData (Site, Department, FirstName, LastName, FacilityCode, Type, CardNo, KeypadReader, Weigand, AccessLevel, CardId, SiteId)
                    VALUES (@Site, @Department, @FirstName, @LastName, @FacilityCode, @Type, @CardNo, @KeypadReader, @Weigand, @AccessLevel, @CardId, @SiteId);";

                var duplicateWeigandErrors = new List<string>();
                var duplicateWeigandRecords = new List<(string Person, string Site, string Weigand, string CardNo, string FacilityCode)>();
                var processedCount = 0;
                var skippedCount = 0;

                // Track Weigand values within the current CSV batch to prevent intra-CSV duplicates
                var csvWeigandTracker = new Dictionary<string, List<(string Person, string Site, string Weigand, string CardNo, string FacilityCode)>>();

                foreach (var record in data)
                {
                    processedCount++;
                    
                    try
                    {
                        var cardId = Guid.NewGuid();
                        var weigand = CalculateWeigand(record);
                        var siteId = await GetSiteIdAsync(record.Site ?? "Unknown", record.Customer ?? "Unknown", connection, transaction);

                        // Skip if Weigand is empty (capture cards)
                        if (string.IsNullOrEmpty(weigand))
                        {
                            _logger.LogInformation($"Skipping {record.FirstName} {record.LastName} - Weigand is empty (capture card)");
                            skippedCount++;
                            continue;
                        }

                        // Check for duplicate Weigand within the current CSV batch first
                        var csvTrackingKey = $"{record.Site}|{weigand}";
                        var currentPersonRecord = ($"{record.FirstName} {record.LastName}", record.Site, weigand, record.CardNo, record.FacilityCode);
                        
                        if (csvWeigandTracker.ContainsKey(csvTrackingKey))
                        {
                            // Check if it's the same person (allowed) or different person (duplicate)
                            var existingRecords = csvWeigandTracker[csvTrackingKey];
                            var isDuplicatePerson = existingRecords.Any(existing => 
                                existing.Person != currentPersonRecord.Item1); // Different person name

                            if (isDuplicatePerson)
                            {
                                var personName = $"{record.FirstName} {record.LastName}";
                                var existingPerson = existingRecords.First(r => r.Person != personName).Person;
                                var errorMsg = $"❌ CSV DUPLICATE WEIGAND: '{weigand}' for {personName} in site '{record.Site}' conflicts with {existingPerson} in same CSV batch (CardNo: {record.CardNo}, FacilityCode: {record.FacilityCode})";
                                duplicateWeigandErrors.Add(errorMsg);
                                duplicateWeigandRecords.Add((personName, record.Site, weigand, record.CardNo, record.FacilityCode));
                                _logger.LogWarning(errorMsg);
                                
                                skippedCount++;
                                continue; // Skip this card and continue to next
                            }
                            else
                            {
                                // Same person with same Weigand, add to tracking but continue processing
                                csvWeigandTracker[csvTrackingKey].Add(currentPersonRecord);
                            }
                        }
                        else
                        {
                            // First occurrence of this Weigand in this site, add to tracking
                            csvWeigandTracker[csvTrackingKey] = new List<(string Person, string Site, string Weigand, string CardNo, string FacilityCode)> { currentPersonRecord };
                        }

                        // Check for existing Weigand in the database (excluding same person)
                        var existingWeigandExists = await CheckWeigandExistsInSiteAsync(weigand, siteId, connection, transaction, record);
                        if (existingWeigandExists)
                        {
                            var personName = $"{record.FirstName} {record.LastName}";
                            var errorMsg = $"❌ DATABASE DUPLICATE WEIGAND: '{weigand}' for {personName} in site '{record.Site}' conflicts with different person in database (CardNo: {record.CardNo}, FacilityCode: {record.FacilityCode})";
                            duplicateWeigandErrors.Add(errorMsg);
                            duplicateWeigandRecords.Add((personName, record.Site, weigand, record.CardNo, record.FacilityCode));
                            _logger.LogWarning(errorMsg);
                            
                            skippedCount++;
                            continue; // Skip this card and continue to next
                        }

                        using var cmd = new SqlCommand(insertTempSql, connection, transaction);
                        cmd.Parameters.AddWithValue("@Site", record.Site);
                        cmd.Parameters.AddWithValue("@Department", record.DepartmentName);
                        cmd.Parameters.AddWithValue("@FirstName", record.FirstName);
                        cmd.Parameters.AddWithValue("@LastName", record.LastName);
                        cmd.Parameters.AddWithValue("@FacilityCode", record.FacilityCode);
                        // CSV columns are swapped: CardType column has reader tech, ReaderType column has card type
                        cmd.Parameters.AddWithValue("@Type", ConvertCardType(record.ReaderType)); // ReaderType contains card type
                        cmd.Parameters.AddWithValue("@CardNo", record.CardNo);
                        cmd.Parameters.AddWithValue("@KeypadReader", ConvertReaderType(record.CardType)); // CardType contains reader tech
                        cmd.Parameters.AddWithValue("@Weigand", weigand);
                        cmd.Parameters.AddWithValue("@AccessLevel", record.AccessLevel);
                        cmd.Parameters.AddWithValue("@CardId", cardId);
                        cmd.Parameters.AddWithValue("@SiteId", siteId);

                        await cmd.ExecuteNonQueryAsync();
                    }
                    catch (Exception ex)
                    {
                        var errorMsg = $"Failed to process card for {record.FirstName} {record.LastName}: {ex.Message}";
                        duplicateWeigandErrors.Add(errorMsg);
                        _logger.LogError(ex, errorMsg);
                        skippedCount++;
                    }
                }

                _logger.LogInformation($"Card processing summary: {processedCount} total, {processedCount - skippedCount} processed, {skippedCount} skipped");
                
                if (duplicateWeigandErrors.Any())
                {
                    _logger.LogWarning($"Found {duplicateWeigandErrors.Count} duplicate Weigand issues");
                }

                // Step 3: Update DriverId based on Person table lookup
                var updateDriverIdSql = @"
                    UPDATE cd
                    SET cd.DriverId = p.DriverId
                    FROM #CardData cd
                    INNER JOIN [dbo].[Person] p ON 
                        p.FirstName = cd.FirstName 
                        AND p.LastName = cd.LastName
                    INNER JOIN [dbo].[Department] d ON 
                        p.DepartmentId = d.Id 
                        AND d.Name = cd.Department
                        AND d.SiteId = cd.SiteId;";

                using (var cmd = new SqlCommand(updateDriverIdSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Step 4: Check if drivers already have cards
                var checkExistingCardsSql = @"
                    UPDATE cd
                    SET cd.HasExistingCard = 1
                    FROM #CardData cd
                    INNER JOIN [dbo].[Driver] dr ON cd.DriverId = dr.Id
                    WHERE cd.DriverId IS NOT NULL 
                      AND dr.CardDetailsId IS NOT NULL;";

                using (var cmd = new SqlCommand(checkExistingCardsSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Step 5: Insert Cards into Card table (only for successful lookups without existing cards)
                var insertCardsSql = @"
                    INSERT INTO [dbo].[Card] (Id, SiteId, Weigand, CardNumber, FacilityCode, Active, Type, KeypadReader)
                    SELECT
                        CardId,
                        SiteId,
                        Weigand,
                        CardNo,
                        FacilityCode,
                        1,
                        Type,
                        KeypadReader
                    FROM #CardData
                    WHERE DriverId IS NOT NULL 
                      AND HasExistingCard = 0;";

                int insertedCards;
                using (var cmd = new SqlCommand(insertCardsSql, connection, transaction))
                {
                    insertedCards = await cmd.ExecuteNonQueryAsync();
                }

                // Step 6: Update Driver records to link to the new Cards
                var updateDriversSql = @"
                    UPDATE dr
                    SET dr.CardDetailsId = cd.CardId
                    FROM [dbo].[Driver] dr
                    INNER JOIN #CardData cd ON dr.Id = cd.DriverId
                    WHERE cd.DriverId IS NOT NULL 
                      AND cd.HasExistingCard = 0;";

                using (var cmd = new SqlCommand(updateDriversSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Step 7: Get failure and warning counts
                var getFailureCountSql = @"
                    SELECT 
                        COUNT(CASE WHEN DriverId IS NULL THEN 1 END) AS FailedLookups,
                        COUNT(CASE WHEN DriverId IS NOT NULL AND HasExistingCard = 1 THEN 1 END) AS SkippedExistingCards
                    FROM #CardData";

                var failedLookups = 0;
                var skippedExistingCards = 0;
                using (var cmd = new SqlCommand(getFailureCountSql, connection, transaction))
                {
                    using var reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        failedLookups = reader.GetInt32(0);
                        skippedExistingCards = reader.GetInt32(1);
                    }
                }

                _logger.LogInformation($"Successfully created {insertedCards} cards, {failedLookups} failed lookups, {skippedExistingCards} skipped existing cards, {duplicateWeigandErrors.Count} duplicate Weigand skipped (CSV + database)");

                var result = new MigrationResult
                {
                    Success = true,
                    RecordsInserted = insertedCards
                    // FIXED: Removed Errors = duplicateWeigandErrors - these are business rule skips, not technical errors
                    // Duplicate Weigand issues are properly handled as warnings and detailed warnings below
                };

                // Add duplicate Weigand information to warnings and detailed errors
                if (duplicateWeigandErrors.Any())
                {
                    result.Warnings.Add($"{duplicateWeigandErrors.Count} records skipped due to duplicate Weigand in same site (CSV + database)");
                    
                    // Add detailed warning reporting for each duplicate Weigand (these are skipped, not failed)
                    for (int i = 0; i < duplicateWeigandRecords.Count; i++)
                    {
                        var record = duplicateWeigandRecords[i];
                        var warningMessage = duplicateWeigandErrors[i].Contains("CSV DUPLICATE") 
                            ? $"Weigand '{record.Weigand}' conflicts with different person in same CSV batch for site '{record.Site}'"
                            : $"Weigand '{record.Weigand}' already exists for different person in database for site '{record.Site}'";
                        
                        var recommendation = duplicateWeigandErrors[i].Contains("CSV DUPLICATE") 
                            ? "Remove or modify duplicate Weigand entries in CSV file for the same site"
                            : "Use a unique Weigand number for this site or remove existing card with same Weigand from different person";
                        
                        _reportingService.AddDetailedWarning(result, i + 1, WarningTypes.EXISTING_RECORD_SKIPPED, 
                            warningMessage, 
                            "Weigand", record.Weigand, 
                            recommendation,
                            new Dictionary<string, string> 
                            {
                                ["Site"] = record.Site,
                                ["PersonName"] = record.Person,
                                ["CardNumber"] = record.CardNo,
                                ["FacilityCode"] = record.FacilityCode,
                                ["Department"] = "Unknown" // Could be extracted from CSV context if available
                            });
                    }
                    
                    // Log summary of duplicate Weigand records (CSV + database)
                    _logger.LogWarning($"📋 DUPLICATE WEIGAND SUMMARY - CSV & DATABASE ({duplicateWeigandRecords.Count} records):");
                    foreach (var record in duplicateWeigandRecords)
                    {
                        _logger.LogWarning($"   • {record.Person} (Site: {record.Site}, Weigand: {record.Weigand}, CardNo: {record.CardNo})");
                    }
                }

                // Add detailed error reporting for failed lookups
                if (failedLookups > 0)
                {
                    result.Warnings.Add($"{failedLookups} records failed driver lookup");
                    
                    try
                    {
                        // Get detailed information about failed lookups
                        var failedLookupDetailsSql = @"
                            SELECT FirstName, LastName, Department, Site
                            FROM #CardData 
                            WHERE DriverId IS NULL";
                        
                        using (var detailCmd = new SqlCommand(failedLookupDetailsSql, connection, transaction))
                        {
                            using var reader = await detailCmd.ExecuteReaderAsync();
                            int rowNum = 1;
                            while (await reader.ReadAsync())
                            {
                                var firstName = reader.GetString(0);
                                var lastName = reader.GetString(1);
                                var department = reader.GetString(2);
                                var site = reader.GetString(3);
                                
                                _reportingService.AddDetailedError(result, rowNum++, ErrorTypes.MISSING_PERSON, 
                                    $"Person '{firstName} {lastName}' not found in department '{department}' at site '{site}'",
                                    "Person", $"{firstName} {lastName}",
                                    "Run Person migration first or verify person exists in specified department and site",
                                    new Dictionary<string, string> 
                                    {
                                        { "PersonName", $"{firstName} {lastName}" },
                                        { "FirstName", firstName },
                                        { "LastName", lastName },
                                        { "Department", department },
                                        { "Site", site }
                                    });
                            }
                        }
                    }
                    catch (Exception reportEx)
                    {
                        _logger.LogWarning($"Failed to generate detailed error report: {reportEx.Message}");
                    }
                }

                // Add detailed warnings for existing cards
                if (skippedExistingCards > 0)
                {
                    result.Warnings.Add($"{skippedExistingCards} records skipped - drivers already have cards");
                    
                    try
                    {
                        // Get detailed information about skipped cards
                        var skippedCardsDetailsSql = @"
                            SELECT FirstName, LastName, Department, Site
                            FROM #CardData 
                            WHERE DriverId IS NOT NULL AND HasExistingCard = 1";
                        
                        var skippedDrivers = new List<(string Name, string Department, string Site)>();
                        
                        using (var detailCmd = new SqlCommand(skippedCardsDetailsSql, connection, transaction))
                        {
                            using var reader = await detailCmd.ExecuteReaderAsync();
                            int rowNum = duplicateWeigandRecords.Count + 1; // Continue from where duplicate Weigand warnings left off
                            while (await reader.ReadAsync())
                            {
                                var firstName = reader.GetString(0);
                                var lastName = reader.GetString(1);
                                var department = reader.GetString(2);
                                var site = reader.GetString(3);
                                var fullName = $"{firstName} {lastName}";
                                
                                skippedDrivers.Add((fullName, department, site));
                                
                                _reportingService.AddDetailedWarning(result, rowNum++, WarningTypes.EXISTING_RECORD_SKIPPED,
                                    $"Driver '{fullName}' already has a card assigned",
                                    "Driver", fullName,
                                    "Remove existing card assignment if you want to replace it",
                                    new Dictionary<string, string> 
                                    {
                                        { "Department", department },
                                        { "Site", site }
                                    });
                            }
                        }
                        
                        // Log summary of skipped drivers
                        _logger.LogWarning($"📋 EXISTING CARD SUMMARY ({skippedDrivers.Count} records):");
                        foreach (var driver in skippedDrivers)
                        {
                            _logger.LogWarning($"   • {driver.Name} (Department: {driver.Department}, Site: {driver.Site}) - already has card");
                        }
                    }
                    catch (Exception reportEx)
                    {
                        _logger.LogWarning($"Failed to generate detailed warning report: {reportEx.Message}");
                    }
                }

                // Step 8: Clean up the temporary table
                var cleanupSql = "DROP TABLE #CardData;";
                using (var cmd = new SqlCommand(cleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Note: Transaction commit/rollback handled by external caller
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Card SQL migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<Guid> GetSiteIdAsync(string siteName, string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{siteName}|{customerName}";
            if (_siteCache.TryGetValue(key, out var cachedSiteId))
                return cachedSiteId;

            var customerId = await GetCustomerIdByNameAsync(customerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Site WHERE Name = @Name AND CustomerId = @CustomerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", siteName);
            cmd.Parameters.AddWithValue("@CustomerId", customerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Site '{siteName}' not found for customer '{customerName}'");

            var siteId = (Guid)result;
            _siteCache.TryAdd(key, siteId);
            return siteId;
        }

        private async Task<Guid> GetDealerIdAsync(string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            if (_dealerCache.TryGetValue(dealerName, out var cachedDealerId))
                return cachedDealerId;

            var sql = "SELECT Id FROM dbo.Dealer WHERE Name = @Name";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", dealerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Dealer '{dealerName}' not found");

            var dealerId = (Guid)result;
            _dealerCache.TryAdd(dealerName, dealerId);
            return dealerId;
        }

        private async Task<Guid> GetCustomerIdAsync(string customerName, string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{customerName}|{dealerName}";
            if (_customerCache.TryGetValue(key, out var cachedCustomerId))
                return cachedCustomerId;

            var dealerId = await GetDealerIdAsync(dealerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName AND DealerId = @DealerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);
            cmd.Parameters.AddWithValue("@DealerId", dealerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found for dealer '{dealerName}'");

            var customerId = (Guid)result;
            _customerCache.TryAdd(key, customerId);
            return customerId;
        }

        private async Task<Guid> GetCustomerIdByNameAsync(string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found");

            return (Guid)result;
        }

        private string CalculateWeigand(CardImportModel record)
        {
            // If weigand is already provided, use it
            if (!string.IsNullOrEmpty(record.Weigand))
            {
                return record.Weigand;
            }

            // Skip calculation if it's a capture card or capture
            if (string.Equals(record.CardType, "capture card", StringComparison.OrdinalIgnoreCase) ||
                string.Equals(record.CardType, "capture", StringComparison.OrdinalIgnoreCase))
            {
                return "";
            }

            // Calculate weigand using the server application logic
            var cardNo = record.CardNo;
            var weigandPre = record.FacilityCode ?? "0";
            // CSV columns are swapped: CardType column has reader tech, ReaderType column has card type
            var cardType = ConvertToCardTypeEnum(record.ReaderType); // ReaderType contains card type (pin/card)
            var keypadReader = ConvertToKeypadReaderEnum(record.CardType); // CardType contains reader tech (rosslare/48bit)

            _logger.LogDebug($"Calculating Weigand for {record.FirstName} {record.LastName}: CardNo={cardNo}, FacilityCode={weigandPre}, CardType={cardType}, KeypadReader={keypadReader}");

            var calculatedWeigand = CardToWeigand(cardNo, weigandPre, cardType, keypadReader);
            
            _logger.LogDebug($"Calculated Weigand: {calculatedWeigand}");
            
            return calculatedWeigand;
        }

        private string CardToWeigand(string cardNo, string weigandPre, CardTypeEnum cardType, KeypadReaderEnum keypadReader)
        {
            long card_no = long.Parse(cardNo);
            long weigandPreVal = int.Parse(weigandPre);
            long wiegand = 0;
            long m = 0;
            long n = 0;
            long temp = 0;
            int bitLength = 0;

            if (cardType == CardTypeEnum.Pin && keypadReader == KeypadReaderEnum.Smart)
            {
                weigandPreVal = 0xF0000000;
                card_no = Convert.ToInt64(cardNo, 16);
                wiegand = card_no;
                bitLength = Convert.ToString(wiegand, 2).Length;

                while (m == 0)
                {
                    m = weigandPreVal & card_no;
                    if (m == 0)
                    {
                        wiegand = weigandPreVal | wiegand;
                    }
                    weigandPreVal = weigandPreVal / 16;
                }
            }
            else if (cardType == CardTypeEnum.Card && keypadReader == KeypadReaderEnum.Rosslare)
            {
                weigandPreVal = 0;
                wiegand = (weigandPreVal << 16) | card_no;
                bitLength = Convert.ToString(wiegand, 2).Length;
            }
            else if (cardType == CardTypeEnum.Card && keypadReader == KeypadReaderEnum._48Bit)
            {
                return CardToWeigand48Bit(card_no, weigandPreVal);
            }
            else
            {
                wiegand = (weigandPreVal << 16) | card_no;
            }

            if (weigandPreVal > 255)
            {
                m = (wiegand & 0xFFFFFFFF);
                wiegand = m * 2;
                n = 0;
                m = 0x10000;

                while (m > 0)
                {
                    if ((m & wiegand) > 0)
                    {
                        n++;
                    }
                    m >>= 1;
                }

                if (n % 2 != 0)
                {
                    temp = wiegand | 1;
                }
                else
                {
                    temp = wiegand;
                }

                temp = temp & 0xFFFFFFFFL;
            }
            else if (bitLength >= 17)
            {
                long i = (wiegand & 0xFFFFFFFFFFL);
                wiegand = i << 1;

                long j = 0;
                long mask = 0x20000000000L;

                while (mask > 0x800000)
                {
                    if ((mask & wiegand) != 0)
                    {
                        j++;
                    }
                    mask >>= 1;
                }

                if (j % 2 != 0)
                {
                    wiegand |= 0x20000000000L;
                }

                j = 0;
                mask = 0x800000;

                while (mask > 0)
                {
                    if ((mask & wiegand) != 0)
                    {
                        j++;
                    }
                    mask >>= 1;
                }

                if (j % 2 != 0)
                {
                    temp = wiegand;
                }
                else
                {
                    temp = wiegand | 1;
                }

                return temp.ToString("X");
            }
            else
            {
                m = (wiegand & 0xFFFFFF);
                wiegand = m * 2;
                m = 0x2000000;

                while (m != 0x1000)
                {
                    if ((m & wiegand) > 0)
                    {
                        n++;
                    }
                    m >>= 1;
                }

                if (n % 2 != 0)
                {
                    wiegand = (wiegand | 0x2000000);
                }

                n = 0;
                m = 0x1000;

                while (m > 0)
                {
                    if ((m & wiegand) > 0)
                    {
                        n++;
                    }
                    m >>= 1;
                }

                if (n % 2 != 0)
                {
                    temp = wiegand;
                }
                else
                {
                    temp = wiegand | 1;
                }
            }

            return temp.ToString("X");
        }

        private string CardToWeigand48Bit(long cardId, long weigandpre)
        {
            long facilityCd = weigandpre;
            string facilityCdBinaryPadded = Convert.ToString(facilityCd, 2).PadLeft(22, '0');
            string cardIdBinaryPadded = Convert.ToString(cardId, 2).PadLeft(23, '0');

            int totalOnesForEvenPar = CountOnes(new int[] { 2, 3, 5, 6, 8, 9, 11, 12, 14, 15, 17, 18, 20, 21 }, facilityCdBinaryPadded)
                    + CountOnes(new int[] { 1, 2, 4, 5, 7, 8, 10, 11, 13, 14, 16, 17, 19, 20, 22, 23 }, cardIdBinaryPadded);

            string evenParStr = "0";
            if (totalOnesForEvenPar % 2 == 0)
            {
                evenParStr = "0";
            }
            else
            {
                evenParStr = "1";
            }

            int totalOnesForOddPar = CountOnes(new int[] { 1, 2, 4, 5, 7, 8, 10, 11, 13, 14, 16, 17, 19, 20, 22 }, facilityCdBinaryPadded)
                    + CountOnes(new int[] { 1, 3, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22 }, cardIdBinaryPadded);

            string oddParStr = "0";
            if (totalOnesForOddPar % 2 != 0)
            {
                oddParStr = "0";
            }
            else
            {
                oddParStr = "1";
            }

            int totalOnesForOddPar2 = CountOnes(evenParStr + facilityCdBinaryPadded + cardIdBinaryPadded + oddParStr);

            string oddParity2Str = "0";
            if (totalOnesForOddPar2 % 2 != 0)
            {
                oddParity2Str = "0";
            }
            else
            {
                oddParity2Str = "1";
            }

            string binaryStr = oddParity2Str + evenParStr + facilityCdBinaryPadded + cardIdBinaryPadded + oddParStr;
            string hexStr = Convert.ToInt64(binaryStr, 2).ToString("X");

            return hexStr;
        }

        private static int CountOnes(string binary)
        {
            int count = 0;
            string[] binaryArray = binary.ToCharArray().Select(c => c.ToString()).ToArray();

            for (int i = 0; i < 47; i++)
            {
                if ("1".Equals(binaryArray[i]))
                {
                    count += 1;
                }
            }

            return count;
        }

        private static int CountOnes(int[] indexes, string binary)
        {
            int count = 0;
            string[] binaryArray = binary.ToCharArray().Select(c => c.ToString()).ToArray();

            foreach (int index in indexes)
            {
                if ("1".Equals(binaryArray[index - 1]))
                {
                    count += 1;
                }
            }

            return count;
        }

        private CardTypeEnum ConvertToCardTypeEnum(string cardType)
        {
            if (string.IsNullOrEmpty(cardType))
                return CardTypeEnum.Card; // Default

            return cardType.ToLower() switch
            {
                "card" => CardTypeEnum.Card,
                "pin" => CardTypeEnum.Pin,
                _ => CardTypeEnum.Card // Default
            };
        }

        private KeypadReaderEnum ConvertToKeypadReaderEnum(string readerType)
        {
            if (string.IsNullOrEmpty(readerType))
                return KeypadReaderEnum.Rosslare; // Default

            return readerType.ToLower() switch
            {
                "rosslare" => KeypadReaderEnum.Smart, // Import3 expects Keypad Reader = 1 for "rosslare"
                "smart" => KeypadReaderEnum.Smart,
                "48bit" => KeypadReaderEnum._48Bit,
                "48 bit" => KeypadReaderEnum._48Bit,
                "48bit weigand" => KeypadReaderEnum._48Bit,
                "48 bit weigand" => KeypadReaderEnum._48Bit,
                _ => KeypadReaderEnum.Rosslare // Default
            };
        }

        private int ConvertCardType(string cardType)
        {
            // Convert string card type to integer for database storage
            // This mapping should match your database schema
            if (string.IsNullOrEmpty(cardType))
                return 0; // Default to card
                
            return cardType.ToLower() switch
            {
                "card" => 0,
                "pin" => 1,
                _ => 0 // Default to card
            };
        }

        private int ConvertReaderType(string readerType)
        {
            // Convert string reader type to integer for database storage
            // This mapping should match your database schema
            if (string.IsNullOrEmpty(readerType))
                return 0; // Default to rosslare
                
            return readerType.ToLower() switch
            {
                "rosslare" => 1, // Import3 expects Keypad Reader = 1 for "rosslare"
                "smart" => 1,
                "48bit" => 4,
                "48 bit" => 4,
                "48bit weigand" => 4,
                "48 bit weigand" => 4,
                _ => 0 // Default to rosslare
            };
        }

        private async Task<bool> CheckWeigandExistsInSiteAsync(string weigand, Guid siteId, SqlConnection connection, SqlTransaction transaction, CardImportModel? currentRecord = null)
        {
            var sql = @"
                SELECT COUNT(1) 
                FROM [dbo].[Card] c
                INNER JOIN [dbo].[Driver] d ON c.Id = d.CardDetailsId
                INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                INNER JOIN [dbo].[Department] dept ON p.DepartmentId = dept.Id
                INNER JOIN [dbo].[Site] s ON p.SiteId = s.Id
                INNER JOIN [dbo].[Customer] cust ON s.CustomerId = cust.Id
                INNER JOIN [dbo].[Dealer] dealer ON cust.DealerId = dealer.Id
                WHERE c.Weigand = @Weigand 
                AND c.SiteId = @SiteId
                AND c.Active = 1";

            // If we have current record info, exclude the same person (not a true duplicate)
            if (currentRecord != null)
            {
                sql += @"
                AND NOT (
                    dealer.Name = @DealerName
                    AND cust.CompanyName = @CustomerName  
                    AND s.Name = @SiteName
                    AND dept.Name = @DepartmentName
                    AND p.FirstName = @FirstName
                    AND p.LastName = @LastName
                )";
            }

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Weigand", weigand);
            cmd.Parameters.AddWithValue("@SiteId", siteId);

            if (currentRecord != null)
            {
                cmd.Parameters.AddWithValue("@DealerName", currentRecord.Dealer);
                cmd.Parameters.AddWithValue("@CustomerName", currentRecord.Customer);
                cmd.Parameters.AddWithValue("@SiteName", currentRecord.Site);
                cmd.Parameters.AddWithValue("@DepartmentName", currentRecord.DepartmentName);
                cmd.Parameters.AddWithValue("@FirstName", currentRecord.FirstName);
                cmd.Parameters.AddWithValue("@LastName", currentRecord.LastName);
            }

            var count = (int)await cmd.ExecuteScalarAsync();
            return count > 0;
        }
    }
} 