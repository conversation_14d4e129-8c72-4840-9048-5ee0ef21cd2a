{"name": "fleetxq-bulkimporter-frontend", "version": "1.0.0", "description": "Vue.js frontend for FleetXQ Bulk Importer", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@microsoft/signalr": "^7.0.11", "@vueuse/core": "^10.4.1", "axios": "^1.5.0", "bootstrap": "^5.3.2", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.17.17", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all": "^4.1.5", "prettier": "^3.0.3", "sass": "^1.69.0", "typescript": "~5.2.0", "vite": "^4.4.9", "vue-tsc": "^1.8.11"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}