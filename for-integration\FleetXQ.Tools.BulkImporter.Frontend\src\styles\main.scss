// Import Bootstrap with custom variables
@import "variables";

// Custom styles for FleetXQ Bulk Importer
body {
    font-family: $font-family-base;
    font-size: $font-size-base;
    line-height: $line-height-base;
    background-color: $white;
    color: $gray-700;
}

// Apply compact typography globally
h1,
.h1 {
    font-size: $h1-font-size;
}

h2,
.h2 {
    font-size: $h2-font-size;
}

h3,
.h3 {
    font-size: $h3-font-size;
}

h4,
.h4 {
    font-size: $h4-font-size;
}

h5,
.h5 {
    font-size: $h5-font-size;
}

h6,
.h6 {
    font-size: $h6-font-size;
}

// Compact form labels and small text
.form-label {
    font-size: $small-font-size;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.badge {
    font-size: $badge-font-size;
}

small,
.small {
    font-size: $small-font-size;
}

// Compact design utilities
.compact {
    padding: $compact-padding-y $compact-padding-x;
    margin: $compact-margin;
}

// Touch-friendly elements
.touch-target {
    min-height: $touch-target-size;
    min-width: $touch-target-size;
    padding: $touch-target-spacing;
}

// Minimalistic design utilities
.minimal-card {
    border: 1px solid $gray-200;
    background: $white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border-radius: $border-radius;

    .card-header {
        border-bottom: 1px solid $gray-200;
        background: $gray-100;
        color: $gray-700;
        padding: 0.75rem 1rem;

        .card-title {
            font-weight: 600;
            margin-bottom: 0;
            color: $gray-700;
            font-size: $h6-font-size;
        }
    }

    .card-body {
        padding: 1rem;
        background: $white;
    }
}

// Compact form styling
.form-compact {
    .form-group {
        margin-bottom: 0.75rem;
    }

    .form-label {
        font-size: $small-font-size;
        font-weight: 600;
        color: $dark;
        margin-bottom: 0.25rem;
    }

    .form-control,
    .form-select {
        font-size: $font-size-base;
        padding: $compact-padding-y $compact-padding-x;
        border: 1px solid $gray-300;
        border-radius: $border-radius;
        background-color: $white;

        &:focus {
            border-color: $gray-600;
            box-shadow: 0 0 0 0.1rem rgba(108, 117, 125, 0.1);
            background-color: $white;
        }
    }

    // Compact button styles
    .btn {
        font-size: $font-size-base;
        padding: $compact-padding-y $compact-padding-x;

        &.btn-sm {
            font-size: $btn-font-size-sm;
            padding: $btn-padding-y-sm $btn-padding-x-sm;
        }

        // Convert large buttons to standard size
        &.btn-lg {
            font-size: $font-size-base;
            padding: $compact-padding-y calc($compact-padding-x * 1.5);
        }
    }

    .form-text {
        font-size: $small-font-size;
        color: #6c757d;
        margin-top: 0.125rem;
    }
}

// Efficient spacing system
.space-y-1>*+* {
    margin-top: 0.25rem;
}

.space-y-2>*+* {
    margin-top: 0.5rem;
}

.space-y-3>*+* {
    margin-top: 1rem;
}

.space-y-4>*+* {
    margin-top: 1.5rem;
}

.space-x-1>*+* {
    margin-left: 0.25rem;
}

.space-x-2>*+* {
    margin-left: 0.5rem;
}

.space-x-3>*+* {
    margin-left: 1rem;
}

.space-x-4>*+* {
    margin-left: 1.5rem;
}

// Custom card styles
.card {
    border: $card-border-width;
    box-shadow: $card-box-shadow;
    border-radius: $card-border-radius;

    .card-header {
        background-color: $light;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    }
}

// Progress indicators
.progress-container {
    .progress {
        height: $progress-height;
        border-radius: $progress-border-radius;

        .progress-bar {
            transition: width 0.3s ease;
        }
    }

    .progress-text {
        font-size: 0.875rem;
        color: $dark;
        margin-top: 0.25rem;
    }
}

// Form enhancements
.form-control {
    border-radius: $input-border-radius;

    &:focus {
        border-color: $input-focus-border-color;
        box-shadow: $input-focus-box-shadow;
    }
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

// Button enhancements
.btn {
    border-radius: $btn-border-radius;
    font-weight: $btn-font-weight;

    &.btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

// Wizard-specific styles
.wizard-container {
    .wizard-step {
        padding: 1.25rem;

        &.active {
            background-color: $light;
            border-radius: $border-radius;
        }
    }

    .wizard-navigation {
        padding: 0.75rem;
        border-top: 1px solid rgba(0, 0, 0, 0.125);

        .btn {
            min-width: 80px;
            font-size: $font-size-base;
        }
    }
}

// Status indicators
.status-indicator {
    display: inline-flex;
    align-items: center;

    &::before {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    &.status-success::before {
        background-color: $gray-700;
    }

    &.status-warning::before {
        background-color: $gray-600;
    }

    &.status-danger::before {
        background-color: $gray-800;
    }

    &.status-info::before {
        background-color: $gray-500;
    }
}

// Loading states
.loading {
    opacity: 0.6;
    pointer-events: none;

    &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid $gray-600;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

// Responsive utilities
@media (max-width: 768px) {
    .wizard-container .wizard-step {
        padding: 0.75rem;
    }

    .btn-lg {
        padding: $compact-padding-y $compact-padding-x;
        font-size: $font-size-base;
    }

    .card-body {
        padding: 0.75rem;
    }

    // Mobile-specific compact layout
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    // Smaller form elements on mobile
    .form-control,
    .form-select {
        font-size: 16px; // Prevent zoom on iOS
        padding: $compact-padding-y $compact-padding-x;
    }

    // Stack navigation buttons on mobile
    .wizard-navigation {
        flex-direction: column;
        gap: 0.75rem;

        .btn {
            width: 100%;
            min-width: auto;
        }
    }

    // Compact alert spacing
    .alert {
        padding: $compact-padding-y $compact-padding-x;
        font-size: $font-size-base;
    }

    // Smaller badges and indicators
    .badge {
        font-size: $badge-font-size;
        padding: 0.125rem 0.375rem;
    }

    // Stack metrics on mobile
    .metric {
        flex-direction: column !important;
        align-items: flex-start !important;

        .metric-label {
            font-size: 0.8rem;
        }

        .metric-value {
            font-size: 1.1rem;
        }
    }
}

// Tablet-specific styles
@media (min-width: 768px) and (max-width: 1024px) {
    .wizard-step {
        padding: 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }
}

// Large screen optimizations
@media (min-width: 1200px) {
    .wizard-step {
        padding: 2.5rem;
    }

    .container-fluid {
        max-width: 1200px;
        margin: 0 auto;
    }
}

// High contrast mode support
@media (prefers-contrast: high) {
    .card {
        border: 1px solid $dark;
    }

    .btn {
        border-width: 2px;
    }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {

    .progress-bar,
    .loading::after {
        transition: none;
        animation: none;
    }
}