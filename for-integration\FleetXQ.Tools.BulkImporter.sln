Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Tools.BulkImporter.Core", "FleetXQ.Tools.BulkImporter.Core\FleetXQ.Tools.BulkImporter.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "FleetXQ.Tools.BulkImporter.WebApi", "FleetXQ.Tools.BulkImporter.WebApi\FleetXQ.Tools.BulkImporter.WebApi.csproj", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BulkImporter", "BulkImporter.csproj", "{C3D4E5F6-G7H8-9012-CDEF-************}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{D4E5F6G7-H8I9-0123-DEF0-456789012345}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yml = docker-compose.yml
		docker-compose.override.yml = docker-compose.override.yml
		README.md = README.md
		todo.md = todo.md
		DEPLOYMENT.md = DEPLOYMENT.md
		SETUP.md = SETUP.md
	EndProjectSection
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Frontend", "Frontend", "{E5F6G7H8-I9J0-1234-EF01-567890123456}"
	ProjectSection(SolutionItems) = preProject
		FleetXQ.Tools.BulkImporter.Frontend\package.json = FleetXQ.Tools.BulkImporter.Frontend\package.json
		FleetXQ.Tools.BulkImporter.Frontend\vite.config.ts = FleetXQ.Tools.BulkImporter.Frontend\vite.config.ts
		FleetXQ.Tools.BulkImporter.Frontend\tsconfig.json = FleetXQ.Tools.BulkImporter.Frontend\tsconfig.json
		FleetXQ.Tools.BulkImporter.Frontend\index.html = FleetXQ.Tools.BulkImporter.Frontend\index.html
		FleetXQ.Tools.BulkImporter.Frontend\Dockerfile = FleetXQ.Tools.BulkImporter.Frontend\Dockerfile
		FleetXQ.Tools.BulkImporter.Frontend\nginx.conf = FleetXQ.Tools.BulkImporter.Frontend\nginx.conf
	EndProjectSection
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Database", "Database", "{F6G7H8I9-J0K1-2345-F012-678901234567}"
	ProjectSection(SolutionItems) = preProject
		Sql\001-CreateStagingSchema.sql = Sql\001-CreateStagingSchema.sql
		Sql\002-CreateDriverStagingTable.sql = Sql\002-CreateDriverStagingTable.sql
		Sql\003-CreateVehicleStagingTable.sql = Sql\003-CreateVehicleStagingTable.sql
		Sql\004-CreateValidationProcedures.sql = Sql\004-CreateValidationProcedures.sql
		Sql\005-CreateMergeProcedures.sql = Sql\005-CreateMergeProcedures.sql
		Sql\006-CreateDataGenerationProcedures.sql = Sql\006-CreateDataGenerationProcedures.sql
		Sql\007-EnhanceModuleTracking.sql = Sql\007-EnhanceModuleTracking.sql
		Sql\008-UpdateStagingTablesForDealerScoping.sql = Sql\008-UpdateStagingTablesForDealerScoping.sql
		Sql\009-CreateModuleAvailabilityView.sql = Sql\009-CreateModuleAvailabilityView.sql
		Sql\010-CreateDealerValidationProcedures.sql = Sql\010-CreateDealerValidationProcedures.sql
	EndProjectSection
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
