using AspNetCoreRateLimit;
using FleetXQ.Tools.BulkImporter.Core.Configuration;
using FleetXQ.Tools.BulkImporter.WebApi.Hubs;
using FleetXQ.Tools.BulkImporter.WebApi.Middleware;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.OpenApi.Models;

namespace FleetXQ.Tools.BulkImporter.WebApi.Extensions;

/// <summary>
/// Extension methods for configuring services in the Web API
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Web API specific services to the container
    /// </summary>
    public static IServiceCollection AddBulkImporterWebApi(this IServiceCollection services, IConfiguration configuration)
    {
        // Add controllers
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.WriteIndented = true;
            });

        // Add API Explorer for Swagger
        services.AddEndpointsApiExplorer();

        // Add Swagger/OpenAPI
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "FleetXQ Bulk Importer API",
                Version = "v1",
                Description = "RESTful API for FleetXQ bulk import operations",
                Contact = new OpenApiContact
                {
                    Name = "FleetXQ Development Team",
                    Email = "<EMAIL>"
                }
            });

            // Include XML comments
            var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                c.IncludeXmlComments(xmlPath);
            }

            // Add JWT authentication to Swagger
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
        });

        // Add CORS
        services.AddCors(options =>
        {
            var corsSettings = configuration.GetSection("Cors");
            options.AddDefaultPolicy(builder =>
            {
                var allowedOrigins = corsSettings.GetSection("AllowedOrigins").Get<string[]>() ?? Array.Empty<string>();
                var allowedMethods = corsSettings.GetSection("AllowedMethods").Get<string[]>() ?? Array.Empty<string>();
                var allowedHeaders = corsSettings.GetSection("AllowedHeaders").Get<string[]>() ?? Array.Empty<string>();
                var allowCredentials = corsSettings.GetValue<bool>("AllowCredentials");

                if (allowedOrigins.Any())
                {
                    builder.WithOrigins(allowedOrigins);
                }
                else
                {
                    builder.AllowAnyOrigin();
                }

                if (allowedMethods.Any())
                {
                    builder.WithMethods(allowedMethods);
                }
                else
                {
                    builder.AllowAnyMethod();
                }

                if (allowedHeaders.Any())
                {
                    builder.WithHeaders(allowedHeaders);
                }
                else
                {
                    builder.AllowAnyHeader();
                }

                if (allowCredentials)
                {
                    builder.AllowCredentials();
                }
            });
        });

        // Add SignalR for real-time updates
        services.AddSignalR();

        // Add Authentication (JWT Bearer)
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                // JWT configuration will be added later
                options.RequireHttpsMetadata = false; // For development
                options.SaveToken = true;
            });

        // Add Authorization
        services.AddAuthorization();

        // Add Rate Limiting
        services.AddMemoryCache();
        services.Configure<IpRateLimitOptions>(configuration.GetSection("RateLimiting"));
        services.AddSingleton<IIpPolicyStore, MemoryCacheIpPolicyStore>();
        services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
        services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
        services.AddSingleton<IProcessingStrategy, AsyncKeyLockProcessingStrategy>();

        // Add Health Checks
        services.AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy())
            .AddSqlServer(
                configuration.GetConnectionString("FleetXQConnection") ?? string.Empty,
                name: "database",
                tags: new[] { "db", "sql", "sqlserver" });

        // Add HTTP Client for external services
        services.AddHttpClient();

        // Add custom middleware services
        services.AddScoped<ErrorHandlingMiddleware>();
        services.AddScoped<RequestLoggingMiddleware>();

        return services;
    }
}
