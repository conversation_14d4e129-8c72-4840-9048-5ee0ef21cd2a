export interface Dealer {
    id: number
    name: string
    subdomain: string
    subDomain: string // Alias for subdomain for backward compatibility
    isActive: boolean
    active: boolean // Alias for isActive for backward compatibility
    contactEmail?: string
    contactPhone?: string
    address?: DealerAddress
    createdAt: Date
    updatedAt: Date
}

export interface DealerAddress {
    street: string
    city: string
    state: string
    postalCode: string
    country: string
}

export interface DealerSearchFilter {
    query?: string
    isActive?: boolean
    limit?: number
    offset?: number
}

export interface DealerSearchResult {
    dealers: Dealer[]
    total: number
    totalCount: number
    pageNumber: number
    pageSize: number
    totalPages: number
    hasMore: boolean
}
