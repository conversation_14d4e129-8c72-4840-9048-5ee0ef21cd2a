using Microsoft.AspNetCore.Mvc;
using XQ360.DataMigration.Services;
using System.Text;

namespace XQ360.DataMigration.Web.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TemplateController : ControllerBase
    {
        private readonly ICsvFormatValidator _csvValidator;
        private readonly ILogger<TemplateController> _logger;

        public TemplateController(ICsvFormatValidator csvValidator, ILogger<TemplateController> logger)
        {
            _csvValidator = csvValidator;
            _logger = logger;
        }

        [HttpGet("download/{migrationType}")]
        public async Task<IActionResult> DownloadTemplate(string migrationType)
        {
            try
            {
                _logger.LogInformation($"Template download requested for: {migrationType}");
                
                var fileName = GetTemplateFileName(migrationType);
                if (string.IsNullOrEmpty(fileName))
                {
                    return BadRequest($"Unknown migration type: {migrationType}");
                }

                // Get the actual template file from CSV_Template folder
                var templatePath = GetTemplatePath(fileName);
                if (!System.IO.File.Exists(templatePath))
                {
                    return NotFound($"Template file not found: {fileName}");
                }

                var fileBytes = await System.IO.File.ReadAllBytesAsync(templatePath);
                return File(fileBytes, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error downloading template for {migrationType}");
                return StatusCode(500, "Error downloading template");
            }
        }

        [HttpGet("view/{migrationType}")]
        public async Task<IActionResult> ViewTemplate(string migrationType)
        {
            try
            {
                _logger.LogInformation($"Template view requested for: {migrationType}");
                
                var fileName = GetTemplateFileName(migrationType);
                if (string.IsNullOrEmpty(fileName))
                {
                    return BadRequest($"Unknown migration type: {migrationType}");
                }

                // Get template data from actual CSV file
                var templatePath = GetTemplatePath(fileName);
                if (!System.IO.File.Exists(templatePath))
                {
                    return NotFound($"Template file not found: {fileName}");
                }

                var (headers, sampleData) = await ReadTemplateDataAsync(templatePath);
                
                if (!headers.Any())
                {
                    return BadRequest($"Template file has no headers: {fileName}");
                }

                var templateInfo = new
                {
                    MigrationType = migrationType,
                    FileName = fileName,
                    Headers = headers,
                    SampleData = sampleData,
                    Description = GetTemplateDescription(migrationType)
                };

                return Ok(templateInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error viewing template for {migrationType}");
                return StatusCode(500, "Error viewing template");
            }
        }

        private string GetTemplateFileName(string migrationType)
        {
            return migrationType switch
            {
                "spare-modules" => "SPARE_MODEL_IMPORT_TEMPLATE.csv",
                "preop-checklist" => "PREOP_CHECKLIST_IMPORT.csv",
                "vehicles" => "VEHICLE_IMPORT.csv",
                "persons" => "PERSON_IMPORT_TEMPLATE.csv",
                "cards" => "CARD_IMPORT.csv",
                "supervisor-access" => "SUPERVISOR_ACCESS_IMPORT.csv",
                "driver-blacklist" => "DRIVER_BLACKLIST_IMPORT.csv",
                "website-users" => "WEBSITE_USER_IMPORT.csv",
                "sync-vehicle-settings" => "VEHICLE_IMPORT.csv",
                _ => $"{migrationType.ToUpper()}_IMPORT.csv"
            };
        }

        private string GetTemplatePath(string fileName)
        {
            var currentDirectory = Directory.GetCurrentDirectory();
            
            // Check if we're running from IIS deployment (XQ360Migration)
            if (currentDirectory.EndsWith("XQ360Migration"))
            {
                // When deployed to IIS, CSV_Template is in the same directory as the application
                return Path.Combine(currentDirectory, "CSV_Template", fileName);
            }
            else if (currentDirectory.EndsWith("XQ360.DataMigration.Web"))
            {
                // During development, navigate to the main migration project
                return Path.Combine(currentDirectory, "..", "XQ360.DataMigration", "CSV_Template", fileName);
            }
            else
            {
                // Fallback for other scenarios
                return Path.Combine(currentDirectory, "CSV_Template", fileName);
            }
        }

        private async Task<(List<string> headers, List<List<string>> sampleData)> ReadTemplateDataAsync(string templatePath)
        {
            var headers = new List<string>();
            var sampleData = new List<List<string>>();
            
            using var fileStream = new FileStream(templatePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var streamReader = new StreamReader(fileStream, Encoding.UTF8);
            
            // Read header line
            var headerLine = await streamReader.ReadLineAsync();
            if (!string.IsNullOrWhiteSpace(headerLine))
            {
                headers = headerLine.Split(',').Select(h => h.Trim().Trim('"')).ToList();
            }
            
            // Read up to 3 sample data rows
            string? dataLine;
            int rowCount = 0;
            while ((dataLine = await streamReader.ReadLineAsync()) != null && rowCount < 3)
            {
                if (!string.IsNullOrWhiteSpace(dataLine))
                {
                    var row = dataLine.Split(',').Select(cell => cell.Trim().Trim('"')).ToList();
                    sampleData.Add(row);
                    rowCount++;
                }
            }
            
            return (headers, sampleData);
        }

        private string GetTemplateDescription(string migrationType)
        {
            return migrationType switch
            {
                "spare-modules" => "Template for importing spare module/device information",
                "persons" => "Template for importing person records with access permissions",
                "cards" => "Template for importing access cards and reader configurations", 
                "vehicles" => "Template for importing vehicle/equipment information",
                "preop-checklist" => "Template for importing pre-operational checklist questions",
                "supervisor-access" => "Template for importing supervisor access permissions",
                "driver-blacklist" => "Template for importing driver blacklist records",
                "website-users" => "Template for importing website user accounts",
                "sync-vehicle-settings" => "Template for syncing vehicle device settings",
                _ => $"Template for {migrationType} migration"
            };
        }
    }
}