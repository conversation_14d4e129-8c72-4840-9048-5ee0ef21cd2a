import { ApiBaseService } from './api-base'

export interface DataValidationRequest {
  dealerId: string
  customerId: string
  driversCount: number
  vehiclesCount: number
  batchSize?: number
  environment?: string
}

export interface DataValidationResponse {
  isValid: boolean
  validationErrors: string[]
  warnings: string[]
  recommendations: string[]
  estimatedDuration: string
  resourceRequirements: {
    memoryUsageMB: number
    diskSpaceMB: number
    networkBandwidthKbps: number
  }
}

export interface DataPreviewRequest {
  dealerId: string
  customerId: string
  driversCount: number
  vehiclesCount: number
  sampleSize?: number
  includeRelationships?: boolean
}

export interface DataPreviewResponse {
  drivers: Array<{
    firstName: string
    lastName: string
    email: string
    phone: string
    licenseNumber: string
    dateOfBirth: string
  }>
  vehicles: Array<{
    make: string
    model: string
    year: number
    vin: string
    registrationNumber: string
    color: string
  }>
  relationships: Array<{
    driverName: string
    vehicleDescription: string
    assignmentType: string
  }>
  metadata: {
    generatedAt: string
    sampleSize: number
    totalDrivers: number
    totalVehicles: number
  }
}

export interface DataTemplate {
  name: string
  description: string
  fields: Array<{
    name: string
    type: string
    required: boolean
    description: string
    validation?: {
      minLength?: number
      maxLength?: number
      pattern?: string
      allowedValues?: string[]
    }
    example?: string
  }>
}

export interface DataTemplatesResponse {
  driverTemplate: DataTemplate
  vehicleTemplate: DataTemplate
  validationRules: {
    drivers: {
      maxCount: number
      requiredFields: string[]
      uniqueFields: string[]
    }
    vehicles: {
      maxCount: number
      requiredFields: string[]
      uniqueFields: string[]
    }
    relationships: {
      allowMultipleDriversPerVehicle: boolean
      allowMultipleVehiclesPerDriver: boolean
      requirePrimaryDriver: boolean
    }
  }
  supportedFormats: string[]
  maxBatchSize: number
  recommendedBatchSize: number
}

export interface GenerateDataRequest {
  dealerId: string
  customerId: string
  driversCount: number
  vehiclesCount: number
  format: 'json' | 'csv' | 'sql'
  options?: {
    includeHeaders?: boolean
    generateRelationships?: boolean
    useRealisticData?: boolean
    locale?: string
  }
}

export class DataGenerationService extends ApiBaseService {
  constructor() {
    super('/api')
  }

  /**
   * Validate data generation parameters
   */
  async validateParameters(request: DataValidationRequest): Promise<DataValidationResponse> {
    try {
      return await this.post<DataValidationResponse>('/data-generation/validate', request)
    } catch (error) {
      console.error('Failed to validate data generation parameters:', error)
      throw error
    }
  }

  /**
   * Generate preview data
   */
  async generatePreview(request: DataPreviewRequest): Promise<DataPreviewResponse> {
    try {
      return await this.post<DataPreviewResponse>('/data-generation/preview', request)
    } catch (error) {
      console.error('Failed to generate data preview:', error)
      throw error
    }
  }

  /**
   * Get data templates and schemas
   */
  async getTemplates(): Promise<DataTemplatesResponse> {
    try {
      return await this.get<DataTemplatesResponse>('/data-generation/templates')
    } catch (error) {
      console.error('Failed to fetch data templates:', error)
      throw error
    }
  }

  /**
   * Generate driver data
   */
  async generateDrivers(request: GenerateDataRequest): Promise<Blob> {
    try {
      const response = await this.client.post('/data-generation/drivers', request, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to generate driver data:', error)
      throw error
    }
  }

  /**
   * Generate vehicle data
   */
  async generateVehicles(request: GenerateDataRequest): Promise<Blob> {
    try {
      const response = await this.client.post('/data-generation/vehicles', request, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      console.error('Failed to generate vehicle data:', error)
      throw error
    }
  }

  /**
   * Get driver data preview
   */
  async getDriverPreview(dealerId: string, customerId: string, count: number = 5): Promise<any[]> {
    try {
      const params = new URLSearchParams({
        dealerId,
        customerId,
        count: count.toString()
      })

      return await this.get<any[]>(`/data-generation/preview/drivers?${params.toString()}`)
    } catch (error) {
      console.error('Failed to get driver preview:', error)
      throw error
    }
  }

  /**
   * Get vehicle data preview
   */
  async getVehiclePreview(dealerId: string, customerId: string, count: number = 5): Promise<any[]> {
    try {
      const params = new URLSearchParams({
        dealerId,
        customerId,
        count: count.toString()
      })

      return await this.get<any[]>(`/data-generation/preview/vehicles?${params.toString()}`)
    } catch (error) {
      console.error('Failed to get vehicle preview:', error)
      throw error
    }
  }

  /**
   * Validate generated data quality
   */
  async validateDataQuality(data: {
    drivers: any[]
    vehicles: any[]
  }): Promise<{
    isValid: boolean
    qualityScore: number
    issues: Array<{
      type: 'error' | 'warning' | 'info'
      field: string
      message: string
      count: number
    }>
    recommendations: string[]
  }> {
    try {
      return await this.post<any>('/data-generation/validate-quality', data)
    } catch (error) {
      console.error('Failed to validate data quality:', error)
      throw error
    }
  }

  /**
   * Get generation statistics
   */
  async getGenerationStats(): Promise<{
    totalGeneratedDrivers: number
    totalGeneratedVehicles: number
    averageGenerationTime: string
    popularConfigurations: Array<{
      driversCount: number
      vehiclesCount: number
      usage: number
    }>
  }> {
    try {
      return await this.get<any>('/data-generation/stats')
    } catch (error) {
      console.error('Failed to get generation statistics:', error)
      return {
        totalGeneratedDrivers: 0,
        totalGeneratedVehicles: 0,
        averageGenerationTime: '0s',
        popularConfigurations: []
      }
    }
  }

  /**
   * Get supported locales for data generation
   */
  async getSupportedLocales(): Promise<Array<{
    code: string
    name: string
    description: string
  }>> {
    try {
      return await this.get<any[]>('/data-generation/locales')
    } catch (error) {
      console.error('Failed to get supported locales:', error)
      return [
        { code: 'en-AU', name: 'English (Australia)', description: 'Australian names and addresses' },
        { code: 'en-US', name: 'English (United States)', description: 'US names and addresses' },
        { code: 'en-GB', name: 'English (United Kingdom)', description: 'UK names and addresses' }
      ]
    }
  }
}
