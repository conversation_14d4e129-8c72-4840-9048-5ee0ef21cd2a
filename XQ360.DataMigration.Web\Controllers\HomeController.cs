using Microsoft.AspNetCore.Mvc;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Models;
using System.Diagnostics;
using Microsoft.Extensions.Options;

namespace XQ360.DataMigration.Web.Controllers;

public class HomeController : Controller
{
    private readonly IMigrationService _migrationService;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly ILogger<HomeController> _logger;
    private readonly AuthenticationConfiguration _authConfig;

    public HomeController(IMigrationService migrationService, IEnvironmentConfigurationService environmentService, ILogger<HomeController> logger, IOptions<AuthenticationConfiguration> authConfig)
    {
        _migrationService = migrationService;
        _environmentService = environmentService;
        _logger = logger;
        _authConfig = authConfig.Value;
    }

    public IActionResult Index()
    {
        ViewBag.AuthBypassEnabled = _authConfig.BypassEnabled;
        return View();
    }

    [HttpPost]
    public async Task<IActionResult> TestEnvironment([FromForm] string environment)
    {
        try
        {
            if (string.IsNullOrEmpty(environment))
            {
                return Json(new { success = false, error = "Environment is required for testing" });
            }

            // Set the environment to test
            _environmentService.SetCurrentEnvironment(environment);
            var currentEnv = _environmentService.CurrentEnvironment;

            var testResults = new
            {
                success = true,
                environment = environment,
                environmentName = currentEnv.Name,
                tests = new[]
                {
                    await TestDatabaseConnectionAsync(currentEnv),
                    await TestApiConnectionAsync(currentEnv),
                    await TestMigrationDependenciesAsync()
                }
            };

            return Json(testResults);
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    [HttpGet]
    public IActionResult Test()
    {
        var model = new
        {
            Environments = _environmentService.GetAvailableEnvironments()
                .Select(e => new EnvironmentInfo
                {
                    Key = e.Key,
                    Name = e.DisplayName,
                    Description = e.Description ?? ""
                }).ToList()
        };
        return View(model);
    }

    [HttpGet]
    public IActionResult SignalRTest()
    {
        return View();
    }

    private async Task<object> TestDatabaseConnectionAsync(EnvironmentSettings env)
    {
        try
        {
            // Simulate async database connection test
            await Task.Delay(100); // Simulate network/DB check delay

            return new
            {
                name = "Database Connection",
                status = "✅ Pass",
                message = $"Successfully configured for: {env.Name}",
                details = $"Connection string configured for {env.DatabaseConnection.Split(';')[0]}"
            };
        }
        catch (Exception ex)
        {
            return new
            {
                name = "Database Connection",
                status = "❌ Fail",
                message = ex.Message
            };
        }
    }

    private async Task<object> TestApiConnectionAsync(EnvironmentSettings env)
    {
        try
        {
            // Simulate async API endpoint test
            await Task.Delay(50); // Simulate API connectivity check

            return new
            {
                name = "API Configuration",
                status = "✅ Pass",
                message = $"API endpoint configured: {env.ApiBaseUrl}",
                details = $"Username: {env.ApiUsername}"
            };
        }
        catch (Exception ex)
        {
            return new
            {
                name = "API Configuration",
                status = "❌ Fail",
                message = ex.Message
            };
        }
    }



    private async Task<object> TestMigrationDependenciesAsync()
    {
        try
        {
            // Simulate async dependency validation
            await Task.Delay(75); // Simulate service resolution time

            // Test if migration orchestrator can be created
            using var scope = HttpContext.RequestServices.CreateScope();
            var orchestrator = scope.ServiceProvider.GetService<XQ360.DataMigration.Services.MigrationOrchestrator>();

            return new
            {
                name = "Migration Services",
                status = orchestrator != null ? "✅ Pass" : "❌ Fail",
                message = orchestrator != null ? "Migration services properly configured" : "Migration services not available",
                details = "MigrationOrchestrator dependency injection check"
            };
        }
        catch (Exception ex)
        {
            return new
            {
                name = "Migration Services",
                status = "❌ Fail",
                message = ex.Message
            };
        }
    }

    private async Task<object> RunMigrationTestSuiteAsync()
    {
        try
        {
            _logger.LogInformation("Running XQ360.DataMigration.Tests suite...");

            var process = new System.Diagnostics.Process();
            var startInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = "dotnet",
                Arguments = "test ../XQ360.DataMigration.Tests/XQ360.DataMigration.Tests.csproj --verbosity quiet --logger console",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            process.StartInfo = startInfo;
            process.Start();

            string output = await process.StandardOutput.ReadToEndAsync();
            string error = await process.StandardError.ReadToEndAsync();

            await process.WaitForExitAsync();

            bool testsPassed = process.ExitCode == 0;
            string statusIcon = testsPassed ? "✅" : "❌";
            string status = testsPassed ? "Pass" : "Fail";

            return new
            {
                name = "Migration Test Suite",
                status = $"{statusIcon} {status}",
                message = testsPassed ?
                    "All migration tests passed successfully" :
                    "Some migration tests failed - check details",
                details = $"Exit Code: {process.ExitCode}\n{output}\n{error}".Trim()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running migration test suite");
            return new
            {
                name = "Migration Test Suite",
                status = "❌ Error",
                message = $"Failed to run test suite: {ex.Message}",
                details = ex.ToString()
            };
        }
    }



    public IActionResult MigrationSimple()
    {
        return View();
    }

    [HttpPost]
    public async Task<IActionResult> StartMigration([FromForm] string environment, [FromForm] string[] migrationTypes, [FromForm] bool testMode = false)
    {
        try
        {
            // Validate inputs
            if (string.IsNullOrEmpty(environment))
            {
                return Json(new { success = false, error = "Environment is required" });
            }

            if (migrationTypes == null || migrationTypes.Length == 0)
            {
                return Json(new { success = false, error = "At least one migration type must be selected" });
            }

            // Build migration request
            var request = new MigrationRequest
            {
                Environment = environment,
                MigrationTypes = migrationTypes.ToList(),
                CsvFiles = new Dictionary<string, IFormFile?>()
            };

            // Extract uploaded files from the request
            foreach (var migrationType in migrationTypes)
            {
                var fileKey = $"csvFiles[{migrationType}]";
                if (Request.Form.Files.Any(f => f.Name == fileKey))
                {
                    request.CsvFiles[migrationType] = Request.Form.Files[fileKey];
                }
            }

            // Set environment for both test and migration
            _environmentService.SetCurrentEnvironment(environment);
            var currentEnv = _environmentService.CurrentEnvironment;

            // Check if test mode - run tests first
            if (testMode)
            {
                _logger.LogInformation("Test Mode: Running pre-migration tests...");

                var configTests = new[]
                {
                    await TestDatabaseConnectionAsync(currentEnv),
                    await TestApiConnectionAsync(currentEnv),
                    await TestMigrationDependenciesAsync()
                };

                // Run XQ360.DataMigration.Tests project
                var migrationTestResult = await RunMigrationTestSuiteAsync();

                var allTests = configTests.Concat(new[] { migrationTestResult }).ToArray();

                // Check if all tests passed
                bool allTestsPassed = allTests.All(test =>
                {
                    var status = test.GetType().GetProperty("status")?.GetValue(test)?.ToString() ?? "";
                    return status.Contains("✅") || status.ToLower().Contains("pass");
                });

                if (!allTestsPassed)
                {
                    var failedTests = allTests.Where(test =>
                    {
                        var status = test.GetType().GetProperty("status")?.GetValue(test)?.ToString() ?? "";
                        return !status.Contains("✅") && !status.ToLower().Contains("pass");
                    }).ToArray();

                    return Json(new
                    {
                        success = false,
                        isTestMode = true,
                        error = "Pre-migration tests failed. Migration aborted for safety.",
                        environment = environment,
                        environmentName = currentEnv.Name,
                        tests = allTests,
                        failedTests = failedTests
                    });
                }

                _logger.LogInformation("All tests passed. Proceeding with migration...");

                // All tests passed, proceed with migration
                var migrationId = await _migrationService.StartMigrationAsync(request);
                return Json(new
                {
                    success = true,
                    migrationId,
                    testsRan = true,
                    testResults = allTests,
                    message = "Tests passed successfully. Migration started."
                });
            }
            else
            {
                // Skip tests, start migration directly
                _logger.LogInformation("Skipping tests. Starting migration directly...");
                var migrationId = await _migrationService.StartMigrationAsync(request);
                return Json(new { success = true, migrationId });
            }
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetProgress(string migrationId)
    {
        try
        {
            if (string.IsNullOrEmpty(migrationId))
            {
                return Json(new { success = false, error = "Migration ID is required" });
            }

            var progress = await _migrationService.GetProgressAsync(migrationId);
            if (progress == null)
            {
                return Json(new { success = false, error = "Migration not found" });
            }

            return Json(new { success = true, progress });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    public IActionResult Progress(string id)
    {
        ViewBag.MigrationId = id;
        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    public IActionResult UserManual()
    {
        return View();
    }

    [HttpGet]
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult GetReports(bool currentOnly = false)
    {
        try
        {
            // Check both Web Reports directory and main project Reports directory
            var webReportsPath = Path.Combine("Reports");
            var mainReportsPath = Path.Combine("..", "XQ360.DataMigration", "Reports");
            var reportsPath = Directory.Exists(webReportsPath) && Directory.GetFiles(webReportsPath, "*.txt").Any()
                ? webReportsPath
                : mainReportsPath;
            var fullReportsPath = Path.GetFullPath(reportsPath);

            _logger.LogInformation($"Looking for reports in: {fullReportsPath}");

            if (!Directory.Exists(reportsPath))
            {
                _logger.LogWarning($"Reports directory not found at: {fullReportsPath}");
                return Json(new { success = true, reports = new List<object>(), message = $"Reports directory not found at: {fullReportsPath}" });
            }

            var reportFiles = Directory.GetFiles(reportsPath, "*.txt")
                .Select(file => new
                {
                    fileName = Path.GetFileName(file),
                    displayName = Path.GetFileNameWithoutExtension(file).Replace("Full-Migration-Report-", ""),
                    filePath = file,
                    createdDate = System.IO.File.GetCreationTime(file),
                    size = new FileInfo(file).Length
                })
                .OrderByDescending(f => f.createdDate);

            // If currentOnly is true, return only the most recent report
            var finalReports = currentOnly ? reportFiles.Take(1).ToList() : reportFiles.Take(100).ToList();

            return Json(new { success = true, reports = finalReports });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    [HttpGet]
    public IActionResult DownloadReport(string fileName)
    {
        try
        {
            if (string.IsNullOrEmpty(fileName) || fileName.Contains("..") || !fileName.EndsWith(".txt"))
            {
                return BadRequest("Invalid file name");
            }

            // Check both Web Reports directory and main project Reports directory
            var webReportsPath = Path.Combine("Reports");
            var mainReportsPath = Path.Combine("..", "XQ360.DataMigration", "Reports");
            var reportsPath = Directory.Exists(webReportsPath) && Directory.GetFiles(webReportsPath, "*.txt").Any()
                ? webReportsPath
                : mainReportsPath;
            var filePath = Path.Combine(reportsPath, fileName);

            if (!System.IO.File.Exists(filePath))
            {
                return NotFound("Report file not found");
            }

            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            var contentType = "text/plain";

            return File(fileBytes, contentType, fileName);
        }
        catch (Exception ex)
        {
            return BadRequest($"Error downloading report: {ex.Message}");
        }
    }

    [HttpGet]
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult GetStepReport(string stepId)
    {
        try
        {
            if (string.IsNullOrEmpty(stepId))
            {
                return BadRequest("Step ID is required");
            }

            // Check both Web Reports directory and main project Reports directory
            var webReportsPath = Path.Combine("Reports");
            var mainReportsPath = Path.Combine("..", "XQ360.DataMigration", "Reports");
            var reportsPath = Directory.Exists(webReportsPath) && Directory.GetFiles(webReportsPath, "*.txt").Any()
                ? webReportsPath
                : mainReportsPath;

            if (!Directory.Exists(reportsPath))
            {
                return Json(new { success = false, error = "Reports directory not found" });
            }

            // Find the latest individual step report for the given stepId
            var candidatePatterns = new List<string>
            {
                $"Step-{stepId}-Report-*.txt"
            };
            // Handle historical naming differences
            if (string.Equals(stepId, "cards-and-vehicle-access", StringComparison.OrdinalIgnoreCase))
            {
                candidatePatterns.Add("Step-card-+-vehicle-access-Report-*.txt");
            }
            else if (string.Equals(stepId, "preop-checklist", StringComparison.OrdinalIgnoreCase))
            {
                // Include possible alternate formatting
                candidatePatterns.Add("Step-preop-checklist-Report-*.txt");
                candidatePatterns.Add("Step-department-checklist-+-preop-questions-Report-*.txt");
            }

            var stepReportFiles = candidatePatterns
                .SelectMany(pattern => Directory.GetFiles(reportsPath, pattern))
                .OrderByDescending(f => System.IO.File.GetCreationTime(f))
                .ToList();

            if (!stepReportFiles.Any())
            {
                return Json(new { success = false, error = $"No individual report found for step: {stepId}" });
            }

            var latestReportPath = stepReportFiles.First();
            var fileName = Path.GetFileName(latestReportPath);
            var reportContent = System.IO.File.ReadAllText(latestReportPath);

            var reportInfo = new
            {
                fileName = fileName,
                displayName = $"{stepId} - Individual Report",
                content = reportContent,
                createdDate = System.IO.File.GetCreationTime(latestReportPath),
                size = new FileInfo(latestReportPath).Length
            };

            return Json(new { success = true, report = reportInfo });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    [HttpGet]
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult ViewReport(string fileName)
    {
        try
        {
            if (string.IsNullOrEmpty(fileName) || fileName.Contains("..") || !fileName.EndsWith(".txt"))
            {
                return BadRequest("Invalid file name");
            }

            // Check both Web Reports directory and main project Reports directory
            var webReportsPath = Path.Combine("Reports");
            var mainReportsPath = Path.Combine("..", "XQ360.DataMigration", "Reports");
            var reportsPath = Directory.Exists(webReportsPath) && Directory.GetFiles(webReportsPath, "*.txt").Any()
                ? webReportsPath
                : mainReportsPath;
            var filePath = Path.Combine(reportsPath, fileName);

            if (!System.IO.File.Exists(filePath))
            {
                return NotFound("Report file not found");
            }

            var reportContent = System.IO.File.ReadAllText(filePath);
            var reportInfo = new
            {
                fileName = fileName,
                displayName = Path.GetFileNameWithoutExtension(fileName).Replace("Full-Migration-Report-", ""),
                content = reportContent,
                createdDate = System.IO.File.GetCreationTime(filePath),
                size = new FileInfo(filePath).Length
            };

            return Json(new { success = true, report = reportInfo });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, error = ex.Message });
        }
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }

    private List<MigrationTypeInfo> GetMigrationTypes()
    {
        return new List<MigrationTypeInfo>
        {
            new() { Key = "spare-modules", Name = "Spare Modules", Description = "Import spare module data" },
            new() { Key = "preop-checklist", Name = "Pre-Op Checklist", Description = "Import pre-operational checklist data" },
            new() { Key = "vehicles", Name = "Vehicles", Description = "Import vehicle information" },
            new() { Key = "persons", Name = "Persons", Description = "Import person/employee data" },
            new() { Key = "cards", Name = "Cards & Access", Description = "Import card and access control data" },
            new() { Key = "supervisor-access", Name = "Supervisor Access", Description = "Import supervisor access permissions" },
            new() { Key = "driver-blacklist", Name = "Driver Blacklist", Description = "Import driver blacklist data" },
            new() { Key = "website-users", Name = "Website Users", Description = "Import website user accounts" },
            new() { Key = "sync-vehicle-settings", Name = "Vehicle Settings Sync", Description = "Synchronize vehicle IoT device settings" }
        };
    }
}
