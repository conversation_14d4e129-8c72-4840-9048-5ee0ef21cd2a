# 🌐 XQ360 Data Migration Web Application - IIS Deployment Guide

This guide provides step-by-step instructions for manually deploying the XQ360 Data Migration web application to IIS.

## 📋 Prerequisites

- **Windows Server** with IIS installed
- **.NET 9.0 Runtime** installed on the server
- **Administrator access** to configure IIS
- **SQL Server** access for target environments

## 🚀 Manual Deployment Steps

### Step 1: Build the Application

```powershell
# Navigate to the project directory
cd C:\FleetXQ\XQ360.DataMigration

# Build the web application
dotnet build XQ360.DataMigration.Web -c Release
```

### Step 2: Prepare Deployment Directory

```powershell
# Create deployment directory
$deployPath = "C:\inetpub\wwwroot\XQ360Migration"
New-Item -ItemType Directory -Path $deployPath -Force

# Copy built files
Copy-Item "XQ360.DataMigration.Web\bin\Release\net9.0\*" -Destination $deployPath -Recurse -Force

# Copy production configuration
Copy-Item "deploy\production-appsettings.json" -Destination "$deployPath\appsettings.json" -Force
```

### Step 3: Configure IIS Application Pool

1. **Open IIS Manager**
2. **Create Application Pool:**
   - Name: `XQ360MigrationPool`
   - .NET CLR Version: `No Managed Code`
   - Managed Pipeline Mode: `Integrated`
   - Identity: `ApplicationPoolIdentity`

### Step 4: Create IIS Website

1. **Create Website:**
   - Site Name: `XQ360Migration`
   - Physical Path: `C:\inetpub\wwwroot\XQ360Migration`
   - Application Pool: `XQ360MigrationPool`
   - Port: `8080` (or your preferred port)

2. **Configure Bindings:**
   - Protocol: `http`
   - IP Address: `All Unassigned`
   - Port: `8080`
   - Host Name: (leave blank for default)

### Step 5: Set Permissions

```powershell
# Grant IIS_IUSRS access to the application directory
icacls "C:\inetpub\wwwroot\XQ360Migration" /grant "IIS_IUSRS:(OI)(CI)(RX)" /T
icacls "C:\inetpub\wwwroot\XQ360Migration" /grant "IIS_IUSRS:(OI)(CI)(M)" /T
```

### Step 6: Start the Website

```powershell
# Start the website
Start-Website -Name "XQ360Migration"
```

## ⚙️ Configuration

### Update Production Settings

Before deployment, update `deploy/production-appsettings.json` with your production credentials:

```json
{
  "ConnectionStrings": {
    "XQ360Database": "Server=your-production-server;Database=FleetXQProd;User Id=prod-user;Password=secure-password;TrustServerCertificate=true;"
  },
  "Migration": {
    "Environments": {
      "US": {
        "DatabaseConnection": "Server=us-fleetxqdb.database.windows.net,1433;Database=FleetXQ.US.Production;User Id=us-fleetxqdb;Password=YOUR_ACTUAL_PASSWORD;",
        "ApiBaseUrl": "https://us-api.xq360.com/",
        "ApiUsername": "us-migration-user",
        "ApiPassword": "YOUR_ACTUAL_API_PASSWORD"
      }
    }
  }
}
```

## 🔧 Troubleshooting

### Common Issues

1. **500.19 Error (Internal Server Error)**
   - Ensure .NET 9.0 Runtime is installed
   - Check application pool configuration
   - Verify file permissions

2. **404 Error (Not Found)**
   - Verify physical path is correct
   - Check website bindings
   - Ensure website is started

3. **Database Connection Errors**
   - Verify connection strings in `appsettings.json`
   - Check SQL Server accessibility
   - Ensure firewall allows database connections

### Log Files

Check these locations for error details:
- **IIS Logs**: `C:\inetpub\logs\LogFiles`
- **Application Logs**: `C:\inetpub\wwwroot\XQ360Migration\logs`

## 🌐 Access the Application

After successful deployment, access your application at:
```
http://localhost:8080
```

Or if deployed on a server:
```
http://your-server-name:8080
```

## 📋 Post-Deployment Verification

1. **Test Environment Selection**
   - Navigate to the web interface
   - Verify environment dropdown works
   - Test environment switching

2. **Test File Upload**
   - Upload a test CSV file
   - Verify file processing

3. **Test Migration Execution**
   - Run a test migration
   - Verify progress tracking
   - Check result reporting

## 🔄 Updating the Application

To update the deployed application:

```powershell
# Stop the website
Stop-Website -Name "XQ360Migration"

# Copy new files
Copy-Item "XQ360.DataMigration.Web\bin\Release\net9.0\*" -Destination "C:\inetpub\wwwroot\XQ360Migration" -Recurse -Force

# Start the website
Start-Website -Name "XQ360Migration"
```

## 📞 Support

For deployment issues:
1. Check IIS logs for detailed error messages
2. Verify all prerequisites are installed
3. Test database connectivity
4. Review application logs in the deployment directory 