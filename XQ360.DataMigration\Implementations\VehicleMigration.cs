using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Implementations
{
    public class VehicleMigration
    {
        private readonly ILogger<VehicleMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly MigrationReportingService _reportingService;
        private readonly string _connectionString;

        // Cache for database lookups to avoid repeated queries
        private readonly Dictionary<string, Guid> _siteCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _dealerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _customerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _departmentCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _modelCache = new Dictionary<string, Guid>();

        public VehicleMigration(
            ILogger<VehicleMigration> logger,
            IOptions<MigrationConfiguration> config,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _reportingService = reportingService;
            _connectionString = _config.DatabaseConnection;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Vehicle migration using direct SQL approach");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Validate data
                await ValidateDataAsync(data);

                // Step 3: Execute SQL migration
                result.RecordsProcessed = data.Count;
                var migrationResult = await ExecuteVehicleSqlAsync(data, result);

                // Step 4: Update Department Checklist assignments
                await UpdateDepartmentChecklistAssignmentsAsync();

                // Step 5: Update Module allocation
                await UpdateModuleAllocationAsync();

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;
                
                // Transfer detailed errors and warnings for Full Migration Report
                // Note: DetailedWarnings were already added directly to 'result' in ExecuteVehicleSqlAsync
                // so we merge instead of overwriting
                if (migrationResult.DetailedErrors?.Any() == true)
                    result.DetailedErrors.AddRange(migrationResult.DetailedErrors);
                if (migrationResult.DetailedWarnings?.Any() == true)
                    result.DetailedWarnings.AddRange(migrationResult.DetailedWarnings);

                _logger.LogInformation($"Vehicle migration completed: {result.RecordsInserted} inserted, {result.RecordsSkipped} skipped, Duration: {result.Duration}");

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Vehicle Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Vehicle migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private async Task<List<VehicleImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Vehicle CSV file...");

            // For VehicleMigration, we need to handle NULL replacements
            // Use streaming with limited content buffering for memory efficiency
            string csvContent;
            using (var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096, useAsync: true))
            using (var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8))
            {
                csvContent = await streamReader.ReadToEndAsync();
            }
            
            // Replace "NULL" strings with empty values for proper nullable field handling
            // This allows nullable integer fields (IdleTimer, QuestionTimeout, FullLockoutTimeout) to remain null
            // instead of being incorrectly set to 0
            csvContent = PreprocessCsvForNullValues(csvContent);

            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<VehicleImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return records;
        }

        /// <summary>
        /// Preprocess CSV content to replace "NULL" strings with empty values for proper nullable field parsing
        /// </summary>
        private string PreprocessCsvForNullValues(string csvContent)
        {
            try
            {
                // Replace standalone "NULL" values (surrounded by commas or at start/end of line) with empty strings
                // This handles cases like: ,NULL, -> ,, and ,NULL\n -> ,\n
                var processedContent = System.Text.RegularExpressions.Regex.Replace(
                    csvContent, 
                    @"(?<=^|,)\s*NULL\s*(?=,|$)", 
                    "", 
                    System.Text.RegularExpressions.RegexOptions.Multiline | System.Text.RegularExpressions.RegexOptions.IgnoreCase
                );
                
                _logger.LogDebug("Preprocessed Vehicle CSV content to replace NULL values with empty strings");
                return processedContent;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error preprocessing Vehicle CSV content, returning original content");
                return csvContent;
            }
        }

        private async Task ValidateDataAsync(List<VehicleImportModel> data)
        {
            _logger.LogInformation("Validating data before migration...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Note: Individual validation is now handled in the main processing loop
            // to allow graceful skipping of vehicles with missing dependencies

            _logger.LogInformation("Data validation completed successfully");
        }

        private async Task<MigrationResult> ExecuteVehicleSqlAsync(List<VehicleImportModel> data, MigrationResult sharedResult)
        {
            _logger.LogInformation("Executing Vehicle SQL migration...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                int insertedCount = 0;
                var warnings = new List<string>();

                foreach (var record in data)
                {
                    try
                    {
                        _logger.LogInformation($"Processing vehicle: {record.SerialNo} (Device ID: {record.DeviceID})");
                        
                        // Check if Device ID (Module) exists in the database
                    var checkModuleExistsSql = "SELECT COUNT(1) FROM dbo.Module WHERE IoTDevice = @DeviceID";
                    using var checkModuleExistsCmd = new SqlCommand(checkModuleExistsSql, connection, transaction);
                    checkModuleExistsCmd.Parameters.AddWithValue("@DeviceID", record.DeviceID);
                    var moduleExists = (int)(await checkModuleExistsCmd.ExecuteScalarAsync() ?? 0) > 0;

                    if (!moduleExists)
                    {
                        _logger.LogWarning($"Device ID '{record.DeviceID}' not found in database, skipping vehicle '{record.SerialNo}'");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT,
                            $"Device ID '{record.DeviceID}' not found in database",
                            "DeviceID", record.DeviceID,
                            "Check if Module migration was run first, or verify Device ID exists",
                            new Dictionary<string, string> 
                            {
                                { "SerialNo", record.SerialNo ?? "Unknown" },
                                { "Site", record.Site ?? "Unknown" },
                                { "Customer", record.Customer ?? "Unknown" }
                            });
                        warnings.Add($"Device ID '{record.DeviceID}' not found in database - skipped vehicle '{record.SerialNo}'");
                        continue;
                    }

                    // Check if Device ID (Module) is already assigned to another Vehicle
                    var checkModuleSql = "SELECT COUNT(1) FROM dbo.Vehicle WHERE ModuleId1 = (SELECT Id FROM dbo.Module WHERE IoTDevice = @DeviceID)";
                    using var checkModuleCmd = new SqlCommand(checkModuleSql, connection, transaction);
                    checkModuleCmd.Parameters.AddWithValue("@DeviceID", record.DeviceID);
                    var moduleAssigned = (int)(await checkModuleCmd.ExecuteScalarAsync() ?? 0) > 0;

                    if (moduleAssigned)
                    {
                        _logger.LogWarning($"Device ID '{record.DeviceID}' is already assigned to another vehicle, skipping vehicle '{record.SerialNo}'");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, WarningTypes.EXISTING_RECORD_SKIPPED,
                            $"Device ID '{record.DeviceID}' is already assigned to another vehicle",
                            "DeviceID", record.DeviceID,
                            "Assign a different Device ID or check existing vehicle assignments",
                            new Dictionary<string, string> 
                            {
                                { "SerialNo", record.SerialNo ?? "Unknown" },
                                { "Site", record.Site ?? "Unknown" },
                                { "Customer", record.Customer ?? "Unknown" }
                            });
                        warnings.Add($"Device ID '{record.DeviceID}' already assigned to another vehicle - skipped vehicle '{record.SerialNo}'");
                        continue;
                    }

                    // Generate IDs
                    var vehicleId = Guid.NewGuid();
                    var checklistId = Guid.NewGuid();
                    var otherSettingsId = Guid.NewGuid();

                    // Get lookup IDs with error handling
                    Guid siteId, customerId, departmentId, modelId, moduleId, canruleId;
                    
                    try
                    {
                        siteId = await GetSiteIdAsync(record.Site ?? "Unknown", record.Customer ?? "Unknown", connection, transaction);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Site lookup failed for vehicle '{record.SerialNo}': {ex.Message}");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT,
                            $"Site '{record.Site}' not found for customer '{record.Customer}'",
                            "Site", record.Site,
                            "Check if Site exists for the specified Customer, or run dependent migrations first",
                            new Dictionary<string, string> 
                            {
                                { "SerialNo", record.SerialNo ?? "Unknown" },
                                { "Customer", record.Customer ?? "Unknown" },
                                { "Dealer", record.Dealer ?? "Unknown" }
                            });
                        warnings.Add($"Site '{record.Site}' not found for customer '{record.Customer}' - skipped vehicle '{record.SerialNo}'");
                        continue;
                    }
                    
                    try
                    {
                        customerId = await GetCustomerIdAsync(record.Customer ?? "Unknown", record.Dealer ?? "Unknown", connection, transaction);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Customer lookup failed for vehicle '{record.SerialNo}': {ex.Message}");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT,
                            $"Customer '{record.Customer}' not found for dealer '{record.Dealer}'",
                            "Customer", record.Customer,
                            "Check if Customer exists for the specified Dealer, or verify Customer name spelling",
                            new Dictionary<string, string> 
                            {
                                { "SerialNo", record.SerialNo },
                                { "Dealer", record.Dealer ?? "Unknown" },
                                { "Site", record.Site ?? "Unknown" }
                            });
                        warnings.Add($"Customer '{record.Customer}' not found for dealer '{record.Dealer}' - skipped vehicle '{record.SerialNo}'");
                        continue;
                    }
                    
                    try
                    {
                        departmentId = await GetDepartmentIdAsync(record.DepartmentName ?? "Unknown", record.Site ?? "Unknown", record.Customer ?? "Unknown", connection, transaction);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Department lookup failed for vehicle '{record.SerialNo}': {ex.Message}");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT,
                            $"Department '{record.DepartmentName}' not found",
                            "DepartmentName", record.DepartmentName,
                            "Check if Department exists for the specified Site/Customer, or verify Department name spelling",
                            new Dictionary<string, string> 
                            {
                                { "SerialNo", record.SerialNo ?? "Unknown" },
                                { "Site", record.Site ?? "Unknown" },
                                { "Customer", record.Customer ?? "Unknown" }
                            });
                        warnings.Add($"Department '{record.DepartmentName}' not found - skipped vehicle '{record.SerialNo}'");
                        continue;
                    }
                    
                    try
                    {
                        modelId = await GetModelIdAsync(record.ModelName ?? "Unknown", record.Dealer ?? "Unknown", connection, transaction);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Model lookup failed for vehicle '{record.SerialNo}': {ex.Message}");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT,
                            $"Model '{record.ModelName}' not found for dealer '{record.Dealer}'",
                            "ModelName", record.ModelName,
                            "Check if Model exists for the specified Dealer, or verify Model name spelling",
                            new Dictionary<string, string> 
                            {
                                { "SerialNo", record.SerialNo },
                                { "Dealer", record.Dealer ?? "Unknown" },
                                { "DeviceID", record.DeviceID ?? "Unknown" }
                            });
                        warnings.Add($"Model '{record.ModelName}' not found for dealer '{record.Dealer}' - skipped vehicle '{record.SerialNo}'");
                        continue;
                    }
                    
                    try
                    {
                        moduleId = await GetModuleIdAsync(record.DeviceID ?? "Unknown", connection, transaction);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Module lookup failed for vehicle '{record.SerialNo}': {ex.Message}");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT,
                            $"Module with Device ID '{record.DeviceID}' not found",
                            "DeviceID", record.DeviceID,
                            "Check if Module migration was run first, or verify Device ID spelling",
                            new Dictionary<string, string> 
                            {
                                { "SerialNo", record.SerialNo ?? "Unknown" },
                                { "ModelName", record.ModelName ?? "Unknown" },
                                { "Site", record.Site ?? "Unknown" }
                            });
                        warnings.Add($"Module with Device ID '{record.DeviceID}' not found - skipped vehicle '{record.SerialNo}'");
                        continue;
                    }
                    
                    try
                    {
                        canruleId = await GetCanruleIdAsync(record.CanruleName ?? "Unknown", connection, transaction);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Canrule lookup failed for vehicle '{record.SerialNo}': {ex.Message}");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT,
                            $"Canrule '{record.CanruleName}' not found",
                            "CanruleName", record.CanruleName ?? "Unknown",
                            "Check if Canrule exists in the system, or verify Canrule name spelling",
                            new Dictionary<string, string> 
                            {
                                { "SerialNo", record.SerialNo ?? "Unknown" },
                                { "ModelName", record.ModelName ?? "Unknown" },
                                { "DeviceID", record.DeviceID ?? "Unknown" }
                            });
                        warnings.Add($"Canrule '{record.CanruleName}' not found - skipped vehicle '{record.SerialNo}'");
                        continue;
                    }

                    // Insert ChecklistSettings first
                    var insertChecklistSql = @"
                        INSERT INTO [dbo].[ChecklistSettings] (
                            [Id], [QuestionTimeout], [ShowComment], [Randomisation], [Type],
                            [TimeslotOne], [TimeslotTwo], [TimeslotThree], [TimeslotFour]
                        )
                        VALUES (
                            @Id, @QuestionTimeout, @ShowComment, @Randomisation, @Type,
                            @TimeslotOne, @TimeslotTwo, @TimeslotThree, @TimeslotFour
                        )";

                    using var checklistCmd = new SqlCommand(insertChecklistSql, connection, transaction);
                    checklistCmd.Parameters.AddWithValue("@Id", checklistId);
                    checklistCmd.Parameters.AddWithValue("@QuestionTimeout", record.QuestionTimeout ?? (object)DBNull.Value);
                    checklistCmd.Parameters.AddWithValue("@ShowComment", record.ShowComment);
                    checklistCmd.Parameters.AddWithValue("@Randomisation", record.Randomisation);
                    
                    // Set checklist type based on ChecklistType field
                    int checklistType;
                    if (record.ChecklistType == "Time Based")
                    {
                        checklistType = 2; // Time Based = 2
                        _logger.LogInformation($"Vehicle {record.SerialNo}: Using Time Based checklist (Type=2)");
                    }
                    else if (record.ChecklistType == "Driver Based")
                    {
                        checklistType = 3; // Driver Based = 3
                        _logger.LogInformation($"Vehicle {record.SerialNo}: Using Driver Based checklist (Type=3)");
                    }
                    else
                    {
                        checklistType = 2; // Default/Other = 2 (same as Time Based)
                        _logger.LogInformation($"Vehicle {record.SerialNo}: Using default checklist type (Type=2) for '{record.ChecklistType}'");
                        warnings.Add($"Vehicle '{record.SerialNo}': Unknown checklist type '{record.ChecklistType}' - defaulted to Type=2");
                    }
                    
                    checklistCmd.Parameters.AddWithValue("@Type", checklistType);
                    
                    // Time slots - for Time Based checklists and unknown types (now defaulted to Type=2), use actual time slot values
                    if (record.ChecklistType == "Time Based" || (record.ChecklistType != "Driver Based"))
                    {
                        checklistCmd.Parameters.AddWithValue("@TimeslotOne", string.IsNullOrEmpty(record.Timeslot1) ? (object)DBNull.Value : record.Timeslot1);
                        checklistCmd.Parameters.AddWithValue("@TimeslotTwo", string.IsNullOrEmpty(record.Timeslot2) ? (object)DBNull.Value : record.Timeslot2);
                        checklistCmd.Parameters.AddWithValue("@TimeslotThree", string.IsNullOrEmpty(record.Timeslot3) ? (object)DBNull.Value : record.Timeslot3);
                        checklistCmd.Parameters.AddWithValue("@TimeslotFour", string.IsNullOrEmpty(record.Timeslot4) ? (object)DBNull.Value : record.Timeslot4);
                        _logger.LogInformation($"Vehicle {record.SerialNo}: Time slots - 1:{record.Timeslot1}, 2:{record.Timeslot2}, 3:{record.Timeslot3}, 4:{record.Timeslot4}");
                    }
                    else
                    {
                        // For Driver Based checklists, set time slots to NULL
                        checklistCmd.Parameters.AddWithValue("@TimeslotOne", DBNull.Value);
                        checklistCmd.Parameters.AddWithValue("@TimeslotTwo", DBNull.Value);
                        checklistCmd.Parameters.AddWithValue("@TimeslotThree", DBNull.Value);
                        checklistCmd.Parameters.AddWithValue("@TimeslotFour", DBNull.Value);
                    }

                    await checklistCmd.ExecuteNonQueryAsync();

                    // Insert VehicleOtherSettings
                    var insertOtherSettingsSql = @"
                        INSERT INTO [dbo].[VehicleOtherSettings] (
                            [Id], [FullLockoutTimeout], [VORStatus], [AmberAlertEnabled],
                            [VORStatusConfirmed], [FullLockout], [DefaultTechnicianAccess], [PedestrianSafety]
                        )
                        VALUES (
                            @Id, @FullLockoutTimeout, @VORStatus, @AmberAlertEnabled,
                            @VORStatusConfirmed, @FullLockout, @DefaultTechnicianAccess, @PedestrianSafety
                        )";

                    using var otherSettingsCmd = new SqlCommand(insertOtherSettingsSql, connection, transaction);
                    otherSettingsCmd.Parameters.AddWithValue("@Id", otherSettingsId);
                    otherSettingsCmd.Parameters.AddWithValue("@FullLockoutTimeout", record.FullLockoutTimeout ?? (object)DBNull.Value);
                    otherSettingsCmd.Parameters.AddWithValue("@VORStatus", record.VORStatus);
                    otherSettingsCmd.Parameters.AddWithValue("@AmberAlertEnabled", record.AmberAlertEnabled);
                    otherSettingsCmd.Parameters.AddWithValue("@VORStatusConfirmed", record.VORStatusConfirmed);
                    otherSettingsCmd.Parameters.AddWithValue("@FullLockout", record.FullLockout);
                    otherSettingsCmd.Parameters.AddWithValue("@DefaultTechnicianAccess", record.DefaultTechnicianAccess);
                    otherSettingsCmd.Parameters.AddWithValue("@PedestrianSafety", record.PedestrianSafety);

                    await otherSettingsCmd.ExecuteNonQueryAsync();

                    // Insert Vehicle
                    var insertVehicleSql = @"
                        INSERT INTO [dbo].[Vehicle] (
                            [Id], [SerialNo], [HireNo], [IDLETimer], [ModuleIsConnected], [OnHire], 
                            [ImpactLockout], [TimeoutEnabled], [IsCanbus], [HireTime],
                            [ModelId], [SiteId], [DepartmentId], [ModuleId1], [CustomerId],
                            [ChecklistSettingsId], [VehicleOtherSettingsId], [CanruleId]
                        )
                        VALUES (
                            @Id, @SerialNo, @HireNo, @IDLETimer, @ModuleIsConnected, @OnHire,
                            @ImpactLockout, @TimeoutEnabled, @IsCanbus, @HireTime,
                            @ModelId, @SiteId, @DepartmentId, @ModuleId1, @CustomerId,
                            @ChecklistSettingsId, @VehicleOtherSettingsId, @CanruleId
                        )";

                    using var vehicleCmd = new SqlCommand(insertVehicleSql, connection, transaction);
                    vehicleCmd.Parameters.AddWithValue("@Id", vehicleId);
                    vehicleCmd.Parameters.AddWithValue("@SerialNo", record.SerialNo);
                    vehicleCmd.Parameters.AddWithValue("@HireNo", record.HireNo);
                    vehicleCmd.Parameters.AddWithValue("@IDLETimer", record.IdleTimer ?? (object)DBNull.Value);
                    vehicleCmd.Parameters.AddWithValue("@ModuleIsConnected", 0); // Always 0 as per specification
                    vehicleCmd.Parameters.AddWithValue("@OnHire", record.OnHire);
                    vehicleCmd.Parameters.AddWithValue("@ImpactLockout", record.ImpactLockout);
                    vehicleCmd.Parameters.AddWithValue("@TimeoutEnabled", record.TimeoutEnabled);
                    vehicleCmd.Parameters.AddWithValue("@IsCanbus", record.IsCanbus);
                    vehicleCmd.Parameters.AddWithValue("@HireTime", DateTime.UtcNow);
                    vehicleCmd.Parameters.AddWithValue("@ModelId", modelId);
                    vehicleCmd.Parameters.AddWithValue("@SiteId", siteId);
                    vehicleCmd.Parameters.AddWithValue("@DepartmentId", departmentId);
                    vehicleCmd.Parameters.AddWithValue("@ModuleId1", moduleId);
                    vehicleCmd.Parameters.AddWithValue("@CustomerId", customerId);
                    vehicleCmd.Parameters.AddWithValue("@ChecklistSettingsId", checklistId);
                    vehicleCmd.Parameters.AddWithValue("@VehicleOtherSettingsId", otherSettingsId);
                    vehicleCmd.Parameters.AddWithValue("@CanruleId", canruleId);

                    await vehicleCmd.ExecuteNonQueryAsync();

                    insertedCount++;
                    _logger.LogInformation($"Successfully created vehicle: {record.SerialNo}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Unexpected error processing vehicle '{record.SerialNo}': {ex.Message}");
                        warnings.Add($"Unexpected error processing vehicle '{record.SerialNo}': {ex.Message}");
                    }
                }

                await transaction.CommitAsync();

                _logger.LogInformation($"Successfully inserted {insertedCount} vehicles");
                
                if (warnings.Count > 0)
                {
                    var moduleAssignedWarnings = warnings.Count(w => w.Contains("already assigned"));
                    var moduleNotFoundWarnings = warnings.Count(w => w.Contains("not found in database"));
                    var lookupFailureWarnings = warnings.Count(w => w.Contains("not found") && !w.Contains("not found in database") && !w.Contains("Unexpected error"));
                    var typeWarnings = warnings.Count(w => w.Contains("Unknown checklist type"));
                    var unexpectedErrorWarnings = warnings.Count(w => w.Contains("Unexpected error"));
                    
                    if (moduleAssignedWarnings > 0)
                    {
                        _logger.LogInformation($"Skipped {moduleAssignedWarnings} vehicles due to already assigned modules");
                    }
                    
                    if (moduleNotFoundWarnings > 0)
                    {
                        _logger.LogInformation($"Skipped {moduleNotFoundWarnings} vehicles due to missing modules");
                    }
                    
                    if (lookupFailureWarnings > 0)
                    {
                        _logger.LogInformation($"Skipped {lookupFailureWarnings} vehicles due to missing dependencies (Site/Customer/Department/Model/Canrule)");
                    }
                    
                    if (unexpectedErrorWarnings > 0)
                    {
                        _logger.LogInformation($"Skipped {unexpectedErrorWarnings} vehicles due to unexpected errors");
                    }
                    
                    if (typeWarnings > 0)
                    {
                        _logger.LogInformation($"Defaulted {typeWarnings} vehicles to Type=2 due to unknown checklist types");
                    }
                }

                return new MigrationResult
                {
                    Success = true,
                    RecordsInserted = insertedCount,
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Vehicle SQL migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<Guid> GetSiteIdAsync(string siteName, string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{siteName}|{customerName}";
            if (_siteCache.ContainsKey(key))
                return _siteCache[key];

            var customerId = await GetCustomerIdByNameAsync(customerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Site WHERE Name = @Name AND CustomerId = @CustomerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", siteName);
            cmd.Parameters.AddWithValue("@CustomerId", customerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Site '{siteName}' not found for customer '{customerName}'");

            var siteId = (Guid)result;
            _siteCache[key] = siteId;
            return siteId;
        }

        private async Task<Guid> GetDealerIdAsync(string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            if (_dealerCache.ContainsKey(dealerName))
                return _dealerCache[dealerName];

            var sql = "SELECT Id FROM dbo.Dealer WHERE Name = @Name";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", dealerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Dealer '{dealerName}' not found");

            var dealerId = (Guid)result;
            _dealerCache[dealerName] = dealerId;
            return dealerId;
        }

        private async Task<Guid> GetCustomerIdAsync(string customerName, string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{customerName}|{dealerName}";
            if (_customerCache.ContainsKey(key))
                return _customerCache[key];

            var dealerId = await GetDealerIdAsync(dealerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName AND DealerId = @DealerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);
            cmd.Parameters.AddWithValue("@DealerId", dealerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found for dealer '{dealerName}'");

            var customerId = (Guid)result;
            _customerCache[key] = customerId;
            return customerId;
        }

        private async Task<Guid> GetCustomerIdByNameAsync(string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found");

            return (Guid)result;
        }

        private async Task<Guid> GetDepartmentIdAsync(string departmentName, string siteName, string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{departmentName}|{siteName}|{customerName}";
            if (_departmentCache.ContainsKey(key))
                return _departmentCache[key];

            var siteId = await GetSiteIdAsync(siteName, customerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Department WHERE Name = @Name AND SiteId = @SiteId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", departmentName);
            cmd.Parameters.AddWithValue("@SiteId", siteId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Department '{departmentName}' not found in site '{siteName}' for customer '{customerName}'");

            var departmentId = (Guid)result;
            _departmentCache[key] = departmentId;
            return departmentId;
        }

        private async Task<Guid> GetModelIdAsync(string modelName, string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{modelName}|{dealerName}";
            if (_modelCache.ContainsKey(key))
                return _modelCache[key];

            var dealerId = await GetDealerIdAsync(dealerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Model WHERE Name = @Name AND DealerId = @DealerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", modelName);
            cmd.Parameters.AddWithValue("@DealerId", dealerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Model '{modelName}' not found for dealer '{dealerName}'");

            var modelId = (Guid)result;
            _modelCache[key] = modelId;
            return modelId;
        }

        private async Task<Guid> GetModuleIdAsync(string deviceId, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var sql = "SELECT Id FROM dbo.Module WHERE IoTDevice = @IoTDevice";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@IoTDevice", deviceId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Module with IoTDevice '{deviceId}' not found");

            return (Guid)result;
        }

        private async Task<Guid> GetCanruleIdAsync(string canruleName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var sql = "SELECT Id FROM dbo.Canrule WHERE Name = @Name";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", canruleName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Canrule '{canruleName}' not found");

            return (Guid)result;
        }

        private async Task UpdateDepartmentChecklistAssignmentsAsync()
        {
            _logger.LogInformation("Updating Department Checklist assignments for vehicles...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                var sql = @"
                    UPDATE v
                    SET v.DepartmentChecklistId = dc.Id
                    FROM [dbo].[Vehicle] v
                    JOIN [dbo].[DepartmentChecklist] dc 
                        ON v.ModelId = dc.ModelId AND v.DepartmentId = dc.DepartmentId
                    WHERE 
                        (v.DepartmentChecklistId IS NULL OR v.DepartmentChecklistId != dc.Id);";

                using var cmd = new SqlCommand(sql, connection, transaction);
                var rowsAffected = await cmd.ExecuteNonQueryAsync();

                await transaction.CommitAsync();

                _logger.LogInformation($"Updated {rowsAffected} vehicle department checklist assignments");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to update department checklist assignments");
                throw;
            }
        }

        private async Task UpdateModuleAllocationAsync()
        {
            _logger.LogInformation("Updating Module allocation status...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                // Update allocation status for modules related to vehicles
                var sql = @"
                    UPDATE m
                    SET 
                        IsAllocatedToVehicle = 1,
                        Status = 2
                    FROM [dbo].[Module] m
                    INNER JOIN [dbo].[Vehicle] v ON m.Id = v.ModuleId1
                    WHERE v.ModuleId1 IS NOT NULL;";

                using var cmd = new SqlCommand(sql, connection, transaction);
                var rowsAffected = await cmd.ExecuteNonQueryAsync();

                // Handle null Calibration values
                var calibrationSql = @"
                    UPDATE [dbo].[Module]
                    SET Calibration = 0
                    WHERE Calibration IS NULL;";

                using var calibrationCmd = new SqlCommand(calibrationSql, connection, transaction);
                await calibrationCmd.ExecuteNonQueryAsync();

                // Handle null BlueImpact values
                var blueImpactSql = @"
                    UPDATE [dbo].[Module]
                    SET BlueImpact = 0
                    WHERE BlueImpact IS NULL;";

                using var blueImpactCmd = new SqlCommand(blueImpactSql, connection, transaction);
                await blueImpactCmd.ExecuteNonQueryAsync();

                await transaction.CommitAsync();

                _logger.LogInformation($"Updated {rowsAffected} module allocation statuses");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to update module allocation");
                throw;
            }
        }
    }
} 