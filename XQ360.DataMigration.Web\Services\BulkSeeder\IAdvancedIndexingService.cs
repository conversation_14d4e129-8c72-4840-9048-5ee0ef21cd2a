using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service interface for advanced indexing operations
/// Implementation of Phase 3.2.1: Advanced Indexing Strategy
/// </summary>
public interface IAdvancedIndexingService
{
    /// <summary>
    /// Creates advanced indexes including covering indexes and filtered indexes
    /// </summary>
    /// <param name="options">Indexing strategy options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Indexing optimization result</returns>
    Task<IndexingOptimizationResult> CreateAdvancedIndexesAsync(
        IndexingStrategyOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs index maintenance including rebuild and reorganize operations
    /// </summary>
    /// <param name="options">Index maintenance options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Index maintenance result</returns>
    Task<IndexMaintenanceResult> PerformIndexMaintenanceAsync(
        IndexMaintenanceOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyzes index usage statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of index usage statistics</returns>
    Task<List<IndexUsageStatistic>> AnalyzeIndexUsageAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Analyzes index fragmentation levels
    /// </summary>
    /// <param name="fragmentationThreshold">Fragmentation threshold percentage</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of index fragmentation information</returns>
    Task<List<IndexFragmentationInfo>> AnalyzeIndexFragmentationAsync(
        double fragmentationThreshold = 10.0,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates optimization suggestions for indexes
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Index optimization suggestions</returns>
    Task<IndexOptimizationSuggestions> GenerateOptimizationSuggestionsAsync(
        CancellationToken cancellationToken = default);
}
