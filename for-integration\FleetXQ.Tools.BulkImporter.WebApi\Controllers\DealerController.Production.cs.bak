using Microsoft.AspNetCore.Mvc;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;

namespace FleetXQ.Tools.BulkImporter.WebApi.Controllers;

/// <summary>
/// Production-ready controller for dealer management operations in the bulk importer
/// This version integrates with the full FleetXQ data layer and business components
/// </summary>
[ApiController]
[Route("api/dealer-production")]
[Produces("application/json")]
public class DealerProductionController : ControllerBase
{
    private readonly IDataFacade _dataFacade;
    private readonly ILogger<DealerProductionController> _logger;

    public DealerProductionController(
        IDataFacade dataFacade,
        ILogger<DealerProductionController> logger)
    {
        _dataFacade = dataFacade ?? throw new ArgumentNullException(nameof(dataFacade));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Gets a list of dealers with optional filtering and pagination
    /// </summary>
    /// <param name="query">Optional search query to filter dealers by name or subdomain</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 100)</param>
    /// <param name="activeOnly">Whether to return only active dealers (default: true)</param>
    /// <returns>List of dealers matching the criteria</returns>
    /// <response code="200">Returns the list of dealers</response>
    /// <response code="400">If the request parameters are invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(DealerListResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DealerListResponse>> GetDealers(
        [FromQuery] string? query = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            // Validate parameters
            if (pageNumber < 1)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page number",
                    Detail = "Page number must be greater than 0",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page size",
                    Detail = "Page size must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Getting dealers with query: {Query}, page: {PageNumber}, size: {PageSize}, activeOnly: {ActiveOnly}",
                query, pageNumber, pageSize, activeOnly);

            // Build filter predicate
            var filterPredicate = activeOnly ? "Active == true" : "";
            var filterArguments = new List<object>();

            if (!string.IsNullOrWhiteSpace(query))
            {
                var searchFilter = "(Name.Contains(@{0}) OR SubDomain.Contains(@{1}))";
                if (!string.IsNullOrEmpty(filterPredicate))
                {
                    filterPredicate += " AND " + searchFilter;
                }
                else
                {
                    filterPredicate = searchFilter;
                }
                filterArguments.Add(query.Trim());
                filterArguments.Add(query.Trim());
            }

            // Get dealers from data provider
            var dealers = await _dataFacade.DealerDataProvider.GetCollectionAsync(
                includes: null,
                filterPredicate: filterPredicate,
                filterArguments: filterArguments.ToArray(),
                pageNumber: pageNumber,
                pageSize: pageSize,
                sortOrder: "ASC",
                sortColumn: "Name"
            );

            // Get total count for pagination
            var totalCount = await _dataFacade.DealerDataProvider.GetCountAsync(
                filterPredicate: filterPredicate,
                filterArguments: filterArguments.ToArray()
            );

            var dealerInfos = dealers.Select(d => new DealerInfo
            {
                Id = d.Id,
                Name = d.Name ?? string.Empty,
                SubDomain = d.SubDomain ?? string.Empty,
                Description = d.Description ?? string.Empty,
                Active = d.Active,
                IsAPIEnabled = d.IsAPIEnabled,
                ContractNumber = d.ContractNumber
            }).ToList();

            var response = new DealerListResponse
            {
                Dealers = dealerInfos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            _logger.LogDebug("Retrieved {Count} dealers out of {TotalCount} total", dealers.Count, totalCount);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dealers with query: {Query}", query);
            return Problem(
                title: "Error retrieving dealers",
                detail: "An error occurred while retrieving the list of dealers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets a specific dealer by ID
    /// </summary>
    /// <param name="id">Dealer ID</param>
    /// <returns>Dealer information</returns>
    /// <response code="200">Returns the dealer information</response>
    /// <response code="404">If the dealer is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(DealerInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DealerInfo>> GetDealer(Guid id)
    {
        try
        {
            _logger.LogDebug("Getting dealer with ID: {DealerId}", id);

            var dealer = await _dataFacade.DealerDataProvider.GetAsync(id);

            if (dealer == null)
            {
                _logger.LogWarning("Dealer not found with ID: {DealerId}", id);
                return NotFound(new ProblemDetails
                {
                    Title = "Dealer not found",
                    Detail = $"No dealer found with ID: {id}",
                    Status = StatusCodes.Status404NotFound
                });
            }

            var dealerInfo = new DealerInfo
            {
                Id = dealer.Id,
                Name = dealer.Name ?? string.Empty,
                SubDomain = dealer.SubDomain ?? string.Empty,
                Description = dealer.Description ?? string.Empty,
                Active = dealer.Active,
                IsAPIEnabled = dealer.IsAPIEnabled,
                ContractNumber = dealer.ContractNumber
            };

            _logger.LogDebug("Retrieved dealer: {DealerName}", dealerInfo.Name);
            return Ok(dealerInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dealer with ID: {DealerId}", id);
            return Problem(
                title: "Error retrieving dealer",
                detail: "An error occurred while retrieving the dealer",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Searches for dealers by name or subdomain
    /// </summary>
    /// <param name="query">Search term to match against dealer name or subdomain</param>
    /// <param name="limit">Maximum number of results to return (default: 20, max: 50)</param>
    /// <param name="activeOnly">Whether to return only active dealers (default: true)</param>
    /// <returns>List of dealers matching the search criteria</returns>
    /// <response code="200">Returns the list of matching dealers</response>
    /// <response code="400">If the search query is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("search")]
    [ProducesResponseType(typeof(IEnumerable<DealerInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<DealerInfo>>> SearchDealers(
        [FromQuery] string query,
        [FromQuery] int limit = 20,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid search query",
                    Detail = "Search query cannot be empty",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (limit < 1 || limit > 50)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid limit",
                    Detail = "Limit must be between 1 and 50",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Searching dealers with query: {Query}, limit: {Limit}, activeOnly: {ActiveOnly}",
                query, limit, activeOnly);

            // Build filter predicate for search
            var searchTerm = query.Trim();
            var filterPredicate = "(Name.Contains(@0) OR SubDomain.Contains(@1))";
            var filterArguments = new object[] { searchTerm, searchTerm };

            if (activeOnly)
            {
                filterPredicate = "Active == true AND (" + filterPredicate + ")";
            }

            // Get dealers from data provider
            var dealers = await _dataFacade.DealerDataProvider.GetCollectionAsync(
                includes: null,
                filterPredicate: filterPredicate,
                filterArguments: filterArguments,
                pageNumber: 1,
                pageSize: limit,
                sortOrder: "ASC",
                sortColumn: "Name"
            );

            var dealerInfos = dealers.Select(d => new DealerInfo
            {
                Id = d.Id,
                Name = d.Name ?? string.Empty,
                SubDomain = d.SubDomain ?? string.Empty,
                Description = d.Description ?? string.Empty,
                Active = d.Active,
                IsAPIEnabled = d.IsAPIEnabled,
                ContractNumber = d.ContractNumber
            }).ToList();

            _logger.LogDebug("Found {Count} dealers matching search query: {Query}", dealerInfos.Count, query);
            return Ok(dealerInfos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching dealers with query: {Query}", query);
            return Problem(
                title: "Error searching dealers",
                detail: "An error occurred while searching for dealers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Validates if a dealer exists and is active
    /// </summary>
    /// <param name="id">Dealer ID to validate</param>
    /// <returns>Validation result for the dealer</returns>
    /// <response code="200">Returns the validation result</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{id:guid}/validation")]
    [ProducesResponseType(typeof(DealerValidationResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DealerValidationResponse>> ValidateDealer(Guid id)
    {
        try
        {
            _logger.LogDebug("Validating dealer with ID: {DealerId}", id);

            var dealer = await _dataFacade.DealerDataProvider.GetAsync(id);

            var response = new DealerValidationResponse
            {
                DealerId = id,
                Exists = dealer != null,
                IsActive = dealer?.Active ?? false,
                IsAPIEnabled = dealer?.IsAPIEnabled ?? false,
                DealerName = dealer?.Name ?? string.Empty,
                SubDomain = dealer?.SubDomain ?? string.Empty
            };

            if (dealer == null)
            {
                response.ValidationErrors.Add("Dealer does not exist");
                response.IsValid = false;
            }
            else if (!dealer.Active)
            {
                response.ValidationErrors.Add("Dealer is not active");
                response.IsValid = false;
            }
            else if (!dealer.IsAPIEnabled)
            {
                response.ValidationWarnings.Add("Dealer does not have API access enabled");
                response.IsValid = true; // Still valid for bulk import, but with warning
            }
            else
            {
                response.IsValid = true;
            }

            _logger.LogDebug("Dealer validation result for {DealerId}: {IsValid}", id, response.IsValid);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating dealer with ID: {DealerId}", id);
            return Problem(
                title: "Error validating dealer",
                detail: "An error occurred while validating the dealer",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }
}
