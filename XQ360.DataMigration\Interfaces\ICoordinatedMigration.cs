using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Interfaces
{
    /// <summary>
    /// Interface for migrations that can participate in coordinated transactions
    /// to ensure data consistency across multiple migration steps
    /// </summary>
    public interface ICoordinatedMigration
    {
        /// <summary>
        /// Execute migration using an external transaction for coordination
        /// </summary>
        /// <param name="csvFilePath">Path to the CSV file</param>
        /// <param name="connection">External SQL connection</param>
        /// <param name="transaction">External SQL transaction for coordination</param>
        /// <returns>Migration result</returns>
        Task<MigrationResult> ExecuteWithTransactionAsync(string csvFilePath, SqlConnection connection, SqlTransaction transaction);
    }
} 