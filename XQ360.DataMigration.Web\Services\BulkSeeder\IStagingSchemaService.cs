using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service for managing optimized staging schema operations
/// Implements Phase 1.1.1: Enhanced Staging Architecture with Proper Indexes
/// </summary>
public interface IStagingSchemaService
{
    /// <summary>
    /// Initializes the optimized staging schema with proper indexes
    /// Performance target: Sub-millisecond FK lookups, 5,000-10,000 records per batch
    /// </summary>
    Task InitializeOptimizedStagingSchemaAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates or updates the session management infrastructure
    /// Supports concurrent operations with session-based data isolation
    /// </summary>
    Task<Guid> CreateEnhancedSeederSessionAsync(
        string sessionName, 
        string environment, 
        string createdBy,
        string? configurationSnapshot = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates session with detailed progress metrics and ETA calculations
    /// Enables real-time progress tracking with throughput analysis
    /// </summary>
    Task UpdateSessionProgressAsync(
        Guid sessionId, 
        SessionProgressUpdate progressUpdate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates staging schema readiness for bulk operations
    /// Ensures all indexes and constraints are properly configured
    /// </summary>
    Task<SchemaValidationResult> ValidateStagingSchemaAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs session-based cleanup of staging data
    /// Supports partial cleanup and orphaned session recovery
    /// </summary>
    Task CleanupStagingDataAsync(
        Guid? sessionId = null, 
        bool includeOrphanedSessions = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets comprehensive session metrics for monitoring and analysis
    /// Provides detailed performance and progress information
    /// </summary>
    Task<SessionMetrics> GetSessionMetricsAsync(Guid sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active sessions with progress information
    /// Supports concurrent session monitoring
    /// </summary>
    Task<IEnumerable<SessionSummary>> GetActiveSessionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Optimizes staging table performance for bulk operations
    /// Updates statistics and recompiles execution plans
    /// </summary>
    Task OptimizeStagingPerformanceAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Progress update information for enhanced session tracking
/// </summary>
public class SessionProgressUpdate
{
    public string Status { get; set; } = string.Empty;
    public string? CurrentOperation { get; set; }
    public decimal ProgressPercentage { get; set; }
    public DateTime? EstimatedCompletionTime { get; set; }
    public decimal? ThroughputRecordsPerSecond { get; set; }

    // Processing Metrics
    public int? ProcessedDrivers { get; set; }
    public int? ProcessedVehicles { get; set; }
    public int? ProcessedCards { get; set; }
    public int? ProcessedAccessRecords { get; set; }

    // API Operation Metrics
    public int? ApiCallsTotal { get; set; }
    public int? ApiCallsSuccessful { get; set; }
    public int? ApiCallsFailed { get; set; }
    public double? ApiResponseTimeMs { get; set; }

    // SQL Operation Metrics
    public int? SqlOperationsTotal { get; set; }
    public int? SqlOperationsSuccessful { get; set; }
    public int? SqlOperationsFailed { get; set; }
    public double? BulkInsertTimeMs { get; set; }
    public double? ValidationTimeMs { get; set; }
    public double? ProcessingTimeMs { get; set; }

    // Error Tracking
    public int? ValidationErrors { get; set; }
    public int? BusinessRuleViolations { get; set; }
    public int? ReferentialIntegrityErrors { get; set; }

    // Memory and Resource Usage
    public decimal? PeakMemoryUsageMB { get; set; }
    public decimal? CacheHitRatio { get; set; }
    public int? ConnectionPoolUsage { get; set; }

    // Error and Warning Logs (JSON)
    public string? ErrorLog { get; set; }
    public string? WarningsLog { get; set; }
}

/// <summary>
/// Result of staging schema validation
/// </summary>
public class SchemaValidationResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationMessages { get; set; } = new();
    public List<string> PerformanceWarnings { get; set; } = new();
    public List<string> MissingIndexes { get; set; } = new();
    public List<string> OptimizationRecommendations { get; set; } = new();
}

/// <summary>
/// Comprehensive session metrics for monitoring
/// </summary>
public class SessionMetrics
{
    public Guid SessionId { get; set; }
    public string SessionName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Environment { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration { get; set; }

    // Progress Information
    public string? CurrentOperation { get; set; }
    public decimal ProgressPercentage { get; set; }
    public DateTime? EstimatedCompletionTime { get; set; }
    public decimal? ThroughputRecordsPerSecond { get; set; }

    // Processing Counts
    public int TotalRows { get; set; }
    public int SuccessfulRows { get; set; }
    public int FailedRows { get; set; }
    public int ProcessedDrivers { get; set; }
    public int ProcessedVehicles { get; set; }
    public int ProcessedCards { get; set; }
    public int ProcessedAccessRecords { get; set; }

    // API Metrics
    public int ApiCallsTotal { get; set; }
    public int ApiCallsSuccessful { get; set; }
    public int ApiCallsFailed { get; set; }
    public double? ApiResponseTimeMs { get; set; }

    // SQL Metrics
    public int SqlOperationsTotal { get; set; }
    public int SqlOperationsSuccessful { get; set; }
    public int SqlOperationsFailed { get; set; }
    public double? BulkInsertTimeMs { get; set; }
    public double? ValidationTimeMs { get; set; }
    public double? ProcessingTimeMs { get; set; }

    // Error Counts
    public int ValidationErrors { get; set; }
    public int BusinessRuleViolations { get; set; }
    public int ReferentialIntegrityErrors { get; set; }

    // Performance Metrics
    public decimal? PeakMemoryUsageMB { get; set; }
    public decimal? CacheHitRatio { get; set; }
    public int? ConnectionPoolUsage { get; set; }

    // Logs (parsed from JSON)
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    
    // Configuration Info
    public string? DataSourceInfo { get; set; }
    public string? ConfigurationSnapshot { get; set; }
}

/// <summary>
/// Summary information for active sessions
/// </summary>
public class SessionSummary
{
    public Guid SessionId { get; set; }
    public string SessionName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public decimal ProgressPercentage { get; set; }
    public string? CurrentOperation { get; set; }
    public int TotalRecords { get; set; }
    public int CompletedRecords { get; set; }
    public int FailedRecords { get; set; }
    public decimal? ThroughputRecordsPerSecond { get; set; }
    public DateTime? EstimatedCompletionTime { get; set; }
}
