import { ApiBaseService } from './api-base'
import type { Dealer } from '@/types/dealer'

export interface DealerListRequest {
  query?: string
  pageNumber?: number
  pageSize?: number
  activeOnly?: boolean
}

export interface DealerListResponse {
  dealers: Dealer[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
}

export interface DealerSearchRequest {
  query: string
  limit?: number
  activeOnly?: boolean
}

export interface DealerValidationResponse {
  isValid: boolean
  exists: boolean
  isActive: boolean
  dealer?: Dealer
  errorMessage?: string
  warnings: string[]
}

export class DealerService extends ApiBaseService {
  constructor() {
    super('/api')
  }

  /**
   * Get paginated list of dealers
   */
  async getDealers(request: DealerListRequest = {}): Promise<DealerListResponse> {
    try {
      const params = new URLSearchParams()
      
      if (request.query) params.append('query', request.query)
      if (request.pageNumber) params.append('pageNumber', request.pageNumber.toString())
      if (request.pageSize) params.append('pageSize', request.pageSize.toString())
      if (request.activeOnly !== undefined) params.append('activeOnly', request.activeOnly.toString())

      return await this.get<DealerListResponse>(`/dealer?${params.toString()}`)
    } catch (error) {
      console.error('Failed to fetch dealers:', error)
      throw error
    }
  }

  /**
   * Search dealers by name or subdomain
   */
  async searchDealers(request: DealerSearchRequest): Promise<Dealer[]> {
    try {
      const params = new URLSearchParams({
        query: request.query
      })
      
      if (request.limit) params.append('limit', request.limit.toString())
      if (request.activeOnly !== undefined) params.append('activeOnly', request.activeOnly.toString())

      const response = await this.get<Dealer[]>(`/dealer/search?${params.toString()}`)
      return response
    } catch (error) {
      console.error('Failed to search dealers:', error)
      throw error
    }
  }

  /**
   * Get dealer by ID
   */
  async getDealerById(id: string): Promise<Dealer> {
    try {
      return await this.get<Dealer>(`/dealer/${id}`)
    } catch (error) {
      console.error(`Failed to fetch dealer ${id}:`, error)
      throw error
    }
  }

  /**
   * Validate dealer exists and is active
   */
  async validateDealer(id: string): Promise<DealerValidationResponse> {
    try {
      return await this.get<DealerValidationResponse>(`/dealer/${id}/validation`)
    } catch (error) {
      console.error(`Failed to validate dealer ${id}:`, error)
      throw error
    }
  }

  /**
   * Get dealer by subdomain
   */
  async getDealerBySubdomain(subdomain: string): Promise<Dealer | null> {
    try {
      const searchResults = await this.searchDealers({ 
        query: subdomain, 
        limit: 1, 
        activeOnly: true 
      })
      
      // Find exact subdomain match
      const dealer = searchResults.find(d => 
        d.subDomain.toLowerCase() === subdomain.toLowerCase()
      )
      
      return dealer || null
    } catch (error) {
      console.error(`Failed to fetch dealer by subdomain ${subdomain}:`, error)
      return null
    }
  }

  /**
   * Check if dealer exists by name or subdomain
   */
  async dealerExists(nameOrSubdomain: string): Promise<boolean> {
    try {
      const searchResults = await this.searchDealers({ 
        query: nameOrSubdomain, 
        limit: 10, 
        activeOnly: true 
      })
      
      return searchResults.some(dealer => 
        dealer.name.toLowerCase().includes(nameOrSubdomain.toLowerCase()) ||
        dealer.subDomain.toLowerCase() === nameOrSubdomain.toLowerCase()
      )
    } catch (error) {
      console.error(`Failed to check if dealer exists: ${nameOrSubdomain}`, error)
      return false
    }
  }

  /**
   * Get dealer statistics
   */
  async getDealerStats(dealerId: string): Promise<{
    totalCustomers: number
    activeCustomers: number
    totalVehicles: number
    totalDrivers: number
    lastImportDate?: string
  }> {
    try {
      return await this.get<any>(`/dealer/${dealerId}/stats`)
    } catch (error) {
      console.error(`Failed to get dealer stats for ${dealerId}:`, error)
      // Return default stats on error
      return {
        totalCustomers: 0,
        activeCustomers: 0,
        totalVehicles: 0,
        totalDrivers: 0
      }
    }
  }

  /**
   * Get recently accessed dealers for user
   */
  async getRecentDealers(limit: number = 5): Promise<Dealer[]> {
    try {
      // This would typically come from user preferences or session storage
      const recentDealerIds = this.getRecentDealerIds()
      
      if (recentDealerIds.length === 0) {
        return []
      }

      // Fetch dealer details for recent IDs
      const dealers = await Promise.all(
        recentDealerIds.slice(0, limit).map(async (id) => {
          try {
            return await this.getDealerById(id)
          } catch {
            return null
          }
        })
      )

      return dealers.filter(dealer => dealer !== null) as Dealer[]
    } catch (error) {
      console.error('Failed to get recent dealers:', error)
      return []
    }
  }

  /**
   * Add dealer to recent list
   */
  addToRecentDealers(dealerId: string): void {
    try {
      const recentIds = this.getRecentDealerIds()
      const updatedIds = [dealerId, ...recentIds.filter(id => id !== dealerId)].slice(0, 10)
      localStorage.setItem('recent_dealers', JSON.stringify(updatedIds))
    } catch (error) {
      console.error('Failed to add dealer to recent list:', error)
    }
  }

  /**
   * Get recent dealer IDs from storage
   */
  private getRecentDealerIds(): string[] {
    try {
      const stored = localStorage.getItem('recent_dealers')
      return stored ? JSON.parse(stored) : []
    } catch {
      return []
    }
  }

  /**
   * Clear recent dealers list
   */
  clearRecentDealers(): void {
    localStorage.removeItem('recent_dealers')
  }
}
