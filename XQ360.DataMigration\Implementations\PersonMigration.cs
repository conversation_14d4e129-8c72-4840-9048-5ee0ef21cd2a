using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Implementations
{
    public class PersonMigration
    {
        private readonly ILogger<PersonMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly XQ360ApiClient _apiClient;
        private readonly MigrationReportingService _reportingService;
        private readonly string _connectionString;

        // Cache for database lookups to avoid repeated queries
        private readonly Dictionary<string, Guid> _siteCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _dealerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _customerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _departmentCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid?> _personCache = new Dictionary<string, Guid?>();

        public PersonMigration(
            ILogger<PersonMigration> logger,
            IOptions<MigrationConfiguration> config,
            XQ360ApiClient apiClient,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _apiClient = apiClient;
            _reportingService = reportingService;
            _connectionString = _config.DatabaseConnection;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Person migration using XQ360 API");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Validate and lookup database IDs
                await ValidateAndLookupDataAsync(data);

                // Step 3: Authenticate with API
                try
                {
                    var authResult = await _apiClient.AuthenticateAsync();
                    if (!authResult)
                    {
                        var errorMessage = "Failed to authenticate with XQ360 API. Please check:\n" +
                                         "- API server is accessible\n" +
                                         "- Environment configuration is correct\n" +
                                         "- API credentials are valid\n" +
                                         "- Network connectivity to the target environment";
                        
                        _logger.LogError("API authentication failed for API endpoint: {ApiUrl}", _config.ApiBaseUrl);
                        
                        return new MigrationResult
                        {
                            Success = false,
                            Errors = new List<string> { errorMessage },
                            Duration = DateTime.UtcNow - startTime
                        };
                    }
                    
                    _logger.LogInformation("Successfully authenticated with XQ360 API");
                }
                catch (HttpRequestException httpEx)
                {
                    var errorMessage = $"Cannot connect to XQ360 API server: {httpEx.Message}\n" +
                                     "Please check:\n" +
                                     "- API server is running and accessible\n" +
                                     "- Network connectivity\n" +
                                     "- Firewall settings\n" +
                                     $"- Environment URL configuration for {_config.ApiBaseUrl}";
                    
                    _logger.LogError(httpEx, "HTTP connection error during API authentication");
                    
                    return new MigrationResult
                    {
                        Success = false,
                        Errors = new List<string> { errorMessage },
                        Duration = DateTime.UtcNow - startTime
                    };
                }
                catch (TaskCanceledException timeoutEx)
                {
                    var errorMessage = $"API connection timeout: {timeoutEx.Message}\n" +
                                     "The API server is not responding within the expected time.\n" +
                                     "Please check:\n" +
                                     "- API server performance\n" +
                                     "- Network latency\n" +
                                     "- Server load on the target environment";
                    
                    _logger.LogError(timeoutEx, "Timeout during API authentication");
                    
                    return new MigrationResult
                    {
                        Success = false,
                        Errors = new List<string> { errorMessage },
                        Duration = DateTime.UtcNow - startTime
                    };
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Unexpected error during API authentication: {ex.Message}\n" +
                                     "This may indicate:\n" +
                                     "- Configuration issues\n" +
                                     "- SSL/TLS certificate problems\n" +
                                     "- API server internal errors\n" +
                                     "Please check the logs for more details.";
                    
                    _logger.LogError(ex, "Unexpected error during API authentication");
                    
                    return new MigrationResult
                    {
                        Success = false,
                        Errors = new List<string> { errorMessage },
                        Duration = DateTime.UtcNow - startTime
                    };
                }

                // Step 4: Execute API migration
                result.RecordsProcessed = data.Count;
                var migrationResult = await ExecutePersonApiAsync(data, result);

                result.Success = migrationResult.Success;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;

                _logger.LogInformation($"Person migration completed: {result.RecordsInserted} inserted, {result.RecordsSkipped} skipped, Duration: {result.Duration}");

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Person Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Person migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private Task<List<PersonImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Person CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<PersonImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return Task.FromResult(records);
        }

        private async Task ValidateAndLookupDataAsync(List<PersonImportModel> data)
        {
            _logger.LogInformation("Validating data and performing database lookups...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var errors = new List<string>();

            // Get unique customer-site-department combinations and validate them
            var uniqueCombinations = data
                .Select(d => new { Customer = d.Customer, Site = d.Site, Department = d.Department })
                .Distinct()
                .ToList();

            foreach (var combo in uniqueCombinations)
            {
                try
                {
                    var customerId = await GetCustomerIdByNameAsync(combo.Customer ?? "Unknown", connection);
                    var siteId = await GetSiteIdAsync(combo.Site ?? "Unknown", combo.Customer ?? "Unknown", connection);
                    var departmentId = await GetDepartmentIdAsync(combo.Department ?? "Unknown", combo.Site ?? "Unknown", combo.Customer ?? "Unknown", connection);

                    _logger.LogDebug($"Validated: Customer '{combo.Customer}' -> Site '{combo.Site}' -> Department '{combo.Department}'");
                }
                catch (Exception ex)
                {
                    errors.Add(ex.Message);
                }
            }

            if (errors.Any())
            {
                throw new InvalidOperationException($"Validation failed: {string.Join("; ", errors)}");
            }

            _logger.LogInformation("Data validation completed successfully");
        }

        private async Task<MigrationResult> ExecutePersonApiAsync(List<PersonImportModel> data, MigrationResult sharedResult)
        {
            _logger.LogInformation("Executing Person API migration...");

            var insertedCount = 0;
            var errors = new List<string>();
            var warnings = new List<string>();

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            foreach (var record in data)
            {
                try
                {
                    // Check if person already exists
                    var existingPersonId = await CheckIfPersonExistsAsync(record.FirstName ?? "Unknown", record.LastName ?? "Unknown", record.Site ?? "Unknown", record.Customer ?? "Unknown", connection);
                    if (existingPersonId != null)
                    {
                        _logger.LogWarning($"Person '{record.FirstName} {record.LastName}' already exists with ID: {existingPersonId}. Skipping creation.");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, WarningTypes.EXISTING_RECORD_SKIPPED,
                            $"Person '{record.FirstName} {record.LastName}' already exists in database",
                            "Person", $"{record.FirstName} {record.LastName}",
                            "Remove duplicate from CSV or verify this person record",
                            new Dictionary<string, string> 
                            {
                                { "Site", record.Site ?? "Unknown" },
                                { "Customer", record.Customer ?? "Unknown" },
                                { "Department", record.Department ?? "Unknown" },
                                { "IsDriver", record.IsDriver.ToString() }
                            });
                        warnings.Add($"Person '{record.FirstName} {record.LastName}' already exists - skipped");
                        continue;
                    }

                    // Get required database IDs
                    var siteId = await GetSiteIdAsync(record.Site ?? "Unknown", record.Customer ?? "Unknown", connection);
                    var departmentId = await GetDepartmentIdAsync(record.Department ?? "Unknown", record.Site ?? "Unknown", record.Customer ?? "Unknown", connection);
                    var customerId = await GetCustomerIdByNameAsync(record.Customer ?? "Unknown", connection);

                    // Convert CSV record to API entity format
                    var personEntity = new PersonApiEntity
                    {
                        IsNew = true, // Critical: Tell server this is a new person, not an update
                                        FirstName = record.FirstName ?? "Unknown",
                LastName = record.LastName ?? "Unknown",
                        SiteId = siteId,
                        DepartmentId = departmentId,
                        CustomerId = customerId,
                        SendDenyMessage = record.SendDenyMessage,
                        WebsiteAccess = record.WebsiteAccess,
                        IsDriver = record.IsDriver,
                        Supervisor = record.IsSupervisor,
                        VORActivateDeactivate = record.VORActivateDeactivate,
                        NormalDriverAccess = record.NormalDriverAccess,
                        CanUnlockVehicle = record.CanUnlockVehicle,
                        // Required fields based on server-side logic
                        Active = true,
                        Language = "en-US",
                        IsActiveDriver = record.IsDriver, // Default: active if they're a driver
                        VehicleAccess = true, // Default to true (server sets to false if OnDemand is true)
                        OnDemand = false, // Default to false
                        MaintenanceMode = false // Default to false
                    };

                    _logger.LogInformation($"Creating person: {record.FirstName} {record.LastName}");
                    
                    // Serialize to JSON string for the entity parameter
                    var entityJson = JsonConvert.SerializeObject(personEntity);

                    // Create form data
                    var formData = new Dictionary<string, string>
                    {
                        ["entity"] = entityJson,
                        ["include"] = "Site,Department" // Optional: include related data in response
                    };
                    
                    // Call the Person API using form-encoded data
                    var apiResult = await _apiClient.PostFormAsync<PersonApiResponse>("person", formData);

                    if (apiResult.Success)
                    {
                        // Additional validation for the API response
                        if (apiResult.Data == null)
                        {
                            var errorMsg = $"API returned null data for person {record.FirstName} {record.LastName}";
                            errors.Add(errorMsg);
                            _logger.LogWarning(errorMsg);
                        }
                        else if (!apiResult.Data.IsObjectsDataSetValid())
                        {
                            // ObjectsDataSet is null but we can still consider this a success
                            // as the person might have been created despite the null dataset
                            _logger.LogWarning($"Person {record.FirstName} {record.LastName} created but ObjectsDataSet is null in response");
                            insertedCount++;
                            _logger.LogInformation($"Successfully created person: {record.FirstName} {record.LastName} (with null ObjectsDataSet)");
                        }
                        else
                        {
                        insertedCount++;
                        _logger.LogInformation($"Successfully created person: {record.FirstName} {record.LastName}");
                        }
                    }
                    else
                    {
                        var errorMsg = $"Failed to create person {record.FirstName} {record.LastName}: {apiResult.ErrorMessage}";
                        errors.Add(errorMsg);
                        _logger.LogWarning(errorMsg);
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = $"Error processing person {record.FirstName} {record.LastName}: {ex.Message}";
                    errors.Add(errorMsg);
                    _logger.LogError(ex, errorMsg);
                }

                // Add small delay to avoid overwhelming the API
                await Task.Delay(100);
            }

            _logger.LogInformation($"Successfully created {insertedCount} persons via API");

            return new MigrationResult
            {
                Success = errors.Count == 0,
                RecordsInserted = insertedCount,
                Errors = errors,
                Warnings = warnings
            };
        }

        private async Task<Guid?> CheckIfPersonExistsAsync(string firstName, string lastName, string siteName, string customerName, SqlConnection connection)
        {
            if (string.IsNullOrEmpty(firstName) || string.IsNullOrEmpty(lastName))
                return null;

            var key = $"{firstName}|{lastName}|{siteName}|{customerName}";
            if (_personCache.ContainsKey(key))
                return _personCache[key];

            try
            {
                var customerId = await GetCustomerIdByNameAsync(customerName, connection);
                var siteId = await GetSiteIdAsync(siteName, customerName, connection);

                var sql = "SELECT Id FROM dbo.Person WHERE FirstName = @FirstName AND LastName = @LastName AND SiteId = @SiteId";
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@FirstName", firstName);
                cmd.Parameters.AddWithValue("@LastName", lastName);
                cmd.Parameters.AddWithValue("@SiteId", siteId);

                var result = await cmd.ExecuteScalarAsync();
                if (result == null)
                {
                    _personCache[key] = null;
                    return null;
                }

                var personId = (Guid)result;
                _personCache[key] = personId;
                return personId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to check if person exists: {firstName} {lastName}");
                return null;
            }
        }

        private async Task<Guid> GetSiteIdAsync(string siteName, string customerName, SqlConnection connection)
        {
            var key = $"{siteName}|{customerName}";
            if (_siteCache.ContainsKey(key))
                return _siteCache[key];

            var customerId = await GetCustomerIdByNameAsync(customerName, connection);

            var sql = "SELECT Id FROM dbo.Site WHERE Name = @Name AND CustomerId = @CustomerId";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@Name", siteName);
            cmd.Parameters.AddWithValue("@CustomerId", customerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Site '{siteName}' not found for customer '{customerName}'");

            var siteId = (Guid)result;
            _siteCache[key] = siteId;
            return siteId;
        }

        private async Task<Guid> GetCustomerIdByNameAsync(string customerName, SqlConnection connection)
        {
            if (_customerCache.ContainsKey(customerName))
                return _customerCache[customerName];

            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found");

            var customerId = (Guid)result;
            _customerCache[customerName] = customerId;
            return customerId;
        }

        private async Task<Guid> GetDepartmentIdAsync(string departmentName, string siteName, string customerName, SqlConnection connection)
        {
            var key = $"{departmentName}|{siteName}|{customerName}";
            if (_departmentCache.ContainsKey(key))
                return _departmentCache[key];

            var siteId = await GetSiteIdAsync(siteName, customerName, connection);

            var sql = "SELECT Id FROM dbo.Department WHERE Name = @Name AND SiteId = @SiteId";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@Name", departmentName);
            cmd.Parameters.AddWithValue("@SiteId", siteId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Department '{departmentName}' not found in site '{siteName}' for customer '{customerName}'");

            var departmentId = (Guid)result;
            _departmentCache[key] = departmentId;
            return departmentId;
        }
    }

    // API entity models for Person API
    public class PersonApiEntity
    {
        public bool IsNew { get; set; } // Required to indicate this is a new entity, not an update
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public Guid SiteId { get; set; }
        public Guid DepartmentId { get; set; }
        public Guid CustomerId { get; set; }
        public bool SendDenyMessage { get; set; }
        public bool WebsiteAccess { get; set; }
        public bool IsDriver { get; set; }
        public bool? IsActiveDriver { get; set; }
        public bool Supervisor { get; set; }
        public bool VORActivateDeactivate { get; set; }
        public bool NormalDriverAccess { get; set; }
        public bool CanUnlockVehicle { get; set; }
        public bool VehicleAccess { get; set; }
        public bool OnDemand { get; set; }
        public bool MaintenanceMode { get; set; }
        public bool Active { get; set; }
        public required string Language { get; set; }
    }

    public class PersonApiResponse
    {
        public int InternalObjectId { get; set; }
        public required string PrimaryKey { get; set; }
        
        private object _objectsDataSet = new object();
        public object ObjectsDataSet 
        { 
            get => _objectsDataSet; 
            set => _objectsDataSet = value;
        }
        
        // Helper method to check if ObjectsDataSet is valid
        public bool IsObjectsDataSetValid()
        {
            return ObjectsDataSet != null;
        }
    }
} 