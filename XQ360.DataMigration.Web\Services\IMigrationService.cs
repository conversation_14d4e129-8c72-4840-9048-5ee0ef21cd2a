using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Implementations;

namespace XQ360.DataMigration.Web.Services;

public interface IMigrationService
{
    Task<string> StartMigrationAsync(MigrationRequest request);
    Task<MigrationProgress?> GetProgressAsync(string migrationId);
}

public class MigrationService : IMigrationService
{
    private readonly ILogger<MigrationService> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IHubContext<MigrationHub> _hubContext;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly ICsvFormatValidator _csvValidator;
    private readonly Dictionary<string, MigrationProgress> _activeMigrations = new();
    
    // Migration sequence mapping based on MigrationOrchestrator
    private readonly Dictionary<string, string> _migrationMapping = new()
    {
        ["spare-modules"] = "spare-modules",
        ["preop-checklist"] = "preop-checklist", 
        ["vehicles"] = "vehicles",
        ["persons"] = "persons",
        ["cards"] = "cards-and-vehicle-access",
        ["supervisor-access"] = "supervisor-access",
        ["driver-blacklist"] = "driver-blacklist",
        ["website-users"] = "website-users",
        ["sync-vehicle-settings"] = "vehicle-sync-settings"
    };

    public MigrationService(
        ILogger<MigrationService> logger,
        IServiceScopeFactory serviceScopeFactory,
        IHubContext<MigrationHub> hubContext,
        IEnvironmentConfigurationService environmentService,
        ICsvFormatValidator csvValidator)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
        _hubContext = hubContext;
        _environmentService = environmentService;
        _csvValidator = csvValidator;
    }

    public async Task<string> StartMigrationAsync(MigrationRequest request)
    {
        var migrationId = Guid.NewGuid().ToString();
        var progress = new MigrationProgress
        {
            Id = migrationId,
            Status = "Starting",
            StartTime = DateTime.Now
        };

        _activeMigrations[migrationId] = progress;

        try
        {
            // Set environment configuration
            _environmentService.SetCurrentEnvironment(request.Environment);
            
            // Save uploaded CSV files to the correct directory
            await SaveUploadedFilesAsync(request.CsvFiles);
            
            progress.Messages.Add($"Environment set to: {request.Environment}");
            progress.Messages.Add("CSV files uploaded and validated");
            progress.Status = "Processing";
            progress.Progress = 10;
            
            await NotifyProgress(migrationId, progress);

            // Start migration in background with proper sequencing
            _ = Task.Run(async () => await ExecuteMigrationsAsync(migrationId, request));

            return migrationId;
        }
        catch (CsvValidationException csvEx)
        {
            _logger.LogWarning("CSV validation warnings during migration {MigrationId}: {Errors}", migrationId, string.Join("; ", csvEx.ValidationErrors));
            
            // Add validation warnings to progress but continue with migration
            progress.Status = "Processing";
            progress.Messages.Add($"Environment set to: {request.Environment}");
            progress.Messages.Add("⚠️ CSV Format Warnings Found:");
            
            foreach (var error in csvEx.ValidationErrors)
            {
                progress.Messages.Add($"⚠️ {error}");
            }
            
            progress.Messages.Add("📋 Migration will continue - please review the warnings above");
            progress.Progress = 10;
            
            await NotifyProgress(migrationId, progress);

            // Continue with migration despite warnings
            _ = Task.Run(async () => await ExecuteMigrationsAsync(migrationId, request));

            return migrationId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start migration {MigrationId}", migrationId);
            progress.Status = "Failed";
            progress.ErrorMessage = ex.Message;
            progress.EndTime = DateTime.Now;
            await NotifyProgress(migrationId, progress);
            throw;
        }
    }

    public async Task<MigrationProgress?> GetProgressAsync(string migrationId)
    {
        _activeMigrations.TryGetValue(migrationId, out var progress);
        return await Task.FromResult(progress);
    }

    private async Task ExecuteMigrationsAsync(string migrationId, MigrationRequest request)
    {
        var progress = _activeMigrations[migrationId];
        
        try
        {
            progress.Status = "Running";
            progress.Messages.Add("Starting migration execution with bulletproof ordering...");

            // Get the real MigrationOrchestrator
            using var scope = _serviceScopeFactory.CreateScope();
            var orchestrator = scope.ServiceProvider.GetRequiredService<MigrationOrchestrator>();

            // Map selected migration types to orchestrator step IDs and sort by execution order
            var requestedSteps = request.MigrationTypes
                .Where(mt => _migrationMapping.ContainsKey(mt))
                .Select(mt => _migrationMapping[mt])
                .ToList();

            if (requestedSteps.Count == 0)
            {
                throw new InvalidOperationException("No valid migration types selected");
            }

            // NOTE: Do NOT add "vehicle-sync-settings" to requestedSteps here!
            // VehicleSync is handled separately in ExecuteVehicleSyncIfNeededAsync with proper callback timing
            // Adding it here would cause it to run twice (once in orchestrator, once in ExecuteVehicleSyncIfNeededAsync)

            // Initialize step summaries and overall progress tracking
            InitializeProgressSummaries(progress, requestedSteps);
            
            // Manually add vehicle-sync-settings step if any types require sync
            var excludedTypes = new[] { "spare-modules", "website-users" };
            var typesRequiringSync = request.MigrationTypes.Where(t => !excludedTypes.Contains(t)).ToList();
            if (typesRequiringSync.Any())
            {
                // Add vehicle-sync-settings step summary (but NOT to requestedSteps for orchestrator)
                progress.StepSummaries["vehicle-sync-settings"] = new MigrationStepSummary
                {
                    StepId = "vehicle-sync-settings",
                    StepName = "Vehicle Settings Sync",
                    Status = "Pending",
                    StartTime = null,
                    EndTime = null,
                    RecordsProcessed = 0,
                    RecordsInserted = 0,
                    RecordsSkipped = 0,
                    ErrorCount = 0
                };
                
                // Update overall summary to reflect the added vehicle sync step
                UpdateOverallSummary(progress);
                
                _logger.LogInformation("Added vehicle-sync-settings step summary for separate execution. Total steps: {TotalSteps}", progress.OverallSummary.TotalSteps);
            }

            progress.Messages.Add($"Selected migrations: {string.Join(", ", requestedSteps)}");
            await NotifyProgress(migrationId, progress);

            // Execute migrations using the real orchestrator
            MigrationOrchestrationResult result;
            
            if (requestedSteps.Count == 1)
            {
                // Single migration
                var stepId = requestedSteps[0];
                progress.CurrentStep = stepId;
                progress.Messages.Add($"Executing single migration: {stepId}");
                await NotifyProgress(migrationId, progress);
                
                result = await orchestrator.ExecuteStepAsync(stepId);
            }
            else
            {
                // Multiple migrations - execute in correct dependency order
                progress.Messages.Add("Executing multiple migrations in dependency order...");
                await NotifyProgress(migrationId, progress);
                
                result = await ExecuteMultipleMigrationsAsync(orchestrator, requestedSteps, migrationId, progress);
            }

            // Update progress based on orchestration result
            if (result.Success)
            {
                progress.Status = "VehicleSync"; // Keep processing status during vehicle sync
                progress.Progress = 90; // Set to 90% before vehicle sync
                progress.Messages.Add("🎉 Migration completed successfully!");
                progress.Messages.Add($"Duration: {result.Duration}");
                progress.Messages.Add($"Completed steps: {string.Join(" → ", result.CompletedSteps)}");
                
                // CRITICAL DEBUG: Log the final result before UpdateProgressSummariesFromResult
                _logger.LogWarning("🔧 FINAL RESULT: Success={Success}, CompletedSteps={CompletedSteps}, StepResults.Count={StepResultsCount}", 
                    result.Success, string.Join(", ", result.CompletedSteps), result.StepResults.Count);
                
                foreach (var finalStepResult in result.StepResults)
                {
                    _logger.LogWarning("🔧 Final StepResult: {StepName} - Processed={Processed}, Inserted={Inserted}, Skipped={Skipped}", 
                        finalStepResult.StepName, finalStepResult.RecordsProcessed, finalStepResult.RecordsInserted, finalStepResult.RecordsSkipped);
                }
                
                // Update final step summaries and overall summary
                UpdateProgressSummariesFromResult(progress, result);
                
                // Add detailed results
                foreach (var stepResult in result.StepResults)
                {
                    progress.Messages.Add($"📊 {stepResult.StepName}: {stepResult.RecordsProcessed} processed, {stepResult.RecordsInserted} inserted, {stepResult.RecordsSkipped} skipped");
                }

                await NotifyProgress(migrationId, progress);

                // ✅ CONDITIONAL VEHICLE SETTINGS SYNC - Execute INSIDE scope before disposal
                _logger.LogInformation("🔍 About to call ExecuteVehicleSyncIfNeededAsync for migration {MigrationId}", migrationId);
                
                // Update VehicleSync step to Running status if it exists
                UpdateStepSummaryStatus(progress, "vehicle-sync-settings", "Running", DateTime.UtcNow);
                _logger.LogInformation("🔧 Step status before sync: {Status}", progress.StepSummaries.TryGetValue("vehicle-sync-settings", out var stepSummary) ? stepSummary.Status : "NOT_FOUND");
                progress.Messages.Add("🔍 Starting vehicle sync check...");
                await NotifyProgress(migrationId, progress);
                
                try
                {
                    await ExecuteVehicleSyncIfNeededAsync(migrationId, progress, request, scope.ServiceProvider);
                    _logger.LogInformation("✅ ExecuteVehicleSyncIfNeededAsync completed for migration {MigrationId}", migrationId);
                    
                    // Log step status after sync to verify callback worked
                    var finalStepStatus = progress.StepSummaries.TryGetValue("vehicle-sync-settings", out var finalStepSummary) ? finalStepSummary.Status : "NOT_FOUND";
                    _logger.LogInformation("🔧 Step status after sync: {Status}, CompletedSteps count: {Count}, FinalStatusSet: {FinalStatusSet}", 
                        finalStepStatus, progress.CompletedSteps.Count, progress.FinalStatusSet);
                    
                    // IMPORTANT: Do NOT modify step status, CompletedSteps, or OverallSummary here!
                    // The progress callback inside ExecuteVehicleSyncIfNeededAsync already handles all of this
                    
                    progress.Messages.Add("✅ Vehicle sync completed successfully");
                    
                    // Note: Progress callback already sent final status update before report generation
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ Exception in ExecuteVehicleSyncIfNeededAsync for migration {MigrationId}", migrationId);
                    
                    // Update overall summary even on failure to show correct step count
                    UpdateOverallSummary(progress);
                    
                    progress.Messages.Add($"❌ Vehicle sync error: {ex.Message}");
                    
                    // Set final status if not already set by exception handling
                    if (!progress.FinalStatusSet)
                    {
                        progress.Status = "Failed";
                        progress.EndTime = DateTime.UtcNow;
                        progress.FinalStatusSet = true;
                        await NotifyProgress(migrationId, progress);
                    }
                }
                
                // CRITICAL: Check if callback already set final status to prevent redundant updates
                if (progress.FinalStatusSet)
                {
                    _logger.LogInformation("Final status already set by callback - skipping redundant notification and message additions");
                    // Do NOT add any more messages or send notifications - final status already handled
                }
                else
                {
                    // Check if there were any errors in the vehicle sync step before setting to completed
                    var hasVehicleSyncErrors = progress.Messages.Any(msg => 
                        msg.Contains("Vehicle sync error") || 
                        msg.Contains("Vehicle Settings Sync failed") ||
                        msg.Contains("Failed to authenticate") ||
                        msg.Contains("Exception during Vehicle Settings Sync") ||
                        msg.Contains("No connection could be made") ||
                        msg.Contains("target machine actively refused")
                    );
                    
                    if (hasVehicleSyncErrors)
                    {
                        _logger.LogWarning("Vehicle sync had errors - setting status to Failed");
                        progress.Status = "Failed";
                        progress.EndTime = DateTime.UtcNow;
                        progress.FinalStatusSet = true;
                        await NotifyProgress(migrationId, progress);
                    }
                    else
                    {
                        _logger.LogWarning("Callback did not set final status - setting it now as fallback");
                        progress.Status = "Completed";
                        progress.Progress = 100;
                        progress.EndTime = DateTime.UtcNow;
                        progress.Messages.Add("📋 Migration report available for download");
                        progress.FinalStatusSet = true;
                        await NotifyProgress(migrationId, progress);
                    }
                }
            }
            else
            {
                progress.Status = "Failed";
                var errorMessage = result.Errors.Any() ? string.Join("; ", result.Errors) : "Migration failed";
                progress.ErrorMessage = errorMessage;
                progress.EndTime = DateTime.Now;
                progress.Messages.Add($"❌ Migration failed: {errorMessage}");
                
                if (result.FailedStep != null)
                {
                    progress.Messages.Add($"Failed at step: {result.FailedStep.Name}");
                }
                
                // Send notification for failed migration
                await NotifyProgress(migrationId, progress);
            }

            // Final status handling is complete - no additional notifications needed
            _logger.LogInformation("Migration processing complete. Final status: {Status}, FinalStatusSet: {FinalStatusSet}", 
                progress.Status, progress.FinalStatusSet);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Migration {MigrationId} failed with exception", migrationId);
            
            progress.Status = "Failed";
            progress.ErrorMessage = ex.Message;
            progress.EndTime = DateTime.Now;
            progress.Messages.Add($"❌ Migration failed with error: {ex.Message}");
            
            await NotifyProgress(migrationId, progress);
        }
    }

    private async Task SaveUploadedFilesAsync(Dictionary<string, IFormFile?> csvFiles)
    {
        // Get the CSV_Input directory path using the helper method
        var csvInputPath = GetCsvInputPath();
        
        if (!Directory.Exists(csvInputPath))
        {
            Directory.CreateDirectory(csvInputPath);
        }

        var validationErrors = new List<string>();

        foreach (var kvp in csvFiles)
        {
            var migrationType = kvp.Key;
            var file = kvp.Value;
            
            if (file != null && file.Length > 0)
            {
                // Map migration type to expected CSV filename
                var fileName = GetExpectedCsvFileName(migrationType);
                var filePath = Path.Combine(csvInputPath, fileName);
                
                _logger.LogInformation("Saving and validating uploaded file for {MigrationType} to {FilePath}", migrationType, filePath);
                
                // Save file first
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Validate CSV format against template
                try
                {
                    var validationResult = await _csvValidator.ValidateAsync(filePath, migrationType);
                    
                    if (!validationResult.IsValid)
                    {
                        var errorMessage = $"CSV format validation failed for {migrationType}:\n" +
                                         $"- Expected headers: {string.Join(", ", validationResult.ExpectedHeaders)}\n" +
                                         $"- Found headers: {string.Join(", ", validationResult.ActualHeaders)}\n" +
                                         $"- Errors: {string.Join("; ", validationResult.Errors)}";
                        
                        validationErrors.Add(errorMessage);
                        _logger.LogError("CSV validation failed for {MigrationType}: {Errors}", 
                            migrationType, string.Join("; ", validationResult.Errors));
                    }
                    else
                    {
                        _logger.LogInformation("CSV validation successful for {MigrationType}: {RecordCount} records", 
                            migrationType, validationResult.RecordCount);
                        
                        if (validationResult.Warnings.Any())
                        {
                            _logger.LogWarning("CSV validation warnings for {MigrationType}: {Warnings}", 
                                migrationType, string.Join("; ", validationResult.Warnings));
                        }
                    }
                }
                catch (Exception ex)
                {
                    var errorMessage = $"CSV validation error for {migrationType}: {ex.Message}";
                    validationErrors.Add(errorMessage);
                    _logger.LogError(ex, "CSV validation error for {MigrationType}", migrationType);
                }
            }
        }

        // If there are validation errors, don't throw exception, but store them for later display
        if (validationErrors.Any())
        {
            var combinedError = $"CSV format validation failed. Please check your files against the templates:\n\n{string.Join("\n\n", validationErrors)}";
            throw new CsvValidationException(combinedError, validationErrors);
        }
    }

    private string GetExpectedCsvFileName(string migrationType)
    {
        return migrationType switch
        {
            "spare-modules" => "SPARE_MODEL_IMPORT_TEMPLATE.csv",
            "preop-checklist" => "PREOP_CHECKLIST_IMPORT.csv",
            "vehicles" => "VEHICLE_IMPORT.csv",
            "persons" => "PERSON_IMPORT_TEMPLATE.csv",
            "cards" => "CARD_IMPORT.csv",
            "supervisor-access" => "SUPERVISOR_ACCESS_IMPORT.csv",
            "driver-blacklist" => "DRIVER_BLACKLIST_IMPORT.csv",
            "website-users" => "WEBSITE_USER_IMPORT.csv",
            "sync-vehicle-settings" => "VEHICLE_IMPORT.csv", // Uses same file for device IDs
            _ => $"{migrationType.ToUpper()}_IMPORT.csv"
        };
    }

    /// <summary>
    /// Gets the correct CSV_Input directory path for both development and deployment environments
    /// </summary>
    private string GetCsvInputPath()
    {
        var currentDirectory = Directory.GetCurrentDirectory();
        
        // Check if we're running from IIS deployment (XQ360Migration)
        if (currentDirectory.EndsWith("XQ360Migration"))
        {
            // When deployed to IIS, CSV_Input is in the same directory as the application
            return Path.Combine(currentDirectory, "CSV_Input");
        }
        else if (currentDirectory.EndsWith("XQ360.DataMigration.Web"))
        {
            // During development, navigate to the main migration project
            return Path.Combine(currentDirectory, "..", "XQ360.DataMigration", "CSV_Input");
        }
        else
        {
            // Fallback for other scenarios
            return Path.Combine(currentDirectory, "CSV_Input");
        }
    }

    private async Task<MigrationOrchestrationResult> ExecuteMultipleMigrationsAsync(
        MigrationOrchestrator orchestrator, 
        List<string> requestedSteps, 
        string migrationId, 
        MigrationProgress progress)
    {
        // Define the correct execution order based on dependencies
        var orderedSteps = new List<string>
        {
            "spare-modules",
            "preop-checklist", 
            "vehicles",
            "persons",
            "cards-and-vehicle-access",
            "supervisor-access",
            "driver-blacklist",
            "website-users",
            "vehicle-sync-settings"
        };

        // Filter to only include requested steps, maintaining order
        var stepsToExecute = orderedSteps.Where(step => requestedSteps.Contains(step)).ToList();
        
        var overallResult = new MigrationOrchestrationResult 
        { 
            Success = true, 
            CompletedSteps = new List<string>(),
            StepResults = new List<MigrationStepResult>()
        };
        
        var startTime = DateTime.UtcNow;

        foreach (var stepId in stepsToExecute)
        {
            try
            {
                progress.CurrentStep = stepId;
                progress.Messages.Add($"🚀 Starting migration step: {stepId}");
                var stepProgress = (int)(30 + (70.0 * overallResult.CompletedSteps.Count / stepsToExecute.Count));
                progress.Progress = stepProgress;
                
                // Update step summary to running status
                UpdateStepSummaryStatus(progress, stepId, "Running", DateTime.UtcNow);
                await NotifyProgress(migrationId, progress);

                var stepResult = await orchestrator.ExecuteStepAsync(stepId);
                
                // CRITICAL DEBUG: Log what we get back from orchestration
                _logger.LogWarning("🔧 ORCHESTRATION RESULT for {StepId}: Success={Success}, StepResults.Count={Count}", 
                    stepId, stepResult.Success, stepResult.StepResults.Count);
                
                foreach (var sr in stepResult.StepResults)
                {
                    _logger.LogWarning("🔧 StepResult: {StepName} - Processed={Processed}, Inserted={Inserted}, Skipped={Skipped}", 
                        sr.StepName, sr.RecordsProcessed, sr.RecordsInserted, sr.RecordsSkipped);
                }
                
                if (stepResult.Success)
                {
                    overallResult.CompletedSteps.Add(stepId);
                    overallResult.StepResults.AddRange(stepResult.StepResults);
                    
                    // Update step summary with completion details
                    UpdateStepSummaryFromResult(progress, stepId, stepResult, "Completed");
                    progress.Messages.Add($"✅ Completed {stepId} successfully");
                }
                else
                {
                    overallResult.Success = false;
                    var errorMessage = stepResult.Errors.Any() ? string.Join("; ", stepResult.Errors) : "Step failed";
                    overallResult.Errors.Add(errorMessage);
                    overallResult.FailedStep = stepResult.FailedStep;
                    
                    _logger.LogWarning("🔧 FAILED STEP {StepId}: StepResults.Count={Count}, Errors={Errors}", 
                        stepId, stepResult.StepResults.Count, string.Join("; ", stepResult.Errors));
                    
                    // Update step summary with failure details
                    UpdateStepSummaryFromResult(progress, stepId, stepResult, "Failed");
                    progress.Messages.Add($"❌ Failed at {stepId}: {errorMessage}");
                    break; // Stop execution on failure to maintain data integrity
                }
            }
            catch (Exception ex)
            {
                overallResult.Success = false;
                overallResult.Errors.Add(ex.Message);
                
                // Update step summary with exception details
                UpdateStepSummaryStatus(progress, stepId, "Failed", endTime: DateTime.UtcNow);
                progress.Messages.Add($"❌ Exception during {stepId}: {ex.Message}");
                break;
            }
        }

        overallResult.Duration = DateTime.UtcNow - startTime;
        return overallResult;
    }

    private async Task NotifyProgress(string migrationId, MigrationProgress progress)
    {
        try
        {
            _logger.LogInformation("Sending progress update for migration {MigrationId}: Status={Status}, Progress={Progress}%", 
                migrationId, progress.Status, progress.Progress);
            
            // Debug: Log step summaries being sent to frontend
            if (progress.StepSummaries.Any())
            {
                _logger.LogInformation("🔧 NotifyProgress: Step summaries being sent to frontend (Status={OverallStatus}):", progress.Status);
                foreach (var kvp in progress.StepSummaries)
                {
                    _logger.LogInformation("🔧   {StepId}: Status={Status}, Name={Name}", 
                        kvp.Key, kvp.Value.Status, kvp.Value.StepName);
                }
                
                // Special focus on vehicle-sync-settings
                if (progress.StepSummaries.TryGetValue("vehicle-sync-settings", out var vehicleStep))
                {
                    _logger.LogWarning("🔧 CRITICAL: vehicle-sync-settings step being sent with Status={Status}, Records={Records}, FinalStatusSet={FinalStatusSet}", 
                        vehicleStep.Status, vehicleStep.RecordsInserted, progress.FinalStatusSet);
                }
            }
            else
            {
                _logger.LogWarning("🔧 No step summaries found in progress object!");
            }
            
            // Debug: Log overall summary being sent to frontend
            if (progress.OverallSummary != null)
            {
                _logger.LogWarning("🔧 CRITICAL: OverallSummary being sent to frontend - TotalRecordsProcessed={Processed}, TotalRecordsInserted={Inserted}, TotalRecordsSkipped={Skipped}, TotalErrors={Errors}", 
                    progress.OverallSummary.TotalRecordsProcessed, progress.OverallSummary.TotalRecordsInserted, progress.OverallSummary.TotalRecordsSkipped, progress.OverallSummary.TotalErrors);
            }
            else
            {
                _logger.LogError("🔧 CRITICAL: OverallSummary is NULL when sending to frontend!");
            }
            
            await _hubContext.Clients.Group(migrationId).SendAsync("UpdateProgress", progress);
            
            _logger.LogInformation("Progress update sent successfully for migration {MigrationId}", migrationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send progress update for migration {MigrationId}", migrationId);
        }
    }

    /// <summary>
    /// Execute Vehicle Settings Sync for all customers in uploaded CSV files
    /// Logic:
    /// 1. Collect distinct customers from all migration CSV files (except spare-modules and website-users)
    /// 2. Find all vehicles for these customers in the database
    /// 3. Sync vehicles once only after all migration types are completed
    /// Excluded from vehicle sync:
    /// - Spare Modules (no customer field)
    /// - Website Users (no vehicle sync needed)
    /// </summary>
    private async Task ExecuteVehicleSyncIfNeededAsync(string migrationId, MigrationProgress progress, MigrationRequest request, IServiceProvider? scopedServiceProvider = null)
    {
        // Execute Vehicle Settings Sync - use scoped service provider to avoid disposal issues
        IServiceProvider serviceProvider;
        IDisposable? scopeToDispose = null;
        
        try
        {
            _logger.LogInformation("🔍 Vehicle sync check starting for migration types: {Types}", string.Join(", ", request.MigrationTypes));
            progress.Messages.Add($"🔍 Checking vehicle sync requirements for: {string.Join(", ", request.MigrationTypes)}");
            await NotifyProgress(migrationId, progress);

            // Determine if vehicle sync is needed - exclude spare-modules and website-users
            var excludedTypes = new[] { "spare-modules", "website-users" };
            var typesRequiringSync = request.MigrationTypes.Where(t => !excludedTypes.Contains(t)).ToList();
            
            _logger.LogInformation("Types requiring sync after filtering: {Types}", string.Join(", ", typesRequiringSync));
            
            if (!typesRequiringSync.Any())
            {
                _logger.LogInformation("No migration types require vehicle sync - only spare-modules and/or website-users selected");
                progress.Messages.Add("ℹ️ No vehicle sync required - only spare-modules and/or website-users were processed");
                await NotifyProgress(migrationId, progress);
                return;
            }

            _logger.LogInformation("Vehicle sync needed for migration types: {Types}", string.Join(", ", typesRequiringSync));
            progress.Messages.Add("🔄 Preparing Vehicle Settings Sync...");
            progress.CurrentStep = "vehicle-sync-settings";
            await NotifyProgress(migrationId, progress);

            // Collect distinct customers from all uploaded CSV files (except excluded types)
            progress.Messages.Add($"📂 Collecting customers from CSV files...");
            await NotifyProgress(migrationId, progress);
            
            var distinctCustomers = await CollectDistinctCustomersFromCsvFilesAsync(typesRequiringSync);
            
            _logger.LogInformation("Collected {Count} distinct customers: {Customers}", distinctCustomers.Count, string.Join(", ", distinctCustomers));
            
            if (!distinctCustomers.Any())
            {
                _logger.LogWarning("No customers found in CSV files for vehicle sync");
                progress.Messages.Add("⚠️ No customers found to sync vehicles");
                await NotifyProgress(migrationId, progress);
                return;
            }

            progress.Messages.Add($"🔍 Found {distinctCustomers.Count} distinct customers: {string.Join(", ", distinctCustomers)}");
            await NotifyProgress(migrationId, progress);

            // Find all vehicles for these customers in the database
            var vehiclesToSync = await GetVehiclesByCustomersAsync(distinctCustomers);
            
            if (!vehiclesToSync.Any())
            {
                _logger.LogWarning("No vehicles found to sync");
                progress.Messages.Add("⚠️ No vehicles found to sync");
                await NotifyProgress(migrationId, progress);
                return;
            }

            // Show customer processing summary
            progress.Messages.Add($"📋 Processing vehicle sync for customers:");
            foreach (var customer in distinctCustomers)
            {
                var customerVehicleCount = await GetVehicleCountForCustomerAsync(customer);
                progress.Messages.Add($"   • {customer}: {customerVehicleCount} vehicles");
            }
            await NotifyProgress(migrationId, progress);

            progress.Messages.Add($"🔄 Starting Vehicle Settings Sync for {vehiclesToSync.Count} vehicles across {distinctCustomers.Count} customers...");
            await NotifyProgress(migrationId, progress);

            // Set up service provider
            if (scopedServiceProvider != null)
            {
                serviceProvider = scopedServiceProvider;
            }
            else
            {
                var scope = _serviceScopeFactory.CreateScope();
                serviceProvider = scope.ServiceProvider;
                scopeToDispose = scope;
            }
            
            var vehicleSyncMigration = serviceProvider.GetRequiredService<VehicleSyncMigration>();
            
            // Create progress callback that updates step status IMMEDIATELY when sync completes
            Func<MigrationResult, Task> progressCallback = async (result) =>
            {
                _logger.LogInformation("🔧 Progress callback triggered - searching for vehicle-sync-settings step");
                _logger.LogInformation("🔧 Available step keys: {StepKeys}", string.Join(", ", progress.StepSummaries.Keys));
                
                if (progress.StepSummaries.TryGetValue("vehicle-sync-settings", out var vehicleSyncSummary))
                {
                    _logger.LogInformation("🔧 Found vehicle-sync-settings step, current status: {CurrentStatus}", vehicleSyncSummary.Status);
                    
                    vehicleSyncSummary.RecordsProcessed = result.RecordsProcessed;
                    vehicleSyncSummary.RecordsInserted = result.RecordsInserted;
                    vehicleSyncSummary.RecordsSkipped = result.RecordsSkipped;
                    vehicleSyncSummary.ErrorCount = result.Errors.Count;
                    vehicleSyncSummary.Status = result.Success ? "Completed" : "Failed";
                    vehicleSyncSummary.EndTime = DateTime.UtcNow;
                    
                    _logger.LogInformation("🔧 Updated vehicle-sync-settings step status to: {NewStatus}", vehicleSyncSummary.Status);
                    
                    // Mark VehicleSync as completed in the progress tracking
                    if (result.Success && !progress.CompletedSteps.Contains("vehicle-sync-settings"))
                    {
                        progress.CompletedSteps.Add("vehicle-sync-settings");
                        _logger.LogInformation("🔧 Added vehicle-sync-settings to CompletedSteps. Total completed: {CompletedCount}", progress.CompletedSteps.Count);
                    }
                    
                    // Update overall summary to include VehicleSync completion
                    UpdateOverallSummary(progress);
                    _logger.LogInformation("🔧 Updated overall summary - Steps: {CompletedSteps}/{TotalSteps}, CompletionPercentage: {CompletionPct}%", 
                        progress.OverallSummary.CompletedSteps, progress.OverallSummary.TotalSteps, progress.OverallSummary.CompletionPercentage);
                    _logger.LogInformation("🔧 CompletedSteps list contains: [{StepsList}]", string.Join(", ", progress.CompletedSteps));
                    
                    // Update overall migration status to Completed since VehicleSync is the final step
                    if (result.Success)
                    {
                        progress.Status = "Completed";
                        progress.Progress = 100;
                        progress.EndTime = DateTime.UtcNow;
                        progress.FinalStatusSet = true; // Prevent redundant updates
                        progress.Messages.Add("📋 Migration report available for download");
                    }
                    else
                    {
                        progress.Status = "Failed";
                        progress.EndTime = DateTime.UtcNow;
                        progress.FinalStatusSet = true; // Prevent redundant updates
                    }
                    
                    _logger.LogInformation("✅ VehicleSync step status updated to {Status} with {RecordsInserted} vehicles synced", vehicleSyncSummary.Status, result.RecordsInserted);
                    
                    // CRITICAL: Send final notification to frontend with complete status
                    _logger.LogWarning("🔧 FINAL NOTIFICATION - CompletedSteps: {CompletedSteps}, TotalSteps: {TotalSteps}, OverallSummary.CompletedSteps: {SummaryCompleted}, OverallSummary.TotalSteps: {SummaryTotal}", 
                        progress.CompletedSteps.Count, progress.StepSummaries.Count, 
                        progress.OverallSummary.CompletedSteps, progress.OverallSummary.TotalSteps);
                    await NotifyProgress(migrationId, progress);
                    _logger.LogInformation("🔧 Final progress notification sent to frontend");
                }
                else
                {
                    _logger.LogError("❌ Could not find vehicle-sync-settings step in StepSummaries! Available keys: {Keys}", string.Join(", ", progress.StepSummaries.Keys));
                }
            };
            
            var syncResult = await ExecuteVehicleSyncForListAsync(vehicleSyncMigration, vehiclesToSync, progressCallback);
            
            if (syncResult.Success)
            {
                // Status update already handled by callback - just add user messages
                progress.Messages.Add($"✅ Vehicle Settings Sync completed: {syncResult.RecordsInserted} vehicles synced");
                if (syncResult.RecordsSkipped > 0)
                {
                    progress.Messages.Add($"⚠️ {syncResult.RecordsSkipped} vehicles could not be synced");
                }
                
                // Show completion summary for each customer
                progress.Messages.Add($"📊 Customer Processing Summary:");
                foreach (var customer in distinctCustomers)
                {
                    progress.Messages.Add($"   ✅ {customer}: Processing completed");
                }
                
                _logger.LogInformation("Vehicle Settings Sync completed successfully for {CustomerCount} customers", distinctCustomers.Count);
                
                // IMPORTANT: Do NOT send progress update here - callback already handled it
                return; // Exit early to prevent redundant processing
            }
            else
            {
                // Vehicle sync failed - update step status and add user messages
                progress.Messages.Add("❌ Vehicle Settings Sync failed - check logs for details");
                var errorMessage = syncResult.Errors.Any() ? string.Join("; ", syncResult.Errors) : "Unknown error";
                _logger.LogError("Vehicle Settings Sync failed: {Error}", errorMessage);
                
                // Update step summary to Failed status
                if (progress.StepSummaries.TryGetValue("vehicle-sync-settings", out var vehicleSyncSummary))
                {
                    vehicleSyncSummary.Status = "Failed";
                    vehicleSyncSummary.EndTime = DateTime.UtcNow;
                    vehicleSyncSummary.ErrorCount = 1;
                    _logger.LogInformation("🔧 Updated vehicle-sync-settings step status to Failed");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception during Vehicle Settings Sync");
            progress.Messages.Add($"❌ Vehicle Settings Sync error: {ex.Message}");
            
            // Update step summary with exception (callback won't have been called in exception case)
            if (progress.StepSummaries.TryGetValue("vehicle-sync-settings", out var vehicleSyncSummary))
            {
                vehicleSyncSummary.Status = "Failed";
                vehicleSyncSummary.EndTime = DateTime.UtcNow;
                vehicleSyncSummary.ErrorCount = 1;
                
                // Update overall summary even on exception
                UpdateOverallSummary(progress);
                
                // Update overall migration status to Failed since VehicleSync had exception
                progress.Status = "Failed";
                progress.EndTime = DateTime.UtcNow;
                progress.FinalStatusSet = true; // Prevent redundant updates
                
                // Send notification to frontend with failed status
                await NotifyProgress(migrationId, progress);
            }
        }
        finally
        {
            // Dispose the scope if we created one
            scopeToDispose?.Dispose();
            
            // Callback already sent notification - no need for redundant updates
            _logger.LogInformation("VehicleSync processing completed, callback already handled status updates");
        }
    }

    /// <summary>
    /// Build consolidated list of vehicles to sync based on selected migration types
    /// </summary>
    private async Task<List<string>> BuildVehicleSyncListAsync(MigrationRequest request, List<string> selectedSyncTypes)
    {
        var vehiclesToSync = new HashSet<string>(); // Use HashSet to avoid duplicates
        
        try
        {
            var csvInputPath = GetCsvInputPath();
            
            foreach (var migrationType in selectedSyncTypes)
            {
                switch (migrationType)
                {
                    case "vehicles":
                        // Sync vehicles from CSV files
                        var vehicleDeviceIds = await GetVehicleDeviceIdsFromCsvAsync(csvInputPath);
                        foreach (var deviceId in vehicleDeviceIds)
                        {
                            vehiclesToSync.Add(deviceId);
                        }
                        _logger.LogInformation("Added {Count} vehicles from CSV for sync", vehicleDeviceIds.Count);
                        break;
                        
                    case "cards":
                        // Sync vehicles from PerVehicleNormalCardAccess table for cards in CSV
                        var cardVehicles = await GetVehiclesFromCardAccessTableAsync("CARD_IMPORT.csv", csvInputPath);
                        foreach (var vehicleId in cardVehicles)
                        {
                            vehiclesToSync.Add(vehicleId);
                        }
                        _logger.LogInformation("Added {Count} vehicles from card access table for sync (matched by person details)", cardVehicles.Count);
                        break;
                        
                    case "supervisor-access":
                        // Sync vehicles from PerVehicleNormalCardAccess table for supervisor cards in CSV
                        var supervisorVehicles = await GetVehiclesFromCardAccessTableAsync("SUPERVISOR_ACCESS_IMPORT.csv", csvInputPath);
                        foreach (var vehicleId in supervisorVehicles)
                        {
                            vehiclesToSync.Add(vehicleId);
                        }
                        _logger.LogInformation("Added {Count} vehicles from supervisor access table for sync (matched by person details)", supervisorVehicles.Count);
                        break;
                        
                    case "driver-blacklist":
                        // Sync vehicles from PerVehicleNormalCardAccess table for blacklist cards in CSV
                        var blacklistVehicles = await GetVehiclesFromCardAccessTableAsync("DRIVER_BLACKLIST_IMPORT.csv", csvInputPath);
                        foreach (var vehicleId in blacklistVehicles)
                        {
                            vehiclesToSync.Add(vehicleId);
                        }
                        _logger.LogInformation("Added {Count} vehicles from blacklist access table for sync (matched by person details)", blacklistVehicles.Count);
                        break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error building vehicle sync list");
        }
        
        return vehiclesToSync.ToList();
    }

    /// <summary>
    /// Collect distinct customers from all migration CSV files (except excluded types)
    /// </summary>
    private async Task<List<string>> CollectDistinctCustomersFromCsvFilesAsync(List<string> migrationTypes)
    {
        var distinctCustomers = new HashSet<string>(); // Use HashSet to avoid duplicates
        
        try
        {
            var csvInputPath = GetCsvInputPath();
            _logger.LogInformation("Looking for CSV files in path: {Path}", csvInputPath);
            
            foreach (var migrationType in migrationTypes)
            {
                _logger.LogInformation("Processing migration type: {Type}", migrationType);
                var customers = await GetCustomersFromCsvByMigrationTypeAsync(migrationType, csvInputPath);
                _logger.LogInformation("Found {Count} customers for {Type}: {Customers}", customers.Count, migrationType, string.Join(", ", customers));
                
                foreach (var customer in customers)
                {
                    if (!string.IsNullOrWhiteSpace(customer))
                    {
                        distinctCustomers.Add(customer);
                        _logger.LogInformation("Added customer: {Customer}", customer);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting distinct customers from CSV files");
        }
        
        return distinctCustomers.ToList();
    }

    /// <summary>
    /// Get customers from CSV file based on migration type
    /// </summary>
    private async Task<List<string>> GetCustomersFromCsvByMigrationTypeAsync(string migrationType, string csvInputPath)
    {
        var customers = new List<string>();
        
        try
        {
            string csvFileName = GetExpectedCsvFileName(migrationType);
            var csvFilePath = Path.Combine(csvInputPath, csvFileName);
            
            _logger.LogInformation("Looking for CSV file: {FileName} at path: {Path}", csvFileName, csvFilePath);
            
            if (!File.Exists(csvFilePath))
            {
                _logger.LogWarning("CSV file not found for migration type {MigrationType}: {Path}", migrationType, csvFilePath);
                return customers;
            }
            
            _logger.LogInformation("Found CSV file for {MigrationType}, processing...", migrationType);

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096, useAsync: true);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            var csvContent = await streamReader.ReadToEndAsync();
            
            // Handle vehicles separately due to "NULL" string handling
            if (migrationType == "vehicles")
            {
                try
                {
                    var customersList = new List<string>();
                    
                    // Preprocess CSV content to handle "NULL" strings in integer fields
                    var cleanedCsvContent = PreprocessCsvForNullValues(csvContent);
                    
                    using var stringReader = new StringReader(cleanedCsvContent);
                    using var csv = new CsvHelper.CsvReader(stringReader, System.Globalization.CultureInfo.InvariantCulture);
                    
                    var vehicleRecords = csv.GetRecords<VehicleImportModel>();
                    foreach (var record in vehicleRecords)
                    {
                        if (!string.IsNullOrWhiteSpace(record.Customer))
                        {
                            customersList.Add(record.Customer);
                        }
                    }
                    customers = customersList;
                    _logger.LogInformation("Successfully parsed vehicle CSV with {Count} customers", customers.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error parsing vehicle CSV - attempting manual parsing");
                    // Fall back to manual parsing to handle "NULL" strings
                    var manualCustomers = await ParseVehicleCsvManuallyAsync(csvFilePath);
                    customers.AddRange(manualCustomers);
                }
            }
            else
            {
                customers = await Task.Run(() =>
                {
                    var customersList = new List<string>();
                    using var stringReader = new StringReader(csvContent);
                    using var csv = new CsvHelper.CsvReader(stringReader, System.Globalization.CultureInfo.InvariantCulture);
                    
                    switch (migrationType)
                    {
                    case "persons":
                        var personRecords = csv.GetRecords<PersonImportModel>();
                        foreach (var record in personRecords)
                        {
                            if (!string.IsNullOrWhiteSpace(record.Customer))
                            {
                                customersList.Add(record.Customer);
                            }
                        }
                        break;
                        
                    case "cards":
                    case "cards-and-vehicle-access":
                        var cardRecords = csv.GetRecords<CardImportModel>();
                        foreach (var record in cardRecords)
                        {
                            if (!string.IsNullOrWhiteSpace(record.Customer))
                            {
                                customersList.Add(record.Customer);
                            }
                        }
                        break;
                        
                    case "preop-checklist":
                        var preopRecords = csv.GetRecords<PreOpChecklistImportModel>();
                        foreach (var record in preopRecords)
                        {
                            if (!string.IsNullOrWhiteSpace(record.Customer))
                            {
                                customersList.Add(record.Customer);
                            }
                        }
                        break;
                        
                    case "supervisor-access":
                        var supervisorRecords = csv.GetRecords<SupervisorAccessImportModel>();
                        foreach (var record in supervisorRecords)
                        {
                            if (!string.IsNullOrWhiteSpace(record.PersonCustomer))
                            {
                                customersList.Add(record.PersonCustomer);
                            }
                        }
                        break;
                        
                    case "driver-blacklist":
                        var blacklistRecords = csv.GetRecords<DriverBlacklistImportModel>();
                        foreach (var record in blacklistRecords)
                        {
                            if (!string.IsNullOrWhiteSpace(record.PersonCustomer))
                            {
                                customersList.Add(record.PersonCustomer);
                            }
                        }
                        break;
                        
                    default:
                        _logger.LogWarning("Unknown migration type for customer extraction: {MigrationType}", migrationType);
                        break;
                }
                
                return customersList;
                });
            }
            
            _logger.LogInformation("Extracted {Count} customers from {MigrationType} CSV", customers.Count, migrationType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading customers from CSV for migration type {MigrationType}", migrationType);
        }
        
        return customers;
    }

    /// <summary>
    /// Manually parse vehicle CSV to handle "NULL" strings in integer fields
    /// </summary>
    private async Task<List<string>> ParseVehicleCsvManuallyAsync(string csvFilePath)
    {
        var customers = new List<string>();
        
        try
        {
            var lines = await File.ReadAllLinesAsync(csvFilePath);
            if (lines.Length <= 1) return customers; // No data rows
            
            var header = lines[0].Split(',');
            var customerIndex = Array.IndexOf(header, "Customer");
            
            if (customerIndex == -1)
            {
                _logger.LogWarning("Customer column not found in vehicle CSV header");
                return customers;
            }
            
            for (int i = 1; i < lines.Length; i++)
            {
                if (string.IsNullOrWhiteSpace(lines[i])) continue;
                
                var values = lines[i].Split(',');
                if (values.Length > customerIndex && !string.IsNullOrWhiteSpace(values[customerIndex]))
                {
                    customers.Add(values[customerIndex].Trim());
                }
            }
            
            _logger.LogInformation("Manual parsing extracted {Count} customers from vehicle CSV", customers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in manual vehicle CSV parsing");
        }
        
        return customers;
    }

    /// <summary>
    /// Preprocess CSV content to replace "NULL" strings with empty values for proper parsing
    /// </summary>
    private string PreprocessCsvForNullValues(string csvContent)
    {
        try
        {
            // Replace standalone "NULL" values (surrounded by commas or at start/end of line) with empty strings
            // This handles cases like: ,NULL, -> ,, and ,NULL\n -> ,\n
            var processedContent = System.Text.RegularExpressions.Regex.Replace(
                csvContent, 
                @"(?<=^|,)\s*NULL\s*(?=,|$)", 
                "", 
                System.Text.RegularExpressions.RegexOptions.Multiline | System.Text.RegularExpressions.RegexOptions.IgnoreCase
            );
            
            _logger.LogDebug("Preprocessed CSV content to replace NULL values with empty strings");
            return processedContent;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error preprocessing CSV content, returning original content");
            return csvContent;
        }
    }

    /// <summary>
    /// Get all vehicle device IDs for the specified customers from the database
    /// </summary>
    private async Task<List<string>> GetVehiclesByCustomersAsync(List<string> customers)
    {
        var vehicleDeviceIds = new List<string>();
        
        try
        {
            using var connection = new Microsoft.Data.SqlClient.SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();
            
            // Build query to get all vehicles for the specified customers
            var customerConditions = new List<string>();
            var parameters = new List<(string name, object value)>();
            
            for (int i = 0; i < customers.Count; i++)
            {
                customerConditions.Add($"customer.CompanyName = @customer{i}");
                parameters.Add(($"@customer{i}", customers[i]));
            }
            
            var query = $@"
                SELECT DISTINCT m.IoTDevice as DeviceID
                FROM dbo.Vehicle v
                INNER JOIN dbo.Module m ON v.ModuleId1 = m.Id
                INNER JOIN dbo.Department dept ON v.DepartmentId = dept.Id
                INNER JOIN dbo.Site site ON dept.SiteId = site.Id
                INNER JOIN dbo.Customer customer ON site.CustomerId = customer.Id
                WHERE ({string.Join(" OR ", customerConditions)})
                AND m.IoTDevice IS NOT NULL
                AND m.IoTDevice != ''";

            using var command = new Microsoft.Data.SqlClient.SqlCommand(query, connection);
            
            // Add all parameters
            foreach (var (name, value) in parameters)
            {
                command.Parameters.AddWithValue(name, value);
            }
            
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var deviceId = reader["DeviceID"]?.ToString();
                if (!string.IsNullOrWhiteSpace(deviceId))
                {
                    vehicleDeviceIds.Add(deviceId);
                }
            }
            
            _logger.LogInformation("Found {Count} vehicles for {CustomerCount} customers", vehicleDeviceIds.Count, customers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicles for customers from database");
        }
        
        return vehicleDeviceIds;
    }

    /// <summary>
    /// Get vehicle count for a specific customer (for progress reporting)
    /// </summary>
    private async Task<int> GetVehicleCountForCustomerAsync(string customer)
    {
        try
        {
            using var connection = new Microsoft.Data.SqlClient.SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();
            
            var query = @"
                SELECT COUNT(DISTINCT m.IoTDevice) 
                FROM dbo.Vehicle v
                INNER JOIN dbo.Module m ON v.ModuleId1 = m.Id
                INNER JOIN dbo.Department dept ON v.DepartmentId = dept.Id
                INNER JOIN dbo.Site site ON dept.SiteId = site.Id
                INNER JOIN dbo.Customer customer ON site.CustomerId = customer.Id
                WHERE customer.CompanyName = @customer
                AND m.IoTDevice IS NOT NULL
                AND m.IoTDevice != ''";

            using var command = new Microsoft.Data.SqlClient.SqlCommand(query, connection);
            command.Parameters.AddWithValue("@customer", customer);
            
            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result ?? 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle count for customer {Customer}", customer);
            return 0;
        }
    }

    /// <summary>
    /// Get vehicle device IDs from CSV file
    /// </summary>
    private async Task<List<string>> GetVehicleDeviceIdsFromCsvAsync(string csvInputPath)
    {
        var deviceIds = new List<string>();
        
        try
        {
            var vehicleCsvPath = Path.Combine(csvInputPath, "VEHICLE_IMPORT.csv");
            if (!File.Exists(vehicleCsvPath))
            {
                _logger.LogWarning("Vehicle CSV file not found: {Path}", vehicleCsvPath);
                return deviceIds;
            }

            // Use async file operations to properly utilize the async method
            using var fileStream = new FileStream(vehicleCsvPath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096, useAsync: true);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            
            // Read the file content asynchronously
            var csvContent = await streamReader.ReadToEndAsync();
            
            // Process CSV content in background task
            deviceIds = await Task.Run(() =>
            {
                var deviceIdsList = new List<string>();
                using var stringReader = new StringReader(csvContent);
                using var csv = new CsvHelper.CsvReader(stringReader, System.Globalization.CultureInfo.InvariantCulture);
                
                var records = csv.GetRecords<VehicleImportModel>();
                foreach (var record in records)
                {
                    if (!string.IsNullOrWhiteSpace(record.DeviceID))
                    {
                        deviceIdsList.Add(record.DeviceID);
                    }
                }
                return deviceIdsList;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading vehicle CSV for device IDs");
        }
        
        return deviceIds;
    }

    /// <summary>
    /// Get vehicles from PerVehicleNormalCardAccess table based on cards in CSV file
    /// </summary>
    private async Task<List<string>> GetVehiclesFromCardAccessTableAsync(string csvFileName, string csvInputPath)
    {
        var vehicleIds = new List<string>();
        
        try
        {
            var csvFilePath = Path.Combine(csvInputPath, csvFileName);
            if (!File.Exists(csvFilePath))
            {
                _logger.LogWarning("CSV file not found: {Path}", csvFilePath);
                return vehicleIds;
            }

            // Read card data from CSV to get person identification fields
            var personIdentifiers = new List<(string Dealer, string Customer, string Site, string Department, string FirstName, string LastName, string Weigand)>();
            using (var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read))
            using (var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8))
            using (var csv = new CsvHelper.CsvReader(streamReader, System.Globalization.CultureInfo.InvariantCulture))
            {
                // Read based on CSV type
                if (csvFileName.Contains("CARD_IMPORT"))
                {
                    var records = csv.GetRecords<CardImportModel>();
                    foreach (var record in records)
                    {
                        if (!string.IsNullOrWhiteSpace(record.Weigand) && 
                            !string.IsNullOrWhiteSpace(record.FirstName) && 
                            !string.IsNullOrWhiteSpace(record.LastName))
                        {
                            personIdentifiers.Add((
                                record.Dealer ?? "",
                                record.Customer ?? "",
                                record.Site ?? "",
                                record.DepartmentName ?? "",
                                record.FirstName,
                                record.LastName,
                                record.Weigand
                            ));
                        }
                    }
                }
                else if (csvFileName.Contains("SUPERVISOR_ACCESS"))
                {
                    var records = csv.GetRecords<SupervisorAccessImportModel>();
                    foreach (var record in records)
                    {
                        if (!string.IsNullOrWhiteSpace(record.Weigand) && 
                            !string.IsNullOrWhiteSpace(record.FirstName) && 
                            !string.IsNullOrWhiteSpace(record.LastName))
                        {
                            personIdentifiers.Add((
                                record.PersonDealer ?? "",
                                record.PersonCustomer ?? "",
                                record.PersonSite ?? "",
                                record.PersonDepartment ?? "",
                                record.FirstName,
                                record.LastName,
                                record.Weigand
                            ));
                        }
                    }
                }
                else if (csvFileName.Contains("DRIVER_BLACKLIST"))
                {
                    var records = csv.GetRecords<DriverBlacklistImportModel>();
                    foreach (var record in records)
                    {
                        if (!string.IsNullOrWhiteSpace(record.Weigand) && 
                            !string.IsNullOrWhiteSpace(record.FirstName) && 
                            !string.IsNullOrWhiteSpace(record.LastName))
                        {
                            personIdentifiers.Add((
                                record.PersonDealer ?? "",
                                record.PersonCustomer ?? "",
                                record.PersonSite ?? "",
                                record.PersonDepartment ?? "",
                                record.FirstName,
                                record.LastName,
                                record.Weigand
                            ));
                        }
                    }
                }
            }

            if (!personIdentifiers.Any())
            {
                _logger.LogWarning("No person identifiers found in CSV file: {Path}", csvFilePath);
                return vehicleIds;
            }

            // Query PerVehicleNormalCardAccess table to get vehicle device IDs
            using var connection = new Microsoft.Data.SqlClient.SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();
            
            // Build dynamic query to match cards based on Person Dealer, Customer, Site, Department, First Name, Last Name, Weigand
            var cardConditions = new List<string>();
            var parameters = new List<(string name, object value)>();
            
            for (int i = 0; i < personIdentifiers.Count; i++)
            {
                var person = personIdentifiers[i];
                var condition = $@"(
                    p.FirstName = @firstName{i} AND 
                    p.LastName = @lastName{i} AND 
                    c.Weigand = @weigand{i} AND
                    dealer.Name = @dealer{i} AND
                    customer.Name = @customer{i} AND
                    site.Name = @site{i} AND
                    dept.Name = @department{i}
                )";
                
                cardConditions.Add(condition);
                parameters.Add(($"@firstName{i}", person.FirstName));
                parameters.Add(($"@lastName{i}", person.LastName));
                parameters.Add(($"@weigand{i}", person.Weigand));
                parameters.Add(($"@dealer{i}", person.Dealer));
                parameters.Add(($"@customer{i}", person.Customer));
                parameters.Add(($"@site{i}", person.Site));
                parameters.Add(($"@department{i}", person.Department));
            }
            
            var query = $@"
                SELECT DISTINCT v.DeviceID 
                FROM PerVehicleNormalCardAccess pvnca
                INNER JOIN Vehicle v ON pvnca.VehicleId = v.Id
                INNER JOIN Card c ON pvnca.CardId = c.Id
                INNER JOIN Person p ON c.PersonId = p.Id
                INNER JOIN Department dept ON p.DepartmentId = dept.Id
                INNER JOIN Site site ON dept.SiteId = site.Id
                INNER JOIN Customer customer ON site.CustomerId = customer.Id
                INNER JOIN Dealer dealer ON customer.DealerId = dealer.Id
                WHERE ({string.Join(" OR ", cardConditions)})
                AND v.DeviceID IS NOT NULL";

            using var command = new Microsoft.Data.SqlClient.SqlCommand(query, connection);
            
            // Add all parameters
            foreach (var (name, value) in parameters)
            {
                command.Parameters.AddWithValue(name, value);
            }

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var deviceId = reader["DeviceID"]?.ToString();
                if (!string.IsNullOrWhiteSpace(deviceId))
                {
                    vehicleIds.Add(deviceId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicles from card access table for {CsvFile}", csvFileName);
        }
        
        return vehicleIds;
    }

    /// <summary>
    /// Execute vehicle sync for a specific list of vehicle device IDs
    /// </summary>
    private async Task<MigrationResult> ExecuteVehicleSyncForListAsync(VehicleSyncMigration vehicleSyncMigration, List<string> vehicleDeviceIds, Func<MigrationResult, Task>? progressCallback = null)
    {
        try
        {
            // Create a temporary CSV file with just the vehicle device IDs we want to sync
            var tempCsvPath = Path.Combine(Path.GetTempPath(), $"vehicle_sync_{Guid.NewGuid()}.csv");
            
            using (var writer = new StreamWriter(tempCsvPath))
            using (var csv = new CsvHelper.CsvWriter(writer, System.Globalization.CultureInfo.InvariantCulture))
            {
                // Write header
                csv.WriteHeader<VehicleImportModel>();
                csv.NextRecord();
                
                // Write vehicle records with minimal data needed for sync
                foreach (var deviceId in vehicleDeviceIds)
                {
                    var record = new VehicleImportModel
                    {
                        DeviceID = deviceId,
                        // Set minimal required fields
                        Dealer = "SYNC",
                        Customer = "SYNC", 
                        Site = "SYNC",
                        DepartmentName = "SYNC"
                    };
                    csv.WriteRecord(record);
                    csv.NextRecord();
                }
            }

            // Execute sync with temporary CSV and progress callback
            var result = await vehicleSyncMigration.ExecuteAsync(tempCsvPath, progressCallback);
            
            // Clean up temporary file
            try
            {
                File.Delete(tempCsvPath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete temporary sync CSV file: {Path}", tempCsvPath);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing vehicle sync for device list");
            return new MigrationResult
            {
                Success = false,
                Errors = new List<string> { $"Vehicle sync failed: {ex.Message}" }
            };
        }
    }

    /// <summary>
    /// Initialize progress summaries for each migration step
    /// </summary>
    private void InitializeProgressSummaries(MigrationProgress progress, List<string> requestedSteps)
    {
        // Map step IDs to friendly names
        var stepNames = new Dictionary<string, string>
        {
            ["spare-modules"] = "Spare Modules",
            ["preop-checklist"] = "Pre-Op Checklist",
            ["vehicles"] = "Vehicles",
            ["persons"] = "Persons",
            ["cards-and-vehicle-access"] = "Cards & Vehicle Access",
            ["supervisor-access"] = "Supervisor Access",
            ["driver-blacklist"] = "Driver Blacklist",
            ["website-users"] = "Website Users",
            ["vehicle-sync-settings"] = "Vehicle Settings Sync"
        };

        progress.StepSummaries.Clear();

        foreach (var stepId in requestedSteps)
        {
            var stepName = stepNames.GetValueOrDefault(stepId, stepId);
            progress.StepSummaries[stepId] = new MigrationStepSummary
            {
                StepId = stepId,
                StepName = stepName,
                Status = "Pending"
            };
        }
        
        // Update overall summary to set TotalSteps based on actual step summaries count
        UpdateOverallSummary(progress);
    }

    /// <summary>
    /// Update step summary status and timing
    /// </summary>
    private void UpdateStepSummaryStatus(MigrationProgress progress, string stepId, string status, DateTime? startTime = null, DateTime? endTime = null)
    {
        if (progress.StepSummaries.TryGetValue(stepId, out var summary))
        {
            summary.Status = status;
            if (startTime.HasValue)
                summary.StartTime = startTime;
            if (endTime.HasValue)
                summary.EndTime = endTime;
        }
    }

    /// <summary>
    /// Update step summary with detailed results from migration execution
    /// </summary>
    private void UpdateStepSummaryFromResult(MigrationProgress progress, string stepId, MigrationOrchestrationResult stepResult, string status)
    {
        if (progress.StepSummaries.TryGetValue(stepId, out var summary))
        {
            summary.Status = status;
            summary.EndTime = DateTime.UtcNow;

            _logger.LogInformation("🔧 UpdateStepSummaryFromResult for {StepId}: Found {Count} step results in orchestration result", 
                stepId, stepResult.StepResults.Count);

            // FIXED: Find the specific step result that matches this stepId
            var matchingResult = stepResult.StepResults.FirstOrDefault(r => IsStepResultForStep(r, stepId));
            
            if (matchingResult != null)
            {
                // Only update with data from the matching step result, not all step results
                summary.RecordsProcessed = matchingResult.RecordsProcessed;
                summary.RecordsInserted = matchingResult.RecordsInserted;
                summary.RecordsSkipped = matchingResult.RecordsSkipped;
                summary.ErrorCount = matchingResult.Errors.Count;
                
                _logger.LogInformation("🔧 Updated {StepId} with matching result: {Processed} processed, {Inserted} inserted, {Skipped} skipped (from StepResult: {StepName})", 
                    stepId, matchingResult.RecordsProcessed, matchingResult.RecordsInserted, matchingResult.RecordsSkipped, matchingResult.StepName);
            }
            else
            {
                _logger.LogWarning("⚠️ No matching step result found for {StepId} in orchestration result", stepId);
            }

            _logger.LogInformation("🔧 Step {StepId} final totals: {Processed} processed, {Inserted} inserted, {Skipped} skipped, {Errors} errors", 
                stepId, summary.RecordsProcessed, summary.RecordsInserted, summary.RecordsSkipped, summary.ErrorCount);

            // Update completed steps tracking
            if (status == "Completed")
            {
                if (!progress.CompletedSteps.Contains(stepId))
                {
                    progress.CompletedSteps.Add(stepId);
                }
            }

            // Update overall summary
            UpdateOverallSummary(progress);
        }
        else
        {
            _logger.LogWarning("⚠️ Could not find step summary for {StepId} to update with result data", stepId);
        }
    }

    /// <summary>
    /// Helper method to determine if a step result belongs to a specific step
    /// </summary>
    private bool IsStepResultForStep(MigrationStepResult stepResult, string stepId)
    {
        // Map step IDs to their corresponding migration names
        var stepMigrationNames = new Dictionary<string, string[]>
        {
            ["spare-modules"] = new[] { "Spare Module Migration" },
            ["preop-checklist"] = new[] { "Department Checklist + PreOp Questions Migration", "PreOp Checklist Migration" },
            ["vehicles"] = new[] { "Vehicle Migration" },
            ["persons"] = new[] { "Person Migration" },
            ["cards-and-vehicle-access"] = new[] { "Card + Vehicle Access Migration", "Card Migration" },
            ["supervisor-access"] = new[] { "Supervisor Access Migration" },
            ["driver-blacklist"] = new[] { "Driver Blacklist Migration" },
            ["website-users"] = new[] { "Website User Migration" },
            ["vehicle-sync-settings"] = new[] { "Vehicle Sync Settings Migration" }
        };

        if (stepMigrationNames.TryGetValue(stepId, out var possibleNames))
        {
            return possibleNames.Contains(stepResult.StepName);
        }

        return false;
    }

    /// <summary>
    /// Update overall summary from completed step summaries
    /// </summary>
    private void UpdateOverallSummary(MigrationProgress progress)
    {
        var summary = progress.OverallSummary;
        summary.CompletedSteps = progress.CompletedSteps.Count;
        
        // Ensure TotalSteps matches the actual number of step summaries
        // This handles cases where vehicle sync step is added dynamically
        summary.TotalSteps = progress.StepSummaries.Count;
        
        // Log individual step summary data for debugging
        foreach (var kvp in progress.StepSummaries)
        {
            var stepSummary = kvp.Value;
            _logger.LogInformation("🔧 Step {StepId} data: {Processed} processed, {Inserted} inserted, {Skipped} skipped, {Errors} errors", 
                kvp.Key, stepSummary.RecordsProcessed, stepSummary.RecordsInserted, stepSummary.RecordsSkipped, stepSummary.ErrorCount);
        }
        
        // Aggregate totals from all step summaries
        summary.TotalRecordsProcessed = progress.StepSummaries.Values.Sum(s => s.RecordsProcessed);
        summary.TotalRecordsInserted = progress.StepSummaries.Values.Sum(s => s.RecordsInserted);
        summary.TotalRecordsSkipped = progress.StepSummaries.Values.Sum(s => s.RecordsSkipped);
        summary.TotalErrors = progress.StepSummaries.Values.Sum(s => s.ErrorCount);
        
        _logger.LogInformation("🔧 UpdateOverallSummary calculated totals: {Processed} processed, {Inserted} inserted, {Skipped} skipped, {Errors} errors", 
            summary.TotalRecordsProcessed, summary.TotalRecordsInserted, summary.TotalRecordsSkipped, summary.TotalErrors);
        
        // Note: Warnings removed as they represent the same data as skipped records
    }

    /// <summary>
    /// Final update of progress summaries from orchestration result
    /// </summary>
    private void UpdateProgressSummariesFromResult(MigrationProgress progress, MigrationOrchestrationResult result)
    {
        // CRITICAL DEBUG: Log what UpdateProgressSummariesFromResult receives
        _logger.LogWarning("🔧 UpdateProgressSummariesFromResult called with {CompletedStepsCount} completed steps, {StepResultsCount} step results", 
            result.CompletedSteps.Count, result.StepResults.Count);
        
        // CRITICAL FIX: Transfer step result data to step summaries
        foreach (var stepId in result.CompletedSteps)
        {
            // Add to CompletedSteps list
            if (!progress.CompletedSteps.Contains(stepId))
            {
                progress.CompletedSteps.Add(stepId);
            }
            
            // Update step status to Completed
            if (progress.StepSummaries.TryGetValue(stepId, out var summary))
            {
                summary.Status = "Completed";
                summary.EndTime = DateTime.UtcNow;
                
                // Find the matching step result for this specific step
                var matchingResult = result.StepResults.FirstOrDefault(r => IsStepResultForStep(r, stepId));
                if (matchingResult != null)
                {
                    // Set the correct data for this step only
                    summary.RecordsProcessed = matchingResult.RecordsProcessed;
                    summary.RecordsInserted = matchingResult.RecordsInserted;
                    summary.RecordsSkipped = matchingResult.RecordsSkipped;
                    summary.ErrorCount = matchingResult.Errors.Count;
                    
                    _logger.LogInformation("🔧 Final update for {StepId}: {Processed} processed, {Inserted} inserted, {Skipped} skipped (from: {StepName})", 
                        stepId, matchingResult.RecordsProcessed, matchingResult.RecordsInserted, matchingResult.RecordsSkipped, matchingResult.StepName);
                }
                else
                {
                    _logger.LogWarning("⚠️ No matching step result found for completed step {StepId}", stepId);
                }
            }
        }

        // CRITICAL FIX: Ensure all steps that were requested are marked as completed if the overall migration succeeded
        if (result.Success)
        {
            foreach (var kvp in progress.StepSummaries)
            {
                var stepId = kvp.Key;
                var summary = kvp.Value;
                
                // If the step is still in "Running" or "Pending" status but the migration succeeded,
                // mark it as completed (this handles edge cases where step status wasn't properly updated)
                if (summary.Status == "Running" || summary.Status == "Pending")
                {
                    _logger.LogWarning("🔧 FIXING: Step {StepId} was in {Status} status but migration succeeded - marking as Completed", 
                        stepId, summary.Status);
                    
                    summary.Status = "Completed";
                    summary.EndTime = DateTime.UtcNow;
                    
                    // Add to completed steps if not already there
                    if (!progress.CompletedSteps.Contains(stepId))
                    {
                        progress.CompletedSteps.Add(stepId);
                    }
                    
                    // Try to find matching step result for data
                    var matchingResult = result.StepResults.FirstOrDefault(r => IsStepResultForStep(r, stepId));
                    if (matchingResult != null)
                    {
                        summary.RecordsProcessed = matchingResult.RecordsProcessed;
                        summary.RecordsInserted = matchingResult.RecordsInserted;
                        summary.RecordsSkipped = matchingResult.RecordsSkipped;
                        summary.ErrorCount = matchingResult.Errors.Count;
                        
                        _logger.LogInformation("🔧 Fixed step {StepId} with data: {Processed} processed, {Inserted} inserted, {Skipped} skipped", 
                            stepId, matchingResult.RecordsProcessed, matchingResult.RecordsInserted, matchingResult.RecordsSkipped);
                    }
                    else
                    {
                        _logger.LogWarning("⚠️ No matching step result found for fixed step {StepId} - using existing data", stepId);
                    }
                }
            }
        }

        // Log overall data before update
        var totalProcessed = progress.StepSummaries.Values.Sum(s => s.RecordsProcessed);
        var totalInserted = progress.StepSummaries.Values.Sum(s => s.RecordsInserted);
        var totalSkipped = progress.StepSummaries.Values.Sum(s => s.RecordsSkipped);
        _logger.LogInformation("🔧 Before UpdateOverallSummary - Totals: {Processed} processed, {Inserted} inserted, {Skipped} skipped", 
            totalProcessed, totalInserted, totalSkipped);

        // Update final overall summary
        UpdateOverallSummary(progress);
        
        // Log overall data after update
        _logger.LogInformation("🔧 After UpdateOverallSummary - OverallSummary: {Processed} processed, {Inserted} inserted, {Skipped} skipped", 
            progress.OverallSummary.TotalRecordsProcessed, progress.OverallSummary.TotalRecordsInserted, progress.OverallSummary.TotalRecordsSkipped);
    }
} 