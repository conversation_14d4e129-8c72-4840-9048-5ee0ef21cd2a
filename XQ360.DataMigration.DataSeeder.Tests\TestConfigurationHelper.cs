using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;

namespace XQ360.DataMigration.DataSeeder.Tests
{
    /// <summary>
    /// Helper class to provide test configuration for DataSeeder tests
    /// Mirrors the pattern from the main test project but with DataSeeder-specific configuration
    /// </summary>
    public static class TestConfigurationHelper
    {
        private static IEnvironmentConfigurationService? _environmentService;
        private static BulkSeederConfiguration? _bulkSeederConfig;
        private static readonly object _lock = new object();

        public static IEnvironmentConfigurationService GetEnvironmentService()
        {
            if (_environmentService == null)
            {
                lock (_lock)
                {
                    if (_environmentService == null)
                    {
                        var configuration = new ConfigurationBuilder()
                            .AddJsonFile("appsettings.json", optional: false)
                            .Build();

                        var environmentConfig = new EnvironmentConfiguration();
                        configuration.GetSection("Migration").Bind(environmentConfig);

                        var options = Options.Create(environmentConfig);
                        _environmentService = new EnvironmentConfigurationService(options);
                        
                        // Set default environment to Development for tests
                        _environmentService.SetCurrentEnvironment("Development");
                    }
                }
            }
            return _environmentService;
        }

        public static BulkSeederConfiguration GetBulkSeederConfiguration()
        {
            if (_bulkSeederConfig == null)
            {
                lock (_lock)
                {
                    if (_bulkSeederConfig == null)
                    {
                        var configuration = new ConfigurationBuilder()
                            .AddJsonFile("appsettings.json", optional: false)
                            .Build();

                        _bulkSeederConfig = new BulkSeederConfiguration();
                        configuration.GetSection("BulkSeeder").Bind(_bulkSeederConfig);
                    }
                }
            }
            return _bulkSeederConfig;
        }

        public static MigrationConfiguration GetTestConfiguration()
        {
            var envService = GetEnvironmentService();
            return envService.CurrentMigrationConfiguration;
        }

        public static string GetTestConnectionString()
        {
            var envService = GetEnvironmentService();
            return envService.CurrentEnvironment.DatabaseConnection;
        }

        public static EnvironmentSettings GetTestEnvironment()
        {
            var envService = GetEnvironmentService();
            return envService.CurrentEnvironment;
        }

        /// <summary>
        /// Creates test SeederOptions with default values for testing
        /// </summary>
        public static SeederOptions CreateTestSeederOptions(
            int? driversCount = 10,
            int? vehiclesCount = 5,
            bool dryRun = true,
            int? batchSize = 100)
        {
            return new SeederOptions
            {
                DriversCount = driversCount,
                VehiclesCount = vehiclesCount,
                DryRun = dryRun,
                BatchSize = batchSize,
                GenerateData = true,
                Interactive = false,
                DealerId = "TEST-DEALER-001",
                CustomerName = "Test Customer"
            };
        }

        /// <summary>
        /// Creates test MigrationPatternSeederOptions with default values for testing
        /// </summary>
        public static MigrationPatternSeederOptions CreateTestMigrationPatternOptions(
            int? driversCount = 10,
            int? vehiclesCount = 5,
            bool dryRun = true)
        {
            return new MigrationPatternSeederOptions
            {
                DriversCount = driversCount,
                VehiclesCount = vehiclesCount,
                DryRun = dryRun,
                BatchSize = 100,
                GenerateData = true,
                Interactive = false,
                DealerId = "TEST-DEALER-001",
                CustomerName = "Test Customer",
                UseApiForPersonCreation = false, // Disable API calls in tests by default
                UseComplexVehicleCreation = true,
                CreateCardAccessPermissions = true,
                SupervisorPercentage = 10,
                WebsiteAccessPercentage = 25,
                ApiBatchSize = 50,
                ApiRateLimit = 25
            };
        }
    }
}
