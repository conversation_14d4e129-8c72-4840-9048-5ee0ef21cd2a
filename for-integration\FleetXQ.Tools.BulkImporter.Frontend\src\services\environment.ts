import { ApiBaseService } from './api-base'
import type { Environment, EnvironmentValidation } from '@/types/environment'

export interface EnvironmentListResponse {
  environments: Environment[]
  currentEnvironment: string
}

export interface EnvironmentValidationRequest {
  operationSize: number
  operationType?: 'import' | 'export' | 'validation'
}

export interface EnvironmentValidationResponse {
  isValid: boolean
  errorMessage?: string
  requiresApproval: boolean
  warnings: string[]
  environmentName: string
  maxOperationSize: number
  isInMaintenanceWindow: boolean
}

export class EnvironmentService extends ApiBaseService {
  constructor() {
    super('/api')
  }

  /**
   * Get list of available environments
   */
  async getEnvironments(): Promise<EnvironmentListResponse> {
    try {
      return await this.get<EnvironmentListResponse>('/environments')
    } catch (error) {
      console.error('Failed to fetch environments:', error)
      throw error
    }
  }

  /**
   * Get current environment information
   */
  async getCurrentEnvironment(): Promise<Environment> {
    try {
      return await this.get<Environment>('/environment')
    } catch (error) {
      console.error('Failed to fetch current environment:', error)
      throw error
    }
  }

  /**
   * Validate if an operation is allowed in the current environment
   */
  async validateOperation(request: EnvironmentValidationRequest): Promise<EnvironmentValidationResponse> {
    try {
      const params = new URLSearchParams({
        operationSize: request.operationSize.toString()
      })

      if (request.operationType) {
        params.append('operationType', request.operationType)
      }

      return await this.get<EnvironmentValidationResponse>(`/environment/validate?${params.toString()}`)
    } catch (error) {
      console.error('Failed to validate environment operation:', error)
      throw error
    }
  }

  /**
   * Check if environment is in maintenance window
   */
  async isInMaintenanceWindow(): Promise<boolean> {
    try {
      const environment = await this.getCurrentEnvironment()
      return environment.isInMaintenanceWindow
    } catch (error) {
      console.error('Failed to check maintenance window:', error)
      return false
    }
  }

  /**
   * Get environment-specific operation limits
   */
  async getOperationLimits(): Promise<{ maxDrivers: number; maxVehicles: number; maxBatchSize: number }> {
    try {
      const environment = await this.getCurrentEnvironment()
      return {
        maxDrivers: environment.maxOperationSize,
        maxVehicles: environment.maxOperationSize,
        maxBatchSize: environment.maxOperationSize
      }
    } catch (error) {
      console.error('Failed to get operation limits:', error)
      // Return safe defaults
      return {
        maxDrivers: 1000,
        maxVehicles: 1000,
        maxBatchSize: 500
      }
    }
  }

  /**
   * Get maintenance windows for environment
   */
  async getMaintenanceWindows(): Promise<Array<{
    start: string
    end: string
    timeZone: string
    description: string
  }>> {
    try {
      const environment = await this.getCurrentEnvironment()
      return environment.maintenanceWindows || []
    } catch (error) {
      console.error('Failed to get maintenance windows:', error)
      return []
    }
  }

  /**
   * Check if environment requires approval for operations
   */
  async requiresApproval(operationSize: number): Promise<boolean> {
    try {
      const validation = await this.validateOperation({ operationSize })
      return validation.requiresApproval
    } catch (error) {
      console.error('Failed to check approval requirement:', error)
      return true // Default to requiring approval on error
    }
  }

  /**
   * Get environment status summary
   */
  async getEnvironmentStatus(): Promise<{
    name: string
    displayName: string
    isHealthy: boolean
    isInMaintenance: boolean
    requiresApproval: boolean
    maxOperationSize: number
    warnings: string[]
  }> {
    try {
      const environment = await this.getCurrentEnvironment()
      return {
        name: environment.name,
        displayName: environment.displayName,
        isHealthy: true, // This would come from health checks
        isInMaintenance: environment.isInMaintenanceWindow,
        requiresApproval: environment.requiresApproval,
        maxOperationSize: environment.maxOperationSize,
        warnings: [] // This would come from environment monitoring
      }
    } catch (error) {
      console.error('Failed to get environment status:', error)
      throw error
    }
  }
}
