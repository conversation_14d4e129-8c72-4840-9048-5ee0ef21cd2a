{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/pinia/dist/pinia.d.ts", "./src/types/environment.ts", "./node_modules/axios/index.d.ts", "./src/services/api-base.ts", "./src/services/environment.ts", "./src/types/dealer.ts", "./src/services/dealer.ts", "./src/types/customer.ts", "./src/services/customer.ts", "./src/types/import-session.ts", "./src/services/bulk-import.ts", "./src/services/data-generation.ts", "./node_modules/@microsoft/signalr/dist/esm/abortcontroller.d.ts", "./node_modules/@microsoft/signalr/dist/esm/itransport.d.ts", "./node_modules/@microsoft/signalr/dist/esm/errors.d.ts", "./node_modules/@microsoft/signalr/dist/esm/ilogger.d.ts", "./node_modules/@microsoft/signalr/dist/esm/ihubprotocol.d.ts", "./node_modules/@microsoft/signalr/dist/esm/httpclient.d.ts", "./node_modules/@microsoft/signalr/dist/esm/defaulthttpclient.d.ts", "./node_modules/@microsoft/signalr/dist/esm/ihttpconnectionoptions.d.ts", "./node_modules/@microsoft/signalr/dist/esm/stream.d.ts", "./node_modules/@microsoft/signalr/dist/esm/hubconnection.d.ts", "./node_modules/@microsoft/signalr/dist/esm/iretrypolicy.d.ts", "./node_modules/@microsoft/signalr/dist/esm/hubconnectionbuilder.d.ts", "./node_modules/@microsoft/signalr/dist/esm/loggers.d.ts", "./node_modules/@microsoft/signalr/dist/esm/jsonhubprotocol.d.ts", "./node_modules/@microsoft/signalr/dist/esm/subject.d.ts", "./node_modules/@microsoft/signalr/dist/esm/utils.d.ts", "./node_modules/@microsoft/signalr/dist/esm/index.d.ts", "./src/services/signalr.ts", "./src/services/index.ts", "./src/stores/environment.ts", "./src/app.vue.ts", "./src/stores/customer.ts", "./src/components/customerselector.vue.ts", "./src/stores/dealer.ts", "./src/components/dealerselector.vue.ts", "./src/components/drivercountinput.vue.ts", "./src/components/environmentselector.vue.ts", "./src/components/progresstracker.vue.ts", "./src/components/vehiclecountinput.vue.ts", "./src/views/homeview.vue.ts", "./node_modules/vue-router/dist/vue-router.d.ts", "./src/types/notification.ts", "./src/stores/notification.ts", "./src/views/importwizardview.vue.ts", "./src/views/notfoundview.vue.ts", "./src/views/sessionsview.vue.ts", "./src/views/settingsview.vue.ts", "./__vls_types.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./env.d.ts", "./src/router/index.ts", "./src/main.ts", "./src/composables/usevalidation.ts", "./src/composables/useformfield.ts", "./src/composables/usebusinessvalidation.ts", "./src/composables/useerrorhandling.ts", "./src/composables/index.ts", "./src/stores/import-session.ts", "./src/utils/validation.ts", "./node_modules/@types/node/compatibility/disposable.d.ts", "./node_modules/@types/node/compatibility/indexable.d.ts", "./node_modules/@types/node/compatibility/iterators.d.ts", "./node_modules/@types/node/compatibility/index.d.ts", "./node_modules/@types/node/ts5.6/globals.typedarray.d.ts", "./node_modules/@types/node/ts5.6/buffer.buffer.d.ts", "./node_modules/undici-types/header.d.ts", "./node_modules/undici-types/readable.d.ts", "./node_modules/undici-types/file.d.ts", "./node_modules/undici-types/fetch.d.ts", "./node_modules/undici-types/formdata.d.ts", "./node_modules/undici-types/connector.d.ts", "./node_modules/undici-types/client.d.ts", "./node_modules/undici-types/errors.d.ts", "./node_modules/undici-types/dispatcher.d.ts", "./node_modules/undici-types/global-dispatcher.d.ts", "./node_modules/undici-types/global-origin.d.ts", "./node_modules/undici-types/pool-stats.d.ts", "./node_modules/undici-types/pool.d.ts", "./node_modules/undici-types/handlers.d.ts", "./node_modules/undici-types/balanced-pool.d.ts", "./node_modules/undici-types/agent.d.ts", "./node_modules/undici-types/mock-interceptor.d.ts", "./node_modules/undici-types/mock-agent.d.ts", "./node_modules/undici-types/mock-client.d.ts", "./node_modules/undici-types/mock-pool.d.ts", "./node_modules/undici-types/mock-errors.d.ts", "./node_modules/undici-types/proxy-agent.d.ts", "./node_modules/undici-types/api.d.ts", "./node_modules/undici-types/cookies.d.ts", "./node_modules/undici-types/patch.d.ts", "./node_modules/undici-types/filereader.d.ts", "./node_modules/undici-types/diagnostics-channel.d.ts", "./node_modules/undici-types/websocket.d.ts", "./node_modules/undici-types/content-type.d.ts", "./node_modules/undici-types/cache.d.ts", "./node_modules/undici-types/interceptors.d.ts", "./node_modules/undici-types/index.d.ts", "./node_modules/@types/node/globals.d.ts", "./node_modules/@types/node/assert.d.ts", "./node_modules/@types/node/assert/strict.d.ts", "./node_modules/@types/node/async_hooks.d.ts", "./node_modules/@types/node/buffer.d.ts", "./node_modules/@types/node/child_process.d.ts", "./node_modules/@types/node/cluster.d.ts", "./node_modules/@types/node/console.d.ts", "./node_modules/@types/node/constants.d.ts", "./node_modules/@types/node/crypto.d.ts", "./node_modules/@types/node/dgram.d.ts", "./node_modules/@types/node/diagnostics_channel.d.ts", "./node_modules/@types/node/dns.d.ts", "./node_modules/@types/node/dns/promises.d.ts", "./node_modules/@types/node/domain.d.ts", "./node_modules/@types/node/dom-events.d.ts", "./node_modules/@types/node/events.d.ts", "./node_modules/@types/node/fs.d.ts", "./node_modules/@types/node/fs/promises.d.ts", "./node_modules/@types/node/http.d.ts", "./node_modules/@types/node/http2.d.ts", "./node_modules/@types/node/https.d.ts", "./node_modules/@types/node/inspector.d.ts", "./node_modules/@types/node/module.d.ts", "./node_modules/@types/node/net.d.ts", "./node_modules/@types/node/os.d.ts", "./node_modules/@types/node/path.d.ts", "./node_modules/@types/node/perf_hooks.d.ts", "./node_modules/@types/node/process.d.ts", "./node_modules/@types/node/punycode.d.ts", "./node_modules/@types/node/querystring.d.ts", "./node_modules/@types/node/readline.d.ts", "./node_modules/@types/node/readline/promises.d.ts", "./node_modules/@types/node/repl.d.ts", "./node_modules/@types/node/stream.d.ts", "./node_modules/@types/node/stream/promises.d.ts", "./node_modules/@types/node/stream/consumers.d.ts", "./node_modules/@types/node/stream/web.d.ts", "./node_modules/@types/node/string_decoder.d.ts", "./node_modules/@types/node/test.d.ts", "./node_modules/@types/node/timers.d.ts", "./node_modules/@types/node/timers/promises.d.ts", "./node_modules/@types/node/tls.d.ts", "./node_modules/@types/node/trace_events.d.ts", "./node_modules/@types/node/tty.d.ts", "./node_modules/@types/node/url.d.ts", "./node_modules/@types/node/util.d.ts", "./node_modules/@types/node/v8.d.ts", "./node_modules/@types/node/vm.d.ts", "./node_modules/@types/node/wasi.d.ts", "./node_modules/@types/node/worker_threads.d.ts", "./node_modules/@types/node/zlib.d.ts", "./node_modules/@types/node/ts5.6/index.d.ts", "./src/views/settingsview.vue", "./src/views/notfoundview.vue", "./src/app.vue"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0"], "root": [59, [61, 69], [87, 99], [101, 107], [114, 123]], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[48, 50, 51, 56, 58, 100, 129, 166], [113, 129, 166], [52, 129, 166], [129, 166], [73, 75, 129, 166], [71, 129, 166], [70, 74, 129, 166], [78, 129, 166], [71, 73, 74, 77, 79, 80, 129, 166], [71, 73, 74, 75, 129, 166], [71, 73, 129, 166], [70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 129, 166], [71, 73, 74, 129, 166], [73, 129, 166], [73, 75, 77, 78, 84, 129, 166], [129, 163, 166], [129, 165, 166], [129, 166, 171, 199], [129, 166, 167, 178, 179, 186, 196, 207], [129, 166, 167, 168, 178, 186], [124, 125, 126, 129, 166], [129, 166, 169, 208], [129, 166, 170, 171, 179, 187], [129, 166, 171, 196, 204], [129, 166, 172, 174, 178, 186], [129, 165, 166, 173], [129, 166, 174, 175], [129, 166, 176, 178], [129, 165, 166, 178], [129, 166, 178, 179, 180, 196, 207], [129, 166, 178, 179, 180, 193, 196, 199], [129, 161, 166], [129, 166, 174, 178, 181, 186, 196, 207], [129, 166, 178, 179, 181, 182, 186, 196, 204, 207], [129, 166, 181, 183, 196, 204, 207], [129, 166, 178, 184], [129, 166, 185, 207, 212], [129, 166, 174, 178, 186, 196], [129, 166, 187], [129, 166, 188], [129, 165, 166, 189], [129, 166, 190, 206, 212], [129, 166, 191], [129, 166, 192], [129, 166, 178, 193, 194], [129, 166, 193, 195, 208, 210], [129, 166, 178, 196, 197, 199], [129, 166, 198, 199], [129, 166, 196, 197], [129, 166, 199], [129, 166, 200], [129, 166, 196, 201], [129, 166, 178, 202, 203], [129, 166, 202, 203], [129, 166, 171, 186, 196, 204], [129, 166, 205], [166], [127, 128, 129, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213], [129, 166, 186, 206], [129, 166, 181, 192, 207], [129, 166, 171, 208], [129, 166, 196, 209], [129, 166, 185, 210], [129, 166, 211], [129, 166, 178, 180, 189, 196, 199, 207, 210, 212], [129, 166, 196, 213], [46, 52, 53, 129, 166], [54, 129, 166], [46, 129, 166], [46, 47, 48, 50, 129, 166], [47, 48, 49, 50, 129, 166], [56, 57, 100, 129, 166], [129, 138, 142, 166, 207], [129, 138, 166, 196, 207], [129, 133, 166], [129, 135, 138, 166, 204, 207], [129, 166, 186, 204], [129, 166, 214], [129, 133, 166, 214], [129, 135, 138, 166, 186, 207], [129, 130, 131, 134, 137, 166, 178, 196, 207], [129, 130, 136, 166], [129, 134, 138, 166, 199, 207, 214], [129, 154, 166, 214], [129, 132, 133, 166, 214], [129, 138, 166], [129, 132, 133, 134, 135, 136, 137, 138, 139, 140, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 155, 156, 157, 158, 159, 160, 166], [129, 138, 145, 146, 166], [129, 136, 138, 146, 147, 166], [129, 137, 166], [129, 130, 133, 138, 166], [129, 138, 142, 146, 147, 166], [129, 142, 166], [129, 136, 138, 141, 166, 207], [129, 130, 135, 136, 138, 142, 145, 166], [129, 166, 196], [129, 133, 138, 154, 166, 212, 214], [112, 129, 166], [108, 129, 166], [109, 129, 166], [110, 111, 129, 166], [56, 58, 100, 129, 166], [50, 55, 129, 166], [50, 129, 166], [51, 56, 58, 89, 100, 129, 166], [51, 56, 58, 65, 91, 100, 129, 166], [51, 56, 58, 63, 93, 100, 129, 166], [51, 56, 58, 100, 129, 166], [51, 56, 58, 59, 89, 100, 129, 166], [51, 56, 58, 87, 100, 129, 166], [51, 117, 118, 119, 120, 129, 166], [51, 56, 58, 59, 63, 65, 89, 91, 93, 100, 117, 129, 166], [51, 56, 58, 88, 100, 102, 129, 166], [51, 56, 58, 100, 117, 129, 166], [51, 56, 58, 90, 100, 113, 115, 129, 166], [51, 99, 100, 103, 104, 105, 106, 129, 166], [51, 60, 129, 166], [51, 61, 67, 129, 166], [51, 61, 65, 129, 166], [51, 61, 129, 166], [51, 61, 63, 129, 166], [51, 59, 61, 129, 166], [51, 61, 62, 64, 66, 68, 69, 87, 129, 166], [51, 56, 58, 86, 100, 129, 166], [51, 56, 58, 65, 88, 100, 129, 166], [51, 56, 58, 63, 88, 100, 129, 166], [51, 56, 58, 59, 88, 100, 129, 166], [51, 56, 58, 67, 88, 100, 129, 166], [51, 56, 58, 100, 101, 129, 166], [51, 129, 166], [51, 65, 129, 166], [51, 117, 129, 166], [51, 56, 58, 59, 63, 65, 89, 91, 92, 93, 94, 95, 96, 97, 98, 100, 102, 129, 166], [48, 50, 51, 56, 58, 128, 136, 173], [52, 137, 174], [137, 174], [134, 171], [73, 75, 134, 171], [71, 134, 171], [70, 74, 134, 171], [78, 134, 171], [71, 73, 74, 77, 79, 80, 134, 171], [71, 73, 74, 75, 134, 171], [71, 73, 134, 171], [70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 134, 171], [71, 73, 74, 134, 171], [73, 134, 171], [73, 75, 77, 78, 84, 134, 171], [137, 162, 174], [137, 143, 146, 174, 194, 215], [137, 141, 146, 162, 174, 216], [137, 141, 174], [137, 146, 174], [51, 56, 58, 65, 93, 137, 174], [100, 137, 174], [51, 119, 131, 168], [51, 137, 174], [137, 140, 141, 142, 143, 144, 145, 146, 147, 148, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 163, 164, 165, 166, 167, 168, 174], [137, 169, 174], [137, 171, 174], [137, 173, 174], [137, 174, 179, 207], [137, 174, 175, 186, 187, 194, 204, 215], [137, 174, 177, 217], [137, 174, 175, 176, 186, 194], [137, 174, 178, 179, 187, 195], [137, 174, 179, 204, 212], [137, 174, 180, 182, 186, 194], [137, 173, 174, 181], [137, 174, 182, 183], [137, 174, 184, 186], [137, 173, 174, 186], [137, 174, 186, 187, 188, 204, 215], [137, 174, 186, 187, 188, 201, 204, 207], [137, 174, 182, 186, 189, 194, 204, 215], [137, 174, 186, 187, 189, 190, 194, 204, 212, 215], [137, 174, 189, 191, 204, 212, 215], [137, 174, 186, 192], [137, 174, 193, 215, 216], [137, 174, 182, 186, 194, 204], [137, 174, 195], [137, 174, 196], [137, 174, 198, 214, 216], [137, 173, 174, 197], [137, 174, 199], [137, 174, 200], [137, 174, 186, 201, 202], [137, 174, 201, 203, 217], [137, 174, 186, 204, 205, 207], [137, 174, 204, 205], [51, 56, 58, 65, 102, 137, 174], [127, 137, 174], [137, 174, 194, 214], [137, 174, 206, 207], [137, 174, 207], [137, 174, 208], [137, 174, 204, 209], [137, 174, 186, 210, 211], [137, 174, 210, 211], [137, 174, 179, 194, 204, 212], [137, 174, 213], [46, 52, 53, 137, 174], [54, 137, 174], [46, 137, 174], [46, 47, 48, 50, 137, 174], [47, 48, 49, 50, 137, 174], [131, 168], [56, 57, 65, 137, 174], [137, 138, 144, 174], [137, 146, 150, 174, 215], [137, 143, 146, 174, 212, 215], [137, 138, 143, 144, 146, 150, 153, 174], [132, 133, 134, 137, 174], [137, 145, 174], [137, 144, 146, 154, 155, 174], [174], [137, 146, 153, 154, 174], [137, 174, 204], [137, 174, 194, 212], [137, 146, 174, 204, 215], [137, 144, 146, 149, 174, 215], [137, 138, 139, 142, 145, 174, 186, 204, 215], [137, 150, 174], [137, 138, 141, 146, 174], [137, 142, 146, 174, 207, 215], [137, 140, 141, 174], [51, 56, 58, 65, 130, 137, 174], [137, 146, 150, 154, 155, 174], [48, 50, 51, 56, 58, 127, 135, 172], [48, 50, 51, 56, 58, 100, 131, 168], [48, 50, 51, 56, 58, 100, 130, 167], [48, 50, 51, 56, 58, 100, 132, 169], [48, 50, 51, 56, 58, 100, 133, 170], [48, 50, 51, 56, 58, 100, 134, 171], [56, 58, 65, 137, 174], [67, 118, 137, 174], [50, 55, 137, 174], [50, 137, 174], [51, 56, 58, 59, 63, 65, 89, 91, 93, 100, 119, 131, 168], [51, 56, 58, 100, 131, 168], [51, 56, 58, 100, 119, 131, 168], [117, 137, 174], [116, 137, 174], [48, 50, 51, 56, 58, 65, 137, 174], [51, 60, 131, 168], [51, 61, 67, 131, 168], [51, 61, 65, 131, 168], [51, 61, 131, 168], [51, 61, 63, 131, 168], [51, 59, 61, 131, 168], [51, 61, 62, 64, 66, 68, 69, 87, 131, 168], [51, 56, 58, 86, 100, 134, 171], [51, 65, 137, 174], [51, 56, 58, 59, 65, 137, 174], [51, 56, 58, 88, 100, 102, 131, 168], [51, 56, 58, 65, 91, 127, 137, 174], [51, 56, 58, 65, 126, 137, 174], [51, 119, 120, 121, 122, 131, 168]], "referencedMap": [[107, 1], [114, 2], [53, 3], [52, 4], [70, 4], [76, 5], [72, 6], [75, 7], [79, 8], [81, 9], [77, 10], [74, 11], [73, 4], [86, 12], [80, 4], [71, 4], [83, 13], [82, 14], [78, 4], [84, 8], [85, 15], [163, 16], [164, 16], [165, 17], [166, 18], [167, 19], [168, 20], [124, 4], [127, 21], [125, 4], [126, 4], [169, 22], [170, 23], [171, 24], [172, 25], [173, 26], [174, 27], [175, 27], [177, 4], [176, 28], [178, 29], [179, 30], [180, 31], [162, 32], [181, 33], [182, 34], [183, 35], [184, 36], [185, 37], [186, 38], [187, 39], [188, 40], [189, 41], [190, 42], [191, 43], [192, 44], [193, 45], [194, 45], [195, 46], [196, 47], [198, 48], [197, 49], [199, 50], [200, 51], [201, 52], [202, 53], [203, 54], [204, 55], [205, 56], [129, 57], [128, 4], [214, 58], [206, 59], [207, 60], [208, 61], [209, 62], [210, 63], [211, 64], [212, 65], [213, 66], [54, 67], [55, 68], [47, 69], [48, 70], [50, 71], [46, 4], [60, 4], [49, 4], [58, 72], [44, 4], [45, 4], [8, 4], [9, 4], [11, 4], [10, 4], [2, 4], [12, 4], [13, 4], [14, 4], [15, 4], [16, 4], [17, 4], [18, 4], [19, 4], [3, 4], [4, 4], [20, 4], [24, 4], [21, 4], [22, 4], [23, 4], [25, 4], [26, 4], [27, 4], [5, 4], [28, 4], [29, 4], [30, 4], [31, 4], [6, 4], [35, 4], [32, 4], [33, 4], [34, 4], [36, 4], [7, 4], [37, 4], [42, 4], [43, 4], [38, 4], [39, 4], [40, 4], [41, 4], [1, 4], [145, 73], [152, 74], [144, 73], [159, 75], [136, 76], [135, 77], [158, 78], [153, 79], [156, 80], [138, 81], [137, 82], [133, 83], [132, 78], [155, 84], [134, 85], [139, 86], [140, 4], [143, 86], [130, 4], [161, 87], [160, 86], [147, 88], [148, 89], [150, 90], [146, 91], [149, 92], [154, 78], [141, 93], [142, 94], [151, 95], [131, 96], [157, 97], [113, 98], [109, 99], [108, 4], [110, 100], [111, 4], [112, 101], [57, 102], [100, 102], [56, 103], [51, 104], [90, 105], [92, 106], [94, 107], [95, 108], [96, 109], [97, 110], [98, 108], [121, 111], [119, 112], [120, 113], [118, 114], [117, 108], [116, 115], [115, 116], [61, 117], [68, 118], [66, 119], [69, 120], [64, 121], [62, 122], [88, 123], [87, 124], [91, 125], [93, 126], [89, 127], [122, 128], [102, 129], [65, 130], [63, 130], [59, 130], [67, 131], [101, 130], [123, 132], [99, 105], [103, 133], [104, 108], [105, 108], [106, 108]], "exportedModulesMap": [[107, 1], [114, 134], [53, 135], [52, 136], [70, 137], [76, 138], [72, 139], [75, 140], [79, 141], [81, 142], [77, 143], [74, 144], [73, 137], [86, 145], [80, 137], [71, 137], [83, 146], [82, 147], [78, 137], [84, 141], [85, 148], [163, 149], [164, 150], [165, 151], [166, 136], [167, 152], [168, 153], [124, 154], [127, 155], [125, 156], [126, 157], [169, 158], [170, 159], [171, 160], [172, 160], [173, 161], [174, 162], [175, 163], [177, 164], [176, 165], [178, 166], [179, 167], [180, 168], [162, 136], [181, 169], [182, 170], [183, 170], [184, 171], [185, 136], [186, 172], [187, 173], [188, 174], [189, 175], [190, 176], [191, 177], [192, 178], [193, 179], [194, 180], [195, 181], [196, 182], [198, 183], [197, 184], [199, 185], [200, 186], [201, 187], [202, 187], [203, 188], [204, 189], [205, 190], [129, 191], [128, 192], [214, 193], [206, 194], [207, 195], [208, 196], [209, 197], [210, 198], [211, 199], [212, 200], [213, 201], [54, 202], [55, 203], [47, 204], [48, 205], [50, 206], [46, 136], [60, 207], [49, 136], [58, 208], [44, 136], [45, 136], [8, 136], [9, 136], [11, 136], [10, 136], [2, 136], [12, 136], [13, 136], [14, 136], [15, 136], [16, 136], [17, 136], [18, 136], [19, 136], [3, 136], [4, 136], [20, 136], [24, 136], [21, 136], [22, 136], [23, 136], [25, 136], [26, 136], [27, 136], [5, 136], [28, 136], [29, 136], [30, 136], [31, 136], [6, 136], [35, 136], [32, 136], [33, 136], [34, 136], [36, 136], [7, 136], [37, 136], [42, 136], [43, 136], [38, 136], [39, 136], [40, 136], [41, 136], [1, 136], [145, 209], [152, 210], [144, 211], [159, 212], [136, 136], [135, 213], [158, 214], [153, 210], [156, 215], [138, 136], [137, 216], [133, 136], [132, 136], [155, 217], [134, 136], [139, 218], [140, 136], [143, 219], [130, 157], [161, 152], [160, 220], [147, 153], [148, 136], [150, 221], [146, 222], [149, 223], [154, 224], [141, 225], [142, 226], [151, 153], [131, 227], [157, 228], [113, 229], [109, 230], [108, 231], [110, 232], [111, 233], [112, 234], [57, 235], [100, 236], [56, 237], [51, 238], [90, 105], [92, 106], [94, 107], [95, 108], [96, 109], [97, 110], [98, 108], [121, 239], [119, 240], [120, 241], [118, 242], [117, 243], [116, 136], [115, 244], [61, 245], [68, 246], [66, 247], [69, 248], [64, 249], [62, 250], [88, 251], [87, 252], [91, 253], [93, 157], [89, 254], [122, 255], [102, 157], [65, 235], [63, 256], [59, 157], [67, 136], [101, 257], [123, 258], [99, 105], [103, 133], [104, 108], [105, 108], [106, 108]], "semanticDiagnosticsPerFile": [107, 114, 53, 52, 70, 76, 72, 75, 79, 81, 77, 74, 73, 86, 80, 71, 83, 82, 78, 84, 85, 163, 164, 165, 166, 167, 168, 124, 127, 125, 126, 169, 170, 171, 172, 173, 174, 175, 177, 176, 178, 179, 180, 162, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 198, 197, 199, 200, 201, 202, 203, 204, 205, 129, 128, 214, 206, 207, 208, 209, 210, 211, 212, 213, 54, 55, 47, 48, 50, 46, 60, 49, 58, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 145, 152, 144, 159, 136, 135, 158, 153, 156, 138, 137, 133, 132, 155, 134, 139, 140, 143, 130, 161, 160, 147, 148, 150, 146, 149, 154, 141, 142, 151, 131, 157, 113, 109, 108, 110, 111, 112, 57, 100, 56, 51, 90, [92, [{"file": "./src/components/customerselector.vue", "start": 11387, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "./src/types/customer.ts", "start": 478, "length": 8, "messageText": "The expected type comes from property 'dealerId' which is declared here on type 'CreateCustomerRequest & { address: any; }'", "category": 3, "code": 6500}]}, {"file": "./src/components/customerselector.vue", "start": 11916, "length": 14, "code": 2345, "category": 1, "messageText": "Argument of type 'number' is not assignable to parameter of type 'string'."}, {"file": "./src/components/customerselector.vue", "start": 12061, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'name' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 12111, "length": 11, "code": 2551, "category": 1, "messageText": "Property 'contactName' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'. Did you mean 'contactNumber'?"}, {"file": "./src/components/customerselector.vue", "start": 12168, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactEmail' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 12239, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactEmail' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 13380, "length": 26, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}, {"file": "./src/components/customerselector.vue", "start": 13450, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'name' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 13481, "length": 11, "code": 2551, "category": 1, "messageText": "Property 'contactName' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'. Did you mean 'contactNumber'?"}, {"file": "./src/components/customerselector.vue", "start": 13519, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactEmail' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 13558, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactPhone' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 13870, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'.", "relatedInformation": [{"file": "./src/types/customer.ts", "start": 478, "length": 8, "messageText": "The expected type comes from property 'dealerId' which is declared here on type 'CreateCustomerRequest'", "category": 3, "code": 6500}]}, {"file": "./src/components/customerselector.vue", "start": 13928, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'name' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 13979, "length": 11, "code": 2551, "category": 1, "messageText": "Property 'contactName' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'. Did you mean 'contactNumber'?"}, {"file": "./src/components/customerselector.vue", "start": 14038, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactEmail' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 14098, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactPhone' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 14140, "length": 7, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ street: any; city: any; state: any; postalCode: any; country: any; } | undefined' is not assignable to type 'string | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ street: any; city: any; state: any; postalCode: any; country: any; }' is not assignable to type 'string'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./src/types/customer.ts", "start": 596, "length": 7, "messageText": "The expected type comes from property 'address' which is declared here on type 'CreateCustomerRequest'", "category": 3, "code": 6500}]}, {"file": "./src/components/customerselector.vue", "start": 15132, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'name' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 15239, "length": 11, "code": 2551, "category": 1, "messageText": "Property 'contactName' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'. Did you mean 'contactNumber'?"}, {"file": "./src/components/customerselector.vue", "start": 15359, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactEmail' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 15493, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactEmail' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 17394, "length": 11, "code": 2345, "category": 1, "messageText": "Argument of type 'number' is not assignable to parameter of type 'string'."}, {"file": "./src/components/customerselector.vue", "start": 17461, "length": 26, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}, {"file": "./src/components/customerselector.vue", "start": 18004, "length": 14, "code": 2345, "category": 1, "messageText": "Argument of type 'number' is not assignable to parameter of type 'string'."}, {"file": "./src/components/customerselector.vue", "start": 18025, "length": 26, "code": 2322, "category": 1, "messageText": "Type 'number' is not assignable to type 'string'."}, {"file": "./src/components/customerselector.vue", "start": 2899, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'name' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 3537, "length": 11, "code": 2551, "category": 1, "messageText": "Property 'contactName' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'. Did you mean 'contactNumber'?"}, {"file": "./src/components/customerselector.vue", "start": 4250, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactEmail' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}, {"file": "./src/components/customerselector.vue", "start": 4843, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'contactPhone' does not exist on type '{ dealerId: string; companyName: string; countryId: string; contactNumber?: string | undefined; email?: string | undefined; address: any; description?: string | undefined; contractNumber?: string | undefined; contractDate?: string | undefined; }'."}]], [94, [{"file": "./src/components/dealerselector.vue", "start": 7549, "length": 9, "code": 2345, "category": 1, "messageText": "Argument of type 'number' is not assignable to parameter of type 'string'."}]], 95, 96, 97, 98, 121, [119, [{"file": "./src/composables/usebusinessvalidation.ts", "start": 0, "length": 37, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}, {"file": "./src/composables/usebusinessvalidation.ts", "start": 3435, "length": 101, "messageText": "Expected 2 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./src/stores/customer.ts", "start": 5217, "length": 18, "messageText": "An argument for 'customerId' was not provided.", "category": 3, "code": 6210}]}, {"file": "./src/composables/usebusinessvalidation.ts", "start": 4018, "length": 101, "messageText": "Expected 2 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./src/stores/customer.ts", "start": 5217, "length": 18, "messageText": "An argument for 'customerId' was not provided.", "category": 3, "code": 6210}]}, {"file": "./src/composables/usebusinessvalidation.ts", "start": 5216, "length": 5, "messageText": "'value' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/composables/usebusinessvalidation.ts", "start": 8122, "length": 42, "messageText": "Expected 2 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./src/stores/customer.ts", "start": 5217, "length": 18, "messageText": "An argument for 'customerId' was not provided.", "category": 3, "code": 6210}]}]], [120, [{"file": "./src/composables/useerrorhandling.ts", "start": 3783, "length": 7, "messageText": "'context' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [118, [{"file": "./src/composables/useformfield.ts", "start": 53, "length": 30, "messageText": "'Ref' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/composables/useformfield.ts", "start": 5586, "length": 4, "code": 2345, "category": 1, "messageText": "Argument of type 'T' is not assignable to parameter of type 'UnwrapRefSimple<T>'."}, {"file": "./src/composables/useformfield.ts", "start": 5620, "length": 20, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'UnwrapRefSimple<T>' is not assignable to type 'T'.", "category": 1, "code": 2322, "next": [{"messageText": "'T' could be instantiated with an arbitrary type which could be unrelated to 'UnwrapRefSimple<T>'.", "category": 1, "code": 5082}]}}, {"file": "./src/composables/useformfield.ts", "start": 5642, "length": 4, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'T | UnwrapRefSimple<T>' is not assignable to type 'T'.", "category": 1, "code": 2322, "next": [{"messageText": "'T' could be instantiated with an arbitrary type which could be unrelated to 'T | UnwrapRefSimple<T>'.", "category": 1, "code": 5082}]}}, {"file": "./src/composables/useformfield.ts", "start": 5718, "length": 41, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'UnwrapRefSimple<T>[]' is not assignable to parameter of type 'T[]'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'UnwrapRefSimple<T>' is not assignable to type 'T'.", "category": 1, "code": 2322, "next": [{"messageText": "'T' could be instantiated with an arbitrary type which could be unrelated to 'UnwrapRefSimple<T>'.", "category": 1, "code": 5082}]}]}}, {"file": "./src/composables/useformfield.ts", "start": 5837, "length": 4, "code": 2345, "category": 1, "messageText": "Argument of type 'T' is not assignable to parameter of type 'UnwrapRefSimple<T>'."}]], [117, [{"file": "./src/composables/usevalidation.ts", "start": 34, "length": 5, "messageText": "'watch' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/composables/usevalidation.ts", "start": 53, "length": 30, "messageText": "'Ref' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/composables/usevalidation.ts", "start": 636, "length": 10, "messageText": "'debounceMs' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 116, 115, 61, 68, 66, 69, 64, [62, [{"file": "./src/services/environment.ts", "start": 71, "length": 21, "messageText": "'EnvironmentValidation' is declared but never used.", "category": 1, "code": 6196, "reportsUnnecessary": true}]], [88, [{"file": "./src/services/index.ts", "start": 43, "length": 43, "messageText": "'ApiBaseService' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 87, [91, [{"file": "./src/stores/customer.ts", "start": 121, "length": 20, "messageText": "'CustomerSearchFilter' is declared but never used.", "category": 1, "code": 6196, "reportsUnnecessary": true}, {"file": "./src/stores/customer.ts", "start": 2002, "length": 8, "messageText": "'cacheKey' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/stores/customer.ts", "start": 2426, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'total' is missing in type '{ customers: Customer[]; totalCount: number; pageNumber: number; pageSize: number; totalPages: number; hasMore: boolean; }' but required in type 'CustomerSearchResult'.", "relatedInformation": [{"file": "./src/types/customer.ts", "start": 926, "length": 5, "messageText": "'total' is declared here.", "category": 3, "code": 2728}]}]], [93, [{"file": "./src/stores/dealer.ts", "start": 96, "length": 18, "messageText": "'DealerSearchFilter' is declared but never used.", "category": 1, "code": 6196, "reportsUnnecessary": true}, {"file": "./src/stores/dealer.ts", "start": 2042, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'total' is missing in type '{ dealers: Dealer[]; totalCount: number; pageNumber: number; pageSize: number; totalPages: number; hasMore: boolean; }' but required in type 'DealerSearchResult'.", "relatedInformation": [{"file": "./src/types/dealer.ts", "start": 716, "length": 5, "messageText": "'total' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/stores/dealer.ts", "start": 3547, "length": 19, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ dealers: Dealer[]; totalCount: number; pageNumber: number; pageSize: number; totalPages: number; hasMore: false; }' is not assignable to type 'DealerSearchResult | { dealers: { id: number; name: string; subdomain: string; subDomain: string; isActive: boolean; active: boolean; contactEmail?: string | undefined; contactPhone?: string | undefined; address?: { ...; } | undefined; createdAt: Date; updatedAt: Date; }[]; ... 5 more ...; hasMore: boolean; } | null'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'total' is missing in type '{ dealers: Dealer[]; totalCount: number; pageNumber: number; pageSize: number; totalPages: number; hasMore: false; }' but required in type '{ dealers: { id: number; name: string; subdomain: string; subDomain: string; isActive: boolean; active: boolean; contactEmail?: string | undefined; contactPhone?: string | undefined; address?: { ...; } | undefined; createdAt: Date; updatedAt: Date; }[]; ... 5 more ...; hasMore: boolean; }'.", "category": 1, "code": 2741}]}, "relatedInformation": [{"file": "./src/types/dealer.ts", "start": 716, "length": 5, "messageText": "'total' is declared here.", "category": 3, "code": 2728}]}]], 89, [122, [{"file": "./src/stores/import-session.ts", "start": 146, "length": 19, "messageText": "'ImportSessionFilter' is declared but never used.", "category": 1, "code": 6196, "reportsUnnecessary": true}, {"file": "./src/stores/import-session.ts", "start": 566, "length": 14, "messageText": "'signalRService' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"file": "./src/stores/import-session.ts", "start": 3640, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'total' is missing in type '{ sessions: ImportSession[]; totalCount: number; hasMore: boolean; }' but required in type 'ImportSessionSearchResult'.", "relatedInformation": [{"file": "./src/types/import-session.ts", "start": 2150, "length": 5, "messageText": "'total' is declared here.", "category": 3, "code": 2728}]}]], 102, 65, 63, 59, [67, [{"file": "./src/types/import-session.ts", "start": 0, "length": 55, "messageText": "'CreateCustomerRequest' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], 101, 123, 99, 103, 104, 105, 106], "affectedFilesPendingEmit": [90, 92, 94, 95, 96, 97, 98, 121, 119, 120, 118, 117, 116, 115, 61, 68, 66, 69, 64, 62, 88, 87, 91, 93, 89, 122, 102, 65, 63, 59, 67, 101, 123, 99, 103, 104, 105, 106], "emitSignatures": [59, 61, 62, 63, 64, 66, 68, 69, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 101, 102, 103, 104, 105, 106, 119, 120, 121, 122, 123]}, "version": "5.2.2"}