<template>
  <div class="vehicle-count-input">
    <div class="mb-3">
      <label for="vehicleCount" class="form-label">
        No. of vehicles ({{ displayCount }})
      </label>
      <input 
        type="number" 
        id="vehicleCount" 
        class="form-control" 
        :class="{ 'is-invalid': validationError }"
        v-model.number="vehicleCount"
        @input="onInput"
        @blur="onBlur"
        :min="minCount"
        :max="maxCount"
        placeholder="Enter vehicle count"
        required
      >
      
      <div v-if="validationError" class="invalid-feedback">
        {{ validationError }}
      </div>
      
      <div class="form-text d-flex justify-content-between align-items-center">
        <span>Range: {{ minCount.toLocaleString() }} - {{ maxCount.toLocaleString() }} vehicles</span>
        <span v-if="environmentLimit && environmentLimit < maxCount" class="text-warning">
          <i class="fas fa-exclamation-triangle me-1"></i>
          Environment limit: {{ environmentLimit.toLocaleString() }}
        </span>
      </div>
    </div>

    <!-- Real-time Validation Feedback -->
    <div v-if="vehicleCount && !validationError" class="validation-feedback">
      <div class="alert alert-info">
        <div class="d-flex align-items-center mb-2">
          <i class="fas fa-info-circle me-2"></i>
          <strong>Vehicle Count Validation</strong>
        </div>
        
        <div class="validation-details">
          <div class="row">
            <div class="col-md-6">
              <div class="metric">
                <span class="metric-label">Vehicles:</span>
                <span class="metric-value">{{ vehicleCount.toLocaleString() }}</span>
              </div>
            </div>
            <div class="col-md-6">
              <div class="metric">
                <span class="metric-label">Estimated Processing Time:</span>
                <span class="metric-value">{{ estimatedProcessingTime }}</span>
              </div>
            </div>
          </div>
          
          <div class="progress-indicator mt-3">
            <div class="d-flex justify-content-between mb-2">
              <span class="small text-muted">Capacity Usage</span>
              <span class="small text-muted">{{ capacityPercentage }}%</span>
            </div>
            <div class="progress">
              <div 
                class="progress-bar" 
                :class="progressBarClass"
                role="progressbar" 
                :style="{ width: capacityPercentage + '%' }"
                :aria-valuenow="capacityPercentage" 
                aria-valuemin="0" 
                :aria-valuemax="100"
              ></div>
            </div>
          </div>
          
          <!-- Performance Warnings -->
          <div v-if="performanceWarnings.length > 0" class="performance-warnings mt-3">
            <div v-for="warning in performanceWarnings" :key="warning.type" class="alert alert-warning alert-sm mb-2">
              <i :class="warning.icon" class="me-2"></i>
              {{ warning.message }}
            </div>
          </div>
          
          <!-- Recommendations -->
          <div v-if="recommendations.length > 0" class="recommendations mt-3">
            <h6 class="text-muted mb-2">
              <i class="fas fa-lightbulb me-1"></i>
              Recommendations
            </h6>
            <ul class="list-unstyled mb-0">
              <li v-for="recommendation in recommendations" :key="recommendation" class="small text-muted mb-1">
                <i class="fas fa-arrow-right me-2"></i>
                {{ recommendation }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Large Volume Warning -->
    <div v-if="vehicleCount && vehicleCount >= largeVolumeThreshold" class="large-volume-warning">
      <div class="alert alert-warning">
        <div class="d-flex align-items-center mb-2">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>Large Volume Operation</strong>
        </div>
        <p class="mb-2">
          You are about to import a large number of vehicles ({{ vehicleCount.toLocaleString() }}). 
          This operation may take significant time and resources.
        </p>
        <ul class="mb-2">
          <li>Estimated processing time: {{ estimatedProcessingTime }}</li>
          <li>Consider breaking into smaller batches if possible</li>
          <li>Ensure sufficient system resources are available</li>
          <li>Monitor system performance during operation</li>
        </ul>
        <div class="form-check">
          <input 
            class="form-check-input" 
            type="checkbox" 
            id="acknowledgeWarning"
            v-model="largeVolumeAcknowledged"
          >
          <label class="form-check-label" for="acknowledgeWarning">
            I understand the implications of this large volume operation
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
const props = defineProps<{
  modelValue?: number | null
  minCount?: number
  maxCount?: number
  environmentLimit?: number | null
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: number | null]
  'validation-change': [isValid: boolean]
}>()

// Local state
const vehicleCount = ref<number | null>(null)
const validationError = ref('')
const largeVolumeAcknowledged = ref(false)

// Constants
const defaultMinCount = 1
const defaultMaxCount = 10000
const largeVolumeThreshold = 5000

// Computed properties
const minCount = computed(() => props.minCount || defaultMinCount)
const maxCount = computed(() => {
  if (props.environmentLimit && props.environmentLimit < (props.maxCount || defaultMaxCount)) {
    return props.environmentLimit
  }
  return props.maxCount || defaultMaxCount
})

const environmentLimit = computed(() => props.environmentLimit)

const displayCount = computed(() => {
  return vehicleCount.value?.toLocaleString() || 'xxxx'
})

const capacityPercentage = computed(() => {
  if (!vehicleCount.value) return 0
  return Math.min(Math.round((vehicleCount.value / maxCount.value) * 100), 100)
})

const progressBarClass = computed(() => {
  const percentage = capacityPercentage.value
  if (percentage >= 90) return 'bg-danger'
  if (percentage >= 75) return 'bg-warning'
  if (percentage >= 50) return 'bg-info'
  return 'bg-success'
})

const estimatedProcessingTime = computed(() => {
  if (!vehicleCount.value) return 'N/A'
  
  // Rough estimation: 100 vehicles per minute
  const minutes = Math.ceil(vehicleCount.value / 100)
  
  if (minutes < 60) {
    return `~${minutes} minute${minutes !== 1 ? 's' : ''}`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `~${hours}h ${remainingMinutes}m`
  }
})

const performanceWarnings = computed(() => {
  if (!vehicleCount.value) return []
  
  const warnings: Array<{ type: string; message: string; icon: string }> = []
  
  if (vehicleCount.value >= 8000) {
    warnings.push({
      type: 'very-large',
      message: 'Very large dataset - consider splitting into multiple batches for optimal performance',
      icon: 'fas fa-exclamation-circle'
    })
  } else if (vehicleCount.value >= largeVolumeThreshold) {
    warnings.push({
      type: 'large',
      message: 'Large dataset - monitor system resources during processing',
      icon: 'fas fa-info-circle'
    })
  }
  
  if (capacityPercentage.value >= 90) {
    warnings.push({
      type: 'capacity',
      message: 'Approaching environment capacity limit',
      icon: 'fas fa-tachometer-alt'
    })
  }
  
  return warnings
})

const recommendations = computed(() => {
  if (!vehicleCount.value) return []
  
  const recommendations: string[] = []
  
  if (vehicleCount.value >= 8000) {
    recommendations.push('Consider breaking into batches of 2,000-5,000 vehicles')
    recommendations.push('Schedule during off-peak hours')
    recommendations.push('Ensure database maintenance is up to date')
  } else if (vehicleCount.value >= largeVolumeThreshold) {
    recommendations.push('Monitor system performance during operation')
    recommendations.push('Consider scheduling during off-peak hours')
  }
  
  if (vehicleCount.value >= 1000) {
    recommendations.push('Enable progress tracking for better visibility')
  }
  
  return recommendations
})

const isValid = computed(() => {
  if (!vehicleCount.value) return false
  
  const hasValidationError = !!validationError.value
  const needsAcknowledgment = vehicleCount.value >= largeVolumeThreshold && !largeVolumeAcknowledged.value
  
  return !hasValidationError && !needsAcknowledgment
})

// Methods
const onInput = () => {
  validateInput()
  emit('update:modelValue', vehicleCount.value)
}

const onBlur = () => {
  validateInput()
}

const validateInput = () => {
  validationError.value = ''
  
  if (vehicleCount.value === null || vehicleCount.value === undefined) {
    validationError.value = 'Vehicle count is required'
    return false
  }
  
  if (!Number.isInteger(vehicleCount.value)) {
    validationError.value = 'Vehicle count must be a whole number'
    return false
  }
  
  if (vehicleCount.value < minCount.value) {
    validationError.value = `Vehicle count must be at least ${minCount.value}`
    return false
  }
  
  if (vehicleCount.value > maxCount.value) {
    validationError.value = `Vehicle count cannot exceed ${maxCount.value.toLocaleString()}`
    return false
  }
  
  return true
}

const validate = () => {
  const inputValid = validateInput()
  
  if (!inputValid) return false
  
  if (vehicleCount.value && vehicleCount.value >= largeVolumeThreshold && !largeVolumeAcknowledged.value) {
    validationError.value = 'Please acknowledge the large volume operation warning'
    return false
  }
  
  return true
}

// Watchers
watch(isValid, (newValue) => {
  emit('validation-change', newValue)
})

watch(() => props.modelValue, (newValue) => {
  vehicleCount.value = newValue || null
  if (newValue) {
    validateInput()
  }
})

watch(largeVolumeAcknowledged, () => {
  if (vehicleCount.value && vehicleCount.value >= largeVolumeThreshold) {
    validateInput()
  }
})

// Lifecycle
onMounted(() => {
  if (props.modelValue) {
    vehicleCount.value = props.modelValue
    validateInput()
  }
})

// Expose validation method for parent components
defineExpose({
  validate,
  isValid
})
</script>

<style scoped>
.vehicle-count-input {
  .validation-feedback {
    .alert {
      border-left: 4px solid var(--bs-info);
    }
    
    .metric {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.5rem;
      
      .metric-label {
        font-weight: 500;
        color: var(--bs-secondary);
      }
      
      .metric-value {
        font-weight: bold;
        color: var(--bs-primary);
      }
    }
    
    .progress {
      height: 0.5rem;
    }
    
    .alert-sm {
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
    }
  }
  
  .large-volume-warning {
    .alert {
      border-left: 4px solid var(--bs-warning);
    }
    
    ul {
      padding-left: 1.5rem;
    }
    
    .form-check {
      margin-top: 1rem;
      padding: 0.75rem;
      background-color: rgba(var(--bs-warning-rgb), 0.1);
      border-radius: 0.25rem;
    }
  }
  
  .performance-warnings {
    .alert {
      border: 1px solid rgba(var(--bs-warning-rgb), 0.3);
      background-color: rgba(var(--bs-warning-rgb), 0.1);
    }
  }
  
  .recommendations {
    padding: 0.75rem;
    background-color: rgba(var(--bs-info-rgb), 0.05);
    border-radius: 0.25rem;
    border: 1px solid rgba(var(--bs-info-rgb), 0.2);
  }
}
</style>
