using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Text;

namespace XQ360.DataMigration.Web.Services.BusinessRules.Rules
{
    /// <summary>
    /// Business rule for validating uniqueness constraints
    /// </summary>
    public class UniquenessRule : BusinessRuleBase
    {
        private readonly ILogger<UniquenessRule> _logger;

        public UniquenessRule(ILogger<UniquenessRule> logger)
        {
            _logger = logger;
        }

        public override string Id => "UNIQUENESS_CONSTRAINT";
        public override string Name => "Uniqueness Constraint Validation";
        public override string Description => "Validates that specified fields maintain uniqueness constraints across the database";
        public override string Category => "Data Integrity";
        public override string[] ApplicableEntityTypes => new[] { "Person", "Driver", "Vehicle", "Card", "Module", "Customer", "Site", "Department" };

        public override async Task<ValidationResult> ValidateAsync(object entity, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var issues = new List<ValidationIssue>();

            try
            {
                var uniquenessRequest = ExtractUniquenessRequest(entity);
                if (uniquenessRequest == null)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        "Invalid uniqueness validation request",
                        "Entity",
                        entity?.GetType().Name,
                        "Ensure entity contains required fields for uniqueness validation"));
                    return CreateFailureResult(issues.ToArray());
                }

                using var connection = new SqlConnection(context.ConnectionString);
                await connection.OpenAsync(cancellationToken);

                // Get the table name from entity type
                var tableName = GetTableName(uniquenessRequest.Entity);
                if (string.IsNullOrEmpty(tableName))
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        "Unable to determine table name for entity",
                        "EntityType",
                        uniquenessRequest.Entity.GetType().Name,
                        "Ensure entity type is supported for uniqueness validation"));
                    return CreateFailureResult(issues.ToArray());
                }

                // Validate each unique field combination
                foreach (var fieldName in uniquenessRequest.UniqueFields)
                {
                    var fieldValue = GetPropertyValue(uniquenessRequest.Entity, fieldName);
                    if (fieldValue == null)
                    {
                        issues.Add(CreateIssue(ValidationSeverity.Warning,
                            $"Unique field '{fieldName}' has null value",
                            fieldName,
                            null,
                            "Consider providing a value for unique fields"));
                        continue;
                    }

                    var duplicateCheck = await CheckFieldUniquenessAsync(tableName, fieldName, fieldValue, uniquenessRequest.Entity, connection, cancellationToken);
                    if (duplicateCheck.HasDuplicates)
                    {
                        issues.Add(CreateIssue(ValidationSeverity.Error,
                            $"Duplicate value found for unique field '{fieldName}': {fieldValue}",
                            fieldName,
                            fieldValue,
                            $"Change the value of '{fieldName}' to ensure uniqueness"));
                    }
                }

                // Check composite uniqueness if multiple fields specified
                if (uniquenessRequest.UniqueFields.Length > 1)
                {
                    var compositeCheck = await CheckCompositeUniquenessAsync(tableName, uniquenessRequest.UniqueFields, uniquenessRequest.Entity, connection, cancellationToken);
                    if (compositeCheck.HasDuplicates)
                    {
                        var fieldValues = string.Join(", ", uniquenessRequest.UniqueFields.Select(f => $"{f}={GetPropertyValue(uniquenessRequest.Entity, f)}"));
                        issues.Add(CreateIssue(ValidationSeverity.Error,
                            $"Duplicate combination found for unique fields: {fieldValues}",
                            string.Join(",", uniquenessRequest.UniqueFields),
                            fieldValues,
                            "Change one or more field values to ensure unique combination"));
                    }
                }

                // Check predefined uniqueness constraints for specific entity types
                var predefinedChecks = await ValidatePredefinedUniquenessAsync(uniquenessRequest.Entity, connection, cancellationToken);
                issues.AddRange(predefinedChecks);

                return issues.Any(i => i.Severity == ValidationSeverity.Error)
                    ? CreateFailureResult(issues.ToArray())
                    : new ValidationResult { IsValid = true, Issues = issues };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating uniqueness constraints");

                issues.Add(CreateIssue(ValidationSeverity.Error,
                    $"Uniqueness validation error: {ex.Message}",
                    "System",
                    null,
                    "Contact system administrator"));

                return CreateFailureResult(issues.ToArray());
            }
        }

        private UniquenessRequest? ExtractUniquenessRequest(object entity)
        {
            // Check if entity is already a uniqueness request
            if (HasProperty(entity, "Entity") && HasProperty(entity, "UniqueFields"))
            {
                var innerEntity = GetPropertyValue(entity, "Entity");
                var uniqueFields = GetPropertyValue(entity, "UniqueFields") as string[];
                
                if (innerEntity != null && uniqueFields != null)
                {
                    return new UniquenessRequest
                    {
                        Entity = innerEntity,
                        UniqueFields = uniqueFields
                    };
                }
            }

            // Default uniqueness fields for common entity types
            var entityType = entity.GetType().Name;
            var defaultUniqueFields = GetDefaultUniqueFields(entityType);
            
            if (defaultUniqueFields.Any())
            {
                return new UniquenessRequest
                {
                    Entity = entity,
                    UniqueFields = defaultUniqueFields
                };
            }

            return null;
        }

        private string[] GetDefaultUniqueFields(string entityType)
        {
            return entityType switch
            {
                "Person" => new[] { "FirstName", "LastName", "DepartmentId" },
                "Driver" => new[] { "LicenseNumber" },
                "Vehicle" => new[] { "SerialNumber" },
                "Card" => new[] { "WeigandNumber" },
                "Module" => new[] { "SerialNumber" },
                "Customer" => new[] { "CompanyName", "DealerId" },
                "Site" => new[] { "Name", "CustomerId" },
                "Department" => new[] { "Name", "SiteId" },
                _ => Array.Empty<string>()
            };
        }

        private string GetTableName(object entity)
        {
            var entityType = entity.GetType().Name;
            
            // Map entity types to table names
            return entityType switch
            {
                "PersonImportModel" => "Person",
                "VehicleImportModel" => "Vehicle",
                "CardImportModel" => "Card",
                "ModuleImportModel" => "Module",
                _ => entityType // Assume entity type matches table name
            };
        }

        private async Task<(bool HasDuplicates, int Count)> CheckFieldUniquenessAsync(string tableName, string fieldName, object fieldValue, object entity, SqlConnection connection, CancellationToken cancellationToken)
        {
            var sql = $@"
                SELECT COUNT(*)
                FROM [dbo].[{tableName}]
                WHERE [{fieldName}] = @FieldValue";

            // If entity has an ID, exclude it from the check (for updates)
            var entityId = GetPropertyValue(entity, "Id");
            if (entityId is Guid id && id != Guid.Empty)
            {
                sql += " AND Id != @EntityId";
            }

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@FieldValue", fieldValue ?? DBNull.Value);
            
            if (entityId is Guid entityGuid && entityGuid != Guid.Empty)
            {
                command.Parameters.AddWithValue("@EntityId", entityGuid);
            }

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return (count > 0, count);
        }

        private async Task<(bool HasDuplicates, int Count)> CheckCompositeUniquenessAsync(string tableName, string[] fieldNames, object entity, SqlConnection connection, CancellationToken cancellationToken)
        {
            var whereClause = new StringBuilder();
            var parameters = new List<SqlParameter>();

            for (int i = 0; i < fieldNames.Length; i++)
            {
                if (i > 0) whereClause.Append(" AND ");
                
                var fieldValue = GetPropertyValue(entity, fieldNames[i]);
                var paramName = $"@Field{i}";
                
                whereClause.Append($"[{fieldNames[i]}] = {paramName}");
                parameters.Add(new SqlParameter(paramName, fieldValue ?? DBNull.Value));
            }

            var sql = $@"
                SELECT COUNT(*)
                FROM [dbo].[{tableName}]
                WHERE {whereClause}";

            // If entity has an ID, exclude it from the check (for updates)
            var entityId = GetPropertyValue(entity, "Id");
            if (entityId is Guid id && id != Guid.Empty)
            {
                sql += " AND Id != @EntityId";
                parameters.Add(new SqlParameter("@EntityId", id));
            }

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddRange(parameters.ToArray());

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return (count > 0, count);
        }

        private async Task<List<ValidationIssue>> ValidatePredefinedUniquenessAsync(object entity, SqlConnection connection, CancellationToken cancellationToken)
        {
            var issues = new List<ValidationIssue>();
            var entityType = entity.GetType().Name;

            switch (entityType)
            {
                case "Person":
                case "PersonImportModel":
                    await ValidatePersonUniquenessAsync(entity, connection, issues, cancellationToken);
                    break;

                case "Vehicle":
                case "VehicleImportModel":
                    await ValidateVehicleUniquenessAsync(entity, connection, issues, cancellationToken);
                    break;

                case "Card":
                case "CardImportModel":
                    await ValidateCardUniquenessAsync(entity, connection, issues, cancellationToken);
                    break;

                case "Driver":
                    await ValidateDriverUniquenessAsync(entity, connection, issues, cancellationToken);
                    break;
            }

            return issues;
        }

        private async Task ValidatePersonUniquenessAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            var firstName = GetPropertyValue(entity, "FirstName")?.ToString();
            var lastName = GetPropertyValue(entity, "LastName")?.ToString();
            var departmentName = GetPropertyValue(entity, "DepartmentName")?.ToString();

            if (!string.IsNullOrEmpty(firstName) && !string.IsNullOrEmpty(lastName) && !string.IsNullOrEmpty(departmentName))
            {
                const string sql = @"
                    SELECT COUNT(*)
                    FROM [dbo].[Person] p
                    INNER JOIN [dbo].[Department] d ON p.DepartmentId = d.Id
                    WHERE p.FirstName = @FirstName 
                    AND p.LastName = @LastName 
                    AND d.Name = @DepartmentName";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@FirstName", firstName);
                command.Parameters.AddWithValue("@LastName", lastName);
                command.Parameters.AddWithValue("@DepartmentName", departmentName);

                var count = (int)await command.ExecuteScalarAsync(cancellationToken);
                if (count > 0)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Person '{firstName} {lastName}' already exists in department '{departmentName}'",
                        "FirstName,LastName,DepartmentName",
                        $"{firstName} {lastName} - {departmentName}",
                        "Use a different name or verify if this is an update operation"));
                }
            }
        }

        private async Task ValidateVehicleUniquenessAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            var serialNumber = GetPropertyValue(entity, "SerialNumber")?.ToString();
            if (!string.IsNullOrEmpty(serialNumber))
            {
                const string sql = @"
                    SELECT COUNT(*)
                    FROM [dbo].[Vehicle]
                    WHERE SerialNumber = @SerialNumber";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@SerialNumber", serialNumber);

                var count = (int)await command.ExecuteScalarAsync(cancellationToken);
                if (count > 0)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Vehicle with serial number '{serialNumber}' already exists",
                        "SerialNumber",
                        serialNumber,
                        "Use a different serial number or verify if this is an update operation"));
                }
            }
        }

        private async Task ValidateCardUniquenessAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            var weigandNumber = GetPropertyValue(entity, "WeigandNumber")?.ToString();
            if (!string.IsNullOrEmpty(weigandNumber))
            {
                const string sql = @"
                    SELECT COUNT(*)
                    FROM [dbo].[Card]
                    WHERE WeigandNumber = @WeigandNumber";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@WeigandNumber", weigandNumber);

                var count = (int)await command.ExecuteScalarAsync(cancellationToken);
                if (count > 0)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Card with Weigand number '{weigandNumber}' already exists",
                        "WeigandNumber",
                        weigandNumber,
                        "Use a different Weigand number or verify if this is an update operation"));
                }
            }
        }

        private async Task ValidateDriverUniquenessAsync(object entity, SqlConnection connection, List<ValidationIssue> issues, CancellationToken cancellationToken)
        {
            var licenseNumber = GetPropertyValue(entity, "LicenseNumber")?.ToString();
            if (!string.IsNullOrEmpty(licenseNumber))
            {
                const string sql = @"
                    SELECT COUNT(*)
                    FROM [dbo].[Driver]
                    WHERE LicenseNumber = @LicenseNumber";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@LicenseNumber", licenseNumber);

                var count = (int)await command.ExecuteScalarAsync(cancellationToken);
                if (count > 0)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Driver with license number '{licenseNumber}' already exists",
                        "LicenseNumber",
                        licenseNumber,
                        "Use a different license number or verify if this is an update operation"));
                }
            }
        }

        private class UniquenessRequest
        {
            public object Entity { get; set; } = null!;
            public string[] UniqueFields { get; set; } = Array.Empty<string>();
        }
    }
}
