import { ref, computed, watch, nextTick } from 'vue'
import type { Ref } from 'vue'
import { useValidation, type ValidationRule } from './useValidation'

export interface FormFieldOptions {
  rules?: ValidationRule[]
  immediate?: boolean
  debounceMs?: number
  validateOnBlur?: boolean
  validateOnChange?: boolean
}

export function useFormField<T = any>(
  initialValue: T,
  fieldName: string,
  validation: ReturnType<typeof useValidation>,
  options: FormFieldOptions = {}
) {
  const {
    rules = [],
    immediate = false,
    debounceMs = 300,
    validateOnBlur = true,
    validateOnChange = false
  } = options

  const value = ref<T>(initialValue)
  const isDirty = ref(false)
  const isFocused = ref(false)
  
  // Add field to validation system
  validation.addField(fieldName, rules)

  // Computed properties
  const fieldValidation = computed(() => validation.formValidation[fieldName])
  
  const isValid = computed(() => fieldValidation.value?.isValid ?? true)
  const errors = computed(() => fieldValidation.value?.errors ?? [])
  const isValidating = computed(() => fieldValidation.value?.isValidating ?? false)
  const touched = computed(() => fieldValidation.value?.touched ?? false)
  
  const hasError = computed(() => errors.value.length > 0)
  const showError = computed(() => hasError.value && (touched.value || validation.submitAttempted.value))
  const firstError = computed(() => errors.value[0] || null)

  // Validation state classes for styling
  const validationClasses = computed(() => ({
    'is-valid': isValid.value && touched.value,
    'is-invalid': hasError.value && showError.value,
    'is-validating': isValidating.value,
    'is-dirty': isDirty.value,
    'is-touched': touched.value,
    'is-focused': isFocused.value
  }))

  // Debounced validation
  let validationTimeout: NodeJS.Timeout | null = null

  const debouncedValidate = (trigger: 'blur' | 'change' = 'change') => {
    if (validationTimeout) {
      clearTimeout(validationTimeout)
    }

    validationTimeout = setTimeout(() => {
      validation.validateField(fieldName, value.value, trigger)
    }, debounceMs)
  }

  // Event handlers
  const handleInput = (newValue: T) => {
    value.value = newValue
    isDirty.value = true

    if (validateOnChange) {
      debouncedValidate('change')
    }
  }

  const handleBlur = () => {
    isFocused.value = false
    validation.markFieldTouched(fieldName)

    if (validateOnBlur) {
      validation.validateField(fieldName, value.value, 'blur')
    }
  }

  const handleFocus = () => {
    isFocused.value = true
  }

  // Manual validation
  const validate = async (trigger: 'blur' | 'change' | 'submit' = 'submit') => {
    return await validation.validateField(fieldName, value.value, trigger)
  }

  const clearErrors = () => {
    validation.clearFieldErrors(fieldName)
  }

  const setError = (error: string) => {
    validation.setFieldError(fieldName, error)
  }

  const setErrors = (errors: string[]) => {
    validation.setFieldErrors(fieldName, errors)
  }

  const reset = () => {
    value.value = initialValue
    isDirty.value = false
    isFocused.value = false
    clearErrors()
  }

  // Watch for external value changes
  watch(() => value.value, (newValue, oldValue) => {
    if (newValue !== oldValue) {
      isDirty.value = true
    }
  })

  // Initial validation if requested
  if (immediate) {
    nextTick(() => {
      validate('change')
    })
  }

  return {
    // Value
    value,
    
    // State
    isDirty,
    isFocused,
    isValid,
    errors,
    isValidating,
    touched,
    hasError,
    showError,
    firstError,
    validationClasses,

    // Event handlers
    handleInput,
    handleBlur,
    handleFocus,

    // Methods
    validate,
    clearErrors,
    setError,
    setErrors,
    reset
  }
}

// Specialized form field composables
export function useTextField(
  initialValue: string = '',
  fieldName: string,
  validation: ReturnType<typeof useValidation>,
  options: FormFieldOptions = {}
) {
  return useFormField(initialValue, fieldName, validation, {
    validateOnChange: true,
    ...options
  })
}

export function useNumberField(
  initialValue: number | null = null,
  fieldName: string,
  validation: ReturnType<typeof useValidation>,
  options: FormFieldOptions = {}
) {
  const field = useFormField(initialValue, fieldName, validation, options)

  const handleNumberInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    const numValue = target.value === '' ? null : Number(target.value)
    field.handleInput(numValue)
  }

  return {
    ...field,
    handleNumberInput
  }
}

export function useSelectField<T = any>(
  initialValue: T | null = null,
  fieldName: string,
  validation: ReturnType<typeof useValidation>,
  options: FormFieldOptions = {}
) {
  return useFormField(initialValue, fieldName, validation, {
    validateOnChange: true,
    ...options
  })
}

export function useCheckboxField(
  initialValue: boolean = false,
  fieldName: string,
  validation: ReturnType<typeof useValidation>,
  options: FormFieldOptions = {}
) {
  return useFormField(initialValue, fieldName, validation, {
    validateOnChange: true,
    ...options
  })
}

export function useMultiSelectField<T = any>(
  initialValue: T[] = [],
  fieldName: string,
  validation: ReturnType<typeof useValidation>,
  options: FormFieldOptions = {}
) {
  const field = useFormField(initialValue, fieldName, validation, {
    validateOnChange: true,
    ...options
  })

  const addItem = (item: T) => {
    if (!field.value.value.includes(item)) {
      field.handleInput([...field.value.value, item])
    }
  }

  const removeItem = (item: T) => {
    field.handleInput(field.value.value.filter(i => i !== item))
  }

  const toggleItem = (item: T) => {
    if (field.value.value.includes(item)) {
      removeItem(item)
    } else {
      addItem(item)
    }
  }

  const clear = () => {
    field.handleInput([])
  }

  return {
    ...field,
    addItem,
    removeItem,
    toggleItem,
    clear
  }
}
