# Database Schema Evolution Testing Strategy

## 🎯 **PURPOSE**
Ensure XQ360 Migration Software continues working when production database schema changes or evolves.

## 🚨 **CRITICAL PRODUCTION SCENARIOS**

### **Scenario 1: Database Gets New Columns**
```sql
-- Production adds new columns to Vehicle table
ALTER TABLE Vehicle ADD CreatedBy NVARCHAR(255);
ALTER TABLE Vehicle ADD CreatedDate DATETIME2 DEFAULT GETUTCDATE();
ALTER TABLE Vehicle ADD VehicleStatus NVARCHAR(50) DEFAULT 'Active';
```
**Test**: `VehicleMigration_ShouldWork_WithAdditionalDatabaseColumns()`

### **Scenario 2: New Foreign Key Relationships** 
```sql
-- Production adds new lookup tables and FKs
CREATE TABLE CardType (Id INT PRIMARY KEY, Name NVARCHAR(50));
ALTER TABLE Card ADD CardTypeId INT REFERENCES CardType(Id);
```
**Test**: `CardMigration_ShouldWork_WithNewForeignKeyRelationships()`

### **Scenario 3: Extended Permission System**
```sql
-- Production extends Permission table
ALTER TABLE Permission ADD Category NVARCHAR(100);
ALTER TABLE Permission ADD Priority INT;
ALTER TABLE Permission ADD RequiresApproval BIT;
```
**Test**: `PermissionService_ShouldWork_WithExtendedPermissionTable()`

### **Scenario 4: Backward Compatibility**
```sql
-- Migration software must work with OLDER database versions
-- Missing recent columns should be handled gracefully
```
**Test**: `AllMigrations_ShouldWork_WithBackwardCompatibleSchema()`

### **Scenario 5: New Constraints and Indexes**
```sql
-- Production adds performance indexes and business rules
CREATE UNIQUE INDEX IX_Vehicle_DeviceID ON Vehicle(DeviceID);
ALTER TABLE Vehicle ADD CONSTRAINT CK_Vehicle_SerialNo_Format 
    CHECK (SerialNo LIKE '[A-Z][A-Z][0-9][0-9][0-9]');
```
**Test**: `Migrations_ShouldHandleNewIndexesAndConstraints()`

## 🔧 **IMPLEMENTATION APPROACH**

### **Option A: Real Database Testing** (Recommended)
```csharp
[Fact]
public async Task VehicleMigration_ShouldWork_WithAdditionalDatabaseColumns()
{
    // 1. Create test database with EXTENDED schema
    await CreateExtendedVehicleSchema();
    
    // 2. Run migration with standard CSV
    var result = await migration.ExecuteAsync(csvPath);
    
    // 3. Verify migration succeeds despite new columns
    Assert.True(result.Success, "Migration should work with extended schema");
    
    // 4. Verify data integrity
    await VerifyVehicleDataIntegrity();
}
```

### **Option B: Migration Logic Enhancement** (Also Recommended)
```csharp
// Make migrations schema-flexible
private string BuildInsertSql(string tableName, Dictionary<string, object> values)
{
    // Only insert into columns that actually exist
    var existingColumns = await GetExistingTableColumns(tableName);
    var filteredValues = values.Where(kv => existingColumns.Contains(kv.Key));
    
    var columns = string.Join(", ", filteredValues.Select(kv => kv.Key));
    var parameters = string.Join(", ", filteredValues.Select(kv => $"@{kv.Key}"));
    
    return $"INSERT INTO {tableName} ({columns}) VALUES ({parameters})";
}
```

## 📋 **TESTING CHECKLIST**

### **Before Production Database Updates:**
- [ ] Run schema compatibility tests
- [ ] Test with additional columns
- [ ] Test with new foreign keys
- [ ] Test with missing optional columns
- [ ] Test with new constraints
- [ ] Test backward compatibility

### **After Production Database Updates:**
- [ ] Verify migration software still works
- [ ] Test all migration types
- [ ] Validate data integrity
- [ ] Check performance impact
- [ ] Test error handling

## 🚀 **QUICK SETUP**

### **1. Run Schema Compatibility Tests:**
```bash
dotnet test --filter "DatabaseSchemaCompatibilityTests" --verbosity normal
```

### **2. Create Test Database with Extended Schema:**
```sql
-- Script in CreateExtendedVehicleSchema() method
USE XQ360_SchemaCompatTest;

CREATE TABLE Vehicle (
    -- Standard columns
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    DeviceID NVARCHAR(255),
    -- ... existing columns ...
    
    -- NEW FUTURE COLUMNS (schema evolution)
    CreatedBy NVARCHAR(255) NULL,
    CreatedDate DATETIME2 DEFAULT GETUTCDATE(),
    VehicleStatus NVARCHAR(50) DEFAULT 'Active',
    MaintenanceSchedule NVARCHAR(255) NULL,
    WarrantyExpiry DATE NULL,
    TelematicsEnabled BIT DEFAULT 0,
    CustomAttributes NVARCHAR(MAX) NULL -- JSON for extensibility
);
```

### **3. Verify Migration Compatibility:**
```csharp
// This should pass even with extended schema
var result = await vehicleMigration.ExecuteAsync("vehicle_test.csv");
Assert.True(result.Success, "Migration should work with extended schema");
```

## ⚡ **AUTOMATED TESTING**

### **CI/CD Integration:**
```yaml
# .github/workflows/schema-compatibility.yml
name: Schema Compatibility Tests
on: [push, pull_request]

jobs:
  schema-tests:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup .NET
        uses: actions/setup-dotnet@v1
        with:
          dotnet-version: 8.0.x
      - name: Run Schema Compatibility Tests
        run: dotnet test --filter "DatabaseSchemaCompatibilityTests" --logger trx
```

## 🎯 **SUCCESS CRITERIA**

✅ **Migration software works with:**
- Additional database columns
- New foreign key relationships  
- Extended lookup tables
- New indexes and constraints
- Older database versions (backward compatibility)

✅ **Data integrity maintained:**
- All existing data preserved
- No corruption during migration
- Referential integrity respected

✅ **Performance acceptable:**
- Migration speed not significantly impacted
- Memory usage within limits
- Database locks minimal

## 📞 **NEXT STEPS**

1. **Implement Real Tests**: Replace mocked schema tests with actual database tests
2. **Enhance Migration Logic**: Make migrations schema-flexible
3. **Automate Testing**: Add to CI/CD pipeline
4. **Document Changes**: Update migration documentation with schema compatibility notes

This testing strategy ensures your migration software is **production-ready** and **future-proof** against database schema changes! 