import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Customer, CreateCustomerRequest, CustomerSearchFilter, CustomerSearchResult } from '@/types/customer'
import { getCustomerService } from '@/services'
import type { CustomerListRequest, CustomerValidationResponse } from '@/services'

export const useCustomerStore = defineStore('customer', () => {
    // Services
    const customerService = getCustomerService()

    // State
    const customers = ref<Customer[]>([])
    const selectedCustomer = ref<Customer | null>(null)
    const searchResults = ref<CustomerSearchResult | null>(null)
    const recentCustomers = ref<Customer[]>([])
    const countries = ref<Array<{ id: string; name: string; code: string }>>([])
    const loading = ref(false)
    const creating = ref(false)
    const error = ref<string | null>(null)
    const lastFetch = ref<Date | null>(null)
    const validationCache = ref<Map<string, CustomerValidationResponse>>(new Map())

    // Getters
    const activeCustomers = computed(() =>
        customers.value.filter(customer => customer.isActive)
    )

    const customersByDealer = computed(() => (dealerId: string) =>
        customers.value.filter(customer => customer.dealerId.toString() === dealerId && customer.isActive)
    )

    const customerById = computed(() => (id: string) =>
        customers.value.find(customer => customer.id.toString() === id)
    )

    const hasSelectedCustomer = computed(() =>
        selectedCustomer.value !== null
    )

    const selectedCustomerName = computed(() =>
        selectedCustomer.value?.companyName || ''
    )

    const isDataStale = computed(() => {
        if (!lastFetch.value) return true
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)
        return lastFetch.value < tenMinutesAgo
    })

    // Actions
    const fetchCustomers = async (request: CustomerListRequest, force = false) => {
        const cacheKey = `${request.dealerId}-${request.query || ''}`
        if (!force && !isDataStale.value && customers.value.some(c => c.dealerId.toString() === request.dealerId)) {
            return
        }

        loading.value = true
        error.value = null

        try {
            const response = await customerService.getCustomers(request)

            // Convert to our internal format
            const result: CustomerSearchResult = {
                customers: response.customers,
                total: response.totalCount,
                totalCount: response.totalCount,
                pageNumber: response.pageNumber,
                pageSize: response.pageSize,
                totalPages: response.totalPages,
                hasMore: response.pageNumber < response.totalPages
            }

            searchResults.value = result
            lastFetch.value = new Date()

            // Update customers array with unique entries
            const existingIds = new Set(customers.value.map(c => c.id))
            const newCustomers = response.customers.filter(c => !existingIds.has(c.id))
            customers.value = [...customers.value, ...newCustomers]

        } catch (err: any) {
            error.value = err.message || 'Failed to fetch customers'
            console.error('Error fetching customers:', err)
        } finally {
            loading.value = false
        }
    }

    const fetchCustomersByDealer = async (dealerId: string, force = false) => {
        await fetchCustomers({
            dealerId,
            activeOnly: true
        }, force)
    }

    const getCustomerById = async (id: string): Promise<Customer | null> => {
        // Check if already in store
        const cached = customerById.value(id)
        if (cached) {
            return cached
        }

        loading.value = true
        error.value = null

        try {
            const customer = await customerService.getCustomerById(id)

            // Add to store
            const index = customers.value.findIndex(c => c.id === customer.id)
            if (index >= 0) {
                customers.value[index] = customer
            } else {
                customers.value.push(customer)
            }

            return customer

        } catch (err: any) {
            error.value = err.message || 'Failed to fetch customer'
            console.error('Error fetching customer:', err)
            return null
        } finally {
            loading.value = false
        }
    }

    const createCustomer = async (request: CreateCustomerRequest): Promise<Customer | null> => {
        creating.value = true
        error.value = null

        try {
            const customer = await customerService.createCustomer(request)

            // Add to store
            customers.value.push(customer)

            return customer

        } catch (err: any) {
            error.value = err.message || 'Failed to create customer'
            console.error('Error creating customer:', err)
            return null
        } finally {
            creating.value = false
        }
    }

    const validateCustomer = async (dealerId: string, customerId: string): Promise<CustomerValidationResponse> => {
        const cacheKey = `${dealerId}-${customerId}`

        // Check cache first
        if (validationCache.value.has(cacheKey)) {
            return validationCache.value.get(cacheKey)!
        }

        try {
            const validation = await customerService.validateCustomer({ dealerId, customerId })

            // Cache the result for 5 minutes
            validationCache.value.set(cacheKey, validation)
            setTimeout(() => {
                validationCache.value.delete(cacheKey)
            }, 5 * 60 * 1000)

            return validation
        } catch (err: any) {
            console.error('Error validating customer:', err)
            throw err
        }
    }

    const searchCustomers = async (dealerId: string, query: string, limit = 10) => {
        try {
            return await customerService.searchCustomers(dealerId, query, limit)
        } catch (err: any) {
            console.error('Error searching customers:', err)
            return []
        }
    }

    const checkCustomerExists = async (dealerId: string, customerName: string): Promise<boolean> => {
        try {
            return await customerService.customerExists(dealerId, customerName)
        } catch (err: any) {
            console.error('Error checking if customer exists:', err)
            return false
        }
    }

    const loadCountries = async () => {
        try {
            countries.value = await customerService.getCountries()
        } catch (err: any) {
            console.error('Error loading countries:', err)
        }
    }

    const setSelectedCustomer = (customer: Customer | null) => {
        selectedCustomer.value = customer
        if (customer) {
            localStorage.setItem('selectedCustomer', JSON.stringify(customer))
            customerService.addToRecentCustomers(customer.dealerId.toString(), customer.id.toString())
        } else {
            localStorage.removeItem('selectedCustomer')
        }

        // Clear validation cache when customer changes
        validationCache.value.clear()
    }

    const loadSavedCustomer = () => {
        const saved = localStorage.getItem('selectedCustomer')
        if (saved) {
            try {
                selectedCustomer.value = JSON.parse(saved)
            } catch (err) {
                console.error('Error loading saved customer:', err)
                localStorage.removeItem('selectedCustomer')
            }
        }
    }

    const clearCustomer = () => {
        selectedCustomer.value = null
        localStorage.removeItem('selectedCustomer')
        validationCache.value.clear()
    }

    const clearCache = () => {
        customers.value = []
        searchResults.value = null
        recentCustomers.value = []
        error.value = null
        validationCache.value.clear()
        lastFetch.value = null
    }

    const clearDealerCustomers = (dealerId: string) => {
        customers.value = customers.value.filter(c => c.dealerId.toString() !== dealerId)
        if (searchResults.value) {
            searchResults.value.customers = searchResults.value.customers.filter(c => c.dealerId.toString() !== dealerId)
        }
    }

    const loadRecentCustomers = async (dealerId: string) => {
        try {
            recentCustomers.value = await customerService.getRecentCustomers(dealerId, 5)
        } catch (err: any) {
            console.error('Error loading recent customers:', err)
        }
    }

    const getCustomerStats = async (customerId: string) => {
        try {
            return await customerService.getCustomerStats(customerId)
        } catch (err: any) {
            console.error('Error getting customer stats:', err)
            return {
                totalVehicles: 0,
                totalDrivers: 0,
                activeVehicles: 0,
                activeDrivers: 0
            }
        }
    }

    const validateCustomerData = async (request: CreateCustomerRequest) => {
        try {
            return await customerService.validateCustomerData(request)
        } catch (err: any) {
            console.error('Error validating customer data:', err)
            throw err
        }
    }

    return {
        // State
        customers,
        selectedCustomer,
        searchResults,
        recentCustomers,
        countries,
        loading,
        creating,
        error,
        lastFetch,
        validationCache,

        // Getters
        activeCustomers,
        customersByDealer,
        customerById,
        hasSelectedCustomer,
        selectedCustomerName,
        isDataStale,

        // Actions
        fetchCustomers,
        fetchCustomersByDealer,
        getCustomerById,
        createCustomer,
        validateCustomer,
        searchCustomers,
        checkCustomerExists,
        loadCountries,
        setSelectedCustomer,
        loadSavedCustomer,
        clearCustomer,
        clearCache,
        clearDealerCustomers,
        loadRecentCustomers,
        getCustomerStats,
        validateCustomerData
    }
})
