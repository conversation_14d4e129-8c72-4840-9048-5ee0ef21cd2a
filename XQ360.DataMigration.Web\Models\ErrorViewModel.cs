namespace XQ360.DataMigration.Web.Models;

public class ErrorViewModel
{
    public string? RequestId { get; set; }

    public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
}

public class MigrationRequest
{
    public required string Environment { get; set; }
    public List<string> MigrationTypes { get; set; } = new();
    public Dictionary<string, IFormFile?> CsvFiles { get; set; } = new();
}

public class MigrationProgress
{
    public required string Id { get; set; }
    public string Status { get; set; } = "Pending";
    public string? CurrentStep { get; set; }
    public int Progress { get; set; } = 0;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public List<string> Messages { get; set; } = new();
    public string? ErrorMessage { get; set; }
    
    // Enhanced progress tracking with record summaries
    public List<string> CompletedSteps { get; set; } = new();
    public Dictionary<string, MigrationStepSummary> StepSummaries { get; set; } = new();
    public MigrationOverallSummary OverallSummary { get; set; } = new();
    
    // Flag to prevent redundant status updates
    public bool FinalStatusSet { get; set; } = false;
}

public class MigrationStepSummary
{
    public required string StepId { get; set; }
    public required string StepName { get; set; }
    public string Status { get; set; } = "Pending"; // Pending, Running, Completed, Failed
    public int RecordsProcessed { get; set; } = 0;
    public int RecordsInserted { get; set; } = 0;
    public int RecordsSkipped { get; set; } = 0;
    public int ErrorCount { get; set; } = 0;
    // WarningCount removed - warnings represent same data as skipped records
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan Duration => EndTime.HasValue && StartTime.HasValue ? EndTime.Value - StartTime.Value : TimeSpan.Zero;
}

public class MigrationOverallSummary
{
    public int TotalSteps { get; set; } = 0;
    public int CompletedSteps { get; set; } = 0;
    public int TotalRecordsProcessed { get; set; } = 0;
    public int TotalRecordsInserted { get; set; } = 0;
    public int TotalRecordsSkipped { get; set; } = 0;
    public int TotalErrors { get; set; } = 0;
    // TotalWarnings removed - warnings represent same data as skipped records
    public double CompletionPercentage => TotalSteps > 0 ? (double)CompletedSteps / TotalSteps * 100 : 0;
}

public class EnvironmentInfo
{
    public required string Key { get; set; }
    public required string Name { get; set; }
    public required string Description { get; set; }
}

public class MigrationTypeInfo
{
    public required string Key { get; set; }
    public required string Name { get; set; }
    public required string Description { get; set; }
}
