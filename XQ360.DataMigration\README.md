# 📋 XQ360 Data Migration Tool

*Import your data from CSV files into XQ360 - Simple and Safe!*

## 🚀 Quick Start

**3 easy steps:**
1. **Test system:** `dotnet test` (make sure it works with your database)
2. **Run migration:** Put CSV files in `CSV_Input` folder, then `dotnet run --migrate-all`
3. **Check results:** Look at report in `Reports` folder

**✅ Safe to run with your full dataset!** The migration is designed to handle issues gracefully.

---

## 📖 Complete User Guide

**👉 [Read the Complete User Guide →](USER-MANUAL.md)**

*Written for non-technical staff - easy to follow!*

**The guide includes:**
- ✅ **Simple step-by-step instructions**
- 📄 **CSV file preparation with examples**
- 🔄 **How the software processes your data**
- 📊 **Understanding your migration reports**
- ❓ **Help with common problems**

---

## 📋 What can you import?

✅ **Staff and Drivers** - Import all your people  
✅ **Vehicles** - Add forklifts, trucks, equipment  
✅ **Access Cards** - Set up who can use what  
✅ **Safety Checklists** - Configure daily safety checks  
✅ **User Permissions** - Control system access  
✅ **Vehicle IoT Sync** - Synchronize device configurations  

---

## 📄 Preparing your CSV files

**📁 Required file names** (put in `CSV_Input` folder):
- `PERSON_IMPORT_TEMPLATE.csv` - Your staff and drivers
- `VEHICLE_IMPORT.csv` - Your vehicles and equipment  
- `CARD_IMPORT.csv` - Access cards
- `PREOP_CHECKLIST_IMPORT.csv` - Safety questions
- `SPARE_MODEL_IMPORT_TEMPLATE.csv` - Equipment types
- `SUPERVISOR_ACCESS_IMPORT.csv` - Special permissions
- `DRIVER_BLACKLIST_IMPORT.csv` - Restricted drivers
- `WEBSITE_USER_IMPORT.csv` - Website login accounts

**💡 The [User Guide](USER-MANUAL.md) has detailed examples for each file!**

---

## ⚡ Simple Commands

```bash
# ALWAYS TEST FIRST - Check if migration works with your database
dotnet test

# Import everything (recommended - does it all in the right order)
dotnet run --migrate-all

# Get help
dotnet run --help
```

**⚠️ Always test first, then use `--migrate-all` unless you know what you're doing!**

---

## 📊 After Migration

**Check your results:**
- 📋 **Migration Report** in `Reports/` folder - Shows what worked and what needs fixing
- 🔧 **Log Files** in `Logs/` folder - Technical details for IT support

**If some records failed:**
1. Look at the migration report
2. Fix your CSV files based on the suggestions
3. Run the migration again

---

## ❓ Need Help?

**📖 First:** Check the [Complete User Guide](USER-MANUAL.md) - most questions are answered there!

**🆘 Still stuck?**
1. Save your migration report file
2. Contact your system administrator
3. Share the report and CSV files (they'll know what to do!)

---

## ✅ Quick Tips

- **🧪 System testing is required** - Always `dotnet test` first to check database compatibility
- **Migration is safe to run** - Designed to handle large datasets and partial failures gracefully
- **Use exact file names** - The tool looks for specific file names
- **Fill in required fields** - Don't leave important information blank
- **Check spelling** - Names must match exactly between files
- **Save as CSV** - Not Excel .xlsx files
- **Don't worry about backups** - Your IT team handles database backups
- **Sample testing is optional** - Only needed for very large datasets (1000+ records)

---

**🎯 Ready to start? Check out the [Complete User Guide](USER-MANUAL.md) for detailed instructions!** 