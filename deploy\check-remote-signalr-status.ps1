# XQ360 Data Migration - Remote Server SignalR Status Check
# This script diagnoses SignalR and logging issues on remote servers

param(
    [string]$ServerName = "localhost",
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [string]$AppPoolName = "XQ360MigrationPool"
)

Write-Host "XQ360 Data Migration - Remote Server SignalR Status Check" -ForegroundColor Green
Write-Host "Target Server: $ServerName" -ForegroundColor Yellow
Write-Host "Deploy Path: $DeployPath" -ForegroundColor Yellow

# Step 1: Check if logs directory exists and has files
Write-Host "`n=== CHECKING LOGS ===" -ForegroundColor Cyan
$logsPath = Join-Path $DeployPath "logs"
if (Test-Path $logsPath) {
    Write-Host "✅ Logs directory exists: $logsPath" -ForegroundColor Green
    
    # Check for stdout log files
    $stdoutLogs = Get-ChildItem -Path $logsPath -Filter "stdout_*.log" -ErrorAction SilentlyContinue
    if ($stdoutLogs) {
        Write-Host "✅ Found stdout log files:" -ForegroundColor Green
        foreach ($log in $stdoutLogs | Sort-Object LastWriteTime -Descending | Select-Object -First 3) {
            Write-Host "  • $($log.Name) - $($log.LastWriteTime)" -ForegroundColor White
        }
        
        # Show last few lines of most recent log
        $latestLog = $stdoutLogs | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        Write-Host "`n📋 Latest log entries from $($latestLog.Name):" -ForegroundColor Yellow
        try {
            $lastLines = Get-Content $latestLog.FullName -Tail 10 -ErrorAction Stop
            foreach ($line in $lastLines) {
                Write-Host "  $line" -ForegroundColor Gray
            }
        } catch {
            Write-Host "  ❌ Could not read log file: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ No stdout log files found" -ForegroundColor Red
    }
    
    # Check for application logs
    $appLogs = Get-ChildItem -Path $DeployPath -Filter "xq360-migration-*.log" -ErrorAction SilentlyContinue
    if ($appLogs) {
        Write-Host "✅ Found application log files:" -ForegroundColor Green
        foreach ($log in $appLogs | Sort-Object LastWriteTime -Descending | Select-Object -First 3) {
            Write-Host "  • $($log.Name) - $($log.LastWriteTime)" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️  No application log files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Logs directory does not exist: $logsPath" -ForegroundColor Red
}

# Step 2: Check web.config
Write-Host "`n=== CHECKING WEB.CONFIG ===" -ForegroundColor Cyan
$webConfigPath = Join-Path $DeployPath "web.config"
if (Test-Path $webConfigPath) {
    Write-Host "✅ Web.config exists: $webConfigPath" -ForegroundColor Green
    
    try {
        $webConfigContent = Get-Content $webConfigPath -Raw -ErrorAction Stop
        $xml = [xml]$webConfigContent
        
        # Check for essential settings
        $aspNetCore = $xml.SelectSingleNode("//aspNetCore")
        if ($aspNetCore) {
            Write-Host "✅ aspNetCore section found" -ForegroundColor Green
            
            $stdoutLogEnabled = $aspNetCore.GetAttribute("stdoutLogEnabled")
            if ($stdoutLogEnabled -eq "true") {
                Write-Host "✅ stdoutLogEnabled='true'" -ForegroundColor Green
            } else {
                Write-Host "❌ stdoutLogEnabled='$stdoutLogEnabled' (should be 'true')" -ForegroundColor Red
            }
            
            $hostingModel = $aspNetCore.GetAttribute("hostingModel")
            if ($hostingModel -eq "outofprocess") {
                Write-Host "✅ hostingModel='outofprocess'" -ForegroundColor Green
            } else {
                Write-Host "❌ hostingModel='$hostingModel' (should be 'outofprocess')" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ No aspNetCore section found" -ForegroundColor Red
        }
        
        # Check for WebSocket section
        $webSocket = $xml.SelectSingleNode("//webSocket")
        if ($webSocket) {
            Write-Host "⚠️  WebSocket section found (may cause conflicts)" -ForegroundColor Yellow
        } else {
            Write-Host "✅ No WebSocket section (good for avoiding conflicts)" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "❌ Could not parse web.config: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Web.config does not exist: $webConfigPath" -ForegroundColor Red
}

# Step 3: Check application pool status
Write-Host "`n=== CHECKING APPLICATION POOL ===" -ForegroundColor Cyan
try {
    Import-Module WebAdministration
    $appPool = Get-WebAppPool -Name $AppPoolName -ErrorAction Stop
    if ($appPool) {
        Write-Host "✅ Application pool '$AppPoolName' found" -ForegroundColor Green
        Write-Host "  • State: $($appPool.State)" -ForegroundColor White
        Write-Host "  • ProcessModel: $($appPool.ProcessModel.IdentityType)" -ForegroundColor White
        Write-Host "  • ManagedRuntimeVersion: $($appPool.ManagedRuntimeVersion)" -ForegroundColor White
        
        if ($appPool.State -eq "Started") {
            Write-Host "✅ Application pool is running" -ForegroundColor Green
        } else {
            Write-Host "❌ Application pool is not running (State: $($appPool.State))" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Application pool '$AppPoolName' not found" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Could not check application pool: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 4: Test SignalR hub accessibility
Write-Host "`n=== TESTING SIGNALR HUB ===" -ForegroundColor Cyan
try {
    $signalRUrl = "http://$ServerName/migrationHub"
    Write-Host "Testing SignalR hub at: $signalRUrl" -ForegroundColor Yellow
    
    $response = Invoke-WebRequest -Uri $signalRUrl -Method GET -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ SignalR hub is accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ SignalR hub test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "This indicates WebSocket/SignalR issues" -ForegroundColor Yellow
}

# Step 5: Check WebSocket support
Write-Host "`n=== CHECKING WEBSOCKET SUPPORT ===" -ForegroundColor Cyan
try {
    $webSocketFeature = Get-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -ErrorAction SilentlyContinue
    if ($webSocketFeature -and $webSocketFeature.State -eq "Enabled") {
        Write-Host "✅ WebSocket Protocol is enabled at server level" -ForegroundColor Green
    } else {
        Write-Host "❌ WebSocket Protocol is NOT enabled at server level" -ForegroundColor Red
        Write-Host "Run this command as Administrator to enable WebSocket:" -ForegroundColor Yellow
        Write-Host "Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets" -ForegroundColor Cyan
    }
} catch {
    Write-Host "⚠️  Could not check WebSocket feature: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 6: Check for running processes
Write-Host "`n=== CHECKING RUNNING PROCESSES ===" -ForegroundColor Cyan
try {
    $dotnetProcesses = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Where-Object { $_.ProcessName -eq "dotnet" }
    if ($dotnetProcesses) {
        Write-Host "✅ Found $($dotnetProcesses.Count) dotnet processes running" -ForegroundColor Green
        foreach ($proc in $dotnetProcesses | Select-Object -First 3) {
            Write-Host "  • PID: $($proc.Id), CPU: $($proc.CPU), Memory: $([math]::Round($proc.WorkingSet64/1MB, 2)) MB" -ForegroundColor White
        }
    } else {
        Write-Host "❌ No dotnet processes found running" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️  Could not check processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 7: Display recommendations
Write-Host "`n=== RECOMMENDATIONS ===" -ForegroundColor Cyan

if (-not (Test-Path $logsPath)) {
    Write-Host "1. Create logs directory: New-Item -ItemType Directory -Path '$logsPath' -Force" -ForegroundColor Yellow
}

if (-not (Get-ChildItem -Path $logsPath -Filter "stdout_*.log" -ErrorAction SilentlyContinue)) {
    Write-Host "2. Check if application is writing logs - may indicate startup issues" -ForegroundColor Yellow
}

try {
    $webSocketFeature = Get-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -ErrorAction SilentlyContinue
    if (-not $webSocketFeature -or $webSocketFeature.State -ne "Enabled") {
        Write-Host "3. Enable WebSocket Protocol (Run as Administrator):" -ForegroundColor Yellow
        Write-Host "   Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets" -ForegroundColor Cyan
    }
} catch { }

Write-Host "4. Restart application pool if needed:" -ForegroundColor Yellow
Write-Host "   Restart-WebAppPool -Name '$AppPoolName'" -ForegroundColor Cyan

Write-Host "5. Check browser console for WebSocket connection errors" -ForegroundColor Yellow
Write-Host "6. Monitor logs in real-time:" -ForegroundColor Yellow
Write-Host "   Get-Content '$logsPath\stdout_*.log' -Wait" -ForegroundColor Cyan

Write-Host "`n✅ Remote server SignalR status check completed!" -ForegroundColor Green 