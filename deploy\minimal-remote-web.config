<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      
      <!-- ESSENTIAL FIXES: Enable logging and use outofprocess hosting model for SignalR -->
      <aspNetCore processPath="dotnet" 
                  arguments=".\XQ360.DataMigration.Web.dll" 
                  stdoutLogEnabled="true" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="outofprocess"
                  forwardWindowsAuthToken="false">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
          <environmentVariable name="ASPNETCORE_URLS" value="http://localhost:8080" />
          <environmentVariable name="DOTNET_ENVIRONMENT" value="Production" />
        </environmentVariables>
      </aspNetCore>
      
      <!-- Enable WebSockets for SignalR -->
      <webSocket enabled="true" />
    </system.webServer>
  </location>
</configuration> 