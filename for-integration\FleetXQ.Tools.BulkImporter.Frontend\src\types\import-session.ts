import type { CreateCustomerRequest } from './customer'

export interface ImportSession {
    id: string
    dealerId: number
    customerId: number
    environmentName: string
    vehicleCount: number
    driverCount: number
    status: ImportSessionStatus
    createdAt: Date
    startedAt?: Date
    completedAt?: Date
    errorMessage?: string
    progress?: ImportProgress
    results?: ImportResults
}

export type ImportSessionStatus =
    | 'created'
    | 'validating'
    | 'validated'
    | 'queued'
    | 'processing'
    | 'in_progress'
    | 'completed'
    | 'failed'
    | 'cancelled'

export interface ImportProgress {
    stage: ImportStage
    percentage: number
    currentOperation: string
    estimatedTimeRemaining?: number
    vehiclesProcessed: number
    driversProcessed: number
    errorsCount: number
    warningsCount: number
}

export type ImportStage =
    | 'validation'
    | 'data_generation'
    | 'vehicles_import'
    | 'drivers_import'
    | 'finalization'

export interface ImportResults {
    totalVehiclesImported: number
    totalDriversImported: number
    totalErrors: number
    totalWarnings: number
    duration: number
    errorDetails: ImportError[]
    warningDetails: ImportWarning[]
}

export interface ImportError {
    type: string
    message: string
    details?: Record<string, any>
    timestamp: Date
}

export interface ImportWarning {
    type: string
    message: string
    details?: Record<string, any>
    timestamp: Date
}

export interface CreateImportSessionRequest {
    dealerId: string
    customerId: string
    driversCount: number
    vehiclesCount: number
    batchSize?: number
    description?: string
    options?: {
        skipValidation?: boolean
        dryRun?: boolean
        overwriteExisting?: boolean
    }
}

export interface ImportSessionFilter {
    dealerId?: number
    status?: ImportSessionStatus[]
    fromDate?: Date
    toDate?: Date
    limit?: number
    offset?: number
}

export interface ImportSessionSearchResult {
    sessions: ImportSession[]
    total: number
    totalCount: number
    hasMore: boolean
}
