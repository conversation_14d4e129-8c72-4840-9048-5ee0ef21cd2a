using Microsoft.AspNetCore.Mvc;
using XQ360.DataMigration.Web.Services.Monitoring;
using System;
using System.Threading.Tasks;

namespace XQ360.DataMigration.Web.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MonitoringController : ControllerBase
    {
        private readonly IPerformanceMonitoringService _performanceMonitoring;
        private readonly IAuditTrailService _auditTrail;
        private readonly IHealthCheckService _healthCheck;
        private readonly IAlertingService _alerting;

        public MonitoringController(
            IPerformanceMonitoringService performanceMonitoring,
            IAuditTrailService auditTrail,
            IHealthCheckService healthCheck,
            IAlertingService alerting)
        {
            _performanceMonitoring = performanceMonitoring;
            _auditTrail = auditTrail;
            _healthCheck = healthCheck;
            _alerting = alerting;
        }

        [HttpGet("health")]
        public async Task<IActionResult> GetSystemHealth()
        {
            var health = await _healthCheck.GetSystemHealthAsync();
            return Ok(health);
        }

        [HttpGet("performance")]
        public async Task<IActionResult> GetPerformanceMetrics()
        {
            var metrics = await _performanceMonitoring.GetCurrentMetricsAsync();
            return Ok(metrics);
        }

        [HttpGet("performance/report")]
        public async Task<IActionResult> GetPerformanceReport([FromQuery] int hours = 1)
        {
            var report = await _performanceMonitoring.GetThroughputReportAsync(TimeSpan.FromHours(hours));
            return Ok(report);
        }

        [HttpGet("alerts")]
        public async Task<IActionResult> GetActiveAlerts()
        {
            var alerts = await _alerting.GetActiveAlertsAsync();
            return Ok(alerts);
        }

        [HttpGet("alerts/statistics")]
        public async Task<IActionResult> GetAlertStatistics([FromQuery] int hours = 24)
        {
            var statistics = await _alerting.GetAlertStatisticsAsync(TimeSpan.FromHours(hours));
            return Ok(statistics);
        }

        [HttpGet("audit/{sessionId}")]
        public async Task<IActionResult> GetAuditSummary(Guid sessionId)
        {
            var summary = await _auditTrail.GetAuditSummaryAsync(sessionId);
            return Ok(summary);
        }

        [HttpGet("diagnostics")]
        public async Task<IActionResult> RunDiagnostics()
        {
            var diagnostics = await _healthCheck.RunDiagnosticsAsync();
            return Ok(diagnostics);
        }

        [HttpPost("alerts/{alertId}/acknowledge")]
        public async Task<IActionResult> AcknowledgeAlert(Guid alertId, [FromBody] string acknowledgedBy)
        {
            await _alerting.AcknowledgeAlertAsync(alertId, acknowledgedBy);
            return Ok();
        }

        [HttpPost("alerts/{alertId}/resolve")]
        public async Task<IActionResult> ResolveAlert(Guid alertId, [FromBody] ResolveAlertRequest request)
        {
            await _alerting.ResolveAlertAsync(alertId, request.ResolvedBy, request.Resolution);
            return Ok();
        }
    }

    public class ResolveAlertRequest
    {
        public string ResolvedBy { get; set; } = string.Empty;
        public string Resolution { get; set; } = string.Empty;
    }
}
