# Environment Configuration Guide

## Overview

The XQ360 Data Migration Tool supports multiple target environments (US, UK, AU, Pilot, Development) with separate configuration settings for each. This guide explains how to configure these environments in the `appsettings.json` file.

## Configuration Structure

Each environment requires the following settings:
- **Database Connection**: SQL Server connection string for the target database
- **API Base URL**: The base URL for the XQ360 API in that environment
- **API Credentials**: Username and password for API authentication
- **Name & Description**: Display information for the UI

## appsettings.json Format

```json
{
  "Migration": {
    "Environments": {
      "US": {
        "Name": "United States Production",
        "Description": "US Production Environment",
        "DatabaseConnection": "Server=us-server;Database=FleetXQ_US;User Id=username;Password=password;",
        "ApiBaseUrl": "https://us-api.xq360.com/",
        "ApiUsername": "us-migration-user",
        "ApiPassword": "your-api-password"
      },
      "UK": {
        "Name": "United Kingdom Production", 
        "Description": "UK Production Environment",
        "DatabaseConnection": "Server=uk-server;Database=FleetXQ_UK;User Id=username;Password=password;",
        "ApiBaseUrl": "https://uk-api.xq360.com/",
        "ApiUsername": "uk-migration-user",
        "ApiPassword": "your-api-password"
      },
      "AU": {
        "Name": "Australia Production",
        "Description": "AU Production Environment", 
        "DatabaseConnection": "Server=au-server;Database=FleetXQ_AU;User Id=username;Password=password;",
        "ApiBaseUrl": "https://au-api.xq360.com/",
        "ApiUsername": "au-migration-user",
        "ApiPassword": "your-api-password"
      },
                  "Development": {
                "Name": "Development Environment",
                "Description": "Development and testing environment",
        "DatabaseConnection": "Server=localhost;Database=FleetXQ_Local;Integrated Security=true;",
        "ApiBaseUrl": "https://localhost:53052/",
        "ApiUsername": "admin",
        "ApiPassword": "your-local-password"
      },
      "Pilot": {
        "Name": "Pilot Testing Environment",
        "Description": "Testing and Validation Environment",
        "DatabaseConnection": "Server=localhost;Database=FleetXQ_Test;Integrated Security=true;",
        "ApiBaseUrl": "https://localhost:53052/",
        "ApiUsername": "Admin",
        "ApiPassword": "Admin"
      }
    },
    "BatchSize": 100,
    "MaxRetryAttempts": 3,
    "BackupEnabled": true,
    "ValidateBeforeMigration": true,
    "ContinueOnError": false
  }
}
```

## Environment-Specific Settings

### US Environment
- **Server Location**: US data centers
- **Database**: US-specific database instance
- **API Endpoint**: US regional API server
- **Timezone**: Consider US timezone implications for data

### UK Environment  
- **Server Location**: UK/European data centers
- **Database**: UK-specific database instance
- **API Endpoint**: UK regional API server
- **Compliance**: GDPR and UK data protection requirements

### AU Environment
- **Server Location**: Australia/Asia-Pacific data centers
- **Database**: AU-specific database instance  
- **API Endpoint**: AU regional API server
- **Compliance**: Australian data protection requirements

### Development Environment

The Development environment is designed for development and testing:
- Uses local SQL Server instance
- Points to localhost API
- Simplified authentication for development

### Pilot Environment
- **Purpose**: Testing and validation
- **Database**: Local or test database
- **API**: Development/staging API server
- **Usage**: Safe environment for testing changes

## Security Considerations

### Password Management
- **Production**: Use secure, complex passwords
- **Rotation**: Regularly rotate API credentials
- **Storage**: Consider using Azure Key Vault or similar for production
- **Access**: Limit access to configuration files

### Connection Strings
- **Encryption**: Use encrypted connections (TrustServerCertificate=false)
- **Timeouts**: Set appropriate connection timeouts
- **Pooling**: Configure connection pooling for performance
- **Authentication**: Use integrated security where possible

## Setup Steps

1. **Copy Template**
   ```bash
   cp server-appsettings-template.json appsettings.json
   ```

2. **Configure Each Environment**
   - Update database connection strings
   - Set correct API URLs
   - Configure authentication credentials
   - Add environment descriptions

3. **Test Connections**
   - Use the GUI to select each environment
   - Run validation to test connectivity
   - Verify API authentication works

4. **Secure Configuration**
   - Protect the appsettings.json file
   - Ensure proper file permissions
   - Consider environment variables for sensitive data

## Dynamic Environment Switching

The GUI automatically:
- Loads available environments from configuration
- Displays environments in the dropdown
- Switches database/API connections when environment changes
- Shows current environment in status bar
- Validates connectivity for selected environment

## CLI Usage with Environments

When using CLI mode, the tool uses the default environment (Development). To use CLI with specific environments, the configuration is automatically applied based on the appsettings.json file.

## Troubleshooting

### Connection Issues
1. **Database**: Verify connection string format and credentials
2. **API**: Check URL format and authentication
3. **Network**: Ensure firewall/network access
4. **SSL**: Verify certificate trust settings

### Configuration Errors
1. **JSON Format**: Validate JSON syntax
2. **Missing Fields**: Ensure all required fields are present
3. **Case Sensitivity**: Check exact field names
4. **Encoding**: Ensure UTF-8 encoding

### Environment Not Found
1. **Check Key**: Ensure environment key matches exactly
2. **Configuration**: Verify environment exists in appsettings.json
3. **Spelling**: Check for typos in environment names

## Best Practices

1. **Backup Configurations**: Keep backup copies of working configurations
2. **Version Control**: Use git to track configuration changes (exclude passwords)
3. **Documentation**: Document environment-specific requirements
4. **Testing**: Always test in Local or Pilot before production migrations
5. **Monitoring**: Monitor migration logs for environment-specific issues
6. **Validation**: Use the validation feature before running migrations 