<template>
  <div class="progress-tracker">
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="fas fa-tasks me-2"></i>
            Import Progress
          </h5>
          <div class="status-indicators">
            <span 
              class="badge me-2" 
              :class="connectionStatusClass"
            >
              <i :class="connectionIcon" class="me-1"></i>
              {{ connectionStatus }}
            </span>
            <span 
              class="badge" 
              :class="sessionStatusClass"
            >
              {{ sessionStatus }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="card-body">
        <!-- Overall Progress -->
        <div v-if="currentProgress" class="overall-progress mb-3">
          <div class="d-flex justify-content-between align-items-center mb-1">
            <small class="mb-0 fw-medium">{{ currentProgress.stage }}</small>
            <span class="badge bg-secondary text-dark small">{{ currentProgress.percentage }}%</span>
          </div>

          <div class="progress mb-1" style="height: 6px;">
            <div 
              class="progress-bar progress-bar-striped" 
              :class="progressBarClass"
              role="progressbar" 
              :style="{ width: currentProgress.percentage + '%' }"
              :aria-valuenow="currentProgress.percentage" 
              aria-valuemin="0" 
              aria-valuemax="100"
            >
            </div>
          </div>
          
          <div class="progress-details">
            <div class="row">
              <div class="col-md-6">
                <small class="text-muted">
                  <i class="fas fa-info-circle me-1"></i>
                  Step {{ currentProgress.currentStep }} of {{ currentProgress.totalSteps }}
                </small>
              </div>
              <div class="col-md-6 text-end">
                <small class="text-muted">
                  <i class="fas fa-clock me-1"></i>
                  {{ currentProgress.estimatedTimeRemaining || 'Calculating...' }}
                </small>
              </div>
            </div>
          </div>
          
          <div class="current-message mt-2">
            <p class="mb-0 text-muted">{{ currentProgress.message }}</p>
          </div>
        </div>

        <!-- No Progress State -->
        <div v-else-if="!isProcessing" class="no-progress text-center py-4">
          <div class="text-muted">
            <i class="fas fa-hourglass-start fa-2x mb-3"></i>
            <p class="mb-0">{{ waitingMessage }}</p>
          </div>
        </div>

        <!-- Processing State without Progress -->
        <div v-else class="processing-state text-center py-4">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">Processing...</span>
          </div>
          <p class="text-muted mb-0">Import operation is starting...</p>
        </div>

        <!-- Detailed Steps -->
        <div v-if="showDetailedSteps && steps.length > 0" class="detailed-steps">
          <h6 class="border-bottom pb-2 mb-3">
            <i class="fas fa-list me-1"></i>
            Processing Steps
          </h6>
          
          <div class="steps-list">
            <div 
              v-for="(step, index) in steps" 
              :key="index"
              class="step-item d-flex align-items-center mb-2"
              :class="getStepClass(index)"
            >
              <div class="step-icon me-3">
                <i :class="getStepIcon(index)"></i>
              </div>
              <div class="step-content flex-grow-1">
                <div class="step-title">{{ step.title }}</div>
                <div v-if="step.description" class="step-description text-muted small">
                  {{ step.description }}
                </div>
              </div>
              <div v-if="getStepStatus(index) === 'current'" class="step-progress">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                  <span class="visually-hidden">Processing...</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="errors.length > 0" class="errors-section mt-4">
          <h6 class="text-danger border-bottom pb-2 mb-3">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Errors ({{ errors.length }})
          </h6>
          
          <div class="errors-list">
            <div 
              v-for="error in errors" 
              :key="error.timestamp.getTime()"
              class="alert alert-danger alert-sm mb-2"
            >
              <div class="d-flex justify-content-between align-items-start">
                <div class="error-content">
                  <strong>{{ error.error }}</strong>
                  <div v-if="error.details" class="error-details mt-1 small">
                    {{ error.details }}
                  </div>
                </div>
                <small class="text-muted">
                  {{ formatTime(error.timestamp) }}
                </small>
              </div>
            </div>
          </div>
        </div>

        <!-- Session Actions -->
        <div v-if="showActions" class="session-actions mt-4 pt-3 border-top">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <button 
                v-if="canToggleDetails"
                type="button" 
                class="btn btn-outline-secondary btn-sm"
                @click="toggleDetailedSteps"
              >
                <i :class="showDetailedSteps ? 'fas fa-eye-slash' : 'fas fa-eye'" class="me-1"></i>
                {{ showDetailedSteps ? 'Hide' : 'Show' }} Details
              </button>
            </div>
            
            <div>
              <button 
                v-if="canCancel"
                type="button" 
                class="btn btn-outline-danger btn-sm me-2"
                @click="cancelImport"
                :disabled="cancelling"
              >
                <span v-if="cancelling" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                <i v-else class="fas fa-stop me-1"></i>
                {{ cancelling ? 'Cancelling...' : 'Cancel' }}
              </button>
              
              <button 
                v-if="canRefresh"
                type="button" 
                class="btn btn-outline-primary btn-sm"
                @click="refreshStatus"
                :disabled="refreshing"
              >
                <span v-if="refreshing" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                <i v-else class="fas fa-sync-alt me-1"></i>
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useSignalR } from '@/services/signalr'

// Props
const props = defineProps<{
  sessionId: string
  showActions?: boolean
  autoConnect?: boolean
}>()

// Emits
const emit = defineEmits<{
  'cancel-requested': []
  'status-changed': [status: string]
}>()

// SignalR composable
const { service, subscribe, unsubscribe, getSessionProgress, getSessionStatus, getSessionErrors, connected } = useSignalR()

// Local state
const showDetailedSteps = ref(false)
const cancelling = ref(false)
const refreshing = ref(false)

// Import steps definition
const steps = ref([
  { title: 'Validation', description: 'Validating input data and prerequisites' },
  { title: 'Data Generation', description: 'Generating vehicle and driver data' },
  { title: 'Database Operations', description: 'Inserting data into database' },
  { title: 'Post-Processing', description: 'Finalizing import and cleanup' },
  { title: 'Completion', description: 'Import operation completed' }
])

// Computed properties
const currentProgress = computed(() => getSessionProgress(props.sessionId))
const currentStatus = computed(() => getSessionStatus(props.sessionId))
const errors = computed(() => getSessionErrors(props.sessionId))

const isProcessing = computed(() => {
  const status = currentStatus.value?.status
  return status === 'processing' || status === 'queued'
})

const sessionStatus = computed(() => {
  const status = currentStatus.value?.status
  if (!status) return 'Unknown'
  
  return status.charAt(0).toUpperCase() + status.slice(1)
})

const sessionStatusClass = computed(() => {
  const status = currentStatus.value?.status
  switch (status) {
    case 'queued': return 'bg-secondary text-dark'
    case 'processing': return 'bg-dark text-white'
    case 'completed': return 'bg-dark text-white'
    case 'failed': return 'bg-secondary text-dark'
    case 'cancelled': return 'bg-light text-dark'
    default: return 'bg-light text-dark'
  }
})

const connectionStatus = computed(() => {
  if (connected.value()) return 'Connected'
  if (service.connecting) return 'Connecting'
  return 'Disconnected'
})

const connectionStatusClass = computed(() => {
  if (connected.value()) return 'bg-dark text-white'
  if (service.connecting) return 'bg-secondary text-dark'
  return 'bg-light text-dark'
})

const connectionIcon = computed(() => {
  if (connected.value()) return 'fas fa-check'
  if (service.connecting) return 'fas fa-spinner fa-spin'
  return 'fas fa-times'
})

const progressBarClass = computed(() => {
  if (!currentProgress.value) return 'bg-secondary'

  if (currentProgress.value.percentage === 100) return 'bg-dark'
  if (errors.value.length > 0) return 'bg-secondary'
  return 'bg-secondary progress-bar-animated'
})

const waitingMessage = computed(() => {
  if (!connected.value()) return 'Connecting to real-time updates...'
  if (currentStatus.value?.status === 'queued') return 'Import queued, waiting to start...'
  return 'Waiting for import to begin...'
})

const canCancel = computed(() => {
  const status = currentStatus.value?.status
  return isProcessing.value && status !== 'cancelled' && !cancelling.value
})

const canRefresh = computed(() => !refreshing.value)

const canToggleDetails = computed(() => steps.value.length > 0)

// Methods
const getStepStatus = (stepIndex: number): 'pending' | 'current' | 'completed' | 'error' => {
  if (!currentProgress.value) return 'pending'
  
  const currentStep = currentProgress.value.currentStep - 1 // Convert to 0-based index
  
  if (stepIndex < currentStep) return 'completed'
  if (stepIndex === currentStep) return 'current'
  return 'pending'
}

const getStepClass = (stepIndex: number) => {
  const status = getStepStatus(stepIndex)
  switch (status) {
    case 'completed': return 'step-completed'
    case 'current': return 'step-current'
    case 'error': return 'step-error'
    default: return 'step-pending'
  }
}

const getStepIcon = (stepIndex: number) => {
  const status = getStepStatus(stepIndex)
  switch (status) {
    case 'completed': return 'fas fa-check-circle text-success'
    case 'current': return 'fas fa-play-circle text-primary'
    case 'error': return 'fas fa-exclamation-circle text-danger'
    default: return 'far fa-circle text-muted'
  }
}

const toggleDetailedSteps = () => {
  showDetailedSteps.value = !showDetailedSteps.value
}

const cancelImport = async () => {
  if (!canCancel.value) return
  
  cancelling.value = true
  try {
    // Call API to cancel import
    const response = await fetch(`/api/bulk-import/sessions/${props.sessionId}`, {
      method: 'DELETE'
    })
    
    if (response.ok) {
      emit('cancel-requested')
    } else {
      console.error('Failed to cancel import')
    }
  } catch (error) {
    console.error('Error cancelling import:', error)
  } finally {
    cancelling.value = false
  }
}

const refreshStatus = async () => {
  if (!canRefresh.value) return
  
  refreshing.value = true
  try {
    // Call API to get current status
    const response = await fetch(`/api/bulk-import/sessions/${props.sessionId}`)
    if (response.ok) {
      await response.json()
      // Status will be updated via SignalR
    }
  } catch (error) {
    console.error('Error refreshing status:', error)
  } finally {
    refreshing.value = false
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString()
}

// Watchers
watch(() => currentStatus.value?.status, (newStatus) => {
  if (newStatus) {
    emit('status-changed', newStatus)
  }
})

// Lifecycle
onMounted(async () => {
  // Subscribe to SignalR events
  subscribe()
  
  // Connect if auto-connect is enabled
  if (props.autoConnect !== false) {
    try {
      await service.connect()
      if (connected.value()) {
        await service.joinSession(props.sessionId)
      }
    } catch (error) {
      console.error('Failed to connect to SignalR:', error)
    }
  }
})

onUnmounted(async () => {
  // Clean up
  unsubscribe()
  
  if (connected.value()) {
    try {
      await service.leaveSession(props.sessionId)
    } catch (error) {
      console.error('Failed to leave session:', error)
    }
  }
})
</script>

<style scoped>
.progress-tracker {
  .card {
    border-left: 4px solid var(--bs-primary);
  }
  
  .status-indicators {
    .badge {
      font-size: 0.75rem;
    }
  }
  
  .overall-progress {
    .progress {
      border-radius: 0.5rem;
    }
    
    .progress-bar {
      border-radius: 0.5rem;
    }
  }
  
  .detailed-steps {
    .step-item {
      padding: 0.75rem;
      border-radius: 0.25rem;
      transition: all 0.3s ease;
      
      &.step-completed {
        background-color: rgba(73, 80, 87, 0.1);
        border-left: 3px solid #495057;
      }

      &.step-current {
        background-color: rgba(108, 117, 125, 0.1);
        border-left: 3px solid #6c757d;
      }

      &.step-error {
        background-color: rgba(73, 80, 87, 0.1);
        border-left: 3px solid #495057;
      }

      &.step-pending {
        background-color: rgba(222, 226, 230, 0.5);
        border-left: 3px solid #dee2e6;
      }
    }
    
    .step-icon {
      width: 24px;
      text-align: center;
    }
    
    .step-title {
      font-weight: 500;
    }
    
    .step-description {
      font-size: 0.875rem;
    }
  }
  
  .errors-section {
    .alert-sm {
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
    }
    
    .error-details {
      font-family: monospace;
      background-color: rgba(0, 0, 0, 0.1);
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
    }
  }
  
  .no-progress,
  .processing-state {
    color: #6c757d;
  }
  
  .session-actions {
    .btn-sm {
      font-size: 0.8rem;
    }
  }
}
</style>
