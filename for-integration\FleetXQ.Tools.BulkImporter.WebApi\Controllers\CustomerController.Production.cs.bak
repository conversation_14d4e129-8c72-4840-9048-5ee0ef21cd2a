using Microsoft.AspNetCore.Mvc;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using FleetXQ.BusinessLayer.Components.Server;

namespace FleetXQ.Tools.BulkImporter.WebApi.Controllers;

/// <summary>
/// Production-ready controller for customer management operations in the bulk importer
/// This version integrates with the full FleetXQ data layer and business components
/// </summary>
[ApiController]
[Route("api/customer-production")]
[Produces("application/json")]
public class CustomerProductionController : ControllerBase
{
    private readonly IDataFacade _dataFacade;
    private readonly ICustomerAPI _customerAPI;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CustomerProductionController> _logger;

    public CustomerProductionController(
        IDataFacade dataFacade,
        ICustomerAPI customerAPI,
        IServiceProvider serviceProvider,
        ILogger<CustomerProductionController> logger)
    {
        _dataFacade = dataFacade ?? throw new ArgumentNullException(nameof(dataFacade));
        _customerAPI = customerAPI ?? throw new ArgumentNullException(nameof(customerAPI));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Gets customers for a specific dealer
    /// </summary>
    /// <param name="dealerId">Dealer ID to get customers for</param>
    /// <param name="activeOnly">Whether to return only active customers (default: true)</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 100)</param>
    /// <returns>List of customers for the dealer</returns>
    /// <response code="200">Returns the list of customers</response>
    /// <response code="400">If the dealer ID is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(CustomerListResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<CustomerListResponse>> GetCustomers(
        [FromQuery] Guid dealerId,
        [FromQuery] bool activeOnly = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            if (dealerId == Guid.Empty)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid dealer ID",
                    Detail = "Dealer ID cannot be empty",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageNumber < 1)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page number",
                    Detail = "Page number must be greater than 0",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page size",
                    Detail = "Page size must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Getting customers for dealer: {DealerId}, activeOnly: {ActiveOnly}, page: {PageNumber}, size: {PageSize}",
                dealerId, activeOnly, pageNumber, pageSize);

            // Build filter predicate
            var filterPredicate = "DealerId == @0";
            var filterArguments = new List<object> { dealerId };

            if (activeOnly)
            {
                filterPredicate += " AND Active == true";
            }

            // Get customers from data provider
            var customers = await _dataFacade.CustomerDataProvider.GetCollectionAsync(
                includes: new List<string> { "Country" },
                filterPredicate: filterPredicate,
                filterArguments: filterArguments.ToArray(),
                pageNumber: pageNumber,
                pageSize: pageSize,
                sortOrder: "ASC",
                sortColumn: "CompanyName"
            );

            // Get total count for pagination
            var totalCount = await _dataFacade.CustomerDataProvider.GetCountAsync(
                filterPredicate: filterPredicate,
                filterArguments: filterArguments.ToArray()
            );

            var customerInfos = customers.Select(c => new CustomerInfo
            {
                Id = c.Id,
                CompanyName = c.CompanyName ?? string.Empty,
                ContactNumber = c.ContactNumber ?? string.Empty,
                Email = c.Email ?? string.Empty,
                Address = c.Addess ?? string.Empty,
                Active = c.Active,
                DealerCustomer = c.DealerCustomer ?? false,
                ContractNumber = c.ContractNumber ?? string.Empty,
                ContractDate = c.ContractDate,
                Description = c.Description ?? string.Empty,
                DealerId = c.DealerId,
                CountryId = c.CountryId,
                SitesCount = c.SitesCount ?? 0
            }).ToList();

            var response = new CustomerListResponse
            {
                Customers = customerInfos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                DealerId = dealerId
            };

            _logger.LogDebug("Retrieved {Count} customers out of {TotalCount} total for dealer {DealerId}",
                customers.Count, totalCount, dealerId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customers for dealer: {DealerId}", dealerId);
            return Problem(
                title: "Error retrieving customers",
                detail: "An error occurred while retrieving the list of customers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets a specific customer by ID
    /// </summary>
    /// <param name="id">Customer ID</param>
    /// <returns>Customer information</returns>
    /// <response code="200">Returns the customer information</response>
    /// <response code="404">If the customer is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(CustomerInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<CustomerInfo>> GetCustomer(Guid id)
    {
        try
        {
            _logger.LogDebug("Getting customer with ID: {CustomerId}", id);

            var customer = await _dataFacade.CustomerDataProvider.GetAsync(id, includes: new List<string> { "Country" });

            if (customer == null)
            {
                _logger.LogWarning("Customer not found with ID: {CustomerId}", id);
                return NotFound(new ProblemDetails
                {
                    Title = "Customer not found",
                    Detail = $"No customer found with ID: {id}",
                    Status = StatusCodes.Status404NotFound
                });
            }

            var customerInfo = new CustomerInfo
            {
                Id = customer.Id,
                CompanyName = customer.CompanyName ?? string.Empty,
                ContactNumber = customer.ContactNumber ?? string.Empty,
                Email = customer.Email ?? string.Empty,
                Address = customer.Addess ?? string.Empty,
                Active = customer.Active,
                DealerCustomer = customer.DealerCustomer ?? false,
                ContractNumber = customer.ContractNumber ?? string.Empty,
                ContractDate = customer.ContractDate,
                Description = customer.Description ?? string.Empty,
                DealerId = customer.DealerId,
                CountryId = customer.CountryId,
                SitesCount = customer.SitesCount ?? 0
            };

            _logger.LogDebug("Retrieved customer: {CompanyName}", customerInfo.CompanyName);
            return Ok(customerInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customer with ID: {CustomerId}", id);
            return Problem(
                title: "Error retrieving customer",
                detail: "An error occurred while retrieving the customer",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Creates a new customer for a dealer
    /// </summary>
    /// <param name="request">Customer creation request</param>
    /// <returns>Created customer information</returns>
    /// <response code="201">Returns the created customer</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost]
    [ProducesResponseType(typeof(CustomerInfo), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<CustomerInfo>> CreateCustomer([FromBody] CreateCustomerRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Validate required fields
            var validationErrors = new List<string>();

            if (request.DealerId == Guid.Empty)
                validationErrors.Add("DealerId is required");

            if (string.IsNullOrWhiteSpace(request.CompanyName))
                validationErrors.Add("CompanyName is required");

            if (request.CountryId == Guid.Empty)
                validationErrors.Add("CountryId is required");

            if (validationErrors.Any())
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Validation failed",
                    Detail = string.Join("; ", validationErrors),
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Creating customer for dealer: {DealerId}, company: {CompanyName}",
                request.DealerId, request.CompanyName);

            // Verify dealer exists
            var dealer = await _dataFacade.DealerDataProvider.GetAsync(request.DealerId);
            if (dealer == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid dealer",
                    Detail = $"Dealer with ID {request.DealerId} does not exist",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Verify country exists
            var country = await _dataFacade.CountryDataProvider.GetAsync(request.CountryId);
            if (country == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid country",
                    Detail = $"Country with ID {request.CountryId} does not exist",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Create new customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.DealerId = request.DealerId;
            customer.CompanyName = request.CompanyName.Trim();
            customer.CountryId = request.CountryId;
            customer.ContactNumber = request.ContactNumber?.Trim();
            customer.Email = request.Email?.Trim();
            customer.Addess = request.Address?.Trim();
            customer.Description = request.Description?.Trim();
            customer.ContractNumber = request.ContractNumber?.Trim();
            customer.ContractDate = request.ContractDate;
            customer.Active = true;
            customer.DealerCustomer = false; // Regular customer, not dealer customer

            // Save customer
            var savedCustomer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var customerInfo = new CustomerInfo
            {
                Id = savedCustomer.Id,
                CompanyName = savedCustomer.CompanyName ?? string.Empty,
                ContactNumber = savedCustomer.ContactNumber ?? string.Empty,
                Email = savedCustomer.Email ?? string.Empty,
                Address = savedCustomer.Addess ?? string.Empty,
                Active = savedCustomer.Active,
                DealerCustomer = savedCustomer.DealerCustomer ?? false,
                ContractNumber = savedCustomer.ContractNumber ?? string.Empty,
                ContractDate = savedCustomer.ContractDate,
                Description = savedCustomer.Description ?? string.Empty,
                DealerId = savedCustomer.DealerId,
                CountryId = savedCustomer.CountryId,
                SitesCount = 0
            };

            _logger.LogDebug("Created customer: {CompanyName} with ID: {CustomerId}",
                customerInfo.CompanyName, customerInfo.Id);

            return CreatedAtAction(nameof(GetCustomer), new { id = customerInfo.Id }, customerInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer for dealer: {DealerId}", request?.DealerId);
            return Problem(
                title: "Error creating customer",
                detail: "An error occurred while creating the customer",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }
}
