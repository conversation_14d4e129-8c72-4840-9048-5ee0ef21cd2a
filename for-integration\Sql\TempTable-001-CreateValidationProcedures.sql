-- =============================================
-- FleetXQ Bulk Importer - Temp Table Validation Procedures
-- Creates stored procedures for validating temporary staging data
-- These procedures work with session temp tables instead of permanent staging tables
-- =============================================

-- =============================================
-- Procedure: usp_ValidateDriverImportTemp
-- Validates driver staging data in temp tables and resolves FK relationships
-- =============================================
IF OBJECT_ID('[dbo].[usp_ValidateDriverImportTemp]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[usp_ValidateDriverImportTemp]
GO

CREATE PROCEDURE [dbo].[usp_ValidateDriverImportTemp]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @RowCount INT = 0;
    
    BEGIN TRY
        -- Validate that temp table exists
        IF NOT EXISTS (SELECT 1 FROM tempdb.sys.objects WHERE name LIKE '#DriverImport%' AND type = 'U')
        BEGIN
            RAISERROR('Temp table #DriverImport does not exist. Ensure CreateTempStagingTables was called first.', 16, 1);
            RETURN;
        END
        
        -- Reset validation status for this session
        UPDATE #DriverImport
        SET [ValidationStatus] = 'Pending',
            [ValidationErrors] = NULL,
            [CustomerId] = NULL,
            [SiteId] = NULL,
            [DepartmentId] = NULL,
            [ExistingPersonId] = NULL,
            [ExistingDriverId] = NULL,
            [ProcessingAction] = NULL
        WHERE [ImportSessionId] = @ImportSessionId;
        
        -- Step 1: Validate required fields
        UPDATE #DriverImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = 'Missing required fields: ' + 
                CASE WHEN [PersonFirstName] IS NULL OR [PersonFirstName] = '' THEN 'FirstName ' ELSE '' END +
                CASE WHEN [PersonLastName] IS NULL OR [PersonLastName] = '' THEN 'LastName ' ELSE '' END +
                CASE WHEN [CustomerName] IS NULL OR [CustomerName] = '' THEN 'CustomerName ' ELSE '' END +
                CASE WHEN [SiteName] IS NULL OR [SiteName] = '' THEN 'SiteName ' ELSE '' END +
                CASE WHEN [DepartmentName] IS NULL OR [DepartmentName] = '' THEN 'DepartmentName ' ELSE '' END
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND ([PersonFirstName] IS NULL OR [PersonFirstName] = '' OR
                 [PersonLastName] IS NULL OR [PersonLastName] = '' OR
                 [CustomerName] IS NULL OR [CustomerName] = '' OR
                 [SiteName] IS NULL OR [SiteName] = '' OR
                 [DepartmentName] IS NULL OR [DepartmentName] = '');
        
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' driver rows as invalid due to missing required fields');
        
        -- Step 2: Resolve Customer references
        UPDATE di
        SET [CustomerId] = c.[Id]
        FROM #DriverImport di
        INNER JOIN [dbo].[Customer] c ON LTRIM(RTRIM(di.[CustomerName])) = LTRIM(RTRIM(c.[CompanyName]))
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Customer references
        UPDATE #DriverImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Customer not found: ', [CustomerName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [CustomerId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' driver rows as invalid due to Customer lookup failures');
        
        -- Step 3: Resolve Site references
        UPDATE di
        SET [SiteId] = s.[Id]
        FROM #DriverImport di
        INNER JOIN [dbo].[Site] s ON LTRIM(RTRIM(di.[SiteName])) = LTRIM(RTRIM(s.[SiteName]))
            AND di.[CustomerId] = s.[CustomerId]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Site references
        UPDATE #DriverImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Site not found: ', [SiteName], ' for Customer: ', [CustomerName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [SiteId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' driver rows as invalid due to Site lookup failures');
        
        -- Step 4: Resolve Department references
        UPDATE di
        SET [DepartmentId] = d.[Id]
        FROM #DriverImport di
        INNER JOIN [dbo].[Department] d ON LTRIM(RTRIM(di.[DepartmentName])) = LTRIM(RTRIM(d.[DepartmentName]))
            AND di.[SiteId] = d.[SiteId]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Department references
        UPDATE #DriverImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Department not found: ', [DepartmentName], ' for Site: ', [SiteName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [DepartmentId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' driver rows as invalid due to Department lookup failures');
        
        -- Step 5: Check for existing Person records (by email if provided)
        UPDATE di
        SET [ExistingPersonId] = p.[Id]
        FROM #DriverImport di
        INNER JOIN [dbo].[Person] p ON LTRIM(RTRIM(di.[PersonEmail])) = LTRIM(RTRIM(p.[Email]))
            AND di.[CustomerId] = p.[CustomerId]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending'
            AND di.[PersonEmail] IS NOT NULL
            AND di.[PersonEmail] != '';
        
        -- Step 6: Check for existing Person records (by name + customer if no email match)
        UPDATE di
        SET [ExistingPersonId] = p.[Id]
        FROM #DriverImport di
        INNER JOIN [dbo].[Person] p ON LTRIM(RTRIM(di.[PersonFirstName])) = LTRIM(RTRIM(p.[FirstName]))
            AND LTRIM(RTRIM(di.[PersonLastName])) = LTRIM(RTRIM(p.[LastName]))
            AND di.[CustomerId] = p.[CustomerId]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending'
            AND di.[ExistingPersonId] IS NULL;
        
        -- Step 7: Check for existing Driver records linked to found Persons
        UPDATE di
        SET [ExistingDriverId] = p.[DriverId]
        FROM #DriverImport di
        INNER JOIN [dbo].[Person] p ON di.[ExistingPersonId] = p.[Id]
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Pending'
            AND di.[ExistingPersonId] IS NOT NULL
            AND p.[DriverId] IS NOT NULL;
        
        -- Step 8: Validate email format (basic validation)
        UPDATE #DriverImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Invalid email format: ', [PersonEmail])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [PersonEmail] IS NOT NULL
            AND [PersonEmail] != ''
            AND [PersonEmail] NOT LIKE '%_@_%._%';
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' driver rows as invalid due to email format issues');
        
        -- Step 9: Determine processing action and mark as valid
        UPDATE #DriverImport
        SET [ProcessingAction] = CASE 
            WHEN [ExistingPersonId] IS NOT NULL AND [ExistingDriverId] IS NOT NULL THEN 'Update'
            WHEN [ExistingPersonId] IS NOT NULL AND [ExistingDriverId] IS NULL THEN 'Insert'
            ELSE 'Insert'
        END,
        [ValidationStatus] = 'Valid'
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending';
        
        -- Final summary
        SELECT 
            [ValidationStatus],
            [ProcessingAction],
            COUNT(*) as [RowCount]
        FROM #DriverImport
        WHERE [ImportSessionId] = @ImportSessionId
        GROUP BY [ValidationStatus], [ProcessingAction]
        ORDER BY [ValidationStatus], [ProcessingAction];
        
        PRINT 'Driver import validation completed successfully for temp tables';
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Driver validation failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_ValidateVehicleImportTemp
-- Validates vehicle staging data in temp tables and resolves FK relationships
-- =============================================
IF OBJECT_ID('[dbo].[usp_ValidateVehicleImportTemp]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[usp_ValidateVehicleImportTemp]
GO

CREATE PROCEDURE [dbo].[usp_ValidateVehicleImportTemp]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @RowCount INT = 0;
    
    BEGIN TRY
        -- Validate that temp table exists
        IF NOT EXISTS (SELECT 1 FROM tempdb.sys.objects WHERE name LIKE '#VehicleImport%' AND type = 'U')
        BEGIN
            RAISERROR('Temp table #VehicleImport does not exist. Ensure CreateTempStagingTables was called first.', 16, 1);
            RETURN;
        END
        
        -- Reset validation status for this session
        UPDATE #VehicleImport
        SET [ValidationStatus] = 'Pending',
            [ValidationErrors] = NULL,
            [CustomerId] = NULL,
            [SiteId] = NULL,
            [DepartmentId] = NULL,
            [ModelId] = NULL,
            [ModuleId] = NULL,
            [AssignedDriverId] = NULL,
            [AssignedPersonId] = NULL,
            [ExistingVehicleId] = NULL,
            [ProcessingAction] = NULL
        WHERE [ImportSessionId] = @ImportSessionId;
        
        -- Step 1: Validate required fields
        UPDATE #VehicleImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = 'Missing required fields: ' + 
                CASE WHEN [HireNo] IS NULL OR [HireNo] = '' THEN 'HireNo ' ELSE '' END +
                CASE WHEN [SerialNo] IS NULL OR [SerialNo] = '' THEN 'SerialNo ' ELSE '' END +
                CASE WHEN [CustomerName] IS NULL OR [CustomerName] = '' THEN 'CustomerName ' ELSE '' END +
                CASE WHEN [SiteName] IS NULL OR [SiteName] = '' THEN 'SiteName ' ELSE '' END +
                CASE WHEN [DepartmentName] IS NULL OR [DepartmentName] = '' THEN 'DepartmentName ' ELSE '' END +
                CASE WHEN [ModelName] IS NULL OR [ModelName] = '' THEN 'ModelName ' ELSE '' END +
                CASE WHEN [ModuleSerialNumber] IS NULL OR [ModuleSerialNumber] = '' THEN 'ModuleSerialNumber ' ELSE '' END
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND ([HireNo] IS NULL OR [HireNo] = '' OR
                 [SerialNo] IS NULL OR [SerialNo] = '' OR
                 [CustomerName] IS NULL OR [CustomerName] = '' OR
                 [SiteName] IS NULL OR [SiteName] = '' OR
                 [DepartmentName] IS NULL OR [DepartmentName] = '' OR
                 [ModelName] IS NULL OR [ModelName] = '' OR
                 [ModuleSerialNumber] IS NULL OR [ModuleSerialNumber] = '');
        
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' vehicle rows as invalid due to missing required fields');
        
        -- Step 2: Resolve Customer references
        UPDATE vi
        SET [CustomerId] = c.[Id]
        FROM #VehicleImport vi
        INNER JOIN [dbo].[Customer] c ON LTRIM(RTRIM(vi.[CustomerName])) = LTRIM(RTRIM(c.[CompanyName]))
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Customer references
        UPDATE #VehicleImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Customer not found: ', [CustomerName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [CustomerId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' vehicle rows as invalid due to Customer lookup failures');
        
        -- Step 3: Resolve Site references
        UPDATE vi
        SET [SiteId] = s.[Id]
        FROM #VehicleImport vi
        INNER JOIN [dbo].[Site] s ON LTRIM(RTRIM(vi.[SiteName])) = LTRIM(RTRIM(s.[SiteName]))
            AND vi.[CustomerId] = s.[CustomerId]
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Site references
        UPDATE #VehicleImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Site not found: ', [SiteName], ' for Customer: ', [CustomerName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [SiteId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' vehicle rows as invalid due to Site lookup failures');
        
        -- Step 4: Resolve Department references
        UPDATE vi
        SET [DepartmentId] = d.[Id]
        FROM #VehicleImport vi
        INNER JOIN [dbo].[Department] d ON LTRIM(RTRIM(vi.[DepartmentName])) = LTRIM(RTRIM(d.[DepartmentName]))
            AND vi.[SiteId] = d.[SiteId]
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Department references
        UPDATE #VehicleImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Department not found: ', [DepartmentName], ' for Site: ', [SiteName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [DepartmentId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' vehicle rows as invalid due to Department lookup failures');
        
        -- Step 5: Resolve Model references
        UPDATE vi
        SET [ModelId] = m.[Id]
        FROM #VehicleImport vi
        INNER JOIN [dbo].[Model] m ON LTRIM(RTRIM(vi.[ModelName])) = LTRIM(RTRIM(m.[ModelName]))
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Model references
        UPDATE #VehicleImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Model not found: ', [ModelName])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [ModelId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' vehicle rows as invalid due to Model lookup failures');
        
        -- Step 6: Resolve Module references
        UPDATE vi
        SET [ModuleId] = m.[Id]
        FROM #VehicleImport vi
        INNER JOIN [dbo].[Module] m ON LTRIM(RTRIM(vi.[ModuleSerialNumber])) = LTRIM(RTRIM(m.[SerialNumber]))
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Mark rows with invalid Module references
        UPDATE #VehicleImport
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE([ValidationErrors] + '; ', ''), 'Module not found: ', [ModuleSerialNumber])
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending'
            AND [ModuleId] IS NULL;
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' vehicle rows as invalid due to Module lookup failures');
        
        -- Step 7: Check for existing Vehicle records (by HireNo)
        UPDATE vi
        SET [ExistingVehicleId] = v.[Id]
        FROM #VehicleImport vi
        INNER JOIN [dbo].[Vehicle] v ON LTRIM(RTRIM(vi.[HireNo])) = LTRIM(RTRIM(v.[HireNo]))
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Pending';
        
        -- Step 8: Check for duplicate HireNo within the import session
        UPDATE vi1
        SET [ValidationStatus] = 'Invalid',
            [ValidationErrors] = CONCAT(COALESCE(vi1.[ValidationErrors] + '; ', ''), 'Duplicate HireNo in import: ', vi1.[HireNo])
        FROM #VehicleImport vi1
        WHERE vi1.[ImportSessionId] = @ImportSessionId
            AND vi1.[ValidationStatus] = 'Pending'
            AND EXISTS (
                SELECT 1 FROM #VehicleImport vi2 
                WHERE vi2.[ImportSessionId] = @ImportSessionId 
                    AND vi2.[HireNo] = vi1.[HireNo] 
                    AND vi2.[Id] < vi1.[Id]
            );
            
        SET @RowCount = @@ROWCOUNT;
        IF @RowCount > 0
            PRINT CONCAT('Marked ', @RowCount, ' vehicle rows as invalid due to duplicate HireNo within import');
        
        -- Step 9: Determine processing action and mark as valid
        UPDATE #VehicleImport
        SET [ProcessingAction] = CASE 
            WHEN [ExistingVehicleId] IS NOT NULL THEN 'Update'
            ELSE 'Insert'
        END,
        [ValidationStatus] = 'Valid'
        WHERE [ImportSessionId] = @ImportSessionId
            AND [ValidationStatus] = 'Pending';
        
        -- Final summary
        SELECT 
            [ValidationStatus],
            [ProcessingAction],
            COUNT(*) as [RowCount]
        FROM #VehicleImport
        WHERE [ImportSessionId] = @ImportSessionId
        GROUP BY [ValidationStatus], [ProcessingAction]
        ORDER BY [ValidationStatus], [ProcessingAction];
        
        PRINT 'Vehicle import validation completed successfully for temp tables';
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Vehicle validation failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_ValidateAllTempData
-- Validates both driver and vehicle data in temp tables
-- =============================================
IF OBJECT_ID('[dbo].[usp_ValidateAllTempData]', 'P') IS NOT NULL
    DROP PROCEDURE [dbo].[usp_ValidateAllTempData]
GO

CREATE PROCEDURE [dbo].[usp_ValidateAllTempData]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        PRINT CONCAT('Starting validation for session: ', @ImportSessionId);
        
        -- Validate driver data if temp table exists
        IF EXISTS (SELECT 1 FROM tempdb.sys.objects WHERE name LIKE '#DriverImport%' AND type = 'U')
        BEGIN
            PRINT 'Validating driver data...';
            EXEC [dbo].[usp_ValidateDriverImportTemp] @ImportSessionId = @ImportSessionId;
        END
        ELSE
        BEGIN
            PRINT 'No driver temp table found, skipping driver validation';
        END
        
        -- Validate vehicle data if temp table exists
        IF EXISTS (SELECT 1 FROM tempdb.sys.objects WHERE name LIKE '#VehicleImport%' AND type = 'U')
        BEGIN
            PRINT 'Validating vehicle data...';
            EXEC [dbo].[usp_ValidateVehicleImportTemp] @ImportSessionId = @ImportSessionId;
        END
        ELSE
        BEGIN
            PRINT 'No vehicle temp table found, skipping vehicle validation';
        END
        
        -- Return combined validation summary
        SELECT 
            'Summary' as ResultType,
            COALESCE(d_valid.ValidCount, 0) as ValidDrivers,
            COALESCE(d_invalid.InvalidCount, 0) as InvalidDrivers,
            COALESCE(v_valid.ValidCount, 0) as ValidVehicles,
            COALESCE(v_invalid.InvalidCount, 0) as InvalidVehicles,
            COALESCE(d_valid.ValidCount, 0) + COALESCE(v_valid.ValidCount, 0) as TotalValid,
            COALESCE(d_invalid.InvalidCount, 0) + COALESCE(v_invalid.InvalidCount, 0) as TotalInvalid
        FROM 
            (SELECT 1 as anchor) a
        LEFT JOIN (
            SELECT COUNT(*) as ValidCount 
            FROM #DriverImport 
            WHERE [ImportSessionId] = @ImportSessionId AND [ValidationStatus] = 'Valid'
        ) d_valid ON 1=1
        LEFT JOIN (
            SELECT COUNT(*) as InvalidCount 
            FROM #DriverImport 
            WHERE [ImportSessionId] = @ImportSessionId AND [ValidationStatus] = 'Invalid'
        ) d_invalid ON 1=1
        LEFT JOIN (
            SELECT COUNT(*) as ValidCount 
            FROM #VehicleImport 
            WHERE [ImportSessionId] = @ImportSessionId AND [ValidationStatus] = 'Valid'
        ) v_valid ON 1=1
        LEFT JOIN (
            SELECT COUNT(*) as InvalidCount 
            FROM #VehicleImport 
            WHERE [ImportSessionId] = @ImportSessionId AND [ValidationStatus] = 'Invalid'
        ) v_invalid ON 1=1;
        
        PRINT 'All temp table validation completed successfully';
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(MAX) = CONCAT('Temp table validation failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

PRINT 'Created temp table validation procedures successfully'

