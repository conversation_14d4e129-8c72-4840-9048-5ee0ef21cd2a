using CsvHelper;
using CsvHelper.Configuration;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Comprehensive tests for all CSV models to ensure they parse correctly
    /// These tests validate the CSV parsing functionality for all import models
    /// </summary>
    public class AllCsvModelTests
    {
        [Fact]
        public void CardImportModel_ShouldParse_AllSampleData()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department Name,First Name,Last Name,Facility Code,Card Type,Card No,Reader Type,Weigand,Access Level
Test Dealer 1,Test Customer 1,Test Site 1,Test Department 1,John,Do<PERSON>,123,Standard,12345,RFID,26-bit,Level1
Test Dealer 2,Test Customer 2,Test Site 2,Test Department 2,Jane,<PERSON>,124,Premium,12346,RFID,26-bit,Level2
Test Dealer 3,Test Customer 3,Test Site 3,Test Department 3,<PERSON>,<PERSON>,125,Standard,12347,PIN,4-digit,Level1";

            // Act
            var results = ParseCsv<CardImportModel>(csvContent);

            // Assert
            Assert.Equal(3, results.Count);
            
            // Verify first record
            Assert.Equal("Test Customer 1", results[0].Customer);
            Assert.Equal("John", results[0].FirstName);
            Assert.Equal("12345", results[0].CardNo);
            
            // Verify second record
            Assert.Equal("Test Customer 2", results[1].Customer);
            Assert.Equal("Jane", results[1].FirstName);
            Assert.Equal("12346", results[1].CardNo);
        }

        [Fact]
        public void VehicleImportModel_ShouldParse_AllSampleData()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department Name,Device ID,Serial No,Hire No,Model Name,Impact Lockout,IsCanbus,On Hire,Timeout Enabled,Idle Timer,Canrule Name,Question Timeout,Show Comment,Randomisation,Full Lockout Timeout,VOR Status,Amber Alert Enabled,VOR Status Confirmed,Full Lockout,Default Technician Access,Pedestrian Safety,Checklist Type,Time slot1,Time slot2,Time slot3,Time slot4
Test Dealer 1,Test Customer 1,Test Site 1,Test Department 1,DEV001,SN001,V001,Model A,true,false,true,false,60,Rule1,30,false,false,120,false,false,false,false,false,false,Daily,08:00,12:00,16:00,20:00
Test Dealer 2,Test Customer 2,Test Site 2,Test Department 2,DEV002,SN002,V002,Model B,false,true,true,true,90,Rule2,45,true,true,180,true,true,true,true,true,true,Weekly,09:00,13:00,17:00,21:00";

            // Act
            var results = ParseCsv<VehicleImportModel>(csvContent);

            // Assert
            Assert.Equal(2, results.Count);
            
            // Verify first record
            Assert.Equal("Test Customer 1", results[0].Customer);
            Assert.Equal("V001", results[0].HireNo);
            Assert.Equal("Model A", results[0].ModelName);
            Assert.True(results[0].ImpactLockout);
            
            // Verify second record
            Assert.Equal("Test Customer 2", results[1].Customer);
            Assert.Equal("V002", results[1].HireNo);
            Assert.Equal("Model B", results[1].ModelName);
            Assert.False(results[1].ImpactLockout);
        }

        [Fact]
        public void PersonImportModel_ShouldParse_AllSampleData()
        {
            // Arrange
            var csvContent = @"Customer,Site,Department,First Name,Last Name,Send Deny Message,Website Access,IsDriver,IsSupervisor,VOR Activate/Deactivate,Normal Driver Access,CanUnlockVehicle
Test Customer 1,Test Site 1,Test Department 1,John,Doe,true,false,true,false,false,true,false
Test Customer 2,Test Site 2,Test Department 2,Jane,Smith,false,true,false,true,true,false,true
Test Customer 3,Test Site 3,Test Department 3,Bob,Johnson,true,false,true,false,true,true,false";

            // Act
            var results = ParseCsv<PersonImportModel>(csvContent);

            // Assert
            Assert.Equal(3, results.Count);
            
            // Verify first record
            Assert.Equal("Test Customer 1", results[0].Customer);
            Assert.Equal("John", results[0].FirstName);
            Assert.True(results[0].SendDenyMessage);
            Assert.True(results[0].IsDriver);
            
            // Verify boolean parsing
            Assert.False(results[0].WebsiteAccess);
            Assert.False(results[0].IsSupervisor);
        }

        [Fact]
        public void SupervisorAccessImportModel_ShouldParse_AllSampleData()
        {
            // Arrange
            var csvContent = @"Person Dealer,Person Customer,Person Site,Person Department,First Name,Last Name,Weigand,Hire No,Serial NO,GMTP ID,Supervisor Authorization
Test Dealer 1,Test Customer 1,Test Site 1,Test Department 1,John,Manager,123456,V001,SN001,GMTP001,true
Test Dealer 2,Test Customer 2,Test Site 2,Test Department 2,Jane,Supervisor,234567,V002,SN002,GMTP002,false";

            // Act
            var results = ParseCsv<SupervisorAccessImportModel>(csvContent);

            // Assert
            Assert.Equal(2, results.Count);
            
            // Verify first record
            Assert.Equal("Test Customer 1", results[0].PersonCustomer);
            Assert.Equal("John", results[0].FirstName);
            Assert.Equal("V001", results[0].HireNo);
            
            // Verify second record
            Assert.Equal("Test Customer 2", results[1].PersonCustomer);
            Assert.Equal("Jane", results[1].FirstName);
        }

        [Fact]
        public void DriverBlacklistImportModel_ShouldParse_AllSampleData()
        {
            // Arrange
            var csvContent = @"Person Dealer,Person Customer,Person Site,Person Department,First Name,Last Name,Weigand,Hire No,Serial NO,GMTP ID
Test Dealer 1,Test Customer 1,Test Site 1,Test Department 1,John,Doe,26-bit,V001,SN001,GMTP001
Test Dealer 2,Test Customer 2,Test Site 2,Test Department 2,Jane,Smith,26-bit,V002,SN002,GMTP002
Test Dealer 3,Test Customer 3,Test Site 3,Test Department 3,Bob,Johnson,26-bit,V003,SN003,GMTP003";

            // Act
            var results = ParseCsv<DriverBlacklistImportModel>(csvContent);

            // Assert
            Assert.Equal(3, results.Count);
            
            // Verify records
            Assert.Equal("Test Customer 1", results[0].PersonCustomer);
            Assert.Equal("John", results[0].FirstName);
            Assert.Equal("V001", results[0].HireNo);
        }

        [Fact]
        public void SpareModuleImportModel_ShouldParse_AllSampleData()
        {
            // Arrange
            var csvContent = @"Dealer,IoT Device ID,CCID,RA Number,Tech Number
Test Dealer 1,IOT001,1234567890,12345,67890
Test Dealer 2,IOT002,0987654321,54321,98765";

            // Act
            var results = ParseCsv<SpareModuleImportModel>(csvContent);

            // Assert
            Assert.Equal(2, results.Count);
            
            // Verify first record
            Assert.Equal("Test Dealer 1", results[0].Dealer);
            Assert.Equal("IOT001", results[0].IoTDeviceID);
            Assert.Equal(1234567890, results[0].CCID);
        }

        [Fact]
        public void WebsiteUserImportModel_ShouldParse_AllSampleData()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department Name,First Name,Last Name,Username,Email,Password,Preferred Locale,Website Access Level,Access Group,Role
Test Dealer 1,Test Customer 1,Test Site 1,Test Department 1,John,Doe,jdoe,<EMAIL>,password123,en-US,Admin,AdminGroup,Administrator
Test Dealer 2,Test Customer 2,Test Site 2,Test Department 2,Jane,Smith,jsmith,<EMAIL>,password456,en-US,User,UserGroup,User
Test Dealer 3,Test Customer 3,Test Site 3,Test Department 3,Bob,Johnson,bjohnson,<EMAIL>,password789,en-US,Manager,ManagerGroup,Manager";

            // Act
            var results = ParseCsv<WebsiteUserImportModel>(csvContent);

            // Assert
            Assert.Equal(3, results.Count);
            
            // Verify first record
            Assert.Equal("jdoe", results[0].Username);
            Assert.Equal("<EMAIL>", results[0].Email);
            Assert.Equal("Administrator", results[0].Role);
            Assert.Equal("Admin", results[0].WebsiteAccessLevel);
            
            // Verify second record  
            Assert.Equal("jsmith", results[1].Username);
            Assert.Equal("User", results[1].WebsiteAccessLevel);
        }

        [Fact]
        public void PreOpChecklistImportModel_ShouldParse_AllSampleData()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department,Model,Question,Expected Answer,Critical,ExcludeFromRandom,Sort Order
Test Dealer 1,Test Customer 1,Test Site 1,Test Department 1,Forklift,Is the seatbelt fastened?,true,true,false,1
Test Dealer 2,Test Customer 2,Test Site 2,Test Department 2,Forklift,Is the engine oil level adequate?,false,false,true,2
Test Dealer 3,Test Customer 3,Test Site 3,Test Department 3,Forklift,Are all lights working?,true,true,false,3";

            // Act
            var results = ParseCsv<PreOpChecklistImportModel>(csvContent);

            // Assert
            Assert.Equal(3, results.Count);
            
            // Verify first record
            Assert.Equal("Test Customer 1", results[0].Customer);
            Assert.Equal("Forklift", results[0].Model);
            Assert.Equal("Is the seatbelt fastened?", results[0].Question);
            Assert.True(results[0].ExpectedAnswer);
            Assert.True(results[0].Critical);
            
            // Verify boolean parsing differences
            Assert.False(results[1].ExpectedAnswer);
            Assert.False(results[1].Critical);
        }

        [Theory]
        [InlineData("true", true)]
        [InlineData("false", false)]
        [InlineData("True", true)]
        [InlineData("False", false)]
        [InlineData("TRUE", true)]
        [InlineData("FALSE", false)]
        [InlineData("1", true)]
        [InlineData("0", false)]
        public void AllModels_ShouldParseBooleans_Correctly(string boolValue, bool expected)
        {
            // Test boolean parsing across different models
            var vehicleCsv = $@"Dealer,Customer,Site,Department Name,Device ID,Serial No,Hire No,Model Name,Impact Lockout,IsCanbus,On Hire,Timeout Enabled,Idle Timer,Canrule Name,Question Timeout,Show Comment,Randomisation,Full Lockout Timeout,VOR Status,Amber Alert Enabled,VOR Status Confirmed,Full Lockout,Default Technician Access,Pedestrian Safety,Checklist Type,Time slot1,Time slot2,Time slot3,Time slot4
TestDealer,TestCustomer,TestSite,TestDept,DEV001,SN001,V001,TestModel,{boolValue},false,true,false,60,Rule1,30,false,false,120,false,false,false,false,false,false,Daily,08:00,12:00,16:00,20:00";

            var vehicleResults = ParseCsv<VehicleImportModel>(vehicleCsv);
            Assert.Equal(expected, vehicleResults[0].ImpactLockout);

            var personCsv = $@"Customer,Site,Department,First Name,Last Name,Send Deny Message,Website Access,IsDriver,IsSupervisor,VOR Activate/Deactivate,Normal Driver Access,CanUnlockVehicle
TestCustomer,TestSite,TestDept,John,Doe,{boolValue},false,true,false,false,true,false";

            var personResults = ParseCsv<PersonImportModel>(personCsv);
            Assert.Equal(expected, personResults[0].SendDenyMessage);
        }

        [Fact]
        public void CsvParsing_ShouldHandleEmptyFields_Gracefully()
        {
            // Test with some empty/null fields
            var csvContent = @"Customer,Site,Department,First Name,Last Name,Send Deny Message,Website Access,IsDriver,IsSupervisor,VOR Activate/Deactivate,Normal Driver Access,CanUnlockVehicle
TestCustomer,,TestDept,John,,true,false,true,false,false,true,false";

            var results = ParseCsv<PersonImportModel>(csvContent);
            
            Assert.Single(results);
            Assert.Equal("TestCustomer", results[0].Customer);
            Assert.Equal("", results[0].Site); // Empty string for missing field
            Assert.Equal("John", results[0].FirstName);
            Assert.Equal("", results[0].LastName); // Empty string for missing field
        }

        [Fact]
        public void CsvParsing_ShouldHandleSpecialCharacters_InFields()
        {
            // Test with special characters that might cause issues
            var csvContent = @"Customer,Site,Department,First Name,Last Name,Send Deny Message,Website Access,IsDriver,IsSupervisor,VOR Activate/Deactivate,Normal Driver Access,CanUnlockVehicle
""Customer & Co., Ltd"",""Site #1 (Main)"",""IT/Development"",""Jean-Pierre"",""O'Connor"",true,false,true,false,false,true,false";

            var results = ParseCsv<PersonImportModel>(csvContent);
            
            Assert.Single(results);
            Assert.Equal("Customer & Co., Ltd", results[0].Customer);
            Assert.Equal("Site #1 (Main)", results[0].Site);
            Assert.Equal("IT/Development", results[0].Department);
            Assert.Equal("Jean-Pierre", results[0].FirstName);
            Assert.Equal("O'Connor", results[0].LastName);
        }

        private List<T> ParseCsv<T>(string csvContent)
        {
            using var reader = new StringReader(csvContent);
            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                MissingFieldFound = null, // Don't throw on missing fields
                HeaderValidated = null,   // Don't throw on header validation
                BadDataFound = null       // Don't throw on bad data
            };
            using var csv = new CsvReader(reader, config);
            
            try
            {
                return csv.GetRecords<T>().ToList();
            }
            catch (Exception ex)
            {
                // Log the exception for debugging
                Console.WriteLine($"CSV parsing failed: {ex.Message}");
                throw;
            }
        }
    }
} 