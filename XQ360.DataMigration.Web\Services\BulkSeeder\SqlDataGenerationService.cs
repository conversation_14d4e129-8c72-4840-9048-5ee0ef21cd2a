using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// SQL-based data generation service integrated with existing migration infrastructure
/// </summary>
public class SqlDataGenerationService : ISqlDataGenerationService
{
    private readonly ILogger<SqlDataGenerationService> _logger;
    private readonly BulkSeederConfiguration _options;
    private readonly IEnvironmentConfigurationService _environmentService;

    public SqlDataGenerationService(
        ILogger<SqlDataGenerationService> logger,
        IOptions<BulkSeederConfiguration> options,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    public async Task<Guid> CreateSeederSessionAsync(string sessionName, CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();

        using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
        await connection.OpenAsync(cancellationToken);

        const string sql = @"
            IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Staging')
            BEGIN
                EXEC('CREATE SCHEMA [Staging]')
            END

            IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Staging].[SeederSession]') AND type in (N'U'))
            BEGIN
                CREATE TABLE [Staging].[SeederSession] (
                    [Id] UNIQUEIDENTIFIER PRIMARY KEY,
                    [SessionName] NVARCHAR(255) NOT NULL,
                    [StartTime] DATETIME2 NOT NULL,
                    [EndTime] DATETIME2 NULL,
                    [Status] NVARCHAR(50) NOT NULL,
                    [TotalRows] INT NOT NULL DEFAULT 0,
                    [SuccessfulRows] INT NOT NULL DEFAULT 0,
                    [FailedRows] INT NOT NULL DEFAULT 0,
                    [CreatedBy] NVARCHAR(100) NOT NULL,
                    [Environment] NVARCHAR(50) NOT NULL
                )
            END

            INSERT INTO [Staging].[SeederSession] 
            ([Id], [SessionName], [StartTime], [Status], [CreatedBy], [Environment])
            VALUES (@SessionId, @SessionName, @StartTime, @Status, @CreatedBy, @Environment)";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@SessionName", sessionName);
        command.Parameters.AddWithValue("@StartTime", DateTime.UtcNow);
        command.Parameters.AddWithValue("@Status", "Running");
        command.Parameters.AddWithValue("@CreatedBy", Environment.UserName);
        command.Parameters.AddWithValue("@Environment", _environmentService.CurrentEnvironmentKey);

        await command.ExecuteNonQueryAsync(cancellationToken);

        _logger.LogInformation("Created seeding session {SessionId} with name '{SessionName}' in environment {Environment}", 
            sessionId, sessionName, _environmentService.CurrentEnvironmentKey);
        return sessionId;
    }

    public async Task<DataGenerationResult> GenerateDriverDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new DataGenerationResult();

        try
        {
            _logger.LogInformation("Generating {Count} driver records for session {SessionId}", count, sessionId);

            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Create temporary staging table for drivers if it doesn't exist
            await CreateDriverStagingTableAsync(connection, cancellationToken);

            // Generate driver data in batches using SQL
            var batchSize = _options.TempTableBatchSize;
            var totalGenerated = 0;

            for (int offset = 0; offset < count; offset += batchSize)
            {
                var currentBatchSize = Math.Min(batchSize, count - offset);
                await GenerateDriverBatchAsync(connection, sessionId, offset, currentBatchSize, cancellationToken);
                totalGenerated += currentBatchSize;

                _logger.LogInformation("Generated {CurrentGenerated}/{TotalCount} driver records", totalGenerated, count);
            }

            result.Success = true;
            result.GeneratedRows = totalGenerated;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Successfully generated {totalGenerated} driver records";

            _logger.LogInformation("Driver data generation completed for session {SessionId}. Generated {Count} records in {Duration}",
                sessionId, totalGenerated, result.Duration);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Driver data generation failed: {ex.Message}";

            _logger.LogError(ex, "Driver data generation failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task<DataGenerationResult> GenerateVehicleDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new DataGenerationResult();

        try
        {
            _logger.LogInformation("Generating {Count} vehicle records for session {SessionId}", count, sessionId);

            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Create temporary staging table for vehicles if it doesn't exist
            await CreateVehicleStagingTableAsync(connection, cancellationToken);

            // Generate vehicle data in batches using SQL
            var batchSize = _options.TempTableBatchSize;
            var totalGenerated = 0;

            for (int offset = 0; offset < count; offset += batchSize)
            {
                var currentBatchSize = Math.Min(batchSize, count - offset);
                await GenerateVehicleBatchAsync(connection, sessionId, offset, currentBatchSize, cancellationToken);
                totalGenerated += currentBatchSize;

                _logger.LogInformation("Generated {CurrentGenerated}/{TotalCount} vehicle records", totalGenerated, count);
            }

            result.Success = true;
            result.GeneratedRows = totalGenerated;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Successfully generated {totalGenerated} vehicle records";

            _logger.LogInformation("Vehicle data generation completed for session {SessionId}. Generated {Count} records in {Duration}",
                sessionId, totalGenerated, result.Duration);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Vehicle data generation failed: {ex.Message}";

            _logger.LogError(ex, "Vehicle data generation failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task UpdateSeederSessionAsync(Guid sessionId, string status, int totalRows, int successfulRows, int failedRows, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                UPDATE [Staging].[SeederSession] 
                SET [Status] = @Status,
                    [EndTime] = @EndTime,
                    [TotalRows] = @TotalRows,
                    [SuccessfulRows] = @SuccessfulRows,
                    [FailedRows] = @FailedRows
                WHERE [Id] = @SessionId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);
            command.Parameters.AddWithValue("@Status", status);
            command.Parameters.AddWithValue("@EndTime", DateTime.UtcNow);
            command.Parameters.AddWithValue("@TotalRows", totalRows);
            command.Parameters.AddWithValue("@SuccessfulRows", successfulRows);
            command.Parameters.AddWithValue("@FailedRows", failedRows);

            await command.ExecuteNonQueryAsync(cancellationToken);

            _logger.LogInformation("Updated seeding session {SessionId} status to {Status}", sessionId, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update seeding session {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<ValidationResult> ValidateStagedDataAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult();

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Perform basic validation checks on staged data
            const string validationSql = @"
                DECLARE @DriverCount INT = 0, @VehicleCount INT = 0, @InvalidDrivers INT = 0, @InvalidVehicles INT = 0

                -- Count staged drivers
                IF OBJECT_ID('[Staging].[DriverStaging]') IS NOT NULL
                BEGIN
                    SELECT @DriverCount = COUNT(*) FROM [Staging].[DriverStaging] WHERE [SessionId] = @SessionId
                    SELECT @InvalidDrivers = COUNT(*) FROM [Staging].[DriverStaging] 
                    WHERE [SessionId] = @SessionId AND ([FirstName] IS NULL OR [LastName] IS NULL OR [Email] IS NULL)
                END

                -- Count staged vehicles  
                IF OBJECT_ID('[Staging].[VehicleStaging]') IS NOT NULL
                BEGIN
                    SELECT @VehicleCount = COUNT(*) FROM [Staging].[VehicleStaging] WHERE [SessionId] = @SessionId
                    SELECT @InvalidVehicles = COUNT(*) FROM [Staging].[VehicleStaging] 
                    WHERE [SessionId] = @SessionId AND ([VehicleName] IS NULL OR [DeviceId] IS NULL)
                END

                SELECT 
                    @DriverCount AS DriverCount, 
                    @VehicleCount AS VehicleCount,
                    @InvalidDrivers AS InvalidDrivers,
                    @InvalidVehicles AS InvalidVehicles";

            using var command = new SqlCommand(validationSql, connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync())
            {
                var driverCount = reader.GetInt32("DriverCount");
                var vehicleCount = reader.GetInt32("VehicleCount");
                var invalidDrivers = reader.GetInt32("InvalidDrivers");
                var invalidVehicles = reader.GetInt32("InvalidVehicles");

                result.ValidRows = (driverCount - invalidDrivers) + (vehicleCount - invalidVehicles);
                result.InvalidRows = invalidDrivers + invalidVehicles;

                if (invalidDrivers > 0)
                {
                    result.ValidationErrors.Add($"{invalidDrivers} drivers have missing required fields");
                }
                
                if (invalidVehicles > 0)
                {
                    result.ValidationErrors.Add($"{invalidVehicles} vehicles have missing required fields");
                }

                result.Success = result.InvalidRows == 0;
                result.Summary = result.Success ? 
                    $"Validation passed: {result.ValidRows} valid records" :
                    $"Validation failed: {result.InvalidRows} invalid records found";
            }

            _logger.LogInformation("Validation completed for session {SessionId}: {ValidRows} valid, {InvalidRows} invalid", 
                sessionId, result.ValidRows, result.InvalidRows);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ValidationErrors.Add(ex.Message);
            result.Summary = $"Validation failed: {ex.Message}";
            _logger.LogError(ex, "Validation failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task<ProcessingResult> ProcessStagedDataAsync(Guid sessionId, bool dryRun = false, CancellationToken cancellationToken = default)
    {
        var result = new ProcessingResult();

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            if (dryRun)
            {
                // For dry run, just count what would be processed
                result.ProcessedRows = await CountStagedRecordsAsync(connection, sessionId, cancellationToken);
                result.Success = true;
                result.Summary = $"Dry run: {result.ProcessedRows} records would be processed";
            }
            else
            {
                // Process actual data (implementation would depend on specific business requirements)
                // For now, this is a placeholder that marks the staging data as processed
                await MarkStagingDataAsProcessedAsync(connection, sessionId, cancellationToken);
                
                result.ProcessedRows = await CountStagedRecordsAsync(connection, sessionId, cancellationToken);
                result.InsertedRows = result.ProcessedRows; // Simplified - all records are "inserted"
                result.Success = true;
                result.Summary = $"Processing completed: {result.InsertedRows} records processed";
            }

            _logger.LogInformation("Processing completed for session {SessionId}: {ProcessedRows} records", 
                sessionId, result.ProcessedRows);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ProcessingErrors.Add(ex.Message);
            result.Summary = $"Processing failed: {ex.Message}";
            _logger.LogError(ex, "Processing failed for session {SessionId}", sessionId);
        }

        return result;
    }

    private async Task CreateDriverStagingTableAsync(SqlConnection connection, CancellationToken cancellationToken)
    {
        const string sql = @"
            IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Staging].[DriverStaging]') AND type in (N'U'))
            BEGIN
                CREATE TABLE [Staging].[DriverStaging] (
                    [Id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                    [SessionId] UNIQUEIDENTIFIER NOT NULL,
                    [FirstName] NVARCHAR(100) NOT NULL,
                    [LastName] NVARCHAR(100) NOT NULL,
                    [Email] NVARCHAR(255) NOT NULL,
                    [Phone] NVARCHAR(50),
                    [EmployeeId] NVARCHAR(50),
                    [Department] NVARCHAR(100),
                    [CreatedAt] DATETIME2 DEFAULT GETUTCDATE()
                )
            END";

        using var command = new SqlCommand(sql, connection);
        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task CreateVehicleStagingTableAsync(SqlConnection connection, CancellationToken cancellationToken)
    {
        const string sql = @"
            IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Staging].[VehicleStaging]') AND type in (N'U'))
            BEGIN
                CREATE TABLE [Staging].[VehicleStaging] (
                    [Id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
                    [SessionId] UNIQUEIDENTIFIER NOT NULL,
                    [VehicleName] NVARCHAR(100) NOT NULL,
                    [DeviceId] NVARCHAR(50) NOT NULL,
                    [LicensePlate] NVARCHAR(20),
                    [Make] NVARCHAR(50),
                    [Model] NVARCHAR(50),
                    [Year] INT,
                    [VIN] NVARCHAR(50),
                    [CreatedAt] DATETIME2 DEFAULT GETUTCDATE()
                )
            END";

        using var command = new SqlCommand(sql, connection);
        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task GenerateDriverBatchAsync(SqlConnection connection, Guid sessionId, int offset, int batchSize, CancellationToken cancellationToken)
    {
        const string sql = @"
            INSERT INTO [Staging].[DriverStaging] ([SessionId], [FirstName], [LastName], [Email], [Phone], [EmployeeId], [Department])
            SELECT 
                @SessionId,
                'Driver' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(10)),
                'LastName' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(10)),
                'driver' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(10)) + '@example.com',
                '******-' + RIGHT('0000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(4)), 4),
                'EMP' + RIGHT('0000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(4)), 4),
                'Department' + CAST(((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) % 5 + 1) AS NVARCHAR(2))
            FROM (SELECT TOP (@BatchSize) 1 AS dummy FROM sys.objects o1 CROSS JOIN sys.objects o2) AS numbers";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@Offset", offset);
        command.Parameters.AddWithValue("@BatchSize", batchSize);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task GenerateVehicleBatchAsync(SqlConnection connection, Guid sessionId, int offset, int batchSize, CancellationToken cancellationToken)
    {
        const string sql = @"
            INSERT INTO [Staging].[VehicleStaging] ([SessionId], [VehicleName], [DeviceId], [LicensePlate], [Make], [Model], [Year], [VIN])
            SELECT 
                @SessionId,
                'Vehicle-' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(10)),
                'DEV' + RIGHT('0000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(4)), 4),
                'ABC' + RIGHT('000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(3)), 3),
                CASE ((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) % 4)
                    WHEN 0 THEN 'Ford' WHEN 1 THEN 'Chevrolet' WHEN 2 THEN 'Toyota' ELSE 'Honda' END,
                CASE ((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) % 4)
                    WHEN 0 THEN 'F-150' WHEN 1 THEN 'Silverado' WHEN 2 THEN 'Camry' ELSE 'Civic' END,
                2020 + ((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) % 5),
                'VIN' + RIGHT('00000000000000' + CAST((ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset) AS NVARCHAR(14)), 14)
            FROM (SELECT TOP (@BatchSize) 1 AS dummy FROM sys.objects o1 CROSS JOIN sys.objects o2) AS numbers";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@Offset", offset);
        command.Parameters.AddWithValue("@BatchSize", batchSize);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task<int> CountStagedRecordsAsync(SqlConnection connection, Guid sessionId, CancellationToken cancellationToken)
    {
        const string sql = @"
            DECLARE @TotalCount INT = 0

            IF OBJECT_ID('[Staging].[DriverStaging]') IS NOT NULL
                SELECT @TotalCount = @TotalCount + COUNT(*) FROM [Staging].[DriverStaging] WHERE [SessionId] = @SessionId

            IF OBJECT_ID('[Staging].[VehicleStaging]') IS NOT NULL
                SELECT @TotalCount = @TotalCount + COUNT(*) FROM [Staging].[VehicleStaging] WHERE [SessionId] = @SessionId

            SELECT @TotalCount";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);

        var result = await command.ExecuteScalarAsync(cancellationToken);
        return Convert.ToInt32(result ?? 0);
    }

    private async Task MarkStagingDataAsProcessedAsync(SqlConnection connection, Guid sessionId, CancellationToken cancellationToken)
    {
        // This is a placeholder implementation
        // In a real scenario, this would move/copy data to production tables
        const string sql = @"
            -- Mark driver records as processed
            IF OBJECT_ID('[Staging].[DriverStaging]') IS NOT NULL
                UPDATE [Staging].[DriverStaging] 
                SET [CreatedAt] = GETUTCDATE() 
                WHERE [SessionId] = @SessionId

            -- Mark vehicle records as processed  
            IF OBJECT_ID('[Staging].[VehicleStaging]') IS NOT NULL
                UPDATE [Staging].[VehicleStaging] 
                SET [CreatedAt] = GETUTCDATE() 
                WHERE [SessionId] = @SessionId";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }
}
