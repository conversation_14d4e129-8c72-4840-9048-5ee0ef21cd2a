# 📋 QA Note: XQ360 Data Migration Web Application

**Quality Assurance Testing Guide for Data Migration and Verification**

---

## 📖 Table of Contents

1. [Overview & Prerequisites](#-overview--prerequisites)
2. [CSV File Preparation & Validation](#-csv-file-preparation--validation)
3. [Web Application Testing Procedures](#-web-application-testing-procedures)
4. [Data Migration Execution](#-data-migration-execution)
5. [FleetXQ Data Verification](#-fleetxq-data-verification)
6. [Post-Migration Validation](#-post-migration-validation)
7. [Troubleshooting & Common Issues](#-troubleshooting--common-issues)
8. [Quality Gates & Sign-off Criteria](#-quality-gates--sign-off-criteria)

---

## 🎯 Overview & Prerequisites

### Application Purpose
The XQ360 Data Migration web application imports organizational data from CSV files into the FleetXQ system, supporting:
- **Personnel Data**: Staff, drivers, supervisors
- **Vehicle Fleet**: Equipment, devices, configurations  
- **Access Control**: Cards, permissions, restrictions
- **Safety Systems**: Checklists, blacklists, supervisor access
- **IoT Integration**: Vehicle device synchronization

### QA Testing Scope
This QA note covers:
✅ **Pre-Migration**: Environment setup, CSV validation, system readiness  
✅ **Migration Process**: Web application functionality, error handling, progress tracking  
✅ **Post-Migration**: Data verification in FleetXQ, report analysis, system integration  
✅ **Edge Cases**: Failed records, retries, partial migrations, rollback scenarios

### Prerequisites for QA Testing
- [ ] **IMPORTANT: Use Development Environment Only** - Always select "Development" environment for all QA testing
- [ ] Test dataset with known values for verification
- [ ] Administrative access to FleetXQ system
- [ ] Understanding of organizational hierarchy (Customer → Site → Department)
- [ ] Network connectivity for IoT device synchronization testing

---

## 📄 CSV File Preparation & Validation

### Required CSV Files & Templates

| Migration Type | CSV File Name | Key Fields to Validate |
|----------------|---------------|------------------------|
| **Personnel** | `PERSON_IMPORT_TEMPLATE.csv` | Customer, Site, Department, First Name, Last Name |
| **Vehicles** | `VEHICLE_IMPORT.csv` | Device ID, Serial No, Hire No, Model Name |
| **Access Cards** | `CARD_IMPORT.csv` | First Name, Last Name, Card No, Weigand |
| **Safety Checklists** | `PREOP_CHECKLIST_IMPORT.csv` | Question text, required responses |
| **Spare Parts** | `SPARE_MODEL_IMPORT_TEMPLATE.csv` | Model specifications |
| **Supervisor Access** | `SUPERVISOR_ACCESS_IMPORT.csv` | Personnel with elevated permissions |
| **Driver Blacklist** | `DRIVER_BLACKLIST_IMPORT.csv` | Restricted driver records |
| **Website Users** | `WEBSITE_USER_IMPORT.csv` | Login credentials and roles |

### CSV Validation Testing

**1. Header Validation**
- [ ] **Required Headers**: All template headers must be present
- [ ] **Extra Headers**: Application should warn but continue with extra columns
- [ ] **Header Order**: Different order should work with warnings
- [ ] **Case Sensitivity**: Headers should be case-insensitive

**2. Data Format Validation**
- [ ] **Boolean Fields**: Accept `true/false`, `TRUE/FALSE`, reject `yes/no`
- [ ] **Required Fields**: Empty required fields should cause validation errors
- [ ] **Special Characters**: Test names with apostrophes, hyphens, spaces
- [ ] **Encoding**: Test UTF-8 encoding with international characters

**3. Data Consistency Testing**
- [ ] **Duplicate Detection**: Same Weigand numbers, Device IDs should be flagged
- [ ] **Cross-File References**: Person names in CARD_IMPORT must exist in PERSON_IMPORT
- [ ] **Hierarchy Validation**: Customer/Site/Department combinations must be valid

### Sample Test Data Sets

**QA Test Dataset 1: Valid Complete Migration**
```csv
# PERSON_IMPORT_TEMPLATE.csv
Customer,Site,Department,First Name,Last Name,Send Deny Message,Website Access,IsDriver,IsSupervisor,VOR Activate/Deactivate,Normal Driver Access,CanUnlockVehicle
QA Test Corp,QA Site Alpha,QA Warehouse,John,Smith,true,true,true,false,true,true,true
QA Test Corp,QA Site Alpha,QA Warehouse,Jane,Doe,true,false,true,true,true,true,false

# VEHICLE_IMPORT.csv  
Dealer,Customer,Site,Department Name,Device ID,Serial No,Hire No,Model Name,Impact Lockout,IsCanbus,On Hire,Timeout Enabled,Idle Timer,Question Timeout,Show Comment,Randomisation,VOR Status,Full Lockout,Default Technician Access,Pedestrian Safety,Checklist Type
QA Dealer,QA Test Corp,QA Site Alpha,QA Warehouse,QA_DEV_001,QA_SN001,QA_H001,Test Forklift,true,false,true,true,300,60,true,false,false,false,true,true,Daily

# CARD_IMPORT.csv
Dealer,Customer,Site,Department Name,First Name,Last Name,Facility Code,Card Type,Card No,Reader Type,Weigand,Access Level
QA Dealer,QA Test Corp,QA Site Alpha,QA Warehouse,John,Smith,001,1,QA12345,1,**********,Department
```

**QA Test Dataset 2: Error Scenarios**
- Missing required fields (empty First Name)
- Duplicate Weigand numbers
- Invalid boolean values (using "yes/no")
- Non-existent person references in card data
- Invalid department hierarchies

---

## 🖥️ Web Application Testing Procedures

### 1. User Interface Testing

**Navigation & Layout**
- [ ] Application loads without errors
- [ ] **IMPORTANT: Select "Development" environment from dropdown**
- [ ] Migration type checkboxes function correctly
- [ ] File upload areas respond to drag-and-drop
- [ ] Progress indicators and real-time updates work
- [ ] Mobile responsiveness (if applicable)

**Form Validation**
- [ ] **Environment selection**: Always select "Development" for QA testing
- [ ] At least one migration type must be selected
- [ ] CSV file uploads are validated before submission
- [ ] Error messages are clear and actionable

### 2. File Upload Testing

**Upload Functionality**
- [ ] **Single File Upload**: Each migration type accepts correct CSV
- [ ] **Multiple File Upload**: Select all migration types and upload corresponding files
- [ ] **File Size Limits**: Test with large CSV files (1000+ records)
- [ ] **File Format**: Only CSV files should be accepted
- [ ] **Invalid Files**: Excel (.xlsx) files should be rejected with clear error
- [ ] **Empty Files**: Empty CSV files should be rejected with clear error
- [ ] **Real-time Validation**: File validation messages appear immediately on file selection

**Upload Validation**
- [ ] **Immediate Validation**: File headers validated on upload
- [ ] **Error Display**: Validation errors shown clearly in UI with red alert boxes
- [ ] **Warning Handling**: Warnings displayed but migration continues
- [ ] **File Replacement**: Re-uploading should replace previous file
- [ ] **File Extension Validation**: Non-CSV files show error and clear input
- [ ] **Empty File Validation**: Empty files show error and clear input
- [ ] **Success Messages**: Valid files show green success message
- [ ] **Form Submission Blocking**: Form won't submit if validation errors exist

### 3. File Validation Testing

**Test File Extension Validation:**
- [ ] **Upload Excel file (.xlsx)**: Should show error "❌ Error: filename.xlsx is not a CSV file"
- [ ] **Upload Word document (.docx)**: Should show error and clear input
- [ ] **Upload PDF file (.pdf)**: Should show error and clear input
- [ ] **Upload text file (.txt)**: Should show error and clear input

**Test Empty File Validation:**
- [ ] **Upload empty CSV file**: Should show error "❌ Error: filename.csv is empty"
- [ ] **Upload 0-byte file**: Should show error and clear input
- [ ] **Upload file with only headers**: Should be accepted (not empty)

**Test Valid File Validation:**
- [ ] **Upload valid CSV file**: Should show green success message "✅ filename.csv selected successfully"
- [ ] **Upload large CSV file (>10MB)**: Should show warning about file size
- [ ] **Re-upload same file**: Should replace previous validation message

**Test Form Submission with Validation Errors:**
- [ ] **Try to submit with invalid file**: Form should not submit, show alert
- [ ] **Try to submit with empty file**: Form should not submit, show alert
- [ ] **Fix validation errors and submit**: Form should submit successfully

### 4. Migration Execution Testing

**Start Migration Process**
- [ ] **Environment Confirmation**: Ensure "Development" environment is selected
- [ ] **Prerequisites Check**: Click "Validate Prerequisites" - all checks pass
- [ ] **Migration Initiation**: "Start Migration" button launches process
- [ ] **Progress Tracking**: Real-time progress updates via SignalR
- [ ] **Cancellation**: Ability to stop migration if needed (if implemented)

**Real-time Updates**
- [ ] **Live Progress**: Percentage completion updates
- [ ] **Step Messages**: Current migration step displayed
- [ ] **Success/Failure**: Real-time indication of record processing
- [ ] **Error Reporting**: Failed records shown immediately

---

## 🔄 Data Migration Execution

### Migration Sequence Testing

**Verify Correct Processing Order:**
1. [ ] **Spare Parts/Models** → Creates equipment types first
2. [ ] **Safety Checklists** → Sets up safety questions
3. [ ] **Vehicles** → Adds vehicles to system
4. [ ] **Personnel** → Creates driver and staff accounts
5. [ ] **Access Cards** → Links cards to people and vehicles
6. [ ] **Supervisor Access** → Grants special permissions
7. [ ] **Driver Blacklist** → Removes access for restricted drivers
8. [ ] **Website Users** → Creates login accounts
9. [ ] **Vehicle Sync** → Synchronizes IoT device configurations

**Dependency Testing**
- [ ] **Cards without People**: Should fail with clear error message
- [ ] **Vehicle Access without Vehicles**: Should fail gracefully
- [ ] **Supervisor Access without Personnel**: Should fail with guidance

### Error Handling & Recovery

**Partial Failure Scenarios**
- [ ] **Single Record Failure**: Other records continue processing
- [ ] **Network Interruption**: Migration handles API connectivity issues
- [ ] **Database Timeout**: Retry mechanisms function correctly
- [ ] **Invalid Data**: Clear error messages with fix instructions

**Retry & Recovery**
- [ ] **Re-run Migration**: Failed records can be retried after CSV fixes
- [ ] **Duplicate Prevention**: Re-running doesn't create duplicates
- [ ] **Incremental Import**: New records added while existing ones skipped

### Performance Testing

**Load Testing**
- [ ] **Small Dataset**: 10-50 records per type (baseline)
- [ ] **Medium Dataset**: 100-500 records per type
- [ ] **Large Dataset**: 1000+ records per type
- [ ] **Memory Usage**: Monitor application memory during large imports
- [ ] **Processing Time**: Document execution times for benchmarking

---

## 🔍 FleetXQ Data Verification

### Post-Migration Data Validation in FleetXQ

**1. Personnel Data Verification**
Navigate to FleetXQ → People Management

**Check Personnel Records:**
- [ ] **Person Creation**: All personnel from CSV exist in system
- [ ] **Name Accuracy**: First Name and Last Name match CSV exactly
- [ ] **Department Assignment**: Personnel assigned to correct Customer/Site/Department
- [ ] **Driver Status**: IsDriver flag correctly set (can access vehicle controls)
- [ ] **Supervisor Status**: IsSupervisor flag enables override capabilities
- [ ] **Website Access**: Website login permissions match CSV settings

**Verification Queries:**
```sql
-- Verify personnel count matches CSV
SELECT COUNT(*) FROM People WHERE Customer = 'QA Test Corp'

-- Check driver permissions
SELECT FirstName, LastName, IsDriver, IsSupervisor 
FROM People 
WHERE Customer = 'QA Test Corp' AND Site = 'QA Site Alpha'

-- Verify department assignments
SELECT FirstName, LastName, Department 
FROM People 
WHERE Customer = 'QA Test Corp'
```

**2. Vehicle Data Verification**
Navigate to FleetXQ → Fleet Management

**Check Vehicle Records:**
- [ ] **Vehicle Creation**: All vehicles from CSV exist in fleet
- [ ] **Device ID Mapping**: Device IDs match exactly (critical for IoT)
- [ ] **Serial Numbers**: Serial No and Hire No correctly recorded
- [ ] **Model Information**: Model Name and specifications accurate
- [ ] **Safety Settings**: Impact Lockout, Timeout settings applied
- [ ] **Checklist Assignment**: Daily/Custom checklists assigned correctly

**Vehicle Configuration Verification:**
- [ ] **Idle Timer**: Timeout values match CSV (in seconds)
- [ ] **Question Timeout**: Safety question timeouts applied
- [ ] **Pedestrian Safety**: Safety features enabled as specified
- [ ] **VOR Status**: Vehicle Out of Service status correct

**3. Access Card Verification**
Navigate to FleetXQ → Access Control → Cards

**Check Card Records:**
- [ ] **Card Creation**: All cards from CSV exist in system
- [ ] **Card Numbers**: Card No and Weigand numbers unique and correct
- [ ] **Person Linkage**: Cards correctly linked to personnel
- [ ] **Access Level**: Department/Site/Supervisor permissions applied
- [ ] **Facility Code**: Correct facility codes assigned

**Access Permission Testing:**
- [ ] **Vehicle Access**: Cards grant appropriate vehicle access
- [ ] **Site Access**: Site-level cards work across departments
- [ ] **Department Restrictions**: Department cards limited to correct areas
- [ ] **Supervisor Override**: Supervisor cards can override safety systems

**4. Safety System Verification**
Navigate to FleetXQ → Safety → Pre-Op Checklists

**Check Safety Configuration:**
- [ ] **Checklist Creation**: Custom checklists from CSV exist
- [ ] **Question Content**: Safety questions match CSV text
- [ ] **Response Requirements**: Required vs. optional responses correct
- [ ] **Vehicle Assignment**: Checklists assigned to correct vehicles

**Driver Blacklist Verification:**
- [ ] **Blacklist Records**: Restricted drivers cannot access vehicles
- [ ] **Access Denial**: Blacklisted cards show appropriate error messages
- [ ] **Temporary Restrictions**: Time-based restrictions work correctly

### 5. IoT Device Synchronization Verification

**Vehicle Sync Status Check:**
- [ ] **Sync Completion**: All vehicles with Device IDs attempted sync
- [ ] **Online Devices**: Devices currently online show successful sync
- [ ] **Offline Devices**: Offline devices flagged for later retry
- [ ] **Configuration Match**: Device settings match FleetXQ configuration

**Device Communication Testing:**
- [ ] **Real-time Connection**: Test vehicle startup with new settings
- [ ] **Safety Enforcement**: New timeout/lockout rules active on device
- [ ] **Checklist Integration**: Daily checklists appear on vehicle display
- [ ] **Access Control**: New card assignments work immediately

---

## 📊 Post-Migration Validation

### Report Analysis

**1. Migration Report Review**
Location: `Reports/Full-Migration-Report-YYYYMMDD-HHMMSS.txt`

**Report Validation:**
- [ ] **Success Counts**: Number of successfully imported records matches expectations
- [ ] **Skipped Records**: Skipped records are legitimate duplicates
- [ ] **Failed Records**: All failures have clear explanations and fix instructions
- [ ] **Processing Time**: Migration completed within acceptable timeframe

**2. Error Investigation**
For each failed record:
- [ ] **Root Cause**: Understand why the record failed
- [ ] **CSV Fix**: Verify the suggested fix is correct
- [ ] **Re-import Test**: Failed records can be successfully imported after fixes

### Data Integrity Testing

**1. Cross-System Validation**
- [ ] **Record Counts**: CSV row counts match FleetXQ record counts
- [ ] **Data Accuracy**: Sample random records for manual verification
- [ ] **Relationship Integrity**: Cards linked to correct people, vehicles to correct departments

**2. System Integration Testing**
- [ ] **User Login**: Website users can log in with imported credentials
- [ ] **Vehicle Operation**: Drivers can start vehicles with imported cards
- [ ] **Safety Systems**: Pre-op checklists appear and function correctly
- [ ] **Supervisor Override**: Supervisor cards can bypass safety lockouts

### Performance & Stability

**1. System Performance Post-Migration**
- [ ] **Login Performance**: User authentication speed unchanged
- [ ] **Search Performance**: Fleet and personnel searches respond quickly
- [ ] **Report Generation**: System reports generate without delays
- [ ] **Real-time Updates**: Live vehicle status updates work normally

**2. Data Volume Impact**
- [ ] **Database Size**: Monitor database growth and performance
- [ ] **Query Performance**: Complex queries maintain acceptable speed
- [ ] **Backup Impact**: Database backups complete successfully

---

## 🔧 Troubleshooting & Common Issues

### Application Setup Issues

**Problem**: "CSV file not found"
- **Symptoms**: File upload errors, validation failures
- **Resolution**:
  1. Verify exact file names match requirements
  2. Ensure files saved as CSV (not Excel .xlsx)
  3. Check file permissions and access
  4. Verify CSV_Input directory exists

**Problem**: "Environment connection issues"
- **Symptoms**: Database connection errors in Development environment
- **Resolution**:
  1. **IMPORTANT: Always use Development environment for QA testing**
  2. Contact system administrator if Development environment is unavailable
  3. Do not attempt to use production environments for testing

### Data Validation Issues

**Problem**: "Person not found in department"
- **Symptoms**: Card import fails, access assignment errors
- **Resolution**:
  1. Import personnel data first
  2. Verify Customer/Site/Department names match exactly
  3. Check for typos in names and hierarchy
  4. Ensure case sensitivity consistency

**Problem**: "Duplicate Weigand number"
- **Symptoms**: Card import failures, access conflicts
- **Resolution**:
  1. Review CSV for duplicate card numbers
  2. Check existing system for number conflicts
  3. Generate unique card numbers
  4. Coordinate with physical card management

### Migration Process Issues

**Problem**: "Migration hangs or times out"
- **Symptoms**: Progress stops, no updates, timeout errors
- **Resolution**:
  1. Check network connectivity
  2. Monitor database server performance
  3. Reduce batch size for large datasets
  4. Restart migration with smaller data sets

**Problem**: "Partial migration success"
- **Symptoms**: Some records imported, others failed
- **Resolution**:
  1. Review migration report for failed records
  2. Fix CSV data based on error messages
  3. Re-run specific migration types
  4. Monitor for duplicate prevention

### FleetXQ Verification Issues

**Problem**: "Data appears in FleetXQ but functionality broken"
- **Symptoms**: Records visible but cards don't work, vehicles can't start
- **Resolution**:
  1. Check relationship integrity (cards to people, people to departments)
  2. Verify permission assignments
  3. Test IoT device synchronization
  4. Review access level configurations

**Problem**: "Vehicle devices not responding to new settings"
- **Symptoms**: Old settings still active, new users can't access
- **Resolution**:
  1. Check vehicle IoT connectivity
  2. Verify Device IDs match exactly
  3. Force manual sync from FleetXQ
  4. Restart vehicle systems if necessary

---

## ✅ Quality Gates & Sign-off Criteria

### Pre-Migration Quality Gates

**Environment Readiness:**
- [ ] **Development environment selected** for all QA testing
- [ ] All system tests pass successfully
- [ ] Database connectivity confirmed for Development environment

**Data Quality:**
- [ ] All CSV files pass header validation
- [ ] Sample data verification successful
- [ ] Dependency relationships verified
- [ ] Test data sets prepared and documented

### Migration Quality Gates

**Process Validation:**
- [ ] Migration sequence follows correct order
- [ ] Real-time progress tracking functional
- [ ] Error handling performs correctly
- [ ] Migration reports generated successfully

**Data Integrity:**
- [ ] Record counts match expectations (allowing for duplicates)
- [ ] Failed records have clear remediation paths
- [ ] No data corruption or loss detected
- [ ] Performance remains acceptable

### Post-Migration Quality Gates

**System Integration:**
- [ ] All imported users can log in successfully
- [ ] Vehicle access cards function correctly
- [ ] Safety systems operate as configured
- [ ] IoT device synchronization successful

**Production Readiness:**
- [ ] FleetXQ system performance stable
- [ ] Data verification procedures completed
- [ ] User acceptance testing passed
- [ ] Documentation updated and training complete

### Final Sign-off Checklist

**Technical Sign-off:**
- [ ] **Data Migration Lead**: All technical requirements met
- [ ] **QA Lead**: All test cases passed and documented
- [ ] **DBA**: Database performance and integrity validated
- [ ] **IT Operations**: System monitoring and alerts configured

**Business Sign-off:**
- [ ] **Fleet Manager**: Vehicle and equipment data accurate
- [ ] **HR Manager**: Personnel and access data correct
- [ ] **Safety Manager**: Safety systems and protocols active
- [ ] **End Users**: User acceptance testing completed

**Documentation Complete:**
- [ ] Migration report archived
- [ ] Test results documented
- [ ] Issues log updated
- [ ] Lessons learned captured
- [ ] Go-live procedures finalized

---

## 📞 Support & Escalation

**For Technical Issues:**
- **Migration Tool Support**: Review developer logs in `Logs/` directory
- **Database Issues**: Contact DBA with connection string and error details
- **API Issues**: Verify authentication and endpoint configuration

**For Data Issues:**
- **CSV Format Problems**: Use migration report for specific fix instructions
- **FleetXQ Verification**: Work with system administrators for access and validation
- **IoT Device Issues**: Coordinate with field technicians for device status

**For Business Issues:**
- **User Access Problems**: Review with HR and fleet management
- **Safety Configuration**: Validate with safety managers and supervisors
- **Operational Impact**: Coordinate with fleet operations and drivers

---

**🎯 This QA Note ensures comprehensive testing of the XQ360 Data Migration web application from CSV preparation through FleetXQ verification, maintaining data integrity and system reliability throughout the migration process.**
