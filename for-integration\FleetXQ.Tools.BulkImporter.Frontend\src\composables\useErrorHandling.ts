import { ref, computed } from 'vue'
import { useNotificationStore } from '@/stores/notification'
import type { ApiError } from '@/services'

export interface ErrorContext {
  operation: string
  component?: string
  userId?: string
  sessionId?: string
  timestamp: Date
  userAgent: string
  url: string
}

export interface ErrorRecoveryAction {
  label: string
  action: () => void | Promise<void>
  isPrimary?: boolean
}

export interface ProcessedError {
  id: string
  title: string
  message: string
  details?: string
  type: 'network' | 'validation' | 'authorization' | 'server' | 'client' | 'unknown'
  severity: 'low' | 'medium' | 'high' | 'critical'
  isRetryable: boolean
  recoveryActions: ErrorRecoveryAction[]
  context: ErrorContext
  originalError: any
}

export function useErrorHandling() {
  const notificationStore = useNotificationStore()
  
  const errors = ref<ProcessedError[]>([])
  const isProcessingError = ref(false)

  // Computed properties
  const hasErrors = computed(() => errors.value.length > 0)
  const criticalErrors = computed(() => errors.value.filter(e => e.severity === 'critical'))
  const hasCriticalErrors = computed(() => criticalErrors.value.length > 0)

  // Error classification
  const classifyError = (error: any): { type: ProcessedError['type'], severity: ProcessedError['severity'] } => {
    // Network errors
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('fetch')) {
      return { type: 'network', severity: 'medium' }
    }

    // API errors
    if (error.status) {
      switch (Math.floor(error.status / 100)) {
        case 4:
          if (error.status === 401 || error.status === 403) {
            return { type: 'authorization', severity: 'high' }
          }
          if (error.status === 400 || error.status === 422) {
            return { type: 'validation', severity: 'medium' }
          }
          return { type: 'client', severity: 'medium' }
        case 5:
          return { type: 'server', severity: 'high' }
        default:
          return { type: 'unknown', severity: 'medium' }
      }
    }

    // Validation errors
    if (error.validationErrors || error.message?.includes('validation')) {
      return { type: 'validation', severity: 'low' }
    }

    // Default classification
    return { type: 'unknown', severity: 'medium' }
  }

  // Error message generation
  const generateUserFriendlyMessage = (error: any, type: ProcessedError['type']): { title: string, message: string } => {
    switch (type) {
      case 'network':
        return {
          title: 'Connection Problem',
          message: 'Unable to connect to the server. Please check your internet connection and try again.'
        }
      
      case 'authorization':
        return {
          title: 'Access Denied',
          message: 'You don\'t have permission to perform this action. Please contact your administrator.'
        }
      
      case 'validation':
        return {
          title: 'Invalid Data',
          message: error.message || 'The information provided is not valid. Please check your input and try again.'
        }
      
      case 'server':
        return {
          title: 'Server Error',
          message: 'A server error occurred. Our team has been notified. Please try again later.'
        }
      
      case 'client':
        return {
          title: 'Request Error',
          message: error.message || 'There was a problem with your request. Please try again.'
        }
      
      default:
        return {
          title: 'Unexpected Error',
          message: error.message || 'An unexpected error occurred. Please try again.'
        }
    }
  }

  // Recovery actions generation
  const generateRecoveryActions = (error: any, type: ProcessedError['type'], context: ErrorContext): ErrorRecoveryAction[] => {
    const actions: ErrorRecoveryAction[] = []

    // Common actions
    actions.push({
      label: 'Dismiss',
      action: () => removeError(error.id),
      isPrimary: false
    })

    // Type-specific actions
    switch (type) {
      case 'network':
        actions.unshift({
          label: 'Retry',
          action: async () => {
            // This would need to be passed in or stored in context
            console.log('Retrying operation...')
          },
          isPrimary: true
        })
        break

      case 'authorization':
        actions.unshift({
          label: 'Login Again',
          action: () => {
            // Redirect to login
            window.location.href = '/login'
          },
          isPrimary: true
        })
        break

      case 'validation':
        actions.unshift({
          label: 'Review Input',
          action: () => {
            // Focus on the problematic field
            console.log('Focusing on problematic field...')
          },
          isPrimary: true
        })
        break

      case 'server':
        actions.unshift({
          label: 'Contact Support',
          action: () => {
            // Open support contact
            window.open('mailto:<EMAIL>', '_blank')
          },
          isPrimary: true
        })
        break
    }

    return actions
  }

  // Error processing
  const processError = (error: any, operation: string, component?: string): ProcessedError => {
    const context: ErrorContext = {
      operation,
      component,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    const { type, severity } = classifyError(error)
    const { title, message } = generateUserFriendlyMessage(error, type)
    
    const processedError: ProcessedError = {
      id: generateErrorId(),
      title,
      message,
      details: error.details || (error.stack ? error.stack : undefined),
      type,
      severity,
      isRetryable: ['network', 'server'].includes(type),
      recoveryActions: [],
      context,
      originalError: error
    }

    processedError.recoveryActions = generateRecoveryActions(error, type, context)

    return processedError
  }

  // Error handling methods
  const handleError = (error: any, operation: string, component?: string, options: {
    showNotification?: boolean
    logToConsole?: boolean
    reportToServer?: boolean
  } = {}) => {
    const {
      showNotification = true,
      logToConsole = true,
      reportToServer = true
    } = options

    isProcessingError.value = true

    try {
      const processedError = processError(error, operation, component)
      errors.value.push(processedError)

      // Log to console
      if (logToConsole) {
        console.error(`[${operation}] Error:`, error)
        console.error('Processed error:', processedError)
      }

      // Show notification
      if (showNotification) {
        const notificationType = processedError.severity === 'critical' ? 'error' : 
                               processedError.severity === 'high' ? 'error' :
                               processedError.severity === 'medium' ? 'warning' : 'info'

        notificationStore.createNotification({
          type: notificationType,
          title: processedError.title,
          message: processedError.message,
          isPersistent: processedError.severity === 'critical' || processedError.severity === 'high',
          autoHideDelay: processedError.severity === 'low' ? 5000 : undefined,
          data: {
            errorId: processedError.id,
            recoveryActions: processedError.recoveryActions
          }
        })
      }

      // Report to server (in a real app)
      if (reportToServer && processedError.severity !== 'low') {
        reportErrorToServer(processedError)
      }

      return processedError
    } finally {
      isProcessingError.value = false
    }
  }

  const handleApiError = (error: ApiError, operation: string, component?: string) => {
    return handleError(error, operation, component)
  }

  const handleValidationErrors = (validationErrors: Record<string, string[]>, operation: string) => {
    const error = {
      message: 'Validation failed',
      validationErrors,
      type: 'validation'
    }
    return handleError(error, operation)
  }

  // Error management
  const removeError = (errorId: string) => {
    const index = errors.value.findIndex(e => e.id === errorId)
    if (index >= 0) {
      errors.value.splice(index, 1)
    }
  }

  const clearErrors = () => {
    errors.value = []
  }

  const clearErrorsByType = (type: ProcessedError['type']) => {
    errors.value = errors.value.filter(e => e.type !== type)
  }

  const clearErrorsByOperation = (operation: string) => {
    errors.value = errors.value.filter(e => e.context.operation !== operation)
  }

  // Utility functions
  const generateErrorId = (): string => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  const reportErrorToServer = async (error: ProcessedError) => {
    try {
      // In a real application, this would send the error to a logging service
      console.log('Reporting error to server:', error)
      
      // Example API call:
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     id: error.id,
      //     type: error.type,
      //     severity: error.severity,
      //     message: error.message,
      //     context: error.context,
      //     stack: error.originalError?.stack
      //   })
      // })
    } catch (reportError) {
      console.error('Failed to report error to server:', reportError)
    }
  }

  // Error boundary functionality
  const withErrorHandling = <T extends (...args: any[]) => any>(
    fn: T,
    operation: string,
    component?: string
  ): T => {
    return ((...args: Parameters<T>) => {
      try {
        const result = fn(...args)
        
        // Handle async functions
        if (result instanceof Promise) {
          return result.catch((error) => {
            handleError(error, operation, component)
            throw error
          })
        }
        
        return result
      } catch (error) {
        handleError(error, operation, component)
        throw error
      }
    }) as T
  }

  return {
    // State
    errors,
    isProcessingError,

    // Computed
    hasErrors,
    criticalErrors,
    hasCriticalErrors,

    // Error handling
    handleError,
    handleApiError,
    handleValidationErrors,
    processError,

    // Error management
    removeError,
    clearErrors,
    clearErrorsByType,
    clearErrorsByOperation,

    // Utilities
    withErrorHandling,
    classifyError,
    generateUserFriendlyMessage
  }
}
