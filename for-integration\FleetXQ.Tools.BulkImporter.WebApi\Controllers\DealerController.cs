using Microsoft.AspNetCore.Mvc;

namespace FleetXQ.Tools.BulkImporter.WebApi.Controllers;

/// <summary>
/// Controller for dealer management operations in the bulk importer
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class DealerController : ControllerBase
{
    private readonly ILogger<DealerController> _logger;

    public DealerController(ILogger<DealerController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Gets a list of dealers with optional filtering and pagination
    /// </summary>
    /// <param name="query">Optional search query to filter dealers by name or subdomain</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 100)</param>
    /// <param name="activeOnly">Whether to return only active dealers (default: true)</param>
    /// <returns>List of dealers matching the criteria</returns>
    /// <response code="200">Returns the list of dealers</response>
    /// <response code="400">If the request parameters are invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(DealerListResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DealerListResponse>> GetDealers(
        [FromQuery] string? query = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            // Validate parameters
            if (pageNumber < 1)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page number",
                    Detail = "Page number must be greater than 0",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page size",
                    Detail = "Page size must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Getting dealers with query: {Query}, page: {PageNumber}, size: {PageSize}, activeOnly: {ActiveOnly}",
                query, pageNumber, pageSize, activeOnly);

            // Mock data for demonstration - in a real implementation, this would query the database
            var allDealers = GetMockDealers();

            // Apply filters
            var filteredDealers = allDealers.AsEnumerable();

            if (activeOnly)
            {
                filteredDealers = filteredDealers.Where(d => d.Active);
            }

            if (!string.IsNullOrWhiteSpace(query))
            {
                var searchTerm = query.Trim().ToLowerInvariant();
                filteredDealers = filteredDealers.Where(d =>
                    d.Name.ToLowerInvariant().Contains(searchTerm) ||
                    d.SubDomain.ToLowerInvariant().Contains(searchTerm));
            }

            var totalCount = filteredDealers.Count();

            // Apply pagination
            var dealerInfos = filteredDealers
                .OrderBy(d => d.Name)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var response = new DealerListResponse
            {
                Dealers = dealerInfos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            _logger.LogDebug("Retrieved {Count} dealers out of {TotalCount} total", dealerInfos.Count, totalCount);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dealers with query: {Query}", query);
            return Problem(
                title: "Error retrieving dealers",
                detail: "An error occurred while retrieving the list of dealers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets a specific dealer by ID
    /// </summary>
    /// <param name="id">Dealer ID</param>
    /// <returns>Dealer information</returns>
    /// <response code="200">Returns the dealer information</response>
    /// <response code="404">If the dealer is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(DealerInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DealerInfo>> GetDealer(Guid id)
    {
        try
        {
            _logger.LogDebug("Getting dealer with ID: {DealerId}", id);

            // Mock data for demonstration - in a real implementation, this would query the database
            var dealer = GetMockDealers().FirstOrDefault(d => d.Id == id);

            if (dealer == null)
            {
                _logger.LogWarning("Dealer not found with ID: {DealerId}", id);
                return NotFound(new ProblemDetails
                {
                    Title = "Dealer not found",
                    Detail = $"No dealer found with ID: {id}",
                    Status = StatusCodes.Status404NotFound
                });
            }

            _logger.LogDebug("Retrieved dealer: {DealerName}", dealer.Name);
            return Ok(dealer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dealer with ID: {DealerId}", id);
            return Problem(
                title: "Error retrieving dealer",
                detail: "An error occurred while retrieving the dealer",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Searches for dealers by name or subdomain
    /// </summary>
    /// <param name="query">Search term to match against dealer name or subdomain</param>
    /// <param name="limit">Maximum number of results to return (default: 20, max: 50)</param>
    /// <param name="activeOnly">Whether to return only active dealers (default: true)</param>
    /// <returns>List of dealers matching the search criteria</returns>
    /// <response code="200">Returns the list of matching dealers</response>
    /// <response code="400">If the search query is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("search")]
    [ProducesResponseType(typeof(IEnumerable<DealerInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<DealerInfo>>> SearchDealers(
        [FromQuery] string query,
        [FromQuery] int limit = 20,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid search query",
                    Detail = "Search query cannot be empty",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (limit < 1 || limit > 50)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid limit",
                    Detail = "Limit must be between 1 and 50",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Searching dealers with query: {Query}, limit: {Limit}, activeOnly: {ActiveOnly}",
                query, limit, activeOnly);

            // Mock data for demonstration - in a real implementation, this would query the database
            var allDealers = GetMockDealers();
            var searchTerm = query.Trim().ToLowerInvariant();

            var filteredDealers = allDealers.AsEnumerable();

            if (activeOnly)
            {
                filteredDealers = filteredDealers.Where(d => d.Active);
            }

            filteredDealers = filteredDealers.Where(d =>
                d.Name.ToLowerInvariant().Contains(searchTerm) ||
                d.SubDomain.ToLowerInvariant().Contains(searchTerm));

            var dealerInfos = filteredDealers
                .OrderBy(d => d.Name)
                .Take(limit)
                .ToList();

            _logger.LogDebug("Found {Count} dealers matching search query: {Query}", dealerInfos.Count, query);
            return Ok(dealerInfos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching dealers with query: {Query}", query);
            return Problem(
                title: "Error searching dealers",
                detail: "An error occurred while searching for dealers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Validates if a dealer exists and is active
    /// </summary>
    /// <param name="id">Dealer ID to validate</param>
    /// <returns>Validation result for the dealer</returns>
    /// <response code="200">Returns the validation result</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{id:guid}/validation")]
    [ProducesResponseType(typeof(DealerValidationResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DealerValidationResponse>> ValidateDealer(Guid id)
    {
        try
        {
            _logger.LogDebug("Validating dealer with ID: {DealerId}", id);

            // Mock data for demonstration - in a real implementation, this would query the database
            var dealer = GetMockDealers().FirstOrDefault(d => d.Id == id);

            var response = new DealerValidationResponse
            {
                DealerId = id,
                Exists = dealer != null,
                IsActive = dealer?.Active ?? false,
                IsAPIEnabled = dealer?.IsAPIEnabled ?? false,
                DealerName = dealer?.Name ?? string.Empty,
                SubDomain = dealer?.SubDomain ?? string.Empty
            };

            if (dealer == null)
            {
                response.ValidationErrors.Add("Dealer does not exist");
                response.IsValid = false;
            }
            else if (!dealer.Active)
            {
                response.ValidationErrors.Add("Dealer is not active");
                response.IsValid = false;
            }
            else if (!dealer.IsAPIEnabled)
            {
                response.ValidationWarnings.Add("Dealer does not have API access enabled");
                response.IsValid = true; // Still valid for bulk import, but with warning
            }
            else
            {
                response.IsValid = true;
            }

            _logger.LogDebug("Dealer validation result for {DealerId}: {IsValid}", id, response.IsValid);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating dealer with ID: {DealerId}", id);
            return Problem(
                title: "Error validating dealer",
                detail: "An error occurred while validating the dealer",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static List<DealerInfo> GetMockDealers()
    {
        return new List<DealerInfo>
        {
            new DealerInfo
            {
                Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                Name = "Demo Dealer 1",
                SubDomain = "demo1",
                Description = "Demo dealer for testing",
                Active = true,
                IsAPIEnabled = true,
                ContractNumber = 1001
            },
            new DealerInfo
            {
                Id = Guid.Parse("*************-2222-2222-************"),
                Name = "Demo Dealer 2",
                SubDomain = "demo2",
                Description = "Another demo dealer",
                Active = true,
                IsAPIEnabled = true,
                ContractNumber = 1002
            },
            new DealerInfo
            {
                Id = Guid.Parse("*************-3333-3333-************"),
                Name = "Inactive Dealer",
                SubDomain = "inactive",
                Description = "Inactive dealer for testing",
                Active = false,
                IsAPIEnabled = false,
                ContractNumber = 1003
            }
        };
    }
}

/// <summary>
/// Dealer information for API responses
/// </summary>
public class DealerInfo
{
    /// <summary>
    /// Dealer unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Dealer name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Dealer subdomain
    /// </summary>
    public string SubDomain { get; set; } = string.Empty;

    /// <summary>
    /// Dealer description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Whether the dealer is active
    /// </summary>
    public bool Active { get; set; }

    /// <summary>
    /// Whether API access is enabled for this dealer
    /// </summary>
    public bool IsAPIEnabled { get; set; }

    /// <summary>
    /// Dealer contract number
    /// </summary>
    public short? ContractNumber { get; set; }
}

/// <summary>
/// Response for dealer list requests
/// </summary>
public class DealerListResponse
{
    /// <summary>
    /// List of dealers
    /// </summary>
    public List<DealerInfo> Dealers { get; set; } = new();

    /// <summary>
    /// Total number of dealers matching the criteria
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Page size used for the request
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }
}

/// <summary>
/// Response for dealer validation requests
/// </summary>
public class DealerValidationResponse
{
    /// <summary>
    /// Dealer ID that was validated
    /// </summary>
    public Guid DealerId { get; set; }

    /// <summary>
    /// Whether the dealer exists
    /// </summary>
    public bool Exists { get; set; }

    /// <summary>
    /// Whether the dealer is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether API access is enabled for the dealer
    /// </summary>
    public bool IsAPIEnabled { get; set; }

    /// <summary>
    /// Whether the dealer is valid for bulk import operations
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Dealer name
    /// </summary>
    public string DealerName { get; set; } = string.Empty;

    /// <summary>
    /// Dealer subdomain
    /// </summary>
    public string SubDomain { get; set; } = string.Empty;

    /// <summary>
    /// List of validation errors
    /// </summary>
    public List<string> ValidationErrors { get; set; } = new();

    /// <summary>
    /// List of validation warnings
    /// </summary>
    public List<string> ValidationWarnings { get; set; } = new();
}
