# XQ360 Data Migration - Manual Deployment Script
# Run this script step by step for manual deployment

param(
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [string]$AppPoolName = "XQ360MigrationPool",
    [string]$WebsiteName = "XQ360Migration",
    [int]$Port = 80
)

Write-Host "XQ360 Data Migration - Manual Deployment Script" -ForegroundColor Green
Write-Host "This script will guide you through manual deployment step by step." -ForegroundColor Yellow
Write-Host ""

# Step 1: Check Prerequisites
Write-Host "Step 1: Checking Prerequisites..." -ForegroundColor Cyan
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}
Write-Host "✅ Running as Administrator" -ForegroundColor Green

# Check if WebAdministration module is available
try {
    Import-Module WebAdministration -ErrorAction Stop
    Write-Host "✅ WebAdministration module available" -ForegroundColor Green
} catch {
    Write-Host "❌ WebAdministration module not available. Please install IIS first." -ForegroundColor Red
    exit 1
}

Write-Host "Step 1 completed!" -ForegroundColor Green
Write-Host "Press any key to continue to Step 2..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 2: Build and Publish
Write-Host "`nStep 2: Building and Publishing Applications..." -ForegroundColor Cyan

$publishPath = "C:\temp\XQ360Publish"
if (Test-Path $publishPath) {
    Remove-Item $publishPath -Recurse -Force
}
New-Item -ItemType Directory -Path $publishPath -Force

Write-Host "Building applications..." -ForegroundColor Yellow
dotnet build XQ360.DataMigration -c Release
dotnet build XQ360.DataMigration.Web -c Release
dotnet build XQ360.DataMigration.Tests -c Release

Write-Host "Publishing web application..." -ForegroundColor Yellow
dotnet publish XQ360.DataMigration.Web -c Release -o "$publishPath\Web"

Write-Host "Publishing main application..." -ForegroundColor Yellow
dotnet publish XQ360.DataMigration -c Release -o "$publishPath\Main"

Write-Host "Copying test application..." -ForegroundColor Yellow
Copy-Item "XQ360.DataMigration.Tests" -Destination "$publishPath\Tests" -Recurse

Write-Host "Step 2 completed!" -ForegroundColor Green
Write-Host "Press any key to continue to Step 3..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 3: Configure IIS
Write-Host "`nStep 3: Configuring IIS..." -ForegroundColor Cyan

Write-Host "Enabling IIS features..." -ForegroundColor Yellow
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -All -ErrorAction SilentlyContinue
Write-Host "✅ WebSockets enabled" -ForegroundColor Green

Write-Host "Creating application pool..." -ForegroundColor Yellow
if (Get-WebAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
    Remove-WebAppPool -Name $AppPoolName
}
New-WebAppPool -Name $AppPoolName

Write-Host "Configuring application pool..." -ForegroundColor Yellow
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "managedRuntimeVersion" -Value ""
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.pingInterval" -Value "00:00:30"
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.pingResponseTime" -Value "00:01:30"

Write-Host "Creating website directory..." -ForegroundColor Yellow
if (Test-Path $DeployPath) {
    Remove-Item $DeployPath -Recurse -Force
}
New-Item -ItemType Directory -Path $DeployPath -Force

Write-Host "Creating website..." -ForegroundColor Yellow
$existingWebsite = Get-Website -Name $WebsiteName -ErrorAction SilentlyContinue
if ($existingWebsite) {
    Stop-Website -Name $WebsiteName -ErrorAction SilentlyContinue
    Remove-Website -Name $WebsiteName -ErrorAction SilentlyContinue
}
New-Website -Name $WebsiteName -PhysicalPath $DeployPath -ApplicationPool $AppPoolName -Port $Port

Write-Host "Step 3 completed!" -ForegroundColor Green
Write-Host "Press any key to continue to Step 4..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 4: Deploy Files
Write-Host "`nStep 4: Deploying Application Files..." -ForegroundColor Cyan

Write-Host "Copying web application files..." -ForegroundColor Yellow
Copy-Item "$publishPath\Web\*" -Destination $DeployPath -Recurse -Force

Write-Host "Ensuring wwwroot files are copied..." -ForegroundColor Yellow
if (-not (Test-Path "$DeployPath\wwwroot")) {
    Copy-Item "XQ360.DataMigration.Web\wwwroot" -Destination "$DeployPath\wwwroot" -Recurse -Force
}

Write-Host "Copying supporting applications..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path "$DeployPath\bin" -Force -ErrorAction SilentlyContinue
Copy-Item "$publishPath\Main\*" -Destination "$DeployPath\bin" -Recurse -Force -ErrorAction SilentlyContinue

New-Item -ItemType Directory -Path "$DeployPath\tests" -Force -ErrorAction SilentlyContinue
Copy-Item "$publishPath\Tests\*" -Destination "$DeployPath\tests" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Copying configuration files..." -ForegroundColor Yellow
if (Test-Path "deploy\production-appsettings.json") {
    Copy-Item "deploy\production-appsettings.json" -Destination "$DeployPath\appsettings.json" -Force
}

New-Item -ItemType Directory -Path "$DeployPath\Logs" -Force -ErrorAction SilentlyContinue

Write-Host "Step 4 completed!" -ForegroundColor Green
Write-Host "Press any key to continue to Step 5..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 5: Set Permissions
Write-Host "`nStep 5: Setting File Permissions..." -ForegroundColor Cyan

Write-Host "Setting IIS permissions..." -ForegroundColor Yellow
icacls $DeployPath /grant "IIS_IUSRS:(OI)(CI)(RX)" /T 2>$null
icacls $DeployPath /grant "IIS_IUSRS:(OI)(CI)(M)" /T 2>$null
icacls $DeployPath /grant "IIS AppPool\$AppPoolName:(OI)(CI)(RX)" /T 2>$null
icacls $DeployPath /grant "IIS AppPool\$AppPoolName:(OI)(CI)(M)" /T 2>$null
icacls "$DeployPath\Logs" /grant "IIS_IUSRS:(OI)(CI)(F)" /T 2>$null
icacls "$DeployPath\Logs" /grant "IIS AppPool\$AppPoolName:(OI)(CI)(F)" /T 2>$null

Write-Host "Step 5 completed!" -ForegroundColor Green
Write-Host "Press any key to continue to Step 6..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 6: Configure Firewall
Write-Host "`nStep 6: Configuring Firewall..." -ForegroundColor Cyan

Write-Host "Creating firewall rule..." -ForegroundColor Yellow
$firewallRule = Get-NetFirewallRule -DisplayName "XQ360 Migration Web" -ErrorAction SilentlyContinue
if (-not $firewallRule) {
    New-NetFirewallRule -DisplayName "XQ360 Migration Web" -Direction Inbound -Protocol TCP -LocalPort $Port -Action Allow -ErrorAction SilentlyContinue
    Write-Host "✅ Firewall rule created" -ForegroundColor Green
} else {
    Write-Host "✅ Firewall rule already exists" -ForegroundColor Green
}

Write-Host "Step 6 completed!" -ForegroundColor Green
Write-Host "Press any key to continue to Step 7..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 7: Start Website
Write-Host "`nStep 7: Starting Website..." -ForegroundColor Cyan

Write-Host "Starting website..." -ForegroundColor Yellow
Start-Website -Name $WebsiteName

$website = Get-Website -Name $WebsiteName
Write-Host "Website State: $($website.State)" -ForegroundColor Green

Write-Host "Step 7 completed!" -ForegroundColor Green
Write-Host "Press any key to continue to Step 8..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 8: Test Deployment
Write-Host "`nStep 8: Testing Deployment..." -ForegroundColor Cyan

Write-Host "Testing web access..." -ForegroundColor Yellow
$testUrl = "http://localhost"
try {
    $response = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Web application is accessible" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Web application responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to access web application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Testing static files..." -ForegroundColor Yellow
$staticUrls = @(
    "/lib/bootstrap/dist/css/bootstrap.min.css",
    "/lib/bootstrap/dist/js/bootstrap.bundle.min.js",
    "/lib/jquery/dist/jquery.min.js",
    "/css/site.css",
    "/js/site.js",
    "/XQ360.DataMigration.Web.styles.css"
)

foreach ($url in $staticUrls) {
    $fullUrl = "$testUrl$url"
    try {
        $response = Invoke-WebRequest -Uri $fullUrl -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $url is accessible" -ForegroundColor Green
        } else {
            Write-Host "❌ $url returned status: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $url is not accessible: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Testing SignalR..." -ForegroundColor Yellow
try {
    $signalRUrl = "$testUrl/migrationHub"
    $response = Invoke-WebRequest -Uri $signalRUrl -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ SignalR hub endpoint is accessible" -ForegroundColor Green
    } else {
        Write-Host "⚠️ SignalR hub responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ SignalR hub endpoint not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Step 8 completed!" -ForegroundColor Green

# Summary
Write-Host "`nDeployment Summary:" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green
Write-Host "✅ Applications built and published" -ForegroundColor White
Write-Host "✅ IIS configured with Application Pool: $AppPoolName" -ForegroundColor White
Write-Host "✅ Website created: $WebsiteName" -ForegroundColor White
Write-Host "✅ Files deployed to: $DeployPath" -ForegroundColor White
Write-Host "✅ Permissions configured" -ForegroundColor White
Write-Host "✅ Firewall rules added" -ForegroundColor White
Write-Host "✅ Website started" -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host "Access URLs:" -ForegroundColor Cyan
Write-Host "   Local: http://localhost" -ForegroundColor White
Write-Host "   SignalR Test: http://localhost/Home/SignalRTest" -ForegroundColor White
Write-Host "   Bootstrap Test: http://localhost/Home/Test" -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host "Manual deployment completed!" -ForegroundColor Green 