-- =============================================
-- FleetXQ Bulk Importer - Driver Staging Table
-- Creates staging table for driver import data
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE schema_id = SCHEMA_ID('Staging') AND name = 'DriverImport')
BEGIN
    CREATE TABLE [Staging].[DriverImport] (
        [Id] BIGINT IDENTITY(1,1) PRIMARY KEY,
        [ImportSessionId] UNIQUEIDENTIFIER NOT NULL,
        [RowNumber] INT NOT NULL,
        [ValidationStatus] NVARCHAR(20) NOT NULL DEFAULT 'Pending', -- Pending, Valid, Invalid, Processed
        [ValidationErrors] NVARCHAR(MAX) NULL,
        
        -- Raw Input Data (from SQL generation)
        [ExternalDriverId] NVARCHAR(50) NULL,
        [PersonFirstName] NVARCHAR(50) NOT NULL,
        [PersonLastName] NVARCHAR(50) NOT NULL,
        [PersonEmail] NVARCHAR(50) NULL,
        [PersonPhone] NVARCHAR(50) NULL,
        [DriverActive] BIT NULL,
        [DriverLicenseMode] INT NULL,
        [DriverVehicleAccess] BIT NULL,
        [PersonIsActiveDriver] BIT NULL,
        [PersonHasLicense] BIT NULL,
        [PersonLicenseActive] BIT NULL,
        [PersonVehicleAccess] BIT NULL,
        [PersonCanUnlockVehicle] BIT NULL,
        [PersonNormalDriverAccess] BIT NULL,
        [CustomerName] NVARCHAR(100) NOT NULL,
        [SiteName] NVARCHAR(100) NOT NULL,
        [DepartmentName] NVARCHAR(100) NOT NULL,
        [Notes] NVARCHAR(155) NULL,
        
        -- Resolved Foreign Keys (populated during validation)
        [CustomerId] UNIQUEIDENTIFIER NULL,
        [SiteId] UNIQUEIDENTIFIER NULL,
        [DepartmentId] UNIQUEIDENTIFIER NULL,
        [ExistingPersonId] UNIQUEIDENTIFIER NULL,
        [ExistingDriverId] UNIQUEIDENTIFIER NULL,
        
        -- Processing Metadata
        [ProcessingAction] NVARCHAR(20) NULL, -- Insert, Update, Skip
        [ProcessedAt] DATETIME2 NULL,
        [ProcessingErrors] NVARCHAR(MAX) NULL,
        
        -- Foreign key to import session
        CONSTRAINT FK_DriverImport_ImportSession 
            FOREIGN KEY ([ImportSessionId]) REFERENCES [Staging].[ImportSession]([Id]),
            
        -- Indexes for performance
        INDEX IX_DriverImport_Session_Status ([ImportSessionId], [ValidationStatus]),
        INDEX IX_DriverImport_Email ([PersonEmail]),
        INDEX IX_DriverImport_Name ([PersonFirstName], [PersonLastName], [CustomerName]),
        INDEX IX_DriverImport_ExternalId ([ExternalDriverId]) WHERE [ExternalDriverId] IS NOT NULL
    )
    
    PRINT 'Created [Staging].[DriverImport] table'
END
ELSE
BEGIN
    PRINT '[Staging].[DriverImport] table already exists'
END
GO