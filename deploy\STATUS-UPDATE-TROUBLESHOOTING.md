# Status Update Troubleshooting Guide

## Problem Description
The migration status is stuck and not updating properly on remote servers. The web interface shows "Migration status unclear" or the status remains at "Starting..." without progress updates.

## Root Causes

### 1. Logging Disabled
**Issue**: `stdoutLogEnabled="false"` in web.config prevents proper logging
**Impact**: SignalR communication fails, status updates don't work
**Fix**: Enable logging with `stdoutLogEnabled="true"`

### 2. Wrong Hosting Model
**Issue**: Using `inprocess` hosting model instead of `outofprocess`
**Impact**: SignalR WebSocket connections fail
**Fix**: Change to `hostingModel="outofprocess"`

### 3. Missing WebSocket Support
**Issue**: WebSocket not enabled in IIS
**Impact**: SignalR cannot establish real-time connections
**Fix**: Add `<webSocket enabled="true" />` to web.config

### 4. Permission Issues
**Issue**: Logs directory not writable by IIS_IUSRS
**Impact**: Application cannot write logs, causing failures
**Fix**: Set proper permissions on logs directory

## Quick Fix Script

Run the automated fix script:

```powershell
# Navigate to deploy directory
cd deploy

# Run the fix script
.\fix-remote-status.ps1 -ServerName "your-server-name" -DeployPath "C:\inetpub\wwwroot\XQ360Migration"
```

## Manual Fix Steps

### Step 1: Update web.config
Replace your current web.config with the corrected version:

```xml
<aspNetCore processPath="dotnet" 
            arguments=".\XQ360.DataMigration.Web.dll" 
            stdoutLogEnabled="true" 
            stdoutLogFile=".\logs\stdout" 
            hostingModel="outofprocess"
            forwardWindowsAuthToken="false">
  <environmentVariables>
    <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
    <environmentVariable name="ASPNETCORE_URLS" value="http://localhost:8080" />
    <environmentVariable name="DOTNET_ENVIRONMENT" value="Production" />
    <environmentVariable name="ASPNETCORE_LOGGING__CONSOLE__DISABLECOLORS" value="true" />
    <environmentVariable name="ASPNETCORE_LOGGING__CONSOLE__FORMAT" value="json" />
  </environmentVariables>
</aspNetCore>

<!-- Enable WebSockets for SignalR -->
<webSocket enabled="true" />
```

### Step 2: Create Logs Directory
```powershell
# Create logs directory
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration\logs" -Force

# Set permissions
$acl = Get-Acl "C:\inetpub\wwwroot\XQ360Migration\logs"
$rule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($rule)
Set-Acl "C:\inetpub\wwwroot\XQ360Migration\logs" $acl
```

### Step 3: Restart Application Pool
```powershell
Import-Module WebAdministration
Restart-WebAppPool -Name "XQ360MigrationPool"
```

## Verification Steps

### 1. Check Logs
Look for these log files:
- `C:\inetpub\wwwroot\XQ360Migration\logs\stdout_*.log`
- `C:\inetpub\wwwroot\XQ360Migration\Logs\xq360-migration-*.log`

### 2. Test SignalR Connection
Open browser console and check for:
- SignalR connection established
- No WebSocket errors
- Progress updates received

### 3. Monitor Network Tab
In browser DevTools → Network tab:
- Look for WebSocket connections to `/migrationHub`
- Check for successful SignalR handshake

## Common Error Messages

### "SignalR Connection Error"
**Cause**: WebSocket not enabled or wrong hosting model
**Fix**: Enable WebSocket and use outofprocess hosting

### "Migration status unclear"
**Cause**: No recent status updates received
**Fix**: Check logs for errors, restart application pool

### "Progress stuck at Starting..."
**Cause**: Migration process crashed or logging disabled
**Fix**: Enable logging, check application logs

## Debug Commands

### Check Application Pool Status
```powershell
Get-WebAppPool -Name "XQ360MigrationPool" | Select-Object Name, State, ProcessModel
```

### Check Logs Directory Permissions
```powershell
Get-Acl "C:\inetpub\wwwroot\XQ360Migration\logs" | Format-List
```

### Test WebSocket Support
```powershell
# Check if WebSocket module is installed
Get-WindowsFeature -Name Web-WebSockets
```

### Monitor Real-time Logs
```powershell
Get-Content "C:\inetpub\wwwroot\XQ360Migration\logs\stdout_*.log" -Wait
```

## Prevention

1. **Always use the corrected web.config** for remote deployments
2. **Test SignalR connection** after deployment
3. **Monitor logs** during first migration
4. **Use the automated fix script** for consistent deployments

## Support

If issues persist after applying these fixes:
1. Check the logs directory for error messages
2. Verify IIS configuration in IIS Manager
3. Test with a simple SignalR connection
4. Contact system administrator for IIS permissions 