using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Implementations
{
    public class WebsiteUserMigration
    {
        private readonly ILogger<WebsiteUserMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly XQ360ApiClient _apiClient;
        private readonly MigrationReportingService _reportingService;
        private readonly string _connectionString;

        // Cache for database lookups to avoid repeated queries
        private readonly Dictionary<string, Guid> _siteCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _dealerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _customerCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid> _departmentCache = new Dictionary<string, Guid>();
        private readonly Dictionary<string, Guid?> _websiteUserCache = new Dictionary<string, Guid?>();

        public WebsiteUserMigration(
            ILogger<WebsiteUserMigration> logger,
            IOptions<MigrationConfiguration> config,
            XQ360ApiClient apiClient,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _apiClient = apiClient;
            _reportingService = reportingService;
            _connectionString = _config.DatabaseConnection;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Website User migration using XQ360 API");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Validate and lookup database IDs
                await ValidateAndLookupDataAsync(data);

                // Step 3: Authenticate with API
                var authResult = await _apiClient.AuthenticateAsync();
                if (!authResult)
                {
                    return new MigrationResult
                    {
                        Success = false,
                        Errors = new List<string> { "Failed to authenticate with XQ360 API" },
                        Duration = DateTime.UtcNow - startTime
                    };
                }

                // Step 4: Execute API migration
                result.RecordsProcessed = data.Count;
                var migrationResult = await ExecuteWebsiteUserApiAsync(data, result);

                result.Success = migrationResult.Success;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;

                _logger.LogInformation($"Website User migration completed: {result.RecordsInserted} inserted, {result.RecordsSkipped} skipped, Duration: {result.Duration}");

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Website User Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Website User migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private Task<List<WebsiteUserImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Website User CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            // Configure CSV reader for better special character support
            csv.Context.Configuration.TrimOptions = CsvHelper.Configuration.TrimOptions.Trim;
            csv.Context.Configuration.BadDataFound = null; // Don't throw on bad data, log instead

            var records = new List<WebsiteUserImportModel>();
            
            try
            {
                foreach (var record in csv.GetRecords<WebsiteUserImportModel>())
                {
                    // Validate and clean special characters
                    if (!string.IsNullOrEmpty(record.FirstName))
                        record.FirstName = record.FirstName.Trim();
                    if (!string.IsNullOrEmpty(record.LastName))
                        record.LastName = record.LastName.Trim();
                    if (!string.IsNullOrEmpty(record.Username))
                        record.Username = record.Username.Trim();
                    if (!string.IsNullOrEmpty(record.Email))
                        record.Email = record.Email.Trim();

                    // Log any names with special characters for visibility
                    if (HasSpecialCharacters(record.FirstName) || HasSpecialCharacters(record.LastName))
                    {
                        _logger.LogInformation($"Processing user with special characters: '{record.FirstName}' '{record.LastName}' (Username: {record.Username})");
                    }

                    records.Add(record);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing CSV file. Check for special characters that may need proper quoting.");
                throw;
            }

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return Task.FromResult(records);
        }

        private bool HasSpecialCharacters(string? input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            // Check for common special characters in names
            return input.Any(c => !char.IsLetterOrDigit(c) && !char.IsWhiteSpace(c) && c != '-' && c != '\'');
        }

        private async Task ValidateAndLookupDataAsync(List<WebsiteUserImportModel> data)
        {
            _logger.LogInformation("Validating data and performing database lookups...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var errors = new List<string>();

            // Note: Duplicate username validation moved to execution phase for warning-based reporting

            // Get unique dealer-customer-site-department combinations and validate them
            var uniqueCombinations = data
                .Select(d => new { Dealer = d.Dealer, Customer = d.Customer, Site = d.Site, Department = d.DepartmentName, Role = d.Role })
                .Distinct()
                .ToList();

            foreach (var combo in uniqueCombinations)
            {
                try
                {
                    // Only validate dealer if role is DealerAdmin
                    if (combo.Role?.ToLowerInvariant() == "dealeradmin")
                    {
                        var dealerId = await GetDealerIdAsync(combo.Dealer ?? "Unknown", connection);
                        _logger.LogDebug($"Validated DealerAdmin: Dealer '{combo.Dealer}' -> Customer '{combo.Customer}' -> Site '{combo.Site}' -> Department '{combo.Department}'");
                    }
                    else
                    {
                        _logger.LogDebug($"Validated Customer: Customer '{combo.Customer}' -> Site '{combo.Site}' -> Department '{combo.Department}' (Dealer validation skipped)");
                    }
                    
                    var customerId = await GetCustomerIdByNameAsync(combo.Customer ?? "Unknown", connection);
                    var siteId = await GetSiteIdAsync(combo.Site ?? "Unknown", combo.Customer ?? "Unknown", connection);
                    var departmentId = await GetDepartmentIdAsync(combo.Department ?? "Unknown", combo.Site ?? "Unknown", combo.Customer ?? "Unknown", connection);
                }
                catch (Exception ex)
                {
                    errors.Add(ex.Message);
                }
            }

            if (errors.Any())
            {
                throw new InvalidOperationException($"Validation failed: {string.Join("; ", errors)}");
            }

            _logger.LogInformation("Data validation completed successfully");
        }

        private async Task<(Guid? PersonId, string ErrorMessage)> FindExistingPersonAsync(string firstName, string lastName, Guid departmentId, SqlConnection connection)
        {
            if (string.IsNullOrEmpty(firstName) || string.IsNullOrEmpty(lastName))
                return (null, null);

            var key = $"person|{firstName}|{lastName}|{departmentId}";
            // Note: Not using cache for person lookup since we now return a tuple with error info
            // and cache is optimized for simple Guid? values

            try
            {
                // First, count how many matches we have
                var countSql = @"
                    SELECT COUNT(*) 
                    FROM dbo.Person p
                    WHERE p.FirstName = @FirstName 
                    AND p.LastName = @LastName 
                    AND p.DepartmentId = @DepartmentId";

                using var countCmd = new SqlCommand(countSql, connection);
                countCmd.Parameters.AddWithValue("@FirstName", firstName);
                countCmd.Parameters.AddWithValue("@LastName", lastName);
                countCmd.Parameters.AddWithValue("@DepartmentId", departmentId);

                var count = (int)(await countCmd.ExecuteScalarAsync() ?? 0);

                if (count == 0)
                {
                    return (null, null); // No person found
                }
                else if (count > 1)
                {
                    var errorMsg = $"Found {count} persons with name '{firstName} {lastName}' in the same department - cannot determine which one to link";
                    return (null, errorMsg); // Ambiguous match
                }

                // Exactly one match - safe to proceed
                var sql = @"
                    SELECT p.Id 
                    FROM dbo.Person p
                    WHERE p.FirstName = @FirstName 
                    AND p.LastName = @LastName 
                    AND p.DepartmentId = @DepartmentId";

                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@FirstName", firstName);
                cmd.Parameters.AddWithValue("@LastName", lastName);
                cmd.Parameters.AddWithValue("@DepartmentId", departmentId);

                var result = await cmd.ExecuteScalarAsync();
                var personId = (Guid)(result ?? Guid.Empty);
                return (personId, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to find existing person: {firstName} {lastName}");
                return (null, $"Database error: {ex.Message}");
            }
        }

        private async Task<bool> CheckIfPersonHasWebsiteAccessAsync(Guid personId, SqlConnection connection)
        {
            var key = $"websiteaccess|{personId}";
            if (_websiteUserCache.ContainsKey(key))
                return _websiteUserCache[key] != null;

            try
            {
                var sql = @"
                    SELECT p.GOUserId 
                    FROM dbo.Person p
                    WHERE p.Id = @PersonId 
                    AND p.GOUserId IS NOT NULL";

                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@PersonId", personId);

                var result = await cmd.ExecuteScalarAsync();
                var hasAccess = result != null;
                
                // Cache the result
                _websiteUserCache[key] = hasAccess ? (Guid)(result ?? Guid.Empty) : null;
                
                return hasAccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to check if person has website access: {personId}");
                return false; // Assume no access on error to allow creation attempt
            }
        }

        private async Task<MigrationResult> ExecuteWebsiteUserApiAsync(List<WebsiteUserImportModel> data, MigrationResult sharedResult)
        {
            _logger.LogInformation("Executing Website User API migration...");

            var insertedCount = 0;
            var errors = new List<string>();
            var warnings = new List<string>();

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Track usernames within this CSV batch to detect intra-CSV duplicates
            var processedUsernames = new Dictionary<string, (string FirstName, string LastName)>(StringComparer.OrdinalIgnoreCase);

            foreach (var record in data)
            {
                try
                {
                    // Check if password is empty or null
                    if (string.IsNullOrWhiteSpace(record.Password))
                    {
                        var warningMsg = $"Password is empty or null for user '{record.Username}' ({record.FirstName} {record.LastName}) - skipping website user creation";
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, WarningTypes.VALIDATION_WARNING,
                            $"Password is empty or null for user '{record.Username}'",
                            "Username", record.Username,
                            "Provide a valid password for this user",
                            new Dictionary<string, string> 
                            {
                                { "FirstName", record.FirstName ?? "Unknown" },
                                { "LastName", record.LastName ?? "Unknown" },
                                { "Email", record.Email ?? "Unknown" }
                            });
                        warnings.Add(warningMsg);
                        _logger.LogWarning(warningMsg);
                        continue;
                    }

                    // Check for duplicate username within this CSV batch
                    if (processedUsernames.ContainsKey(record.Username))
                    {
                        var previousRecord = processedUsernames[record.Username];
                        var warningMsg = $"Duplicate username '{record.Username}' within CSV: '{previousRecord.FirstName} {previousRecord.LastName}' already processed, skipping '{record.FirstName} {record.LastName}'";
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.DUPLICATE_CARD,
                            $"Duplicate username '{record.Username}' within CSV file",
                            "Username", record.Username,
                            "Remove duplicate username from CSV or use unique usernames",
                            new Dictionary<string, string> 
                            {
                                { "FirstName", record.FirstName ?? "Unknown" },
                                { "LastName", record.LastName ?? "Unknown" },
                                { "PreviousRecord", $"{previousRecord.FirstName} {previousRecord.LastName}" }
                            });
                        warnings.Add(warningMsg);
                        _logger.LogWarning(warningMsg);
                        continue;
                    }

                    // Check for duplicate username across entire database
                    var existingUserId = await CheckIfUsernameExistsAsync(record.Username, connection);
                    if (existingUserId != null)
                    {
                        var warningMsg = $"Username '{record.Username}' already exists in database (ID: {existingUserId}) - skipping creation for '{record.FirstName} {record.LastName}'";
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, WarningTypes.EXISTING_RECORD_SKIPPED,
                            $"Username '{record.Username}' already exists in database",
                            "Username", record.Username,
                            "Use a different username or verify if this user should be updated",
                            new Dictionary<string, string> 
                            {
                                { "FirstName", record.FirstName ?? "Unknown" },
                                { "LastName", record.LastName ?? "Unknown" },
                                { "ExistingId", existingUserId.ToString() }
                            });
                        warnings.Add(warningMsg);
                        _logger.LogWarning(warningMsg);
                        continue;
                    }

                    // Add to processed usernames tracking
                    processedUsernames[record.Username] = (record.FirstName, record.LastName);

                    // Get required database IDs
                    var customerId = await GetCustomerIdByNameAsync(record.Customer ?? "Unknown", connection);
                    var siteId = await GetSiteIdAsync(record.Site ?? "Unknown", record.Customer ?? "Unknown", connection);
                    var departmentId = await GetDepartmentIdAsync(record.DepartmentName ?? "Unknown", record.Site ?? "Unknown", record.Customer ?? "Unknown", connection);

                    // Conditionally get dealer ID based on role
                    Guid? dealerId = null;
                    var isDealerAdmin = record.Role?.ToLowerInvariant() == "dealeradmin";
                    
                    if (isDealerAdmin)
                    {
                        dealerId = await GetDealerIdAsync(record.Dealer ?? "Unknown", connection);
                        _logger.LogInformation($"User {record.Username} will get DealerAdmin role (DealerId set)");
                    }
                    else
                    {
                        _logger.LogInformation($"User {record.Username} will get Customer role (DealerId not set)");
                    }

                    // Parse preferred locale
                    var preferredLocale = ParsePreferredLocale(record.PreferredLocale ?? string.Empty);

                    // Parse website access level
                    var websiteAccessLevel = ParseWebsiteAccessLevel(record.WebsiteAccessLevel ?? string.Empty);

                    // Validate access group exists in database and get its ID
                    var (accessGroupId, accessGroupWarning) = await ValidateAccessGroupExistsAsync(record.AccessGroup ?? "Unknown", record.Username, connection);

                    // Lookup existing person to link
                    var (existingPersonId, findError) = await FindExistingPersonAsync(
                        record.FirstName ?? "Unknown", 
                        record.LastName ?? "Unknown", 
                        departmentId, 
                        connection);

                    // Skip if person doesn't exist
                    if (existingPersonId == null)
                    {
                        string warningMsg;
                        if (!string.IsNullOrEmpty(findError))
                        {
                            // Ambiguous match or other error
                            warningMsg = $"{findError} - skipping website user creation for '{record.Username}'";
                        }
                        else
                        {
                            // Person not found
                            warningMsg = $"Person '{record.FirstName} {record.LastName}' not found in department '{record.DepartmentName}' - skipping website user creation for '{record.Username}'";
                        }
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, WarningTypes.MISSING_DEPENDENCY,
                            $"Person '{record.FirstName} {record.LastName}' not found in department '{record.DepartmentName}'",
                            "Person", $"{record.FirstName} {record.LastName}",
                            "Ensure person exists in the specified department or run Person migration first",
                            new Dictionary<string, string> 
                            {
                                { "Username", record.Username },
                                { "DepartmentName", record.DepartmentName },
                                { "Email", record.Email ?? "Unknown" }
                            });
                        warnings.Add(warningMsg);
                        _logger.LogWarning(warningMsg);
                        continue;
                    }

                    // Check if person already has website access
                    var hasExistingWebsiteAccess = await CheckIfPersonHasWebsiteAccessAsync(existingPersonId.Value, connection);
                    if (hasExistingWebsiteAccess)
                    {
                        var warningMsg = $"Person '{record.FirstName} {record.LastName}' already has website access - skipping website user creation for '{record.Username}'";
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, WarningTypes.EXISTING_RECORD_SKIPPED,
                            $"Person '{record.FirstName} {record.LastName}' already has website access",
                            "Username", record.Username,
                            "Person already has website access, no need to create duplicate",
                            new Dictionary<string, string> 
                            {
                                { "FirstName", record.FirstName },
                                { "LastName", record.LastName },
                                { "DepartmentName", record.DepartmentName }
                            });
                        warnings.Add(warningMsg);
                        _logger.LogWarning(warningMsg);
                        continue;
                    }

                    _logger.LogInformation($"Found existing person for {record.FirstName} {record.LastName}, proceeding to create linked website user {record.Username}");

                    // Skip if access group doesn't exist for this customer
                    if (!string.IsNullOrEmpty(accessGroupWarning) || accessGroupId == Guid.Empty)
                    {
                        if (!string.IsNullOrEmpty(accessGroupWarning))
                        {
                            _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, ErrorTypes.FOREIGN_KEY_CONSTRAINT,
                                accessGroupWarning,
                                "AccessGroup", record.AccessGroup,
                                "Check if AccessGroup exists for the specified Customer or verify AccessGroup name",
                                new Dictionary<string, string> 
                                {
                                    { "Username", record.Username },
                                    { "FirstName", record.FirstName },
                                    { "LastName", record.LastName },
                                    { "Customer", record.Customer ?? "Unknown" }
                                });
                            warnings.Add(accessGroupWarning);
                        }
                        continue; // Skip this record
                    }

                    // Convert CSV record to API entity format
                    var websiteUserEntity = new WebsiteUserApiEntity
                    {
                        IsNew = true, // Critical: Tell server this is a new user, not an update
                        UserName = record.Username,
                        EmailAddress = record.Email,
                        Password = record.Password, // Server will hash this
                        FirstName = record.FirstName,
                        LastName = record.LastName,
                        DealerId = dealerId, // Only set if role is DealerAdmin
                        PreferredLocale = preferredLocale,
                        WebsiteAccessLevel = websiteAccessLevel,
                        DealerAdmin = isDealerAdmin,
                        // Required fields with defaults
                        Active = true,
                        Language = "en-US"
                    };

                    _logger.LogInformation($"Creating website user: {record.Username} ({record.FirstName} {record.LastName})");
                    
                    // Serialize to JSON string for the entity parameter
                    var entityJson = JsonConvert.SerializeObject(websiteUserEntity);

                    // Create form data
                    var formData = new Dictionary<string, string>
                    {
                        ["entity"] = entityJson,
                        ["include"] = "Dealer" // Optional: include related data in response
                    };
                    
                    // Call the GOUser API using form-encoded data
                    var apiResult = await _apiClient.PostFormAsync<WebsiteUserApiResponse>("gouser", formData);

                    if (apiResult.Success)
                    {
                        // Additional validation for the API response
                        if (apiResult.Data == null)
                        {
                            var errorMsg = $"API returned null data for website user {record.Username}";
                            errors.Add(errorMsg);
                            _logger.LogWarning(errorMsg);
                        }
                        else 
                        {
                            // GOUser created successfully - now link it to the Person
                            var goUserId = await GetCreatedGoUserIdAsync(record.Username, connection);
                            if (goUserId != null && existingPersonId != null)
                            {
                                await LinkPersonToGoUserAsync(existingPersonId.Value, goUserId.Value, accessGroupId, connection);
                                _logger.LogInformation($"Successfully created and linked website user: {record.Username} to person {record.FirstName} {record.LastName}");
                            }
                            else
                            {
                                _logger.LogInformation($"Successfully created website user: {record.Username} (standalone - no person linking)");
                            }
                            
                            insertedCount++;
                        }
                    }
                    else
                    {
                        var errorMsg = $"Failed to create website user {record.Username}: {apiResult.ErrorMessage}";
                        errors.Add(errorMsg);
                        _logger.LogWarning(errorMsg);
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = $"Error processing website user {record.Username}: {ex.Message}";
                    errors.Add(errorMsg);
                    _logger.LogError(ex, errorMsg);
                }

                // Add small delay to avoid overwhelming the API
                await Task.Delay(100);
            }

            _logger.LogInformation($"Successfully created {insertedCount} website users via API");

            return new MigrationResult
            {
                Success = errors.Count == 0,
                RecordsInserted = insertedCount,
                Errors = errors,
                Warnings = warnings
            };
        }

        private async Task<Guid?> CheckIfUsernameExistsAsync(string username, SqlConnection connection)
        {
            if (string.IsNullOrEmpty(username))
                return null;

            var key = $"username|{username}";
            if (_websiteUserCache.ContainsKey(key))
                return _websiteUserCache[key];

            try
            {
                // Check for duplicate username across entire GOSecurity.GOUser table
                var sql = "SELECT Id FROM GOSecurity.GOUser WHERE UserName = @UserName";
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@UserName", username);

                var result = await cmd.ExecuteScalarAsync();
                if (result == null)
                {
                    _websiteUserCache[key] = null;
                    return null;
                }

                var userId = (Guid)result;
                _websiteUserCache[key] = userId;
                return userId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to check if username exists: {username}");
                return null;
            }
        }

        private async Task<Guid> GetDealerIdAsync(string dealerName, SqlConnection connection)
        {
            if (_dealerCache.ContainsKey(dealerName))
                return _dealerCache[dealerName];

            var sql = "SELECT Id FROM dbo.Dealer WHERE Name = @Name";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@Name", dealerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Dealer '{dealerName}' not found");

            var dealerId = (Guid)result;
            _dealerCache[dealerName] = dealerId;
            return dealerId;
        }

        private async Task<Guid> GetSiteIdAsync(string siteName, string customerName, SqlConnection connection)
        {
            var key = $"{siteName}|{customerName}";
            if (_siteCache.ContainsKey(key))
                return _siteCache[key];

            var customerId = await GetCustomerIdByNameAsync(customerName, connection);

            var sql = "SELECT Id FROM dbo.Site WHERE Name = @Name AND CustomerId = @CustomerId";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@Name", siteName);
            cmd.Parameters.AddWithValue("@CustomerId", customerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Site '{siteName}' not found for customer '{customerName}'");

            var siteId = (Guid)result;
            _siteCache[key] = siteId;
            return siteId;
        }

        private async Task<Guid> GetCustomerIdByNameAsync(string customerName, SqlConnection connection)
        {
            if (_customerCache.ContainsKey(customerName))
                return _customerCache[customerName];

            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found");

            var customerId = (Guid)result;
            _customerCache[customerName] = customerId;
            return customerId;
        }

        private async Task<Guid> GetDepartmentIdAsync(string departmentName, string siteName, string customerName, SqlConnection connection)
        {
            var key = $"{departmentName}|{siteName}|{customerName}";
            if (_departmentCache.ContainsKey(key))
                return _departmentCache[key];

            var siteId = await GetSiteIdAsync(siteName, customerName, connection);

            var sql = "SELECT Id FROM dbo.Department WHERE Name = @Name AND SiteId = @SiteId";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@Name", departmentName);
            cmd.Parameters.AddWithValue("@SiteId", siteId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Department '{departmentName}' not found in site '{siteName}' for customer '{customerName}'");

            var departmentId = (Guid)result;
            _departmentCache[key] = departmentId;
            return departmentId;
        }

        private int? ParsePreferredLocale(string localeString)
        {
            if (string.IsNullOrEmpty(localeString) || localeString == "-")
            {
                _logger.LogDebug("No preferred locale provided, using default: M/d/yyyy (0)");
                return 0; // Default to EnglishUnitedStates (M/d/yyyy)
            }

            // Map locale strings to their enum values based on LocaleEnum server implementation
            // LocaleEnum: EnglishUnitedStates = 0, EnglishUnitedKingdom = 1
            var localeValue = localeString.Trim() switch
            {
                // Date format strings (as shown in UI)
                "M/d/yyyy" => 0,        // EnglishUnitedStates
                "dd/MM/yyyy" => 1,      // EnglishUnitedKingdom
                
                // Language code alternatives (for convenience)
                "en-US" or "en-us" or "english (us)" or "english us" or "us" => 0,
                "en-GB" or "en-gb" or "english (uk)" or "english uk" or "uk" => 1,
                
                _ => (int?)null
            };

            if (localeValue == null)
            {
                _logger.LogWarning($"Unknown preferred locale '{localeString}', using default: M/d/yyyy (0). Valid options: 'M/d/yyyy' or 'dd/MM/yyyy'");
                return 0; // Default to EnglishUnitedStates for unknown locales
            }

            return localeValue;
        }

        private async Task<(Guid AccessGroupId, string WarningMessage)> ValidateAccessGroupExistsAsync(string accessGroup, string username, SqlConnection connection)
        {
            try
            {
                if (string.IsNullOrEmpty(accessGroup))
                {
                    var warningMsg = $"User '{username}': No access group provided - skipping creation";
                    _logger.LogWarning($"No access group provided for user {username}, skipping creation");
                    return (Guid.Empty, warningMsg);
                }

                var trimmedAccessGroup = accessGroup.Trim();

                // Check if the access group exists in database (case-insensitive)
                var sql = @"
                    SELECT TOP 1 Id, Name 
                    FROM dbo.AccessGroup 
                    WHERE Name = @Name 
                    ORDER BY Name";

                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@Name", trimmedAccessGroup);

                using var reader = await cmd.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    var accessGroupId = reader.GetGuid(0);
                    var actualName = reader.GetString(1);
                    
                    // Log case correction if needed
                    if (actualName != trimmedAccessGroup)
                    {
                        _logger.LogDebug($"User '{username}': Access group case corrected from '{trimmedAccessGroup}' to '{actualName}'");
                    }
                    
                    return (accessGroupId, null); // Return the AccessGroup ID
                }

                // Access group doesn't exist in database
                var invalidWarning = $"User '{username}': Access group '{accessGroup}' not found in database - skipping creation";
                _logger.LogWarning($"Access group '{accessGroup}' not found in database, skipping user {username}");
                return (Guid.Empty, invalidWarning);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to validate access group for user {username}");
                var errorWarning = $"User '{username}': Failed to validate access group - skipping creation";
                return (Guid.Empty, errorWarning);
            }
        }

        private async Task<Guid?> GetCreatedGoUserIdAsync(string username, SqlConnection connection)
        {
            try
            {
                var sql = "SELECT Id FROM GOSecurity.GOUser WHERE UserName = @UserName";
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@UserName", username);

                var result = await cmd.ExecuteScalarAsync();
                return result as Guid?;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get created GOUser ID for username: {username}");
                return null;
            }
        }

        private async Task LinkPersonToGoUserAsync(Guid personId, Guid goUserId, Guid accessGroupId, SqlConnection connection)
        {
            try
            {
                // Update Person with GOUserId and AccessGroupId
                var sql = "UPDATE dbo.Person SET GOUserId = @GOUserId, AccessGroupId = @AccessGroupId WHERE Id = @PersonId";
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@GOUserId", goUserId);
                cmd.Parameters.AddWithValue("@AccessGroupId", accessGroupId);
                cmd.Parameters.AddWithValue("@PersonId", personId);

                var rowsAffected = await cmd.ExecuteNonQueryAsync();
                if (rowsAffected > 0)
                {
                    _logger.LogDebug($"Successfully linked Person {personId} to GOUser {goUserId} with AccessGroupId {accessGroupId}");
                }
                else
                {
                    _logger.LogWarning($"Failed to link Person {personId} to GOUser {goUserId} - no rows updated");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to link Person {personId} to GOUser {goUserId} with AccessGroupId {accessGroupId}");
                throw; // Re-throw to handle this as an error in the calling method
            }
        }

        private int ParseWebsiteAccessLevel(string accessLevelString)
        {
            if (string.IsNullOrEmpty(accessLevelString))
                return 0; // Default to Department level

            // Map access level strings to their enum values
            // WebsiteAccessLevelEnum: Department = 0, Site = 1, Customer = 2
            return accessLevelString.ToLowerInvariant() switch
            {
                "department" => 0,  // Lowest access level - Department-level permissions
                "site" => 1,        // Site-level permissions - Higher than Department
                "customer" => 2,    // Highest access level - Customer-wide permissions
                _ => 0 // Default to Department level
            };
        }
    }

    // API entity models for Website User API
    public class WebsiteUserApiEntity
    {
        public bool IsNew { get; set; } // Required to indicate this is a new entity, not an update
        public required string UserName { get; set; }
        public required string EmailAddress { get; set; }
        public required string Password { get; set; }
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public Guid? DealerId { get; set; } // Nullable - only set for DealerAdmin role
        public int? PreferredLocale { get; set; }
        public int WebsiteAccessLevel { get; set; }
        public bool DealerAdmin { get; set; }
        public bool Active { get; set; }
        public required string Language { get; set; }
    }

    public class WebsiteUserApiResponse
    {
        public int InternalObjectId { get; set; }
        public required string PrimaryKey { get; set; }
        
        private object _objectsDataSet = new object();
        public object ObjectsDataSet 
        { 
            get => _objectsDataSet; 
            set => _objectsDataSet = value;
        }
        
        // Helper method to check if ObjectsDataSet is valid
        public bool IsObjectsDataSetValid()
        {
            return ObjectsDataSet != null;
        }
    }

    public class AccessGroupInfo
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
    }
} 