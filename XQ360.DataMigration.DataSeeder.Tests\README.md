# DataSeeder Test Suite Documentation

## Overview

This test suite provides comprehensive testing for the DataSeeder implementation in the XQ360 Data Migration system. The tests cover unit testing, integration testing, performance testing, and load testing for all DataSeeder services.

## Test Project Structure

```
XQ360.DataMigration.DataSeeder.Tests/
├── BulkSeederServiceTests.cs              # Unit tests for BulkSeederService
├── MigrationPatternSeederServiceTests.cs  # Unit tests for MigrationPatternSeederService
├── SqlDataGenerationServiceTests.cs       # Unit tests for SqlDataGenerationService
├── DataSeederIntegrationTests.cs          # Integration tests for service interactions
├── DataSeederPerformanceTests.cs          # Performance and load tests
├── TestConfigurationHelper.cs             # Test configuration utilities
├── appsettings.json                        # Test configuration settings
├── XQ360.DataMigration.DataSeeder.Tests.csproj  # Project file
└── README.md                               # This documentation
```

## Test Categories

### 1. Unit Tests

#### BulkSeederServiceTests.cs
- **Purpose**: Tests the core BulkSeederService functionality
- **Coverage**: 
  - Constructor validation
  - ExecuteSeederAsync method with various options
  - Error handling scenarios
  - Edge cases (zero counts, null values)
  - Cancellation token handling
  - Progress notification verification
- **Key Test Methods**:
  - `ExecuteSeederAsync_WithValidOptions_ShouldReturnSuccessResult`
  - `ExecuteSeederAsync_WithDriversCount_ShouldCallGenerateDriverDataAsync`
  - `ExecuteSeederAsync_WhenSqlDataServiceThrows_ShouldReturnFailureResult`

#### MigrationPatternSeederServiceTests.cs
- **Purpose**: Tests the enhanced MigrationPatternSeederService
- **Coverage**:
  - Person/Driver seeding via API
  - Vehicle seeding with complex entity creation
  - Card/Access permission creation
  - Full migration pattern execution
  - Validation methods
  - Error handling and recovery
- **Key Test Methods**:
  - `ExecutePersonDriverSeederAsync_WithValidOptions_ShouldReturnSuccessResult`
  - `ExecuteFullMigrationPatternAsync_WhenPersonCreationFails_ShouldStopAndReturnFailure`
  - `ValidateMigrationPatternPrerequisitesAsync_WithValidSetup_ShouldReturnValidResult`

#### SqlDataGenerationServiceTests.cs
- **Purpose**: Tests the SQL data generation service
- **Coverage**:
  - Driver data generation
  - Vehicle data generation
  - Staged data validation
  - Data processing operations
  - Parameter validation
  - Cancellation handling
- **Key Test Methods**:
  - `GenerateDriverDataAsync_WithValidParameters_ShouldReturnSuccessResult`
  - `GenerateVehicleDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows`
  - `ProcessStagedDataAsync_WithValidParameters_ShouldReturnProcessingResult`

### 2. Integration Tests

#### DataSeederIntegrationTests.cs
- **Purpose**: Tests service interactions and dependency injection
- **Coverage**:
  - Service integration with real dependency injection
  - SignalR notification integration
  - Configuration integration
  - Error handling across service boundaries
  - Cancellation propagation
- **Key Test Methods**:
  - `BulkSeederService_IntegratedWithSqlDataService_ShouldWorkTogether`
  - `MigrationPatternSeederService_ShouldSendSignalRNotifications`
  - `ServiceProvider_ShouldResolveAllRequiredServices`

### 3. Performance Tests

#### DataSeederPerformanceTests.cs
- **Purpose**: Tests performance characteristics and load handling
- **Coverage**:
  - Performance benchmarks for different load sizes
  - Memory usage validation
  - Concurrent operation handling
  - Rate limiting verification
  - Stress testing with repeated operations
- **Key Test Methods**:
  - `BulkSeederService_SmallLoad_ShouldCompleteWithinTimeLimit`
  - `MigrationPatternSeederService_ApiCalls_ShouldRespectRateLimit`
  - `BulkSeederService_ConcurrentRequests_ShouldHandleCorrectly`

## Test Configuration

### appsettings.json
The test configuration includes:
- **Migration environments**: Development, Pilot, US, UK, AU
- **BulkSeeder configuration**: Batch sizes, timeouts, performance settings
- **Logging configuration**: Console and file logging for test execution

### TestConfigurationHelper.cs
Provides utility methods for:
- Environment configuration setup
- Test option creation
- Configuration retrieval
- Mock data generation

## Running the Tests

### Prerequisites
1. .NET 9.0 SDK installed
2. Visual Studio 2022 or VS Code with C# extension
3. SQL Server access (for integration tests)
4. XQ360 API access (for API integration tests)

### Command Line Execution

```bash
# Run all tests
dotnet test XQ360.DataMigration.DataSeeder.Tests

# Run specific test category
dotnet test XQ360.DataMigration.DataSeeder.Tests --filter "Category=Unit"
dotnet test XQ360.DataMigration.DataSeeder.Tests --filter "Category=Integration"
dotnet test XQ360.DataMigration.DataSeeder.Tests --filter "Category=Performance"

# Run with detailed output
dotnet test XQ360.DataMigration.DataSeeder.Tests --logger "console;verbosity=detailed"

# Run with code coverage
dotnet test XQ360.DataMigration.DataSeeder.Tests --collect:"XPlat Code Coverage"
```

### Visual Studio Execution
1. Open the solution in Visual Studio
2. Build the solution (Ctrl+Shift+B)
3. Open Test Explorer (Test → Test Explorer)
4. Run all tests or select specific tests to run

## Test Environment Setup

### Development Environment
- Uses local SQL Server instance
- Mock API services for external dependencies
- In-memory SignalR hub for notifications

### CI/CD Environment
- Requires test database setup
- Environment variables for connection strings
- Mock services for external API dependencies

### Database Requirements
For integration tests that require database access:
```sql
-- Ensure test database exists
CREATE DATABASE FleetXQTest;

-- Grant appropriate permissions
GRANT ALL ON FleetXQTest TO [test-user];
```

## Mocking Strategy

### External Dependencies
- **IApiOrchestrationService**: Mocked to simulate API responses
- **IComplexEntityCreationService**: Mocked for entity creation operations
- **IStagingSchemaService**: Mocked for schema operations
- **IHubContext<MigrationHub>**: Mocked for SignalR notifications

### Database Operations
- Unit tests: Fully mocked to avoid database dependencies
- Integration tests: Use test database or in-memory database
- Performance tests: Mocked with realistic delays

## Expected Test Results

### Unit Tests
- **Total Tests**: ~50 tests
- **Expected Pass Rate**: 100%
- **Execution Time**: < 30 seconds

### Integration Tests
- **Total Tests**: ~15 tests
- **Expected Pass Rate**: 95% (some may fail without proper database setup)
- **Execution Time**: < 60 seconds

### Performance Tests
- **Total Tests**: ~10 tests
- **Expected Pass Rate**: 90% (performance may vary by environment)
- **Execution Time**: < 120 seconds

## Troubleshooting

### Common Issues

#### Database Connection Failures
```
Error: Cannot connect to database
Solution: Update connection string in appsettings.json or use mock services
```

#### API Service Failures
```
Error: API orchestration service failed
Solution: Ensure API endpoints are accessible or use mocked services
```

#### SignalR Hub Failures
```
Error: SignalR hub context not available
Solution: Verify hub context mocking in test setup
```

### Debug Configuration
Enable detailed logging by updating appsettings.json:
```json
{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug"
    }
  }
}
```

## Maintenance Guidelines

### Adding New Tests
1. Follow existing naming conventions
2. Use appropriate test categories (Unit, Integration, Performance)
3. Include comprehensive assertions
4. Add proper documentation

### Updating Existing Tests
1. Maintain backward compatibility
2. Update related documentation
3. Verify all test categories still pass
4. Update performance benchmarks if needed

### Test Data Management
1. Use TestConfigurationHelper for consistent test data
2. Avoid hardcoded values in tests
3. Clean up test data after execution
4. Use realistic but safe test data

## Continuous Integration

### Build Pipeline
```yaml
# Example Azure DevOps pipeline
- task: DotNetCoreCLI@2
  displayName: 'Run DataSeeder Tests'
  inputs:
    command: 'test'
    projects: 'XQ360.DataMigration.DataSeeder.Tests/*.csproj'
    arguments: '--configuration Release --collect:"XPlat Code Coverage"'
```

### Quality Gates
- Minimum 80% code coverage
- All unit tests must pass
- Performance tests must meet benchmarks
- No critical security vulnerabilities

## Reporting

### Test Results
Test results are automatically generated and include:
- Pass/fail status for each test
- Execution time metrics
- Code coverage reports
- Performance benchmark results

### Coverage Reports
Generate coverage reports using:
```bash
dotnet test --collect:"XPlat Code Coverage"
reportgenerator -reports:"coverage.cobertura.xml" -targetdir:"coverage-report"
```

## Support

For questions or issues with the test suite:
1. Check this documentation first
2. Review test logs for specific error messages
3. Consult the main project documentation
4. Contact the development team

---

**Last Updated**: 2025-08-16
**Version**: 1.0.0
**Maintainer**: XQ360 Development Team
