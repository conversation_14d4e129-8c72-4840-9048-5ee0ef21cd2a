# XQ360 Data Migration Tool

A comprehensive .NET migration application for XQ360 system data migration with bulletproof ordering guarantee. Now available with both CLI and GUI interfaces.

## 🎨 **NEW: Graphical User Interface**

The migration tool now includes a modern WPF-based GUI that provides:
- ✅ **Multiple Choice Data Selection**: Choose exactly which data types to migrate
- ✅ **Environment Selection**: Target US, UK, AU, Pilot, or Development environments  
- ✅ **CSV File Management**: Easy browse and upload functionality
- ✅ **Real-time Progress Tracking**: Visual progress indicators and live logs
- ✅ **Detailed Results Display**: Success/failure information with comprehensive reporting
- ✅ **User Manual Integration**: Built-in documentation access

### Quick Start - GUI Mode
```bash
cd XQ360.DataMigration
dotnet run
```
*Use the web interface for all migration operations*

### Quick Start - CLI Mode (Legacy)
```bash
cd XQ360.DataMigration
dotnet run --migrate-all
```

## Features

### 🔒 Bulletproof Ordering Guarantee System
- **Dependency Management**: Enforces critical execution order across 9 migration steps
- **Atomic Operations**: Combined migrations (Cards + Vehicle Access) run as single transactions
- **Error Handling**: Stops on first failure to preserve data integrity
- **Comprehensive Validation**: Prerequisites checked before execution

### 📊 Supported Migration Types

1. **🔧 Spare Modules (API)** - IoT hub integration
2. **📋 PreOp Checklist (SQL)** - Department checklist + questions  
3. **🚗 Vehicles (SQL)** - Vehicle data import
4. **👥 Persons (API)** - Creates driver entities
5. **💳 Cards + Vehicle Access (SQL)** - Atomic card and access operation
6. **👮 Supervisor Access (SQL)** - Enhanced permissions
7. **🚫 Driver Blacklist (SQL)** - Removes access rights
8. **🌐 Website Users (API)** - User accounts with roles
9. **🔄 Vehicle Sync Settings (API)** - IoT device synchronization

### 🌍 Multi-Environment Support
- **US** - United States production environment
- **UK** - United Kingdom production environment  
- **AU** - Australia production environment
- **Development** - Development environment
- **Pilot** - Testing/validation environment

## Getting Started

### Prerequisites
- **.NET 9.0** or later
- **Windows OS** (for GUI mode)
- **SQL Server** access for target environment
- **XQ360 API** credentials for target environment

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd XQ360.DataMigration
   ```

2. **Configure settings**
   - Copy `server-appsettings-template.json` to `appsettings.json`
   - Update database connection strings and API credentials
   - Ensure target environment configuration is correct

3. **Prepare CSV files**
   - Place your CSV files in the `CSV_Input/` directory
   - Or use the GUI browse functionality to select files

### Usage

#### Graphical Interface (Recommended)

1. **Start the GUI**
   ```bash
   cd XQ360.DataMigration
   dotnet run
   ```
   *Use the web interface: `cd XQ360.DataMigration.Web && dotnet run`*

2. **Configure Migration**
   - Select destination environment (US/UK/AU/Local/Pilot/Development)
   - Choose which data types to migrate
   - Browse and upload required CSV files

3. **Execute Migration**
   - Click "Validate Prerequisites" to check readiness
   - Click "Start Migration" to begin process
   - Monitor progress in real-time

4. **Review Results**
   - Check the results panel for success/failure status
   - Access detailed reports via "View Reports" button
   - Review logs for troubleshooting

#### Command Line Interface

```bash
# Full migration (all 9 steps)
dotnet run --migrate-all

# Individual migrations
dotnet run --migrate-spare-modules
dotnet run --migrate-preop-checklist
dotnet run --migrate-vehicles
dotnet run --migrate-persons
dotnet run --migrate-cards-and-vehicle-access
dotnet run --migrate-supervisor-access
dotnet run --migrate-driver-blacklist
dotnet run --migrate-website-users
dotnet run --sync-vehicle-settings

# Help
dotnet run --help
```

## CSV File Requirements

Each migration type requires specific CSV format:

| Migration Type | Required CSV File |
|----------------|-------------------|
| Spare Modules | `SPARE_MODEL_IMPORT_TEMPLATE.csv` |
| PreOp Checklist | `PREOP_CHECKLIST_IMPORT.csv` |
| Vehicles | `VEHICLE_IMPORT.csv` |
| Persons | `PERSON_IMPORT_TEMPLATE.csv` |
| Cards + Vehicle Access | `CARD_IMPORT.csv` |
| Supervisor Access | `SUPERVISOR_ACCESS_IMPORT.csv` |
| Driver Blacklist | `DRIVER_BLACKLIST_IMPORT.csv` |
| Website Users | `WEBSITE_USER_IMPORT.csv` |
| Vehicle Sync | `VEHICLE_IMPORT.csv` (reused) |

## Build and Test

### Build the Application
```bash
dotnet build
```

### Run Tests
```bash
cd XQ360.DataMigration.Tests
dotnet test
```

### Create Release Build
```bash
dotnet publish -c Release -o publish/
```

## Architecture

### Technology Stack
- **.NET 9.0** with WPF for GUI
- **Entity Framework Core** for database operations
- **Serilog** for comprehensive logging
- **CsvHelper** for CSV file processing
- **HttpClient** for API communications

### Key Components
- **MigrationOrchestrator**: Coordinates execution order and dependencies
- **Migration Implementations**: Individual migration logic for each data type
- **MigrationReportingService**: Generates detailed reports and statistics
- **XQ360ApiClient**: Handles API communications with XQ360 systems
- **PermissionService**: Manages access control and validation

## Configuration

### appsettings.json Structure
```json
{
  "Migration": {
    "DatabaseConnection": "Server=...;Database=...;",
    "ApiBaseUrl": "https://api.xq360.com",
    "ApiUsername": "your-username",
    "ApiPassword": "your-password",
    "BatchSize": 100,
    "MaxRetryAttempts": 3,
    "BackupEnabled": true,
    "ValidateBeforeMigration": true,
    "ContinueOnError": false
  }
}
```

## Logging and Reporting

### Log Files
- **Console Logs**: Real-time progress and status
- **File Logs**: Detailed technical logs in `Logs/` directory
- **Migration Reports**: Comprehensive results in `Reports/` directory

### Report Types
- **Full Migration Report**: Record-level details for all processed data
- **Summary Report**: High-level statistics and completion status
- **Error Report**: Detailed error analysis with recommendations

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Verify database connection strings
   - Check API credentials and network access
   - Ensure target environment is accessible

2. **CSV Format Errors**
   - Validate CSV file structure matches expected format
   - Check for required columns and data types
   - Ensure proper encoding (UTF-8)

3. **Dependency Violations**
   - Follow the correct migration order (1-9)
   - Ensure prerequisite migrations completed successfully
   - Check for missing reference data

### Getting Help
- Review the `USER-MANUAL.md` for detailed operational guidance
- Check `README-UI.md` for GUI-specific instructions
- Examine log files for detailed error information
- Contact support with log files and error messages

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/improvement`)
3. Commit your changes (`git commit -am 'Add improvement'`)
4. Push to the branch (`git push origin feature/improvement`)
5. Create a Pull Request

## License

This project is proprietary software for XQ360 data migration operations.

## Support

For technical support and questions:
- Review documentation in the repository
- Check existing issues and solutions
- Contact the development team with detailed error logs