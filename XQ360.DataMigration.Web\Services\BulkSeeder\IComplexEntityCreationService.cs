using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service for creating complex entities following migration patterns
/// Part of Phase 2.2: Complex Entity Creation Sequences
/// </summary>
public interface IComplexEntityCreationService
{
    /// <summary>
    /// Creates vehicles following the migration pattern sequence:
    /// ChecklistSettings → VehicleOtherSettings → Vehicle creation
    /// </summary>
    /// <param name="sessionId">Session ID for operation tracking</param>
    /// <param name="vehicleRequests">Vehicle creation requests</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Results with created vehicle details</returns>
    Task<ComplexEntityResult> CreateVehicleBatchAsync(
        Guid sessionId,
        IEnumerable<VehicleCreateRequest> vehicleRequests,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates cards and access permissions for drivers
    /// Generates 4-tier access permissions (Site/Department/Model/Vehicle)
    /// </summary>
    /// <param name="sessionId">Session ID for operation tracking</param>
    /// <param name="accessRequests">Card and access creation requests</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Results with created cards and permissions</returns>
    Task<ComplexEntityResult> CreateCardAccessPermissionsBatchAsync(
        Guid sessionId,
        IEnumerable<CardAccessCreateRequest> accessRequests,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates and allocates modules to vehicles
    /// Prevents double-allocation and updates module status
    /// </summary>
    /// <param name="sessionId">Session ID for operation tracking</param>
    /// <param name="allocationRequests">Module allocation requests</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Results with allocation status</returns>
    Task<ComplexEntityResult> AllocateModulesBatchAsync(
        Guid sessionId,
        IEnumerable<ModuleAllocationRequest> allocationRequests,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Request for Vehicle creation with dependencies
/// </summary>
public class VehicleCreateRequest
{
    public required string SerialNo { get; set; }
    public required string HireNo { get; set; }
    public int? IdleTimer { get; set; }
    public bool OnHire { get; set; }
    public bool ImpactLockout { get; set; }
    public bool TimeoutEnabled { get; set; }
    public bool IsCanbus { get; set; }
    public Guid ModelId { get; set; }
    public Guid SiteId { get; set; }
    public Guid DepartmentId { get; set; }
    public Guid CustomerId { get; set; }
    public string? ModuleIoTDevice { get; set; }
    public ChecklistType ChecklistType { get; set; } = ChecklistType.TimeBased;
}

/// <summary>
/// Request for Card and Access permission creation
/// </summary>
public class CardAccessCreateRequest
{
    public Guid DriverId { get; set; }
    public required string WeigandNumber { get; set; }
    public AccessLevel AccessLevel { get; set; }
    public Guid SiteId { get; set; }
    public Guid DepartmentId { get; set; }
    public List<Guid> VehicleIds { get; set; } = new();
    public List<Guid> ModelIds { get; set; } = new();
}

/// <summary>
/// Request for Module allocation
/// </summary>
public class ModuleAllocationRequest
{
    public required string IoTDevice { get; set; }
    public Guid VehicleId { get; set; }
    public bool ValidateAvailability { get; set; } = true;
}

/// <summary>
/// Result for complex entity operations
/// </summary>
public class ComplexEntityResult
{
    public bool Success { get; set; }
    public int TotalRequests { get; set; }
    public int SuccessfulRequests { get; set; }
    public int FailedRequests { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public TimeSpan Duration { get; set; }
    public Dictionary<string, object> Results { get; set; } = new();
}

/// <summary>
/// Checklist type for vehicles
/// </summary>
public enum ChecklistType
{
    TimeBased,
    DriverBased
}

/// <summary>
/// Access level for card permissions
/// </summary>
public enum AccessLevel
{
    Site,
    Department,
    Model,
    Vehicle
}
