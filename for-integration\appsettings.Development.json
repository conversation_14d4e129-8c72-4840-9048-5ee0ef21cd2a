{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ_Dev;Trusted_Connection=true;MultipleActiveResultSets=true;", "FleetXQConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ_Dev;Trusted_Connection=true;MultipleActiveResultSets=true;"}, "BulkImporter": {"DefaultDriversCount": 100, "DefaultVehiclesCount": 50, "DefaultBatchSize": 100, "MaxBatchSize": 1000, "ValidationEnabled": true, "StopOnFirstError": true, "CleanupStagingData": false, "DealerValidationEnabled": true, "RequireDealerSelection": false}, "Serilog": {"MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/bulkimporter-dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7}}], "Properties": {"Application": "FleetXQ.BulkImporter", "Environment": "Development"}}, "Environment": {"Name": "Development", "Description": "Local development environment", "RequiresApproval": false, "MaxOperationSize": 10000, "NotificationWebhooks": []}}