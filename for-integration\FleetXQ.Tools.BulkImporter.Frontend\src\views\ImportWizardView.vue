<template>
  <div class="container-fluid py-4">
    <div class="row">
      <!-- Main Form Column -->
      <div class="col-lg-8">
        <!-- Import Progress Tracker (shown when import is active) -->
        <div v-if="activeSessionId" class="mb-4">
          <ProgressTracker 
            :session-id="activeSessionId"
            :show-actions="true"
            @cancel-requested="onImportCancelled"
            @status-changed="onStatusChanged"
          />
        </div>

        <!-- Single-Page Import Form -->
        <div class="card minimal-card import-form-card">
          <div class="card-header">
            <h4 class="card-title mb-0">
              <i class="fas fa-upload me-2"></i>
              Bulk Import Configuration
            </h4>
            <p class="text-muted mb-0">Configure your bulk import operation by filling out all required fields below</p>
          </div>
          
          <div class="card-body">
            <form @submit.prevent="startImport">
              <!-- Environment Selection Section -->
              <div class="form-section mb-4">
                <div class="section-header mb-3">
                  <h5 class="section-title">
                    <i class="fas fa-server me-2"></i>
                    Environment
                  </h5>
                </div>

                <EnvironmentSelector 
                  v-model="selectedEnvironment"
                  @validation-change="(valid) => updateValidation('environment', valid)"
                />
              </div>

              <!-- Dealer Selection Section -->
              <div class="form-section mb-4">
                <div class="section-header mb-3">
                  <h5 class="section-title">
                    <i class="fas fa-building me-2"></i>
                    Dealer
                  </h5>
                  <p class="text-muted mb-0">Select an existing dealer from the system</p>
                </div>

                <DealerSelector 
                  v-model="selectedDealer"
                  @validation-change="(valid) => updateValidation('dealer', valid)"
                />
              </div>

              <!-- Customer Selection Section -->
              <div class="form-section mb-4">
                <div class="section-header mb-3">
                  <h5 class="section-title">
                    <i class="fas fa-users me-2"></i>
                    Customer
                  </h5>
                  <p class="text-muted mb-0">Select an existing customer or create a new one</p>
                </div>
                
                <CustomerSelector 
                  v-model="selectedCustomer"
                  :dealer-id="selectedDealer?.id"
                  @validation-change="(valid) => updateValidation('customer', valid)"
                />
              </div>

              <!-- Count Inputs Section -->
              <div class="form-section mb-4">
                <div class="section-header mb-3">
                  <h5 class="section-title">
                    <i class="fas fa-calculator me-2"></i>
                    Import Quantities
                  </h5>
                  <p class="text-muted mb-0">Specify the number of vehicles and drivers to import</p>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <VehicleCountInput 
                      v-model="vehicleCount"
                      :environment-limit="selectedEnvironment?.maxOperationSize"
                      @validation-change="(valid) => updateValidation('vehicleCount', valid)"
                    />
                  </div>

                  <div class="col-md-6 mb-3">
                    <DriverCountInput 
                      v-model="driverCount"
                      :vehicle-count="vehicleCount"
                      :environment-limit="selectedEnvironment?.maxOperationSize"
                      @validation-change="(valid) => updateValidation('driverCount', valid)"
                    />
                  </div>
                </div>
              </div>

              <!-- Submit Section -->
              <div class="form-section">
                <div class="d-flex justify-content-end">
                  <button 
                    type="submit"
                    class="btn btn-success btn-lg" 
                    :disabled="!canStartImport || isImporting"
                  >
                    <span v-if="isImporting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    <i v-else class="fas fa-play me-2"></i>
                    {{ isImporting ? 'Starting Import...' : 'Start Bulk Import' }}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Summary Sidebar -->
      <div class="col-lg-4">
        <div class="sticky-top" style="top: 2rem;">
          <!-- Configuration Summary -->
          <div class="card summary-card mb-3">
            <div class="card-header">
              <h6 class="card-title mb-0">
                <i class="fas fa-list-check me-2"></i>
                Configuration Summary
              </h6>
            </div>
            <div class="card-body">
              <div class="summary-items">
                <!-- Environment Summary -->
                <div class="summary-item" :class="{ 'completed': selectedEnvironment }">
                  <div class="summary-icon">
                    <i class="fas fa-server"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-label">Environment</div>
                    <div class="summary-value" v-if="selectedEnvironment">
                      <span class="badge bg-primary">{{ selectedEnvironment.name.toUpperCase() }}</span>
                    </div>
                    <div class="summary-value text-muted" v-else>Not selected</div>
                  </div>
                </div>

                <!-- Dealer Summary -->
                <div class="summary-item" :class="{ 'completed': selectedDealer }">
                  <div class="summary-icon">
                    <i class="fas fa-building"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-label">Dealer</div>
                    <div class="summary-value" v-if="selectedDealer">
                      {{ selectedDealer.name }}
                    </div>
                    <div class="summary-value text-muted" v-else>Not selected</div>
                  </div>
                </div>

                <!-- Customer Summary -->
                <div class="summary-item" :class="{ 'completed': selectedCustomer }">
                  <div class="summary-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-label">Customer</div>
                    <div class="summary-value" v-if="selectedCustomer">
                      {{ selectedCustomer.name }}
                    </div>
                    <div class="summary-value text-muted" v-else>Not selected</div>
                  </div>
                </div>

                <!-- Vehicle Count Summary -->
                <div class="summary-item" :class="{ 'completed': vehicleCount }">
                  <div class="summary-icon">
                    <i class="fas fa-car"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-label">Vehicles</div>
                    <div class="summary-value" v-if="vehicleCount">
                      <span class="fw-bold text-primary">{{ vehicleCount.toLocaleString() }}</span>
                    </div>
                    <div class="summary-value text-muted" v-else>Not specified</div>
                  </div>
                </div>

                <!-- Driver Count Summary -->
                <div class="summary-item" :class="{ 'completed': driverCount }">
                  <div class="summary-icon">
                    <i class="fas fa-id-card"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-label">Drivers</div>
                    <div class="summary-value" v-if="driverCount">
                      <span class="fw-bold text-primary">{{ driverCount.toLocaleString() }}</span>
                    </div>
                    <div class="summary-value text-muted" v-else>Not specified</div>
                  </div>
                </div>

                <!-- Estimated Time -->
                <div class="summary-item" :class="{ 'completed': estimatedTotalTime !== 'N/A' }">
                  <div class="summary-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-label">Estimated Time</div>
                    <div class="summary-value">
                      <span class="text-info">{{ estimatedTotalTime }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Production Warning -->
              <div v-if="selectedEnvironment?.name === 'production'" class="alert alert-warning mt-3 mb-0">
                <div class="d-flex align-items-center">
                  <i class="fas fa-exclamation-triangle me-2"></i>
                  <div>
                    <strong>Production Environment</strong>
                    <div class="small">This operation will affect production data</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Validation Status -->
          <div v-if="hasAnyInput" class="card validation-card">
            <div class="card-header">
              <h6 class="card-title mb-0">
                <i class="fas fa-check-circle me-2"></i>
                Validation Status
              </h6>
            </div>
            <div class="card-body">
              <!-- Pre-import Validation -->
              <div v-if="preImportValidation" class="validation-result">
                <div class="alert" :class="preImportValidation.success ? 'alert-success' : 'alert-danger'">
                  <div class="d-flex align-items-center mb-2">
                    <i :class="preImportValidation.success ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'" class="me-2"></i>
                    <strong>{{ preImportValidation.success ? 'Ready to Import' : 'Validation Issues' }}</strong>
                  </div>
                  
                  <div v-if="preImportValidation.success" class="small">
                    All validation checks passed successfully
                  </div>
                  
                  <div v-else>
                    <div class="small mb-2">Please resolve the following issues:</div>
                    <ul class="mb-0 small">
                      <li v-for="error in preImportValidation.errors" :key="error">{{ error }}</li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Validation in Progress -->
              <div v-else-if="allFieldsValid" class="text-center py-3">
                <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
                <span class="text-muted">Validating configuration...</span>
              </div>

              <!-- Incomplete Form -->
              <div v-else class="alert alert-light">
                <div class="d-flex align-items-center">
                  <i class="fas fa-info-circle me-2"></i>
                  <div>
                    <div class="fw-bold">Complete Configuration</div>
                    <div class="small text-muted">Fill out all required fields to validate</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import EnvironmentSelector from '@/components/EnvironmentSelector.vue'
import DealerSelector from '@/components/DealerSelector.vue'
import CustomerSelector from '@/components/CustomerSelector.vue'
import VehicleCountInput from '@/components/VehicleCountInput.vue'
import DriverCountInput from '@/components/DriverCountInput.vue'
import ProgressTracker from '@/components/ProgressTracker.vue'
import { useEnvironmentStore } from '@/stores/environment'
import { useDealerStore } from '@/stores/dealer'
import { useCustomerStore } from '@/stores/customer'

import { useNotificationStore } from '@/stores/notification'
import type { Environment } from '@/types/environment'
import type { Dealer } from '@/types/dealer'
import type { Customer } from '@/types/customer'

// Router
const router = useRouter()

// Stores
const environmentStore = useEnvironmentStore()
const dealerStore = useDealerStore()
const customerStore = useCustomerStore()
const notificationStore = useNotificationStore()

// Form state
const validationStates = ref<Record<string, boolean>>({
  environment: false,
  dealer: false,
  customer: false,
  vehicleCount: false,
  driverCount: false
})
const isImporting = ref(false)
const activeSessionId = ref<string | null>(null)
const preImportValidation = ref<{ success: boolean; errors: string[] } | null>(null)

// Form data (reactive references to store values)
const selectedEnvironment = ref<Environment | null>(null)
const selectedDealer = ref<Dealer | null>(null)
const selectedCustomer = ref<Customer | null>(null)
const vehicleCount = ref<number | null>(null)
const driverCount = ref<number | null>(null)

// Computed properties
const allFieldsValid = computed(() => {
  return Object.values(validationStates.value).every(valid => valid)
})

const hasAnyInput = computed(() => {
  return selectedEnvironment.value || selectedDealer.value || selectedCustomer.value || vehicleCount.value || driverCount.value
})

const canStartImport = computed(() => {
  const hasValidation = preImportValidation.value?.success === true
  return allFieldsValid.value && hasValidation && !isImporting.value
})

const estimatedTotalTime = computed(() => {
  if (!vehicleCount.value || !driverCount.value) return 'N/A'
  
  // Rough estimation: 100 vehicles per minute, 200 drivers per minute
  const vehicleMinutes = Math.ceil(vehicleCount.value / 100)
  const driverMinutes = Math.ceil(driverCount.value / 200)
  const totalMinutes = Math.max(vehicleMinutes, driverMinutes) + 2 // Add setup time
  
  if (totalMinutes < 60) {
    return `~${totalMinutes} minute${totalMinutes !== 1 ? 's' : ''}`
  } else {
    const hours = Math.floor(totalMinutes / 60)
    const remainingMinutes = totalMinutes % 60
    return `~${hours}h ${remainingMinutes}m`
  }
})

// Methods
const updateValidation = (field: string, isValid: boolean) => {
  validationStates.value[field] = isValid
  
  // Trigger pre-import validation if all fields are valid
  if (allFieldsValid.value) {
    validateImportConfiguration()
  } else {
    preImportValidation.value = null
  }
}

const validateImportConfiguration = async () => {
  try {
    const config = {
      environmentName: selectedEnvironment.value?.name,
      dealerId: selectedDealer.value?.id,
      customerId: selectedCustomer.value?.id,
      vehicleCount: vehicleCount.value,
      driverCount: driverCount.value
    }
    
    const response = await fetch('/api/data-generation/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    })
    
    if (response.ok) {
      const result = await response.json()
      preImportValidation.value = {
        success: result.isValid,
        errors: result.errors || []
      }
    } else {
      preImportValidation.value = {
        success: false,
        errors: ['Failed to validate import configuration. Please check your settings.']
      }
    }
  } catch (error) {
    console.error('Error validating import configuration:', error)
    preImportValidation.value = {
      success: false,
      errors: ['Validation service is temporarily unavailable. Please try again.']
    }
  }
}

const startImport = async () => {
  if (!canStartImport.value) return
  
  isImporting.value = true
  
  try {
    // Create import session
    const sessionData = {
      environmentName: selectedEnvironment.value!.name,
      dealerId: selectedDealer.value!.id,
      customerId: selectedCustomer.value!.id,
      vehicleCount: vehicleCount.value!,
      driverCount: driverCount.value!
    }
    
    const sessionResponse = await fetch('/api/bulk-import/sessions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(sessionData)
    })
    
    if (!sessionResponse.ok) {
      throw new Error('Failed to create import session')
    }
    
    const session = await sessionResponse.json()
    activeSessionId.value = session.id
    
    // Start the import
    const importResponse = await fetch(`/api/bulk-import/sessions/${session.id}/execute`, {
      method: 'POST'
    })
    
    if (!importResponse.ok) {
      throw new Error('Failed to start import operation')
    }
    
    notificationStore.addNotification({
      type: 'success',
      title: 'Import Started',
      message: `Bulk import operation has been started successfully. Session ID: ${session.id}`,
      duration: 5000
    })
    
  } catch (error) {
    console.error('Error starting import:', error)
    
    notificationStore.addNotification({
      type: 'error',
      title: 'Import Failed',
      message: error instanceof Error ? error.message : 'Failed to start import operation',
      duration: 10000
    })
    
    isImporting.value = false
    activeSessionId.value = null
  }
}

const onImportCancelled = () => {
  activeSessionId.value = null
  isImporting.value = false
  
  notificationStore.addNotification({
    type: 'warning',
    title: 'Import Cancelled',
    message: 'The import operation has been cancelled.',
    duration: 5000
  })
}

const onStatusChanged = (status: string) => {
  if (status === 'completed' || status === 'failed' || status === 'cancelled') {
    isImporting.value = false
    
    if (status === 'completed') {
      notificationStore.addNotification({
        type: 'success',
        title: 'Import Completed',
        message: 'The bulk import operation has completed successfully.',
        duration: 10000
      })
      
      // Navigate to sessions view to see results
      setTimeout(() => {
        router.push('/sessions')
      }, 2000)
    }
  }
}

// Watchers
watch(() => environmentStore.currentEnvironment, (env) => {
  selectedEnvironment.value = env
})

watch(() => dealerStore.selectedDealer, (dealer) => {
  selectedDealer.value = dealer
})

watch(() => customerStore.selectedCustomer, (customer) => {
  selectedCustomer.value = customer
})

// Lifecycle
onMounted(() => {
  // Load saved data from stores
  environmentStore.loadSavedEnvironment()
  dealerStore.loadSavedDealer()
  customerStore.loadSavedCustomer()
  
  // Set initial values
  selectedEnvironment.value = environmentStore.currentEnvironment
  selectedDealer.value = dealerStore.selectedDealer
  selectedCustomer.value = customerStore.selectedCustomer
})
</script>

<style scoped>
.import-form-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  
  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    
    .card-title {
      font-weight: 600;
      color: #495057;
    }
  }
}

.form-section {
  border-bottom: 1px solid #f8f9fa;
  padding-bottom: 1.5rem;
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
  
  .section-header {
    .section-title {
      color: #495057;
      font-weight: 600;
      font-size: 1.1rem;
    }
  }
}

.summary-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  
  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    
    .card-title {
      font-weight: 600;
      color: #495057;
    }
  }
  
  .summary-items {
    .summary-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #f8f9fa;
      transition: all 0.3s ease;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.completed {
        .summary-icon {
          background-color: #d4edda;
          color: #155724;
        }
        
        .summary-value {
          color: #495057;
          font-weight: 500;
        }
      }
      
      .summary-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: #f8f9fa;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
      }
      
      .summary-content {
        flex: 1;
        
        .summary-label {
          font-size: 0.875rem;
          color: #6c757d;
          font-weight: 500;
          margin-bottom: 0.25rem;
        }
        
        .summary-value {
          font-size: 0.875rem;
          color: #adb5bd;
          
          &.text-muted {
            font-style: italic;
          }
        }
      }
    }
  }
}

.validation-card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  
  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    
    .card-title {
      font-weight: 600;
      color: #495057;
    }
  }
  
  .validation-result {
    .alert {
      border-left: 4px solid;
      margin-bottom: 0;
    }
    
    .alert-success {
      border-left-color: #28a745;
    }
    
    .alert-danger {
      border-left-color: #dc3545;
    }
  }
}

/* Mobile responsiveness */
@media (max-width: 992px) {
  .row {
    flex-direction: column-reverse;
  }
  
  .col-lg-4 {
    margin-bottom: 2rem;
  }
  
  .sticky-top {
    position: relative !important;
    top: auto !important;
  }
}

@media (max-width: 768px) {
  .container-fluid {
    padding: 1rem;
  }
  
  .form-section {
    .section-header {
      .section-title {
        font-size: 1rem;
      }
    }
  }
  
  .summary-items {
    .summary-item {
      padding: 0.5rem 0;
      
      .summary-icon {
        width: 28px;
        height: 28px;
        margin-right: 0.5rem;
        font-size: 0.75rem;
      }
      
      .summary-content {
        .summary-label,
        .summary-value {
          font-size: 0.8rem;
        }
      }
    }
  }
}
</style>