{"Migration": {"Environments": {"US": {"Name": "United States Production", "Description": "US Production Environment", "DatabaseConnection": "Server=us-fleetxqdb.database.windows.net,1433;Database=FleetXQ.US.Production;User Id=us-fleetxqdb;Password=YOUR_US_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false", "ApiBaseUrl": "https://us-api.xq360.com/", "ApiUsername": "us-migration-user", "ApiPassword": "YOUR_US_API_PASSWORD"}, "UK": {"Name": "United Kingdom Production", "Description": "UK Production Environment", "DatabaseConnection": "", "ApiBaseUrl": "https://uk-api.xq360.com/", "ApiUsername": "uk-migration-user", "ApiPassword": "YOUR_UK_API_PASSWORD"}, "AU": {"Name": "Australia Production", "Description": "AU Production Environment", "DatabaseConnection": "", "ApiBaseUrl": "https://au-api.xq360.com/", "ApiUsername": "au-migration-user", "ApiPassword": "YOUR_AU_API_PASSWORD"}, "Pilot": {"Name": "Pilot Testing Environment", "Description": "Pilot testing and validation environment", "DatabaseConnection": "", "ApiBaseUrl": "https://godev.collectiveintelligence.com.au/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/", "ApiUsername": "Admin", "ApiPassword": "Admin"}, "Development": {"Name": "Development Environment", "Description": "Development and testing environment", "DatabaseConnection": "data source=.\\SQLEXPRESS; initial catalog=us0807;integrated security=SSPI;", "ApiBaseUrl": "https://localhost:53052/", "ApiUsername": "Admin", "ApiPassword": "Admin"}}, "BatchSize": 100, "MaxRetryAttempts": 3, "BackupEnabled": true, "ValidateBeforeMigration": true, "ContinueOnError": false}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "./Logs/developer-migration-{Date}.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}"}}]}, "Reporting": {"GenerateUserReports": true, "UserReportDirectory": "./Reports", "IncludeDetailedRecords": true, "MaxRecordsPerErrorType": 50}, "Authentication": {"BypassEnabled": false}, "BulkSeeder": {"DefaultDriversCount": 10000, "DefaultVehiclesCount": 5000, "DefaultBatchSize": 1000, "MaxBatchSize": 50000, "BulkCopyTimeout": 300, "CommandTimeout": 120, "NotifyAfter": 1000, "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelaySeconds": 5, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": false, "CleanupStagingData": true, "UseTempTables": true, "TempTableMode": "SessionScoped", "TempTableBatchSize": 5000, "TempTableIndexes": true, "LogTempTableOperations": true}, "Observability": {"Logging": {"Enabled": true, "EnableConsoleLogging": true, "EnableFileLogging": true, "EnableStructuredLogging": true, "EnablePerformanceLogging": true, "EnableErrorLogging": true, "EnableDebugLogging": false}, "Monitoring": {"Enabled": true, "EnablePerformanceMonitoring": true, "EnableResourceMonitoring": true, "EnableDatabaseMonitoring": true, "EnableOperationMonitoring": true, "CollectionIntervalSeconds": 5}, "Metrics": {"Enabled": true, "EnableThroughputMetrics": true, "EnableDurationMetrics": true, "EnableErrorRateMetrics": true, "EnableSuccessRateMetrics": true, "MaxHistorySize": 10000}, "HealthChecks": {"Enabled": true, "EnableDatabaseHealthChecks": true, "EnableSystemHealthChecks": true, "EnableServiceHealthChecks": true, "EnableNetworkHealthChecks": true, "CheckIntervalSeconds": 30}, "Tracing": {"Enabled": false, "EnableDistributedTracing": false, "EnableOperationTracing": false, "EnableDatabaseTracing": false, "SamplingRate": 0.1}, "Alerting": {"Enabled": true, "EnablePerformanceAlerts": true, "EnableErrorAlerts": true, "EnableResourceAlerts": true, "EnableAutoResolution": true, "EnableEscalation": true, "EnableDeduplication": true}, "Audit": {"Enabled": true, "EnableOperationAuditing": true, "EnableDataChangeAuditing": true, "EnableSecurityAuditing": true, "EnableAutomaticCleanup": true, "MaskSensitiveData": true, "DefaultRetentionPeriod": "90.00:00:00"}, "Reporting": {"Enabled": true, "EnableMigrationReports": true, "EnablePerformanceReports": true, "EnableErrorReports": true, "EnableDetailedRecordReports": true, "GenerateUserReports": true, "UserReportDirectory": "./Reports", "IncludeDetailedRecords": true, "MaxRecordsPerErrorType": 50}}}