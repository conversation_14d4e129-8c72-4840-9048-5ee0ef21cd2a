using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service interface for parallel processing operations
/// Implementation of Phase 3.1.1: Parallel Processing Architecture
/// </summary>
public interface IParallelProcessingService
{
    /// <summary>
    /// Processes a batch of items in parallel using TPL
    /// </summary>
    /// <typeparam name="TInput">Input item type</typeparam>
    /// <typeparam name="TResult">Result item type</typeparam>
    /// <param name="inputItems">Items to process</param>
    /// <param name="processor">Processing function</param>
    /// <param name="options">Parallel processing options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Parallel processing result</returns>
    Task<ParallelProcessingResult<TResult>> ProcessBatchInParallelAsync<TInput, TResult>(
        IEnumerable<TInput> inputItems,
        Func<TInput, CancellationToken, Task<TResult>> processor,
        ParallelProcessingOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes a stream of items using dataflow pipeline for memory efficiency
    /// </summary>
    /// <typeparam name="TInput">Input item type</typeparam>
    /// <typeparam name="TResult">Result item type</typeparam>
    /// <param name="inputStream">Async stream of input items</param>
    /// <param name="processor">Processing function</param>
    /// <param name="options">Parallel processing options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Parallel processing result</returns>
    Task<ParallelProcessingResult<TResult>> ProcessBatchWithDataflowAsync<TInput, TResult>(
        IAsyncEnumerable<TInput> inputStream,
        Func<TInput, CancellationToken, Task<TResult>> processor,
        ParallelProcessingOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes items in partitions for large datasets
    /// </summary>
    /// <typeparam name="TInput">Input item type</typeparam>
    /// <typeparam name="TResult">Result item type</typeparam>
    /// <param name="inputItems">Items to process</param>
    /// <param name="batchProcessor">Batch processing function</param>
    /// <param name="partitionSize">Size of each partition</param>
    /// <param name="options">Parallel processing options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Parallel processing result</returns>
    Task<ParallelProcessingResult<TResult>> ProcessBatchWithPartitioningAsync<TInput, TResult>(
        IEnumerable<TInput> inputItems,
        Func<IEnumerable<TInput>, CancellationToken, Task<IEnumerable<TResult>>> batchProcessor,
        int partitionSize,
        ParallelProcessingOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current parallel processing performance metrics
    /// </summary>
    /// <returns>Performance metrics</returns>
    ParallelProcessingMetrics GetPerformanceMetrics();

    /// <summary>
    /// Optimizes thread pool settings for high-throughput scenarios
    /// </summary>
    /// <returns>Task representing the optimization operation</returns>
    Task OptimizeThreadPoolAsync();
}
