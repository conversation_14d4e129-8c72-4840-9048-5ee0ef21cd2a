using CsvHelper;
using CsvHelper.Configuration;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Tests
{
    public class CsvSchemaCompatibilityTests
    {
        [Fact]
        public void CardImportCsv_ShouldParseCorrectly_WithAllFields()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department Name,First Name,Last Name,Facility Code,Card Type,Card No,Reader Type,Weigand,Access Level
Customer1,Customer1,Site1,Department1,John,Doe,123,Standard,12345,RFID,26-bit,Level1
Customer1,Customer1,Site1,Department1,Jane,Smith,124,Premium,12346,RFID,26-bit,Level2";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<CardImportModel>().ToList();

            // Assert
            Assert.Equal(2, results.Count);
            Assert.Equal("Customer1", results[0].Customer);
            Assert.Equal("12345", results[0].CardNo);
            Assert.Equal("John", results[0].FirstName);
        }

        [Fact]
        public void CardImportCsv_ShouldParseCorrectly_WithMissingOptionalFields()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department Name,First Name,Last Name,Facility Code,Card Type,Card No,Reader Type,Weigand,Access Level
Customer1,Customer1,Site1,Department1,John,Doe,123,Standard,12345,RFID,26-bit,Level1";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<CardImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal("Customer1", results[0].Customer);
            Assert.Equal("12345", results[0].CardNo);
        }

        [Fact]
        public void VehicleImportCsv_ShouldParseCorrectly_WithAllFields()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department Name,Device ID,Serial No,Hire No,Model Name,Impact Lockout,IsCanbus,On Hire,Timeout Enabled,Idle Timer,Canrule Name,Question Timeout,Show Comment,Randomisation,Full Lockout Timeout,VOR Status,Amber Alert Enabled,VOR Status Confirmed,Full Lockout,Default Technician Access,Pedestrian Safety,Checklist Type,Time slot1,Time slot2,Time slot3,Time slot4
Customer1,Customer1,Site1,Department1,DEV001,SN001,V001,Forklift,true,false,true,false,60,Rule1,30,false,false,120,false,false,false,false,false,false,Daily,08:00,12:00,16:00,20:00";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<VehicleImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal("Customer1", results[0].Customer);
            Assert.Equal("V001", results[0].HireNo);
            Assert.Equal("Forklift", results[0].ModelName);
        }

        [Theory]
        [InlineData("true", true)]
        [InlineData("false", false)]
        [InlineData("1", true)]
        [InlineData("0", false)]
        public void PersonImportCsv_ShouldParseBooleanFields_Correctly(string boolValue, bool _)
        {
            // Arrange
            var csvContent = $@"Customer,Site,Department,First Name,Last Name,Send Deny Message,Website Access,IsDriver,IsSupervisor,VOR Activate/Deactivate,Normal Driver Access,CanUnlockVehicle
Customer1,Site1,Department1,John,Doe,{boolValue},false,true,false,false,true,false
Customer1,Site1,Department1,Jane,Smith,false,{boolValue},false,true,true,false,true";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<PersonImportModel>().ToList();

            // Assert
            Assert.Equal(2, results.Count);
            Assert.Equal("John", results[0].FirstName);
            Assert.Equal("Jane", results[1].FirstName);
        }

        [Theory]
        [InlineData("true", true)]
        [InlineData("false", false)]
        [InlineData("1", true)]
        [InlineData("0", false)]
        public void VehicleImportCsv_ShouldParseBooleanFields_Correctly(string boolValue, bool expected)
        {
            // Arrange
            var csvContent = $@"Dealer,Customer,Site,Department Name,Device ID,Serial No,Hire No,Model Name,Impact Lockout,IsCanbus,On Hire,Timeout Enabled,Idle Timer,Canrule Name,Question Timeout,Show Comment,Randomisation,Full Lockout Timeout,VOR Status,Amber Alert Enabled,VOR Status Confirmed,Full Lockout,Default Technician Access,Pedestrian Safety,Checklist Type,Time slot1,Time slot2,Time slot3,Time slot4
Customer1,Customer1,Site1,Department1,DEV001,SN001,V001,Forklift,{boolValue},false,true,false,60,Rule1,30,false,false,120,false,false,false,false,false,false,Daily,08:00,12:00,16:00,20:00";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<VehicleImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal(expected, results[0].ImpactLockout);
        }

        [Fact]
        public void CsvFiles_ShouldHandleCommasInQuotedFields_Correctly()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department Name,First Name,Last Name,Facility Code,Card Type,Card No,Reader Type,Weigand,Access Level
""Customer 1, Inc"",""Customer 1, Inc"",""Site 1, Building A"",""Department 1, Section A"",""John, Jr"",""Doe, Sr"",123,Standard,12345,RFID,26-bit,Level1";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<CardImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal("Customer 1, Inc", results[0].Customer);
            Assert.Equal("Site 1, Building A", results[0].Site);
            Assert.Equal("John, Jr", results[0].FirstName);
        }

        [Fact]
        public void SupervisorAccessImportCsv_ShouldParseCorrectly_WithAllFields()
        {
            // Arrange
            var csvContent = @"Person Dealer,Person Customer,Person Site,Person Department,First Name,Last Name,Weigand,Hire No,Serial NO,GMTP ID,Supervisor Authorization
Customer1,Customer1,Site1,Department1,John,Doe,123456,V001,SN001,GMTP001,true";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<SupervisorAccessImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal("Customer1", results[0].PersonCustomer);
            Assert.Equal("John", results[0].FirstName);
        }

        [Fact]
        public void DriverBlacklistImportCsv_ShouldParseCorrectly_WithAllFields()
        {
            // Arrange
            var csvContent = @"Person Dealer,Person Customer,Person Site,Person Department,First Name,Last Name,Weigand,Hire No,Serial NO,GMTP ID
TestDealer,Customer1,Site1,Department1,John,Doe,26-bit,V001,SN001,GMTP001";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<DriverBlacklistImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal("Customer1", results[0].PersonCustomer);
            Assert.Equal("John", results[0].FirstName);
            Assert.Equal("V001", results[0].HireNo);
        }

        [Fact]
        public void SpareModuleImportCsv_ShouldParseCorrectly_WithAllFields()
        {
            // Arrange
            var csvContent = @"Dealer,IoT Device ID,CCID,RA Number,Tech Number
TestDealer,IOT001,1234567890,12345,67890";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<SpareModuleImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal("TestDealer", results[0].Dealer);
            Assert.Equal("IOT001", results[0].IoTDeviceID);
            Assert.Equal(1234567890, results[0].CCID);
        }

        [Fact]
        public void WebsiteUserImportCsv_ShouldParseCorrectly_WithAllFields()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department Name,First Name,Last Name,Username,Email,Password,Preferred Locale,Website Access Level,Access Group,Role
TestDealer,TestCustomer,TestSite,TestDepartment,John,Doe,jdoe,<EMAIL>,password123,en-US,Admin,AdminGroup,Administrator";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<WebsiteUserImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal("jdoe", results[0].Username);
            Assert.Equal("<EMAIL>", results[0].Email);
            Assert.Equal("Administrator", results[0].Role);
        }

        [Fact]
        public void PreOpChecklistImportCsv_ShouldParseCorrectly_WithAllFields()
        {
            // Arrange
            var csvContent = @"Dealer,Customer,Site,Department,Model,Question,Expected Answer,Critical,ExcludeFromRandom,Sort Order
TestDealer,Customer1,Site1,Department1,Forklift,Is the seatbelt fastened?,true,true,false,1";

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
            };

            // Act
            using var reader = new StringReader(csvContent);
            using var csv = new CsvReader(reader, config);
            var results = csv.GetRecords<PreOpChecklistImportModel>().ToList();

            // Assert
            Assert.Single(results);
            Assert.Equal("Customer1", results[0].Customer);
            Assert.Equal("Is the seatbelt fastened?", results[0].Question);
            Assert.True(results[0].ExpectedAnswer);
            Assert.True(results[0].Critical);
        }
    }
} 