/**
 * Simplified <PERSON>der Styles
 * CSS for the simplified bulk data seeder interface
 */

/* Form label styles */
.form-label {
    font-weight: 600;
    color: #495057;
}

.form-label i {
    color: #6c757d;
}

/* Selected item display styles */
.alert.d-flex {
    align-items: center;
}

.alert .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Progress Tracker Styles */
#progressTracker {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#progressTracker .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

#progressTracker .progress {
    height: 1.5rem;
}

#progressTracker .progress-bar {
    font-weight: 600;
    transition: width 0.6s ease;
}

/* Responsive Design */
@media (max-width: 991.98px) {    
    .sticky-top {
        position: static !important;
    }
}
