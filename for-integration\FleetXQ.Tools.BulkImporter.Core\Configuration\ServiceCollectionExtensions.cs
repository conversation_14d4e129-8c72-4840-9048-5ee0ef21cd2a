using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Core.Services;

namespace FleetXQ.Tools.BulkImporter.Core.Configuration;

/// <summary>
/// Extension methods for configuring Core services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Core services and configuration to the container
    /// </summary>
    public static IServiceCollection AddBulkImporterCore(this IServiceCollection services, IConfiguration configuration)
    {
        // Add configuration options
        services.Configure<BulkImporterOptions>(configuration.GetSection(BulkImporterOptions.SectionName));
        services.Configure<DataGenerationOptions>(configuration.GetSection(DataGenerationOptions.SectionName));
        services.Configure<ConnectionStringOptions>(configuration.GetSection(ConnectionStringOptions.SectionName));
        services.Configure<EnvironmentOptions>(configuration.GetSection(EnvironmentOptions.SectionName));

        // Validate configuration options
        services.AddSingleton<IValidateOptions<BulkImporterOptions>, BulkImporterOptionsValidator>();
        services.AddSingleton<IValidateOptions<DataGenerationOptions>, DataGenerationOptionsValidator>();
        services.AddSingleton<IValidateOptions<ConnectionStringOptions>, ConnectionStringOptionsValidator>();

        // Add core services
        services.AddScoped<IBulkImportService, BulkImportService>();
        services.AddScoped<ISqlDataGenerationService, SqlDataGenerationService>();
        services.AddScoped<IEnvironmentService, EnvironmentService>();

        // Add HTTP client for EnvironmentService notifications
        services.AddHttpClient<EnvironmentService>();

        return services;
    }
}

/// <summary>
/// Validator for BulkImporterOptions
/// </summary>
public class BulkImporterOptionsValidator : IValidateOptions<BulkImporterOptions>
{
    public ValidateOptionsResult Validate(string? name, BulkImporterOptions options)
    {
        var failures = new List<string>();

        if (options.DefaultDriversCount <= 0)
            failures.Add("DefaultDriversCount must be greater than 0");

        if (options.DefaultVehiclesCount <= 0)
            failures.Add("DefaultVehiclesCount must be greater than 0");

        if (options.DefaultBatchSize <= 0)
            failures.Add("DefaultBatchSize must be greater than 0");

        if (options.MaxBatchSize < options.DefaultBatchSize)
            failures.Add("MaxBatchSize must be greater than or equal to DefaultBatchSize");

        if (options.BulkCopyTimeout <= 0)
            failures.Add("BulkCopyTimeout must be greater than 0");

        if (options.CommandTimeout <= 0)
            failures.Add("CommandTimeout must be greater than 0");

        if (failures.Any())
        {
            return ValidateOptionsResult.Fail(failures);
        }

        return ValidateOptionsResult.Success;
    }
}

/// <summary>
/// Validator for DataGenerationOptions
/// </summary>
public class DataGenerationOptionsValidator : IValidateOptions<DataGenerationOptions>
{
    public ValidateOptionsResult Validate(string? name, DataGenerationOptions options)
    {
        var failures = new List<string>();

        if (string.IsNullOrWhiteSpace(options.OutputDirectory))
            failures.Add("OutputDirectory cannot be null or empty");

        if (string.IsNullOrWhiteSpace(options.ArchiveDirectory))
            failures.Add("ArchiveDirectory cannot be null or empty");

        if (string.IsNullOrWhiteSpace(options.ErrorDirectory))
            failures.Add("ErrorDirectory cannot be null or empty");

        if (options.GenerationBatchSize <= 0)
            failures.Add("GenerationBatchSize must be greater than 0");

        if (options.MaxMemoryUsageMB <= 0)
            failures.Add("MaxMemoryUsageMB must be greater than 0");

        if (failures.Any())
        {
            return ValidateOptionsResult.Fail(failures);
        }

        return ValidateOptionsResult.Success;
    }
}

/// <summary>
/// Validator for ConnectionStringOptions
/// </summary>
public class ConnectionStringOptionsValidator : IValidateOptions<ConnectionStringOptions>
{
    public ValidateOptionsResult Validate(string? name, ConnectionStringOptions options)
    {
        var failures = new List<string>();

        if (string.IsNullOrWhiteSpace(options.DefaultConnection))
            failures.Add("DefaultConnection cannot be null or empty");

        if (string.IsNullOrWhiteSpace(options.FleetXQConnection))
            failures.Add("FleetXQConnection cannot be null or empty");

        if (failures.Any())
        {
            return ValidateOptionsResult.Fail(failures);
        }

        return ValidateOptionsResult.Success;
    }
}
