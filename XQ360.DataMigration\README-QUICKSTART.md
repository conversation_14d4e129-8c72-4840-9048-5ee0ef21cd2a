# XQ360 Data Migration Tool - Quick Start Guide

## 🚀 Getting Started (2 Minutes)

### Method 1: Using the Batch File (Recommended)
1. **Start the web interface**: `cd XQ360.DataMigration.Web && dotnet run`
2. The GUI will open automatically with all environments loaded

### Method 2: Using Command Line
1. **Open Command Prompt or PowerShell**
2. **Navigate to project directory:**
   ```bash
   cd XQ360.DataMigration
   ```
3. **Start the GUI:**
   ```bash
   dotnet run
   ```

### Method 3: CLI Mode (Legacy)
```bash
cd XQ360.DataMigration
dotnet run --migrate-all
dotnet run --migrate-spare-modules
# ... other CLI commands
```

## 🎯 First Migration Steps

### 1. **Select Environment**
- Choose from: US, UK, AU, Local, Pilot, or Development
- Each environment has separate database/API settings
- Start with **Local** for development or **Pilot** for testing

### 2. **Choose Data Types**
- Select which migrations to run (1-9 available)
- Use **Select All** for complete migration
- Dependencies are handled automatically

### 3. **Upload CSV Files**
- Browse and select CSV files for each chosen migration type
- Files are validated before migration starts

### 4. **Validate & Migrate**
- Click **"Validate Prerequisites"** first
- Click **"Start Migration"** when ready
- Monitor progress in real-time

### 5. **Review Results**
- Check results panel for success/failure status
- Click **"View Reports"** for detailed analysis
- Access user manual via **"📖 User Manual"** button

## 🔧 Environment Configuration

Edit `appsettings.json` to configure environments:

```json
{
  "Migration": {
    "Environments": {
      "Pilot": {
        "Name": "Pilot Testing Environment",
        "DatabaseConnection": "Your-Connection-String",
        "ApiBaseUrl": "https://your-api-url/",
        "ApiUsername": "your-username",
        "ApiPassword": "your-password"
      }
    }
  }
}
```

## 📋 CSV File Requirements

| Migration Type | Required CSV File |
|----------------|-------------------|
| Spare Modules | `SPARE_MODEL_IMPORT_TEMPLATE.csv` |
| PreOp Checklist | `PREOP_CHECKLIST_IMPORT.csv` |
| Vehicles | `VEHICLE_IMPORT.csv` |
| Persons | `PERSON_IMPORT_TEMPLATE.csv` |
| Cards + Access | `CARD_IMPORT.csv` |
| Supervisor Access | `SUPERVISOR_ACCESS_IMPORT.csv` |
| Driver Blacklist | `DRIVER_BLACKLIST_IMPORT.csv` |
| Website Users | `WEBSITE_USER_IMPORT.csv` |
| Vehicle Sync | `VEHICLE_IMPORT.csv` (reused) |

## ⚡ Troubleshooting

### **"Couldn't find a project to run"**
- **Solution**: Make sure you're in the correct directory
- **Correct**: `XQ360.DataMigration\` (project folder)
- **Incorrect**: Root folder without navigating to project

### **Environment Connection Errors**
- Check database connection strings in `appsettings.json`
- Verify API credentials and URLs
- Test with Development or Pilot environment first

### **CSV File Errors**
- Ensure CSV files match the required format
- Check for required columns and data types
- Use the provided template files as reference

### **Migration Failures**
- Review the live logs for specific error messages
- Check the Reports folder for detailed error analysis
- Ensure proper migration order (dependencies)

## 🆘 Getting Help

1. **Built-in Help**: Click **"📖 User Manual"** in the application
2. **Documentation**: Review `README-UI.md` for detailed UI guide
3. **Environment Setup**: See `README-Environment-Setup.md`
4. **Logs**: Check the `Logs/` folder for technical details
5. **Reports**: Review `Reports/` folder for migration results

## 🔒 Security Note

- Keep `appsettings.json` secure (contains passwords)
- Test in Development or Pilot environment before production
- Always backup data before migration
- Use the validation feature before starting

---

**Ready to migrate? Start the web interface to get started! 🚀** 