@{
    ViewData["Title"] = "Bootstrap Test";
}

<div class="container mt-4">
    <h1>Bootstrap JavaScript Test</h1>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Bootstrap Components Test</h5>
                </div>
                <div class="card-body">
                    <p>This page tests if Bootstrap JavaScript is working correctly.</p>
                    
                    <!-- Test Bootstrap Tooltip -->
                    <button type="button" class="btn btn-primary mb-3" data-bs-toggle="tooltip" data-bs-placement="top" title="This is a Bootstrap tooltip">
                        Hover for Tooltip
                    </button>
                    
                    <!-- Test Bootstrap Collapse -->
                    <div class="mb-3">
                        <button class="btn btn-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                            Toggle Collapse
                        </button>
                        <div class="collapse mt-2" id="collapseExample">
                            <div class="card card-body">
                                This is a collapsible content area. If Bootstrap JavaScript is working, you should be able to toggle this.
                            </div>
                        </div>
                    </div>
                    
                    <!-- Test Bootstrap Modal -->
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#testModal">
                        Open Modal
                    </button>
                    
                    <!-- Modal -->
                    <div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="testModalLabel">Bootstrap Modal Test</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    If you can see this modal, Bootstrap JavaScript is working correctly!
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Console Output</h5>
                </div>
                <div class="card-body">
                    <p>Open your browser's developer tools (F12) and check the Console tab for debugging information.</p>
                    <div id="console-output" class="bg-dark text-light p-3 rounded" style="font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                        <div>Waiting for console output...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Test Results</h5>
                </div>
                <div class="card-body">
                    <div id="test-results">
                        <p>Running tests...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const consoleOutput = document.getElementById('console-output');
    const testResults = document.getElementById('test-results');
    
    // Override console.log to capture output
    const originalLog = console.log;
    const originalError = console.error;
    
    console.log = function(...args) {
        originalLog.apply(console, args);
        const message = args.join(' ');
        consoleOutput.innerHTML += `<div class="text-success">✅ ${message}</div>`;
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
    };
    
    console.error = function(...args) {
        originalError.apply(console, args);
        const message = args.join(' ');
        consoleOutput.innerHTML += `<div class="text-danger">❌ ${message}</div>`;
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
    };
    
    // Run tests
    setTimeout(function() {
        let results = '<ul class="list-group">';
        
        // Test Bootstrap availability
        if (typeof bootstrap !== 'undefined') {
            results += '<li class="list-group-item list-group-item-success">✅ Bootstrap JavaScript loaded</li>';
        } else {
            results += '<li class="list-group-item list-group-item-danger">❌ Bootstrap JavaScript not loaded</li>';
        }
        
        // Test jQuery availability
        if (typeof $ !== 'undefined') {
            results += '<li class="list-group-item list-group-item-success">✅ jQuery loaded</li>';
        } else {
            results += '<li class="list-group-item list-group-item-danger">❌ jQuery not loaded</li>';
        }
        
        // Test Bootstrap components
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            results += '<li class="list-group-item list-group-item-success">✅ Bootstrap Tooltip available</li>';
        } else {
            results += '<li class="list-group-item list-group-item-danger">❌ Bootstrap Tooltip not available</li>';
        }
        
        if (typeof bootstrap !== 'undefined' && bootstrap.Collapse) {
            results += '<li class="list-group-item list-group-item-success">✅ Bootstrap Collapse available</li>';
        } else {
            results += '<li class="list-group-item list-group-item-danger">❌ Bootstrap Collapse not available</li>';
        }
        
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            results += '<li class="list-group-item list-group-item-success">✅ Bootstrap Modal available</li>';
        } else {
            results += '<li class="list-group-item list-group-item-danger">❌ Bootstrap Modal not available</li>';
        }
        
        results += '</ul>';
        testResults.innerHTML = results;
        
        // Initialize tooltips if Bootstrap is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }, 1000);
});
</script>