# Technical Documentation – Bulk Data Seeder (FXQ-3150)

## Overview

Implements a sessionized, SQL-backed bulk data seeder integrated with the existing XQ360 migration web stack. It generates synthetic Driver and Vehicle data into optimized staging tables, validates them, and optionally processes them into production via a controlled pipeline. Real-time progress is published over SignalR using the existing `MigrationHub` infrastructure. The implementation provides environment-aware configuration, retry resilience, dry-run safety, and post-run session metrics.

This work was guided by existing migration architecture, performance goals outlined in `OPTIMIZED-DATA-SEEDING-ENHANCEMENT-PLAN.md`, and iterative prompts analyzing service boundaries, SQL staging design, and progress reporting via SignalR.

## Scope of Changes

- [x] Files and components affected
  - `XQ360.DataMigration.Web/Services/BulkSeeder/BulkSeederService.cs`
  - `XQ360.DataMigration.Web/Services/BulkSeeder/SqlDataGenerationService.cs`
  - `XQ360.DataMigration.Web/Services/BulkSeeder/StagingSchemaService.cs`
  - `XQ360.DataMigration.Web/Controllers/BulkSeederController.cs`
  - Uses existing `XQ360.DataMigration.Web/Hubs/MigrationHub` for progress broadcast
  - Configuration knobs in `appsettings.json` (`BulkImporter` section; defaults and limits)
- [x] Services, APIs, or modules involved
  - `IBulkSeederService`, `ISqlDataGenerationService`, `IStagingSchemaService`
  - `IEnvironmentConfigurationService` for environment selection/connection strings
  - Polly-based retry inside `BulkSeederService.ExecuteWithRetry`
  - SignalR `MigrationHub` group messaging for session-scoped updates
- [x] Frontend/backend/database layers touched
  - Backend Web API: `BulkSeederController` (create/execute/cancel/list sessions)
  - Database: `Staging` schema, `SeederSession`, `DriverStaging`, `VehicleStaging` (+ validation views/procs via script)
  - Reuses existing web hub for UI progress; no direct frontend edits in this ticket

## Behavior Summary

- Session lifecycle: create → execute (background) → complete/cancel → delete.
- Execution path (`BulkSeederService.ExecuteSeederAsync`):
  - Validates options and applies defaults from config.
  - Creates a database seeding session row (`[Staging].[SeederSession]`).
  - Generates synthetic Drivers and Vehicles in batches via T-SQL into `[Staging].*Staging` tables.
  - Validates staged rows (required fields, counts) and summarizes errors.
  - If not `DryRun`, marks staged data processed (placeholder for real merges) and updates counts.
  - Publishes progress to SignalR; final status and summary recorded.
- Options supported: `DriversCount`, `VehiclesCount`, `BatchSize`, `DryRun`, `GenerateData`, `DealerId`, with retry controls and max limits enforced from config.

## Architecture Notes

- Key design choices
  - Environment-aware execution using `IEnvironmentConfigurationService` for connection strings.
  - Sessionized runs with persistent metrics in `[Staging].[SeederSession]`.
  - Batched SQL generation via `ROW_NUMBER()` approach for deterministic synthetic data.
  - Retry with backoff (Polly) for generation operations.
  - Real-time progress via SignalR group per session ID.
- Data flow and integration points
  1. API `POST /api/bulk-seeder/sessions` creates in-memory session and assigns SignalR group.
  2. API `POST /api/bulk-seeder/sessions/{id}/execute` kicks off background task.
  3. Service creates `[Staging].[SeederSession]` and batches inserts into `DriverStaging` and `VehicleStaging` via SQL.
  4. Validation aggregates per-entity issues; on success, processing step runs (safe no-op in dry-run).
  5. Session status and metrics updated; final notification emitted.
- Staging schema notes
  - `StagingSchemaService.InitializeOptimizedStagingSchemaAsync` executes `001-CreateOptimizedStagingSchema.sql` transactionally, splitting on GO.
  - Cleanup utilities provided for completed and orphaned sessions; schema validation checks required tables/views.

```mermaid
flowchart TD
  A[Create Session API] --> B[Set Environment]
  B --> C[Execute Session]
  C --> D[Create SeederSession row]
  D --> E[Generate Drivers (batched SQL)]
  D --> F[Generate Vehicles (batched SQL)]
  E --> G[Validate Staged Data]
  F --> G
  G -->|DryRun| H[Summarize and Complete]
  G -->|Process| I[Process to Prod Tables]
  I --> H
  H --> J[Update Session + SignalR Notification]
```

## Edge Cases & Limitations

- `DealerId` required when `RequireDealerSelection` is true; default dealer applied if configured.
- Batch size validated against `MaxBatchSize`; non-positive counts rejected.
- Dry-run bypasses production writes; `ProcessStagedDataAsync` is a placeholder and should be replaced with real merges.
- Staging schema script must exist; initialization is transactional and guarded to one-time per process.
- Progress notifications are best-effort; failures are logged and do not break execution.
- Current synthetic data covers Drivers and Vehicles only; Cards/Access/etc. are planned in follow-ups.

## Developer Notes

- Config (`BulkImporter`): `DefaultDriversCount`, `DefaultVehiclesCount`, `DefaultBatchSize`, `MaxBatchSize`, timeouts, retry policy, dealer validation, temp-table options.
- SQL generation uses cross-joined `sys.objects` and `ROW_NUMBER()` to synthesize deterministic batches.
- Session metrics and summaries persisted; `StagingSchemaService` exposes cleanup and optimization helpers.
- Aligns with layered architecture; complex entity creation and API orchestration reserved for subsequent phases.
- References consulted: `OPTIMIZED-DATA-SEEDING-ENHANCEMENT-PLAN.md`, `XQ360.DataMigration/README*.md`, `USER-MANUAL.md`.

## Related Artifacts

- JIRA Ticket(s):  FXQ-3150
- Key Endpoints:
  - `POST /api/bulk-seeder/sessions`
  - `POST /api/bulk-seeder/sessions/{sessionId}/execute`
  - `POST /api/bulk-seeder/sessions/{sessionId}/cancel`
  - `GET /api/bulk-seeder/sessions`, `GET /api/bulk-seeder/sessions/{sessionId}`
- Core Types/Services:
  - `BulkSeederService`, `SqlDataGenerationService`, `StagingSchemaService`
  - `SeederOptions`, `SeederResult`, `ValidationResult`, `ProcessingResult`
