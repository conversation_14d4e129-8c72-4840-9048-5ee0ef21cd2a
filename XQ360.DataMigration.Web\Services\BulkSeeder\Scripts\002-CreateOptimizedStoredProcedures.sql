-- XQ360 Optimized Data Seeding Enhancement Plan
-- Phase 1.2.2: Create Optimized Stored Procedures for Data Migration
-- Performance target: Dedicated SPs for Vehicle creation sequence, Person/Driver creation, Card/Access setup
-- Optimization: Use table-valued parameters, avoid cursors, optimize execution plans

-- =====================================================================================
-- Vehicle Creation Sequence Stored Procedure
-- Handles ChecklistSettings → VehicleOtherSettings → Vehicle creation sequence
-- =====================================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_BulkCreateVehicleSequence')
    DROP PROCEDURE [dbo].[sp_BulkCreateVehicleSequence]
GO

CREATE PROCEDURE [dbo].[sp_BulkCreateVehicleSequence]
    @SessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @TotalCount INT;
    DECLARE @ErrorCount INT = 0;
    
    -- Get total count for processing
    SELECT @TotalCount = COUNT(*) 
    FROM [Staging].[VehicleStaging] 
    WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Pending';
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        -- Process vehicles in batches
        WHILE @ProcessedCount < @TotalCount
        BEGIN
            -- Create ChecklistSettings for batch
            WITH VehicleBatch AS (
                SELECT TOP (@BatchSize) *
                FROM [Staging].[VehicleStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                AND [ChecklistSettingsRequired] = 1
                ORDER BY [Id]
            )
            INSERT INTO [dbo].[ChecklistSettings] (
                [Id], [Name], [Type], [DepartmentId], [CreatedDate]
            )
            SELECT 
                NEWID(),
                vs.[VehicleName] + '_Checklist',
                vs.[ChecklistType],
                vs.[DepartmentId],
                GETUTCDATE()
            FROM VehicleBatch vs
            WHERE vs.[DepartmentId] IS NOT NULL;
            
            -- Create VehicleOtherSettings for batch
            WITH VehicleBatch AS (
                SELECT TOP (@BatchSize) *
                FROM [Staging].[VehicleStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                AND [VehicleOtherSettingsRequired] = 1
                ORDER BY [Id]
            )
            INSERT INTO [dbo].[VehicleOtherSettings] (
                [Id], [Name], [SettingsData], [CreatedDate]
            )
            SELECT 
                NEWID(),
                vs.[VehicleName] + '_Settings',
                '{"DefaultSettings": true}',
                GETUTCDATE()
            FROM VehicleBatch vs;
            
            -- Create Vehicles for batch
            WITH VehicleBatch AS (
                SELECT TOP (@BatchSize) *
                FROM [Staging].[VehicleStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                ORDER BY [Id]
            )
            INSERT INTO [dbo].[Vehicle] (
                [Id], [Name], [DeviceId], [LicensePlate], [VIN], 
                [Make], [Model], [Year], [Color], [FuelType],
                [DepartmentId], [ModelId], [ModuleId], [CreatedDate]
            )
            SELECT 
                NEWID(),
                vs.[VehicleName],
                vs.[DeviceId],
                vs.[LicensePlate],
                vs.[VIN],
                vs.[Make],
                vs.[Model],
                vs.[Year],
                vs.[Color],
                vs.[FuelType],
                vs.[DepartmentId],
                vs.[ModelId],
                vs.[ModuleId],
                GETUTCDATE()
            FROM VehicleBatch vs
            WHERE vs.[DepartmentId] IS NOT NULL 
            AND vs.[ModelId] IS NOT NULL
            AND (vs.[ModuleId] IS NOT NULL OR vs.[ModuleSerialNumber] IS NULL);
            
            -- Update processing status
            WITH VehicleBatch AS (
                SELECT TOP (@BatchSize) [Id]
                FROM [Staging].[VehicleStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                ORDER BY [Id]
            )
            UPDATE vs SET 
                [ProcessingStatus] = 'Completed',
                [ProcessedAt] = GETUTCDATE()
            FROM [Staging].[VehicleStaging] vs
            INNER JOIN VehicleBatch vb ON vs.[Id] = vb.[Id];
            
            SET @ProcessedCount = @ProcessedCount + @BatchSize;
        END
        
        COMMIT TRANSACTION;
        
        SELECT 
            @ProcessedCount as ProcessedCount,
            @ErrorCount as ErrorCount,
            'Success' as Status;
            
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SELECT 
            @ProcessedCount as ProcessedCount,
            @ErrorCount + 1 as ErrorCount,
            ERROR_MESSAGE() as Status;
    END CATCH
END
GO

-- =====================================================================================
-- Person/Driver Creation Stored Procedure with API Integration Support
-- Optimized for bulk Person and Driver record creation
-- =====================================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_BulkCreatePersonDrivers')
    DROP PROCEDURE [dbo].[sp_BulkCreatePersonDrivers]
GO

CREATE PROCEDURE [dbo].[sp_BulkCreatePersonDrivers]
    @SessionId UNIQUEIDENTIFIER,
    @BatchSize INT = 1000,
    @CreateCards BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @TotalCount INT;
    DECLARE @ErrorCount INT = 0;
    
    -- Get total count for processing
    SELECT @TotalCount = COUNT(*) 
    FROM [Staging].[DriverStaging] 
    WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Pending';
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        -- Process drivers in batches
        WHILE @ProcessedCount < @TotalCount
        BEGIN
            -- Create Person records for batch
            WITH DriverBatch AS (
                SELECT TOP (@BatchSize) *
                FROM [Staging].[DriverStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                ORDER BY [Id]
            )
            INSERT INTO [dbo].[Person] (
                [Id], [FirstName], [LastName], [Email], [Phone], 
                [EmployeeId], [DateOfBirth], [DepartmentId], [CreatedDate]
            )
            OUTPUT INSERTED.[Id], INSERTED.[Email] INTO @PersonIds(PersonId, Email)
            SELECT 
                NEWID(),
                ds.[FirstName],
                ds.[LastName],
                ds.[Email],
                ds.[Phone],
                ds.[EmployeeId],
                ds.[DateOfBirth],
                ds.[DepartmentId],
                GETUTCDATE()
            FROM DriverBatch ds
            WHERE ds.[DepartmentId] IS NOT NULL;
            
            -- Create Driver records for batch
            WITH DriverBatch AS (
                SELECT TOP (@BatchSize) ds.*, p.[Id] as PersonId
                FROM [Staging].[DriverStaging] ds
                INNER JOIN [dbo].[Person] p ON ds.[Email] = p.[Email]
                WHERE ds.[SessionId] = @SessionId 
                AND ds.[ProcessingStatus] = 'Pending'
                ORDER BY ds.[Id]
            )
            INSERT INTO [dbo].[Driver] (
                [Id], [PersonId], [LicenseNumber], [LicenseExpiryDate], 
                [CreatedDate], [CardDetailsId]
            )
            SELECT 
                NEWID(),
                db.[PersonId],
                db.[LicenseNumber],
                db.[LicenseExpiryDate],
                GETUTCDATE(),
                NULL -- Will be updated when cards are created
            FROM DriverBatch db;
            
            -- Create Cards if requested
            IF @CreateCards = 1
            BEGIN
                WITH DriverBatch AS (
                    SELECT TOP (@BatchSize) ds.*, d.[Id] as DriverId
                    FROM [Staging].[DriverStaging] ds
                    INNER JOIN [dbo].[Person] p ON ds.[Email] = p.[Email]
                    INNER JOIN [dbo].[Driver] d ON p.[Id] = d.[PersonId]
                    WHERE ds.[SessionId] = @SessionId 
                    AND ds.[ProcessingStatus] = 'Pending'
                    AND ds.[CardNumber] IS NOT NULL
                    ORDER BY ds.[Id]
                )
                INSERT INTO [dbo].[Card] (
                    [Id], [CardNumber], [WeigandNumber], [CardType], 
                    [Active], [ExpiryDate], [CreatedDate]
                )
                SELECT 
                    NEWID(),
                    db.[CardNumber],
                    db.[WeigandNumber],
                    ISNULL(db.[CardType], 'Normal'),
                    1,
                    NULL,
                    GETUTCDATE()
                FROM DriverBatch db
                WHERE db.[CardNumber] IS NOT NULL;
                
                -- Update Driver records with CardDetailsId
                UPDATE d SET [CardDetailsId] = c.[Id]
                FROM [dbo].[Driver] d
                INNER JOIN [dbo].[Person] p ON d.[PersonId] = p.[Id]
                INNER JOIN [Staging].[DriverStaging] ds ON p.[Email] = ds.[Email]
                INNER JOIN [dbo].[Card] c ON ds.[CardNumber] = c.[CardNumber]
                WHERE ds.[SessionId] = @SessionId 
                AND ds.[ProcessingStatus] = 'Pending';
            END
            
            -- Update processing status
            WITH DriverBatch AS (
                SELECT TOP (@BatchSize) [Id]
                FROM [Staging].[DriverStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                ORDER BY [Id]
            )
            UPDATE ds SET 
                [ProcessingStatus] = 'Completed',
                [ProcessedAt] = GETUTCDATE()
            FROM [Staging].[DriverStaging] ds
            INNER JOIN DriverBatch db ON ds.[Id] = db.[Id];
            
            SET @ProcessedCount = @ProcessedCount + @BatchSize;
        END
        
        COMMIT TRANSACTION;
        
        SELECT 
            @ProcessedCount as ProcessedCount,
            @ErrorCount as ErrorCount,
            'Success' as Status;
            
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SELECT 
            @ProcessedCount as ProcessedCount,
            @ErrorCount + 1 as ErrorCount,
            ERROR_MESSAGE() as Status;
    END CATCH
END
GO

-- =====================================================================================
-- 4-Tier Access Permission Setup Stored Procedure
-- Creates Site/Department/Model/Vehicle access permissions in bulk
-- =====================================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_BulkCreateAccessPermissions')
    DROP PROCEDURE [dbo].[sp_BulkCreateAccessPermissions]
GO

CREATE PROCEDURE [dbo].[sp_BulkCreateAccessPermissions]
    @SessionId UNIQUEIDENTIFIER,
    @AccessLevel NVARCHAR(50) = 'Department',
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @TotalCount INT;
    DECLARE @ErrorCount INT = 0;
    DECLARE @NormalDriverPermissionId UNIQUEIDENTIFIER;
    
    -- Get Normal Driver Permission ID
    SELECT @NormalDriverPermissionId = [Id] 
    FROM [dbo].[Permission] 
    WHERE [Name] = 'Normal Driver' OR [Name] = 'Driver';
    
    IF @NormalDriverPermissionId IS NULL
    BEGIN
        SELECT 0 as ProcessedCount, 1 as ErrorCount, 'Normal Driver Permission not found' as Status;
        RETURN;
    END
    
    -- Get total count for processing
    SELECT @TotalCount = COUNT(*) 
    FROM [Staging].[AccessPermissionStaging] 
    WHERE [SessionId] = @SessionId 
    AND [ProcessingStatus] = 'Pending'
    AND [AccessLevel] = @AccessLevel;
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        -- Process based on access level
        IF @AccessLevel = 'Site'
        BEGIN
            -- Create Site Vehicle Normal Card Access
            WITH AccessBatch AS (
                SELECT TOP (@BatchSize) *
                FROM [Staging].[AccessPermissionStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                AND [AccessLevel] = 'Site'
                ORDER BY [Id]
            )
            INSERT INTO [dbo].[SiteVehicleNormalCardAccess] (
                [Id], [SiteId], [PermissionId], [CardId]
            )
            SELECT DISTINCT
                NEWID(),
                ab.[SiteId],
                @NormalDriverPermissionId,
                c.[Id]
            FROM AccessBatch ab
            INNER JOIN [dbo].[Card] c ON ab.[WeigandNumber] = c.[WeigandNumber]
            WHERE ab.[SiteId] IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM [dbo].[SiteVehicleNormalCardAccess] svnca
                WHERE svnca.[SiteId] = ab.[SiteId]
                AND svnca.[CardId] = c.[Id]
                AND svnca.[PermissionId] = @NormalDriverPermissionId
            );
        END
        ELSE IF @AccessLevel = 'Department'
        BEGIN
            -- Create Department Vehicle Normal Card Access
            WITH AccessBatch AS (
                SELECT TOP (@BatchSize) *
                FROM [Staging].[AccessPermissionStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                AND [AccessLevel] = 'Department'
                ORDER BY [Id]
            )
            INSERT INTO [dbo].[DepartmentVehicleNormalCardAccess] (
                [Id], [DepartmentId], [PermissionId], [CardId]
            )
            SELECT DISTINCT
                NEWID(),
                ab.[DepartmentId],
                @NormalDriverPermissionId,
                c.[Id]
            FROM AccessBatch ab
            INNER JOIN [dbo].[Card] c ON ab.[WeigandNumber] = c.[WeigandNumber]
            WHERE ab.[DepartmentId] IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM [dbo].[DepartmentVehicleNormalCardAccess] dvnca
                WHERE dvnca.[DepartmentId] = ab.[DepartmentId]
                AND dvnca.[CardId] = c.[Id]
                AND dvnca.[PermissionId] = @NormalDriverPermissionId
            );
        END
        ELSE IF @AccessLevel = 'Model'
        BEGIN
            -- Create Model Vehicle Normal Card Access
            WITH AccessBatch AS (
                SELECT TOP (@BatchSize) *
                FROM [Staging].[AccessPermissionStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                AND [AccessLevel] = 'Model'
                ORDER BY [Id]
            )
            INSERT INTO [dbo].[ModelVehicleNormalCardAccess] (
                [Id], [ModelId], [PermissionId], [CardId], [DepartmentId]
            )
            SELECT DISTINCT
                NEWID(),
                ab.[TargetModelId],
                @NormalDriverPermissionId,
                c.[Id],
                ab.[DepartmentId]
            FROM AccessBatch ab
            INNER JOIN [dbo].[Card] c ON ab.[WeigandNumber] = c.[WeigandNumber]
            WHERE ab.[TargetModelId] IS NOT NULL 
            AND ab.[DepartmentId] IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM [dbo].[ModelVehicleNormalCardAccess] mvnca
                WHERE mvnca.[ModelId] = ab.[TargetModelId]
                AND mvnca.[CardId] = c.[Id]
                AND mvnca.[DepartmentId] = ab.[DepartmentId]
                AND mvnca.[PermissionId] = @NormalDriverPermissionId
            );
        END
        ELSE IF @AccessLevel = 'Vehicle'
        BEGIN
            -- Create Per Vehicle Normal Card Access
            WITH AccessBatch AS (
                SELECT TOP (@BatchSize) *
                FROM [Staging].[AccessPermissionStaging]
                WHERE [SessionId] = @SessionId 
                AND [ProcessingStatus] = 'Pending'
                AND [AccessLevel] = 'Vehicle'
                ORDER BY [Id]
            )
            INSERT INTO [dbo].[PerVehicleNormalCardAccess] (
                [Id], [VehicleId], [PermissionId], [CardId]
            )
            SELECT DISTINCT
                NEWID(),
                ab.[TargetVehicleId],
                @NormalDriverPermissionId,
                c.[Id]
            FROM AccessBatch ab
            INNER JOIN [dbo].[Card] c ON ab.[WeigandNumber] = c.[WeigandNumber]
            WHERE ab.[TargetVehicleId] IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM [dbo].[PerVehicleNormalCardAccess] pvnca
                WHERE pvnca.[VehicleId] = ab.[TargetVehicleId]
                AND pvnca.[CardId] = c.[Id]
                AND pvnca.[PermissionId] = @NormalDriverPermissionId
            );
        END
        
        -- Update processing status for current access level
        UPDATE aps SET 
            [ProcessingStatus] = 'Completed',
            [ProcessedAt] = GETUTCDATE()
        FROM [Staging].[AccessPermissionStaging] aps
        WHERE aps.[SessionId] = @SessionId 
        AND aps.[AccessLevel] = @AccessLevel
        AND aps.[ProcessingStatus] = 'Pending';
        
        SET @ProcessedCount = @@ROWCOUNT;
        
        COMMIT TRANSACTION;
        
        SELECT 
            @ProcessedCount as ProcessedCount,
            @ErrorCount as ErrorCount,
            'Success' as Status;
            
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SELECT 
            @ProcessedCount as ProcessedCount,
            @ErrorCount + 1 as ErrorCount,
            ERROR_MESSAGE() as Status;
    END CATCH
END
GO

-- =====================================================================================
-- Session Progress Update Stored Procedure
-- Updates session metrics and progress tracking in real-time
-- =====================================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_UpdateSeederSessionProgress')
    DROP PROCEDURE [dbo].[sp_UpdateSeederSessionProgress]
GO

CREATE PROCEDURE [dbo].[sp_UpdateSeederSessionProgress]
    @SessionId UNIQUEIDENTIFIER,
    @CurrentOperation NVARCHAR(100) = NULL,
    @ProgressPercentage DECIMAL(5,2) = NULL,
    @ThroughputRecordsPerSecond DECIMAL(10,2) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Calculate current session metrics
    DECLARE @ProcessedDrivers INT = 0;
    DECLARE @ProcessedVehicles INT = 0;
    DECLARE @ProcessedCards INT = 0;
    DECLARE @ProcessedAccessRecords INT = 0;
    DECLARE @TotalRows INT = 0;
    DECLARE @SuccessfulRows INT = 0;
    DECLARE @FailedRows INT = 0;
    
    -- Count completed records by type
    SELECT @ProcessedDrivers = COUNT(*) 
    FROM [Staging].[DriverStaging] 
    WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Completed';
    
    SELECT @ProcessedVehicles = COUNT(*) 
    FROM [Staging].[VehicleStaging] 
    WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Completed';
    
    SELECT @ProcessedCards = COUNT(*) 
    FROM [Staging].[CardStaging] 
    WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Completed';
    
    SELECT @ProcessedAccessRecords = COUNT(*) 
    FROM [Staging].[AccessPermissionStaging] 
    WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Completed';
    
    -- Calculate totals
    SET @SuccessfulRows = @ProcessedDrivers + @ProcessedVehicles + @ProcessedCards + @ProcessedAccessRecords;
    
    SELECT @FailedRows = COUNT(*) 
    FROM (
        SELECT 1 FROM [Staging].[DriverStaging] WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Failed'
        UNION ALL
        SELECT 1 FROM [Staging].[VehicleStaging] WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Failed'
        UNION ALL
        SELECT 1 FROM [Staging].[CardStaging] WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Failed'
        UNION ALL
        SELECT 1 FROM [Staging].[AccessPermissionStaging] WHERE [SessionId] = @SessionId AND [ProcessingStatus] = 'Failed'
    ) AS FailedRecords;
    
    SET @TotalRows = @SuccessfulRows + @FailedRows;
    
    -- Update session with calculated metrics
    UPDATE [Staging].[SeederSession] 
    SET 
        [CurrentOperation] = ISNULL(@CurrentOperation, [CurrentOperation]),
        [ProgressPercentage] = ISNULL(@ProgressPercentage, [ProgressPercentage]),
        [ThroughputRecordsPerSecond] = ISNULL(@ThroughputRecordsPerSecond, [ThroughputRecordsPerSecond]),
        [ProcessedDrivers] = @ProcessedDrivers,
        [ProcessedVehicles] = @ProcessedVehicles,
        [ProcessedCards] = @ProcessedCards,
        [ProcessedAccessRecords] = @ProcessedAccessRecords,
        [TotalRows] = @TotalRows,
        [SuccessfulRows] = @SuccessfulRows,
        [FailedRows] = @FailedRows,
        [EstimatedCompletionTime] = CASE 
            WHEN @ThroughputRecordsPerSecond > 0 AND @FailedRows > 0 
            THEN DATEADD(SECOND, @FailedRows / @ThroughputRecordsPerSecond, GETUTCDATE()) 
            ELSE [EstimatedCompletionTime] 
        END
    WHERE [Id] = @SessionId;
    
    -- Return updated metrics
    SELECT 
        @SessionId as SessionId,
        @ProcessedDrivers as ProcessedDrivers,
        @ProcessedVehicles as ProcessedVehicles,
        @ProcessedCards as ProcessedCards,
        @ProcessedAccessRecords as ProcessedAccessRecords,
        @TotalRows as TotalRows,
        @SuccessfulRows as SuccessfulRows,
        @FailedRows as FailedRows,
        @ProgressPercentage as ProgressPercentage,
        @ThroughputRecordsPerSecond as ThroughputRecordsPerSecond;
END
GO

-- =====================================================================================
-- Cleanup and Performance Optimization
-- =====================================================================================

-- Grant execute permissions to appropriate roles
GRANT EXECUTE ON [dbo].[sp_BulkCreateVehicleSequence] TO [public];
GRANT EXECUTE ON [dbo].[sp_BulkCreatePersonDrivers] TO [public];
GRANT EXECUTE ON [dbo].[sp_BulkCreateAccessPermissions] TO [public];
GRANT EXECUTE ON [dbo].[sp_UpdateSeederSessionProgress] TO [public];

PRINT '========================================================================='
PRINT 'Phase 1.2.2: Optimized Stored Procedures - COMPLETED'
PRINT '========================================================================='
PRINT 'Created high-performance stored procedures:'
PRINT '✓ sp_BulkCreateVehicleSequence - Vehicle creation with dependencies'
PRINT '✓ sp_BulkCreatePersonDrivers - Person/Driver creation with card support'
PRINT '✓ sp_BulkCreateAccessPermissions - 4-tier access permission system'
PRINT '✓ sp_UpdateSeederSessionProgress - Real-time session metrics'
PRINT ''
PRINT 'Performance Features:'
PRINT '- Table-valued parameters for optimal batch processing'
PRINT '- Cursor-free operations for maximum performance'
PRINT '- Optimized execution plans with proper indexing'
PRINT '- Batch processing with configurable batch sizes'
PRINT '- Comprehensive error handling and rollback support'
PRINT '- Real-time progress tracking and metrics collection'
PRINT '========================================================================='
