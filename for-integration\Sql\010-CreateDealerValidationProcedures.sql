-- =============================================
-- FleetXQ Bulk Importer - Dealer Validation Procedures
-- Creates procedures for dealer selection and validation workflows
-- =============================================

-- =============================================
-- Procedure: usp_ValidateDealerSelection
-- Validates dealer selection and returns dealer-scoped data
-- =============================================
IF OBJECT_ID('[Staging].[usp_ValidateDealerSelection]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[usp_ValidateDealerSelection]
GO

CREATE PROCEDURE [Staging].[usp_ValidateDealerSelection]
    @DealerId UNIQUEIDENTIFIER = NULL,
    @DealerName NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @ValidDealerId UNIQUEIDENTIFIER;
    DECLARE @ValidDealerName NVARCHAR(100);
    
    BEGIN TRY
        -- Validate dealer by ID or Name
        IF @DealerId IS NOT NULL
        BEGIN
            SELECT @ValidDealerId = [Id], @ValidDealerName = [Name]
            FROM [dbo].[Dealer] 
            WHERE [Id] = @DealerId AND [Active] = 1;
        END
        ELSE IF @DealerName IS NOT NULL
        BEGIN
            SELECT @ValidDealerId = [Id], @ValidDealerName = [Name]
            FROM [dbo].[Dealer] 
            WHERE LTRIM(RTRIM([Name])) = LTRIM(RTRIM(@DealerName)) AND [Active] = 1;
        END
        
        -- Check if dealer was found
        IF @ValidDealerId IS NULL
        BEGIN
            SELECT 
                CAST(0 AS BIT) as [IsValid],
                'Selected dealer does not exist or is not active' as [ErrorMessage],
                NULL as [DealerId],
                NULL as [DealerName],
                0 as [CustomerCount],
                0 as [AvailableModuleCount];
            RETURN;
        END
        
        -- Return dealer validation results with counts
        SELECT 
            CAST(1 AS BIT) as [IsValid],
            NULL as [ErrorMessage],
            @ValidDealerId as [DealerId],
            @ValidDealerName as [DealerName],
            (SELECT COUNT(*) FROM [dbo].[Customer] WHERE [DealerId] = @ValidDealerId) as [CustomerCount],
            (SELECT COUNT(*) FROM [dbo].[vw_DealerAvailableModules] WHERE [DealerId] = @ValidDealerId) as [AvailableModuleCount];
        
        -- Return dealer-scoped customer list
        SELECT 
            [Id] as [CustomerId], 
            [CompanyName] as [CustomerName],
            (SELECT COUNT(*) FROM [dbo].[Site] WHERE [CustomerId] = c.[Id]) as [SiteCount],
            (SELECT COUNT(*) FROM [dbo].[Department] WHERE [CustomerId] = c.[Id]) as [DepartmentCount]
        FROM [dbo].[Customer] c
        WHERE [DealerId] = @ValidDealerId
        ORDER BY [CompanyName];
        
        -- Return available modules for this dealer
        SELECT 
            [ModuleId],
            [TechNumber],
            [IoTDevice],
            [ModuleType],
            [AvailabilityStatus]
        FROM [dbo].[vw_DealerAvailableModules]
        WHERE [DealerId] = @ValidDealerId
        ORDER BY [TechNumber];
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Dealer validation failed: ', ERROR_MESSAGE());
        
        SELECT 
            CAST(0 AS BIT) as [IsValid],
            @ErrorMessage as [ErrorMessage],
            NULL as [DealerId],
            NULL as [DealerName],
            0 as [CustomerCount],
            0 as [AvailableModuleCount];
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_PopulateDealerCaches
-- Populates dealer-specific validation caches for import session
-- =============================================
IF OBJECT_ID('[Staging].[usp_PopulateDealerCaches]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[usp_PopulateDealerCaches]
GO

CREATE PROCEDURE [Staging].[usp_PopulateDealerCaches]
    @ImportSessionId UNIQUEIDENTIFIER,
    @DealerId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @CustomerCount INT = 0;
    DECLARE @ModuleCount INT = 0;
    
    BEGIN TRY
        -- Clear existing caches for this session
        DELETE FROM [Staging].[DealerCustomerCache] WHERE [ImportSessionId] = @ImportSessionId;
        DELETE FROM [Staging].[DealerModuleCache] WHERE [ImportSessionId] = @ImportSessionId;
        
        -- Populate customer cache
        INSERT INTO [Staging].[DealerCustomerCache] 
            ([ImportSessionId], [DealerId], [CustomerId], [CustomerName], [IsValid])
        SELECT 
            @ImportSessionId,
            @DealerId,
            [Id],
            [CompanyName],
            1
        FROM [dbo].[Customer]
        WHERE [DealerId] = @DealerId;
        
        SET @CustomerCount = @@ROWCOUNT;
        
        -- Populate module cache
        INSERT INTO [Staging].[DealerModuleCache] 
            ([ImportSessionId], [DealerId], [ModuleId], [ModuleSerialNumber], [ModuleStatus], 
             [IsAllocatedToVehicle], [AssignedVehicleId], [AssignedVehicleHireNo], [IsAvailable])
        SELECT 
            @ImportSessionId,
            @DealerId,
            [ModuleId],
            [TechNumber],
            [ModuleStatus],
            [IsAllocatedToVehicle],
            [AssignedVehicleId],
            [AssignedVehicleHireNo],
            CASE WHEN [AvailabilityStatus] = 'Available' THEN 1 ELSE 0 END
        FROM [dbo].[vw_AvailableModules]
        WHERE [DealerId] = @DealerId;
        
        SET @ModuleCount = @@ROWCOUNT;
        
        -- Return cache population summary
        SELECT 
            @ImportSessionId as [ImportSessionId],
            @DealerId as [DealerId],
            @CustomerCount as [CustomersCached],
            @ModuleCount as [ModulesCached],
            GETUTCDATE() as [CacheCreatedAt];
        
        PRINT CONCAT('Populated dealer caches for session ', @ImportSessionId, 
                    ': ', @CustomerCount, ' customers, ', @ModuleCount, ' modules');
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Failed to populate dealer caches: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_ValidateDealerScoping
-- Comprehensive dealer scoping validation for import session
-- =============================================
IF OBJECT_ID('[Staging].[usp_ValidateDealerScoping]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[usp_ValidateDealerScoping]
GO

CREATE PROCEDURE [Staging].[usp_ValidateDealerScoping]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @SelectedDealerId UNIQUEIDENTIFIER;
    DECLARE @DealerValidationEnabled BIT;
    DECLARE @DriverErrors INT = 0;
    DECLARE @VehicleErrors INT = 0;
    
    BEGIN TRY
        -- Get session dealer configuration
        SELECT 
            @SelectedDealerId = [SelectedDealerId],
            @DealerValidationEnabled = [DealerValidationEnabled]
        FROM [Staging].[ImportSession]
        WHERE [Id] = @ImportSessionId;
        
        -- Skip validation if dealer scoping is disabled
        IF @DealerValidationEnabled = 0 OR @SelectedDealerId IS NULL
        BEGIN
            PRINT 'Dealer scoping validation skipped (not enabled or no dealer selected)';
            RETURN;
        END
        
        PRINT CONCAT('Starting dealer scoping validation for session: ', @ImportSessionId, 
                    ', dealer: ', @SelectedDealerId);
        
        -- Validate driver customer assignments
        UPDATE di
        SET [DealerValidationError] = CONCAT(
                CASE WHEN [DealerValidationError] IS NOT NULL THEN [DealerValidationError] + '; ' ELSE '' END,
                'Customer "', di.[CustomerName], '" is not accessible to selected dealer'
            ),
            [ValidationStatus] = 'Invalid'
        FROM [Staging].[DriverImport] di
        WHERE di.[ImportSessionId] = @ImportSessionId
            AND di.[ValidationStatus] = 'Valid'
            AND NOT EXISTS (
                SELECT 1 FROM [Staging].[DealerCustomerCache] dcc
                WHERE dcc.[ImportSessionId] = @ImportSessionId
                    AND dcc.[CustomerId] = di.[CustomerId]
                    AND dcc.[IsValid] = 1
            );
        
        SET @DriverErrors = @@ROWCOUNT;
        
        -- Validate vehicle customer and module assignments
        UPDATE vi
        SET [DealerValidationError] = CONCAT(
                CASE WHEN [DealerValidationError] IS NOT NULL THEN [DealerValidationError] + '; ' ELSE '' END,
                CASE 
                    WHEN NOT EXISTS (SELECT 1 FROM [Staging].[DealerCustomerCache] dcc 
                                   WHERE dcc.[ImportSessionId] = @ImportSessionId 
                                     AND dcc.[CustomerId] = vi.[CustomerId] AND dcc.[IsValid] = 1)
                        THEN 'Customer "' + vi.[CustomerName] + '" is not accessible to selected dealer'
                    ELSE ''
                END,
                CASE 
                    WHEN NOT EXISTS (SELECT 1 FROM [Staging].[DealerModuleCache] dmc 
                                   WHERE dmc.[ImportSessionId] = @ImportSessionId 
                                     AND dmc.[ModuleId] = vi.[ModuleId] AND dmc.[IsAvailable] = 1)
                        THEN CASE WHEN LEN([DealerValidationError]) > 0 THEN '; ' ELSE '' END + 
                             'Module "' + vi.[ModuleSerialNumber] + '" is not available to selected dealer'
                    ELSE ''
                END
            ),
            [ValidationStatus] = 'Invalid'
        FROM [Staging].[VehicleImport] vi
        WHERE vi.[ImportSessionId] = @ImportSessionId
            AND vi.[ValidationStatus] = 'Valid'
            AND (
                NOT EXISTS (SELECT 1 FROM [Staging].[DealerCustomerCache] dcc 
                           WHERE dcc.[ImportSessionId] = @ImportSessionId 
                             AND dcc.[CustomerId] = vi.[CustomerId] AND dcc.[IsValid] = 1)
                OR 
                NOT EXISTS (SELECT 1 FROM [Staging].[DealerModuleCache] dmc 
                           WHERE dmc.[ImportSessionId] = @ImportSessionId 
                             AND dmc.[ModuleId] = vi.[ModuleId] AND dmc.[IsAvailable] = 1)
            );
        
        SET @VehicleErrors = @@ROWCOUNT;
        
        -- Return validation summary
        SELECT 
            @ImportSessionId as [ImportSessionId],
            @SelectedDealerId as [SelectedDealerId],
            @DriverErrors as [DriverErrorsFound],
            @VehicleErrors as [VehicleErrorsFound],
            (@DriverErrors + @VehicleErrors) as [TotalErrorsFound],
            GETUTCDATE() as [ValidationCompletedAt];
        
        PRINT CONCAT('Dealer scoping validation completed. Errors found: ', 
                    @DriverErrors, ' drivers, ', @VehicleErrors, ' vehicles');
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = CONCAT('Dealer scoping validation failed: ', ERROR_MESSAGE());
        PRINT @ErrorMessage;
        THROW;
    END CATCH
END
GO

-- =============================================
-- Procedure: usp_GetDealerSummary
-- Returns summary information for all dealers
-- =============================================
IF OBJECT_ID('[Staging].[usp_GetDealerSummary]', 'P') IS NOT NULL
    DROP PROCEDURE [Staging].[usp_GetDealerSummary]
GO

CREATE PROCEDURE [Staging].[usp_GetDealerSummary]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        d.[Id] as [DealerId],
        d.[Name] as [DealerName],
        d.[Active] as [IsActive],
        d.[Description],
        d.[SubDomain],
        mas.[TotalModules],
        mas.[AvailableModules],
        mas.[AssignedModules],
        mas.[AvailabilityPercentage],
        (SELECT COUNT(*) FROM [dbo].[Customer] WHERE [DealerId] = d.[Id]) as [CustomerCount],
        (SELECT COUNT(DISTINCT v.[Id]) 
         FROM [dbo].[Vehicle] v 
         INNER JOIN [dbo].[Customer] c ON v.[CustomerId] = c.[Id] 
         WHERE c.[DealerId] = d.[Id]) as [VehicleCount]
    FROM [dbo].[Dealer] d
    LEFT JOIN [dbo].[vw_ModuleAssignmentSummary] mas ON d.[Id] = mas.[DealerId]
    WHERE d.[Active] = 1
    ORDER BY d.[Name];
END
GO

PRINT 'Created dealer validation procedures successfully';
