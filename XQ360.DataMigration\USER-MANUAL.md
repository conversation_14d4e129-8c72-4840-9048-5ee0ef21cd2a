# 📋 XQ360 Data Migration - User Guide

*A simple guide for importing your data using CSV files*

---

## 🚀 Quick Start (3 Steps!)

1. **🧪 Test system** → Run `dotnet test` to check database compatibility
2. **Run migration** → Put CSV files in `CSV_Input` folder, then `dotnet run --migrate-all`
3. **Check results** → Look at report in `Reports` folder to see what worked

**✅ The migration is designed to be safe** - you can run it directly with your full dataset!

**Need help?** Jump to any section below ⬇️

---

## 📖 Table of Contents

**🎯 GETTING STARTED**
- [What does this tool do?](#what-does-this-tool-do)
- [Before you start](#before-you-start)
- [Running your first migration](#running-your-first-migration)

**📄 PREPARING YOUR CSV FILES**
- [CSV file rules](#csv-file-rules)
- [Required file names](#required-file-names)
- [Data format examples](#data-format-examples)
- [Common data mistakes](#common-data-mistakes)

**🔄 HOW THE SOFTWARE WORKS**
- [Migration order (important!)](#migration-order-important)
- [How data gets processed](#how-data-gets-processed)
- [When data gets skipped](#when-data-gets-skipped)

**📊 UNDERSTANDING RESULTS**
- [Reading your migration report](#reading-your-migration-report)
- [Fixing failed records](#fixing-failed-records)
- [What to do next](#what-to-do-next)

**❓ HELP & TROUBLESHOOTING**
- [Common problems](#common-problems)
- [Error messages explained](#error-messages-explained)

---

## 🎯 GETTING STARTED

### What does this tool do?

This tool helps you move data from Excel/CSV files into your XQ360 system. Instead of typing everything manually, you can:

✅ Import all your drivers and staff  
✅ Add vehicles to the system  
✅ Set up access cards  
✅ Create safety checklists  
✅ Configure user permissions  

### Before you start

**✅ You need:**
- Your CSV files ready (see examples below)
- Permission to access the XQ360 system
- About 15-30 minutes for the import



**🧪 SYSTEM TESTING (REQUIRED):**
1. **Test the system** - Run `dotnet test` to make sure migration software works with your database
2. **All tests must pass** before proceeding with any migration

**📊 MIGRATION IS DESIGNED TO BE SAFE:**
- **Safe to run multiple times** - won't create duplicates
- **Detailed reports** show exactly what worked and what didn't
- **Partial failures are OK** - fix the issues and run again
- **Sample data testing is optional** but recommended for very large datasets (1000+ records)

**💡 The migration tool is built to handle problems:**
- Validates all data before importing
- Skips duplicates automatically
- Provides clear error messages with fix instructions
- You can always re-run after fixing CSV files

### Running your first migration

**🧪 STEP 1: Test the system (REQUIRED)**
1. **Open command prompt** in the project folder
2. **Run the system test:** `dotnet test` (checks if migration software works with your database)
3. **Wait for test results** - All tests should pass ✅
4. **If tests fail** - Contact your IT administrator before proceeding

**🚀 STEP 2: Run your migration**
1. **Put your CSV files** in the `CSV_Input` folder
2. **Run:** `dotnet run --migrate-all`
3. **Wait** for it to finish (watch the progress messages)
4. **Check the report** in the `Reports` folder

**📊 What happens during migration:**
- Tool validates each record before importing
- Skips duplicates and records already in system
- Creates detailed report showing successes and failures
- You can fix CSV files and re-run for any failed records

**💡 For very large datasets (1000+ records):** Consider testing with a few records first, but this is optional - the migration is designed to handle large datasets safely.

---

## 📄 PREPARING YOUR CSV FILES

### CSV file rules

**📋 BASIC RULES:**
- Save files as CSV (not Excel .xlsx)
- Use exact file names (see list below)
- Don't leave required fields empty
- No special characters in names (stick to letters, numbers, spaces)
- One person/vehicle/card per row

**📝 FORMATTING RULES:**
- **True/False fields:** Type `true` or `false` (not yes/no)
- **Numbers:** No commas (write 1000, not 1,000)
- **Names:** First Name and Last Name in separate columns
- **Dates:** Not used in current CSV files
- **Text:** Keep it simple, avoid quotes and special symbols

### Required file names

**📁 Place these files in `CSV_Input` folder with EXACT names:**

| What you're importing | File name (EXACT) |
|----------------------|-------------------|
| **Drivers and Staff** | `PERSON_IMPORT_TEMPLATE.csv` |
| **Vehicles** | `VEHICLE_IMPORT.csv` |
| **Access Cards** | `CARD_IMPORT.csv` |
| **Safety Checklists** | `PREOP_CHECKLIST_IMPORT.csv` |
| **Spare Parts** | `SPARE_MODEL_IMPORT_TEMPLATE.csv` |
| **Supervisor Access** | `SUPERVISOR_ACCESS_IMPORT.csv` |
| **Blacklisted Drivers** | `DRIVER_BLACKLIST_IMPORT.csv` |
| **Website Users** | `WEBSITE_USER_IMPORT.csv` |

### Data format examples

#### 👥 PERSON_IMPORT_TEMPLATE.csv (Drivers & Staff)
```csv
Customer,Site,Department,First Name,Last Name,Send Deny Message,Website Access,IsDriver,IsSupervisor,VOR Activate/Deactivate,Normal Driver Access,CanUnlockVehicle
ABC Company,Main Warehouse,Forklift Ops,John,Smith,true,false,true,false,true,true,true
ABC Company,Main Warehouse,Forklift Ops,Jane,Doe,true,true,true,true,true,true,true
```

**📋 Field explanations:**
- **Customer/Site/Department:** Must match exactly what's in your system
- **First Name/Last Name:** Required for every person
- **IsDriver:** `true` if they can drive vehicles, `false` if not
- **IsSupervisor:** `true` if they can override safety systems
- **Website Access:** `true` if they need to log into the website

#### 🚗 VEHICLE_IMPORT.csv (Vehicles)
```csv
Dealer,Customer,Site,Department Name,Device ID,Serial No,Hire No,Model Name,Impact Lockout,IsCanbus,On Hire,Timeout Enabled,Idle Timer,Question Timeout,Show Comment,Randomisation,VOR Status,Full Lockout,Default Technician Access,Pedestrian Safety,Checklist Type
ABC Dealer,ABC Company,Main Warehouse,Forklift Ops,DEV001,SN12345,H001,Toyota Forklift,true,false,true,true,300,60,true,true,false,false,true,true,Daily
```

**📋 Field explanations:**
- **Device ID/Serial No/Hire No:** Must be unique for each vehicle
- **Model Name:** The type of vehicle (forklift, truck, etc.)
- **Idle Timer:** Minutes before vehicle shuts down (300 = 5 minutes)
- **Checklist Type:** Usually "Daily" for daily safety checks

#### 💳 CARD_IMPORT.csv (Access Cards)
```csv
Dealer,Customer,Site,Department Name,First Name,Last Name,Facility Code,Card Type,Card No,Reader Type,Weigand,Access Level
ABC Dealer,ABC Company,Main Warehouse,Forklift Ops,John,Smith,123,Proximity,456,HID,789012,Standard
```

**📋 Field explanations:**
- **First Name/Last Name:** Must match exactly with someone from PERSON_IMPORT
- **Card No/Weigand:** Must be unique numbers for each card
- **Access Level:** Usually "Standard" or "Supervisor"

### Common data mistakes

**❌ AVOID THESE MISTAKES:**

| Problem | Example | Fix |
|---------|---------|-----|
| **Empty required fields** | First Name: (blank) | Fill in all required information |
| **Wrong true/false format** | IsDriver: "yes" | Use `true` or `false` |
| **Duplicate card numbers** | Two cards with Weigand "123456" | Make each card number unique |
| **Names don't match** | Card for "Jon Smith" but person is "John Smith" | Spell names exactly the same |
| **Missing departments** | Department "Warehouse A" doesn't exist | Use existing department names |
| **Special characters** | Name: "John O'Connor" | Use "John OConnor" instead |

---

## 🔄 HOW THE SOFTWARE WORKS

### Migration order (important!)

**The tool imports data in this specific order:**

```
1. Spare Parts/Models → Creates equipment types
2. Safety Checklists → Sets up safety questions  
3. Vehicles → Adds all vehicles to system
4. People → Creates driver and staff accounts
5. Access Cards → Links cards to people and vehicles
6. Supervisor Access → Gives special permissions
7. Driver Blacklist → Removes access for banned drivers
8. Website Users → Creates login accounts
9. **Vehicle Sync Settings** → **Final step** - synchronizes IoT device configurations for all imported vehicles
```

**⚠️ Why this order matters:**
- You can't create cards for people who don't exist yet
- You can't assign vehicle access without vehicles and people first
- Safety checklists need to exist before vehicles can use them

**💡 That's why you should always use `--migrate-all` - it does everything in the right order!**

### Vehicle Sync Settings (Step 9)

**🔄 What is Vehicle Sync Settings?**

This is the **final step** that synchronizes your vehicle IoT devices with the XQ360 system. After all your vehicles are imported (Step 3), this step ensures each vehicle's electronic systems are properly configured.

**📡 What it does:**
- **Reads Device IDs** from your VEHICLE_IMPORT.csv file
- **Connects to each vehicle's IoT device** via the internet
- **Synchronizes settings** like safety timers, lockout rules, and checklist configurations
- **Updates device firmware settings** to match your XQ360 system configuration

**⚙️ Technical details:**
- Only syncs vehicles that have Device IDs in your CSV
- Processes vehicles one at a time with small delays (avoids overwhelming the network)
- Requires internet connection and vehicle devices to be online
- Creates detailed report showing which vehicles synced successfully

**❓ When might sync fail:**
- Vehicle device is offline or disconnected
- Network connectivity issues
- Device ID doesn't match any real device
- Vehicle is not powered on

**✅ This is normal and safe:**
- Failed syncs can be re-run later when devices are online
- Successfully synced devices won't be re-synced unnecessarily
- No harm if some vehicles fail to sync initially

### How data gets processed

**For each CSV file, the software:**

1. **📖 Reads your CSV file** row by row
2. **🔍 Checks each row** for required information
3. **✅ Validates data** (checks if people/departments exist)
4. **🔗 Links related data** (connects cards to people, etc.)
5. **💾 Saves to database** (only if everything is correct)
6. **📝 Creates a report** showing what worked and what didn't

**🛡️ Safety features:**
- If one row fails, others still get processed
- Nothing gets half-saved (either complete success or nothing)
- You can run the tool multiple times safely
- Duplicate data gets skipped automatically

### When data gets skipped

**✅ Data gets SKIPPED (not an error) when:**

| Migration Type | Skipped When | Why |
|----------------|--------------|-----|
| **People** | Person with same name already exists in department | Prevents duplicates |
| **Vehicles** | Vehicle with same Device ID already exists | Prevents duplicates |
| **Cards** | Card number (Weigand) already exists | Card numbers must be unique |
| **Spare Parts** | Part with same ID already exists | Prevents duplicates |
| **Vehicle Sync** | Device already has current settings | Avoids unnecessary updates |

**❌ Data FAILS (needs fixing) when:**

| Migration Type | Fails When | How to Fix |
|----------------|------------|------------|
| **Cards** | Person doesn't exist | Import people first, or check name spelling |
| **Vehicle Access** | Vehicle or person missing | Import vehicles and people first |
| **Vehicle Sync** | Device offline or unreachable | Check vehicle power and internet connection |
| **Any** | Required field is empty | Fill in all required information |
| **Any** | Department doesn't exist | Check department names match your system |

---

## 📊 UNDERSTANDING RESULTS

### Reading your migration report

**After migration, check:** `Reports/Full-Migration-Report-YYYYMMDD-HHMMSS.txt`

**📋 Report sections explained:**

```
✅ PERSON MIGRATION - COMPLETED SUCCESSFULLY
📅 Date: 2025-01-23 14:30:15
📊 Records Processed: 25      ← Total rows in your CSV
✅ Successfully Imported: 20   ← New records added
⏭️ Records Skipped: 5         ← Already existed (usually OK)
❌ Records Failed: 0          ← Need to be fixed
```

**🎯 Focus on the FAILED RECORDS section - these need your attention!**

### Fixing failed records

**Example of failed records:**

```
❌ FAILED RECORDS REQUIRING CSV FIXES:
Row | Record      | Reason                    | Action Needed
----|-------------|---------------------------|------------------
003 | John Smith  | Person not found in dept  | Check department name
007 | Jane Doe    | Missing First Name        | Add first name to CSV
012 | Card 12345  | Duplicate card number     | Use different card number
```

**🔧 How to fix:**

1. **Open your CSV file** in Excel or text editor
2. **Find the row number** from the report (row 3, 7, 12 in example)
3. **Make the suggested fix** (check spelling, add missing data, etc.)
4. **Save the CSV file**
5. **Run the migration again** with `dotnet run --migrate-[type]`

**💡 Pro tip:** Fix all similar errors at once (all missing names, all duplicate numbers, etc.)

### What to do next

**✅ If everything succeeded:**
- Check that data appears correctly in XQ360
- Archive your CSV files for future reference
- Document any manual adjustments needed

**⚠️ If some records failed:**
1. Fix the CSV files using the report guidance
2. Re-run the specific migration: `dotnet run --migrate-[type]`
3. Check the new report to confirm fixes worked

**❌ If many records failed:**
- Double-check your CSV file format
- Verify department/site names match your system
- Consider testing with just a few rows first

---

## ❓ HELP & TROUBLESHOOTING

### Common problems

#### 🧪 "Tests are failing"
**Problem:** Migration software doesn't work with your database  
**Fix:** 
- Contact your IT administrator immediately
- Don't proceed with migration until tests pass
- Database might need updates or configuration changes

#### 🚫 "CSV file not found"
**Problem:** File is missing or named wrong  
**Fix:** 
- Check file is in `CSV_Input` folder
- Use exact file names from the list above
- Make sure file is saved as CSV (not Excel)

#### 🔐 "Authentication failed"
**Problem:** Can't connect to XQ360 system  
**Fix:** Contact your IT administrator - they need to check login settings

#### 📄 "Person not found in department"
**Problem:** Names don't match or department doesn't exist  
**Fix:** 
- Check spelling of names exactly
- Verify department exists in XQ360
- Import people before importing cards

#### 🔢 "Duplicate card number"
**Problem:** Same card number used twice  
**Fix:** 
- Check CSV for duplicate Weigand numbers
- Make sure each card has a unique number
- Check if cards already exist in system

#### 📂 "Required field missing"
**Problem:** Empty cells in required columns  
**Fix:** 
- Fill in all required information
- Check for extra commas creating empty columns
- Make sure CSV headers match exactly

### Error messages explained

| Error Message | What it means | How to fix |
|---------------|---------------|------------|
| "Person not found" | Can't find the person in database | Check name spelling, import people first |
| "Duplicate Weigand" | Card number already used | Use different card number |
| "Department not found" | Department doesn't exist | Check department name spelling |
| "Missing required field" | Empty required information | Fill in the missing data |
| "Invalid format" | Data in wrong format | Use true/false for checkboxes, numbers for number fields |
| "Connection failed" | Can't reach XQ360 system | Contact IT support |

**🆘 Still need help?**
1. Save your migration report file
2. Note the exact error message
3. Contact your system administrator
4. Share the report file and CSV file (remove sensitive data first)

---

## 📋 Quick Reference

### ⚡ Commands you need
```bash
# ALWAYS RUN TESTS FIRST
dotnet test

# Import everything (recommended)
dotnet run --migrate-all

# Import specific types (only if needed)
dotnet run --migrate-persons
dotnet run --migrate-vehicles  
dotnet run --migrate-cards-and-vehicle-access

# Sync vehicle IoT settings (after vehicle import)
dotnet run --sync-vehicle-settings

# Get help
dotnet run --help
```

### 📁 Important folders
```
CSV_Input/           ← Put your CSV files here
Reports/             ← Check results here
Logs/                ← Technical details (for IT support)
```

### ✅ Before you start checklist
- [ ] **Run system tests first** - `dotnet test` passes successfully
- [ ] CSV files in correct folder with exact names
- [ ] All required fields filled in
- [ ] No duplicate card numbers or device IDs
- [ ] Department names match your system
- [ ] Names spelled consistently
- [ ] File saved as CSV format
- [ ] **Optional for large datasets:** Test with sample data first (1000+ records)

---

**🎯 Remember: The migration is designed to be safe - test the system, then run with confidence!**

**🛡️ You don't need to worry about:** Database backups or sample data testing (unless very large datasets)  
**✅ You DO need to focus on:** System testing first - then the migration handles the rest safely

**📞 Need help?** Contact your system administrator with your migration report and CSV files. 