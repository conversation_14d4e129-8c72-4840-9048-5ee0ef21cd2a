using Microsoft.AspNetCore.Mvc;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// MVC Controller for the Data Seeder user interface
/// </summary>
public class SeederController : Controller
{
    private readonly ILogger<SeederController> _logger;
    private readonly IEnvironmentConfigurationService _environmentService;

    public SeederController(
        ILogger<SeederController> logger,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    /// <summary>
    /// Display the main seeder wizard interface
    /// </summary>
    /// <returns>Seeder wizard view</returns>
    public IActionResult Index()
    {
        var availableEnvironments = _environmentService.GetAvailableEnvironments()
            .Select(env => new EnvironmentOption
            {
                Key = env.Key,
                DisplayName = env.DisplayName,
                Description = env.Description
            }).ToList();

        var model = new BulkSeederViewModel
        {
            CurrentEnvironment = _environmentService.CurrentEnvironmentKey,
            SelectedEnvironment = _environmentService.CurrentEnvironmentKey, // Default to current
            AvailableEnvironments = availableEnvironments
        };

        return View(model);
    }

    /// <summary>
    /// Handle form submission to create a new seeding session
    /// This redirects to the API endpoint
    /// </summary>
    /// <param name="model">Seeder view model</param>
    /// <returns>Redirect or view with validation errors</returns>
    [HttpPost]
    public async Task<IActionResult> CreateSession(BulkSeederViewModel model)
    {
        try
        {
            // If an environment is selected and different from current, switch to it
            if (!string.IsNullOrEmpty(model.SelectedEnvironment) && 
                model.SelectedEnvironment != _environmentService.CurrentEnvironmentKey)
            {
                try
                {
                    _environmentService.SetCurrentEnvironment(model.SelectedEnvironment);
                    _logger.LogInformation("Environment switched to {Environment} for seeding operation", model.SelectedEnvironment);
                }
                catch (ArgumentException ex)
                {
                    _logger.LogWarning(ex, "Failed to switch to environment {Environment}", model.SelectedEnvironment);
                    ModelState.AddModelError("SelectedEnvironment", $"Invalid environment selected: {model.SelectedEnvironment}");
                }
            }

            if (!ModelState.IsValid)
            {
                // Reload data for the view
                PopulateEnvironmentData(model);
                
                return View("Index", model);
            }

            // The actual session creation will be handled by JavaScript calling the API
            // This is here as a fallback for non-JavaScript scenarios
            
            TempData["SuccessMessage"] = "Seeding session created successfully!";
            return RedirectToAction("Index");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating seeding session");
            ModelState.AddModelError("", "An error occurred while creating the seeding session. Please try again.");
            
            // Reload data for the view
            PopulateEnvironmentData(model);
            
            return View("Index", model);
        }
    }

    /// <summary>
    /// Helper method to populate environment data in the view model
    /// </summary>
    /// <param name="model">The view model to populate</param>
    private void PopulateEnvironmentData(BulkSeederViewModel model)
    {
        var availableEnvironments = _environmentService.GetAvailableEnvironments()
            .Select(env => new EnvironmentOption
            {
                Key = env.Key,
                DisplayName = env.DisplayName,
                Description = env.Description
            }).ToList();

        model.CurrentEnvironment = _environmentService.CurrentEnvironmentKey;
        model.AvailableEnvironments = availableEnvironments;
        model.Dealers = new List<DealerInfo>();
        model.Customers = new List<CustomerInfo>();
        
        // Set selected environment to current if not already set
        if (string.IsNullOrEmpty(model.SelectedEnvironment))
        {
            model.SelectedEnvironment = _environmentService.CurrentEnvironmentKey;
        }
    }

    /// <summary>
    /// Handle environment switching via AJAX
    /// </summary>
    /// <param name="environmentKey">The environment key to switch to</param>
    /// <returns>JSON result indicating success or failure</returns>
    [HttpPost]
    public IActionResult SwitchEnvironment([FromBody] string environmentKey)
    {
        try
        {
            if (string.IsNullOrEmpty(environmentKey))
            {
                return Json(new { success = false, message = "Environment key is required" });
            }

            _environmentService.SetCurrentEnvironment(environmentKey);
            _logger.LogInformation("Environment switched to {Environment}", environmentKey);

            return Json(new { 
                success = true, 
                message = $"Environment switched to {environmentKey}",
                newEnvironment = environmentKey
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Failed to switch to environment {Environment}", environmentKey);
            return Json(new { 
                success = false, 
                message = $"Invalid environment: {environmentKey}" 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error switching environment to {Environment}", environmentKey);
            return Json(new { 
                success = false, 
                message = "An error occurred while switching environments" 
            });
        }
    }

    /// <summary>
    /// Display sessions management page (placeholder for future implementation)
    /// </summary>
    /// <returns>Sessions view</returns>
    public IActionResult Sessions()
    {
        // This would show a list of seeding sessions
        // For now, redirect to the main seeder page
        return RedirectToAction("Index");
    }
}
