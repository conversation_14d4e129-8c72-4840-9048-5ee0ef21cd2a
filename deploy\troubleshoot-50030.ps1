# XQ360 Data Migration - HTTP 500.30 Troubleshooting Script
# Run this script on your remote server as Administrator

Write-Host "XQ360 Data Migration - HTTP 500.30 Troubleshooting Script" -ForegroundColor Green
Write-Host "This script will help diagnose the ASP.NET Core startup failure" -ForegroundColor Yellow
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}
Write-Host "✅ Running as Administrator" -ForegroundColor Green

# Define paths
$deployPath = "C:\inetpub\wwwroot\XQ360Migration"
$appPoolName = "XQ360MigrationPool"

Write-Host "Deploy Path: $deployPath" -ForegroundColor Cyan
Write-Host "Application Pool: $appPoolName" -ForegroundColor Cyan
Write-Host ""

# Step 1: Check Application Pool Status
Write-Host "Step 1: Checking Application Pool Status..." -ForegroundColor Cyan

try {
    Import-Module WebAdministration -ErrorAction Stop
    
    $pool = Get-WebAppPool -Name $appPoolName -ErrorAction SilentlyContinue
    if ($pool) {
        Write-Host "Application Pool Status: $($pool.State)" -ForegroundColor Green
        Write-Host "Application Pool Identity: $($pool.ProcessModel.IdentityType)" -ForegroundColor Green
        Write-Host "Application Pool .NET CLR Version: $($pool.ManagedRuntimeVersion)" -ForegroundColor Green
    } else {
        Write-Host "❌ Application Pool '$appPoolName' not found!" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Failed to check application pool: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 2: Check Website Status
Write-Host "`nStep 2: Checking Website Status..." -ForegroundColor Cyan

try {
    $website = Get-Website -Name "XQ360Migration" -ErrorAction SilentlyContinue
    if ($website) {
        Write-Host "Website Status: $($website.State)" -ForegroundColor Green
        Write-Host "Website Physical Path: $($website.PhysicalPath)" -ForegroundColor Green
        Write-Host "Website Application Pool: $($website.ApplicationPool)" -ForegroundColor Green
    } else {
        Write-Host "❌ Website 'XQ360Migration' not found!" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Failed to check website: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 3: Check Application Files
Write-Host "`nStep 3: Checking Application Files..." -ForegroundColor Cyan

if (Test-Path $deployPath) {
    Write-Host "✅ Application directory exists: $deployPath" -ForegroundColor Green
    
    # Check for essential files
    $essentialFiles = @(
        "web.config",
        "XQ360.DataMigration.Web.dll",
        "appsettings.json"
    )
    
    foreach ($file in $essentialFiles) {
        $filePath = Join-Path $deployPath $file
        if (Test-Path $filePath) {
            Write-Host "✅ Found: $file" -ForegroundColor Green
        } else {
            Write-Host "❌ Missing: $file" -ForegroundColor Red
        }
    }
    
    # Check for wwwroot directory
    $wwwrootPath = Join-Path $deployPath "wwwroot"
    if (Test-Path $wwwrootPath) {
        Write-Host "✅ wwwroot directory exists" -ForegroundColor Green
    } else {
        Write-Host "❌ wwwroot directory missing" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ Application directory not found: $deployPath" -ForegroundColor Red
}

# Step 4: Check .NET Core Runtime
Write-Host "`nStep 4: Checking .NET Core Runtime..." -ForegroundColor Cyan

try {
    $dotnetVersion = dotnet --version 2>$null
    if ($dotnetVersion) {
        Write-Host "✅ .NET Core Runtime Version: $dotnetVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ .NET Core Runtime not found!" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Failed to check .NET Core Runtime: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 5: Check IIS Modules
Write-Host "`nStep 5: Checking IIS Modules..." -ForegroundColor Cyan

try {
    $aspNetCoreModule = Get-WebGlobalModule -Name "AspNetCoreModuleV2" -ErrorAction SilentlyContinue
    if ($aspNetCoreModule) {
        Write-Host "✅ ASP.NET Core Module V2 is installed" -ForegroundColor Green
    } else {
        Write-Host "❌ ASP.NET Core Module V2 not found!" -ForegroundColor Red
        Write-Host "Please install .NET Core Hosting Bundle" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to check IIS modules: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 6: Check Event Logs
Write-Host "`nStep 6: Checking Recent Event Logs..." -ForegroundColor Cyan

try {
    $recentEvents = Get-EventLog -LogName "Application" -Source "IIS*" -Newest 5 -ErrorAction SilentlyContinue
    if ($recentEvents) {
        Write-Host "Recent IIS Application Events:" -ForegroundColor Yellow
        foreach ($event in $recentEvents) {
            Write-Host "  [$($event.TimeGenerated)] $($event.Message)" -ForegroundColor White
        }
    } else {
        Write-Host "No recent IIS application events found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Failed to check event logs: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 7: Check Application Logs
Write-Host "`nStep 7: Checking Application Logs..." -ForegroundColor Cyan

$logPath = Join-Path $deployPath "Logs"
if (Test-Path $logPath) {
    $logFiles = Get-ChildItem -Path $logPath -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 3
    if ($logFiles) {
        Write-Host "Recent application log files:" -ForegroundColor Yellow
        foreach ($logFile in $logFiles) {
            Write-Host "  $($logFile.Name) - $($logFile.LastWriteTime)" -ForegroundColor White
        }
    } else {
        Write-Host "No application log files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Logs directory not found: $logPath" -ForegroundColor Red
}

# Step 8: Try to Restart Application Pool
Write-Host "`nStep 8: Attempting to Restart Application Pool..." -ForegroundColor Cyan

try {
    Write-Host "Stopping application pool..." -ForegroundColor Yellow
    Stop-WebAppPool -Name $appPoolName -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 3
    
    Write-Host "Starting application pool..." -ForegroundColor Yellow
    Start-WebAppPool -Name $appPoolName
    
    $pool = Get-WebAppPool -Name $appPoolName
    Write-Host "✅ Application pool restarted. Status: $($pool.State)" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restart application pool: $($_.Exception.Message)" -ForegroundColor Red
}

# Summary
Write-Host "`nTroubleshooting Summary:" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green
Write-Host "✅ Application pool status checked" -ForegroundColor White
Write-Host "✅ Website status checked" -ForegroundColor White
Write-Host "✅ Application files checked" -ForegroundColor White
Write-Host "✅ .NET Core runtime checked" -ForegroundColor White
Write-Host "✅ IIS modules checked" -ForegroundColor White
Write-Host "✅ Event logs checked" -ForegroundColor White
Write-Host "✅ Application logs checked" -ForegroundColor White
Write-Host "✅ Application pool restarted" -ForegroundColor White

Write-Host "" -ForegroundColor White
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "   1. Check the output above for any ❌ errors" -ForegroundColor White
Write-Host "   2. If .NET Core Hosting Bundle is missing, install it" -ForegroundColor White
Write-Host "   3. If application files are missing, redeploy the application" -ForegroundColor White
Write-Host "   4. Try accessing the web application again" -ForegroundColor White
Write-Host "   5. Check IIS logs for more detailed error messages" -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host "Troubleshooting completed!" -ForegroundColor Green 