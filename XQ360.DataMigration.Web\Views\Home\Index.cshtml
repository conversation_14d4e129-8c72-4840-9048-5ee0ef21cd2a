﻿@{
    ViewData["Title"] = "XQ360 Data Migration Tool";
}

<style>
    .file-validation-message .alert {
        margin-bottom: 0;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .file-validation-message .alert-danger {
        border-color: #dc3545;
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .file-validation-message .alert-warning {
        border-color: #ffc107;
        background-color: #fff3cd;
        color: #856404;
    }
    
    .file-validation-message .alert-success {
        border-color: #198754;
        background-color: #d1e7dd;
        color: #0f5132;
    }
    
    .csv-file-input:invalid {
        border-color: #dc3545;
    }
    
    .csv-file-input:valid {
        border-color: #198754;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5 mt-5">
                <h1 class="display-3"> XQ360 Data Migration Tool</h1>
                <p class="lead">Professional data migration solution for XQ360 systems</p>
                
                <div class="mt-4">
                    <a href="#migrationForm" class="btn btn-primary btn-lg">
                        <i class="fas fa-database"></i> Start Migration
                    </a>
                    <a href="https://collectiveintelligence.atlassian.net/wiki/x/AYBXd" target="_blank" class="btn btn-info btn-lg ml-3">
                        <i class="fas fa-book"></i> User Manual
                    </a>
                </div>
            </div>
        </div>
    </div>



    <div class="row mt-5">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-globe fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Multi-Environment</h5>
                    <p class="card-text">Deploy to US, UK, AU, Pilot, or Development environments.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-list-check fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Multiple Data Types</h5>
                    <p class="card-text">Migrate vehicles, persons, cards, and more.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Real-time Progress</h5>
                    <p class="card-text">Monitor migration progress with live updates.</p>
                </div>
            </div>
        </div>
    </div>



    <div class="row mt-5" id="migrationForm">
        <div class="col-12">
            <h2 class="text-center mb-4"> Start Your Migration</h2>
            <form id="migrationFormData">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-globe"></i> Environment Selection</h5>
                            </div>
                            <div class="card-body">
                                <select class="form-control form-control-lg" id="environment" required>
                                    <option value="">Choose environment...</option>
                                    <option value="US"> United States</option>
                                    <option value="UK"> United Kingdom</option>
                                    <option value="AU"> Australia</option>
                                    <option value="Pilot"> Pilot Environment</option>
                                    <option value="Development"> Development Environment</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5><i class="fas fa-list-check"></i> Migration Types</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input migration-type" id="spare" value="spare-modules" name="migrationTypes">
                                            <label class="form-check-label" for="spare">🔧 Spare Modules</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input migration-type" id="preop" value="preop-checklist" name="migrationTypes">
                                            <label class="form-check-label" for="preop">📋 Pre-Op Checklist</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input migration-type" id="vehicles" value="vehicles" name="migrationTypes">
                                            <label class="form-check-label" for="vehicles">🚗 Vehicles</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input migration-type" id="persons" value="persons" name="migrationTypes">
                                            <label class="form-check-label" for="persons">👤 Persons</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input migration-type" id="cards" value="cards" name="migrationTypes">
                                            <label class="form-check-label" for="cards">💳 Cards & Access</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input migration-type" id="supervisor" value="supervisor-access" name="migrationTypes">
                                            <label class="form-check-label" for="supervisor">👨‍💼 Supervisor Access</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input migration-type" id="blacklist" value="driver-blacklist" name="migrationTypes">
                                            <label class="form-check-label" for="blacklist">🚫 Driver Blacklist</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input migration-type" id="website" value="website-users" name="migrationTypes">
                                            <label class="form-check-label" for="website">🌐 Website Users</label>
                                        </div>
                                        
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()"> Select All</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAll()"> Clear All</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card border-warning">
                            <div class="card-header bg-warning">
                                <h5><i class="fas fa-file-csv"></i> CSV File Upload</h5>
                            </div>
                            <div class="card-body">
                                <div id="csvSection">
                                    <p class="text-muted">Select migration types above to see CSV upload options</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <div class="form-check form-check-inline mb-3">
                            <input class="form-check-input" type="checkbox" id="testMode" name="testMode" checked>
                            <label class="form-check-label" for="testMode">
                                <i class="fas fa-database"></i> Run Database Checking
                            </label>
                        </div>
                        <br>
                        <button type="submit" class="btn btn-success btn-lg" id="startBtn">
                            <i class="fas fa-play"></i> Start Migration
                        </button>
                        <button type="button" class="btn btn-info btn-lg ms-3" onclick="loadAndShowReports()">
                            <i class="fas fa-history"></i> View Historical Reports
                        </button>
                    </div>
                </div>
            </form>

            <div class="row mt-4">
                <div class="col-12">
                    <div id="results" style="display:none"></div>


                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reports Modal -->
<div class="modal fade" id="reportsModal" tabindex="-1" aria-labelledby="reportsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportsModalLabel">
                    <i class="fas fa-file-text"></i> Migration Reports
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="modalReportsLoading" class="text-center" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> Loading reports...
                </div>
                
                <div id="modalReportsContainer" style="max-height: 60vh; overflow-y: auto;">
                    <!-- Reports will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="refreshModalReports()">
                    <i class="fas fa-refresh"></i> Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Report Viewer Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">
                    <i class="fas fa-file-text"></i> Migration Report
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="reportContent" style="white-space: pre-wrap; font-family: monospace; max-height: 70vh; overflow-y: auto;">
                    <!-- Report content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="downloadCurrentModalReport">
                    <i class="fas fa-download"></i> Download Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Validation Modal -->
<div class="modal fade" id="validationModal" tabindex="-1" aria-labelledby="validationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="validationModalLabel"><i class="fas fa-exclamation-triangle"></i> Warning</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="validationModalBody" style="white-space: nowrap;">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
      </div>
    </div>
  </div>
</div>

<script>
function showValidationModal(message) {
  const body = document.getElementById('validationModalBody');
  if (body) {
    body.textContent = message;
  }
  const modalEl = document.getElementById('validationModal');
  const modal = new bootstrap.Modal(modalEl);
  modal.show();
}
</script>

@section Scripts {
    <script>
        function selectAll() {
            document.querySelectorAll('.migration-type').forEach(cb => cb.checked = true);
            updateCsvSection();
        }

        function clearAll() {
            document.querySelectorAll('.migration-type').forEach(cb => cb.checked = false);
            updateCsvSection();
        }

        function displayTestResults(result) {
            const resultsDiv = document.getElementById('results');
            
            let testsHtml = '';
            result.tests.forEach(test => {
                const alertType = test.status.includes('✅') ? 'success' : 
                                test.status.includes('⚠️') ? 'warning' : 'danger';
                
                testsHtml += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-${alertType === 'success' ? 'success' : alertType === 'warning' ? 'warning' : 'danger'}">
                            <div class="card-header bg-${alertType === 'success' ? 'success' : alertType === 'warning' ? 'warning' : 'danger'} text-white">
                                <h6 class="mb-0">${test.status} ${test.name}</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-1">${test.message}</p>
                                ${test.details ? `<small class="text-muted">${test.details}</small>` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    <h4><i class="fas fa-flask"></i> Test Mode Results for ${result.environmentName}</h4>
                    <p><strong>Environment:</strong> ${result.environment} - ${result.environmentName}</p>
                    <p><i class="fas fa-info-circle"></i> <strong>Test Mode:</strong> Configuration validated without executing migration.</p>
                </div>
                <div class="row">
                    ${testsHtml}
                </div>
                <div class="alert alert-success mt-3">
                    <strong><i class="fas fa-check-circle"></i> Ready to migrate!</strong> 
                    Uncheck "Test Mode" and click "Execute Migration" to run the actual migration.
                </div>
            `;
            
            resultsDiv.style.display = 'block';
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function updateCsvSection() {
            const csvSection = document.getElementById('csvSection');
            const checked = Array.from(document.querySelectorAll('.migration-type:checked'));
            
            if (checked.length === 0) {
                csvSection.innerHTML = '<p class="text-muted">Select migration types above to see CSV upload options</p>';
                return;
            }
            
            let html = '<div class="row">';
            checked.forEach(cb => {
                const migrationLabel = cb.nextElementSibling.textContent;
                html += `
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">
                            ${migrationLabel} CSV:
                            <span class="ms-2">
                                <a href="/api/template/download/${cb.value}" class="btn btn-outline-primary btn-sm" title="Download CSV Template">
                                    <i class="fas fa-download"></i> Template
                                </a>
                                <button type="button" class="btn btn-outline-info btn-sm ms-1" onclick="viewTemplate('${cb.value}')" title="View Template Info">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </span>
                        </label>
                        <input type="file" class="form-control csv-file-input" name="csvFiles[${cb.value}]" accept=".csv" onchange="validateCsvFile(this, '${migrationLabel}')">
                        <div class="file-validation-message mt-1" id="validation-${cb.value}"></div>
                        <small class="form-text text-muted">
                            Upload CSV file for ${migrationLabel}. 
                            <strong>Important:</strong> File format must match the template exactly.
                        </small>
                    </div>
                `;
            });
            html += '</div>';
            csvSection.innerHTML = html;
        }

        function validateCsvFile(input, migrationLabel) {
            const file = input.files[0];
            const validationDiv = document.getElementById(`validation-${input.name.match(/\[(.*?)\]/)[1]}`);
            
            // Clear previous validation message
            validationDiv.innerHTML = '';
            validationDiv.className = 'file-validation-message mt-1';
            
            if (!file) {
                return; // No file selected
            }
            
            // Check file extension
            if (!file.name.toLowerCase().endsWith('.csv')) {
                showFileValidationTooltip(input, `❌ ${file.name} is not a CSV file. Please select a .csv file.`);
                input.value = ''; // Clear the input
                return;
            }
            
            // Check if file is empty
            if (file.size === 0) {
                showFileValidationTooltip(input, `❌ ${file.name} is empty. Please select a file with data.`);
                input.value = ''; // Clear the input
                return;
            }
            
            // Check file size (optional - warn if very large)
            if (file.size > 10 * 1024 * 1024) { // 10MB
                showFileValidationTooltip(input, `⚠️ Warning: ${file.name} is very large (${(file.size / 1024 / 1024).toFixed(1)}MB). This may take a while to process.`);
            }
            // No success message - just clear the validation div
        }

        function showFileValidationTooltip(input, message) {
            console.log('showFileValidationTooltip called with:', input.name, message);
            const migrationType = input.name.match(/\[(.*?)\]/)[1];
            console.log('Migration type:', migrationType);
            
            // Remove any existing tooltip for this field
            const existingTooltip = document.getElementById(`tooltip-${migrationType}`);
            if (existingTooltip) {
                existingTooltip.remove();
            }
            
            // Get input position for tooltip placement
            const rect = input.getBoundingClientRect();
            
            // Create tooltip-style alert appearing inline at end of helper text line
            const tooltipHtml = `<span id="tooltip-${migrationType}" class="file-validation-tooltip" style="display: inline-flex; align-items: center; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 6px 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); white-space: nowrap; font-size: 14px; margin-left: 8px; vertical-align: middle;"><span style="width: 18px; height: 18px; background: #ffc107; border-radius: 2px; display: inline-flex; align-items: center; justify-content: center; margin-right: 8px;"><span style="color: white; font-weight: bold; font-size: 12px; line-height: 1;">!</span></span><span style="color: #dc3545; font-size: 14px; font-weight: 600;">${message}</span></span>`;
            
            console.log('Tooltip HTML created:', tooltipHtml);
            console.log('Input parent element:', input.parentElement);
            
            // Append tooltip at the end of the helper text line directly below the input
            const helperLine = input.parentElement.querySelector('small.form-text');
            if (helperLine) {
                helperLine.insertAdjacentHTML('beforeend', tooltipHtml);
            } else {
                // Fallback: insert right after the input without breaking layout
                input.insertAdjacentHTML('afterend', `<div class="mt-2">${tooltipHtml}</div>`);
            }
            
            console.log('Tooltip added to DOM');
            
            // Auto-remove after 4 seconds
            setTimeout(() => {
                const tooltip = document.getElementById(`tooltip-${migrationType}`);
                if (tooltip) {
                    tooltip.remove();
                }
            }, 4000);
        }

        // Success messages removed - no longer needed
        
        // Test function to manually trigger tooltip (for debugging)
        function testTooltip() {
            const firstInput = document.querySelector('.csv-file-input');
            if (firstInput) {
                showFileValidationTooltip(firstInput, 'Test tooltip message');
            } else {
                console.log('No CSV input found for testing');
            }
        }
        
        // Test function to show multiple tooltips (for debugging)
        function testMultipleTooltips() {
            const csvInputs = document.querySelectorAll('.csv-file-input');
            console.log('Found', csvInputs.length, 'CSV inputs');
            
            csvInputs.forEach((input, index) => {
                const migrationType = input.name.match(/\[(.*?)\]/)[1];
                console.log(`Showing tooltip ${index + 1} for ${migrationType}`);
                showFileValidationTooltip(input, `Test tooltip ${index + 1} for ${migrationType}`);
            });
        }
        
        // Environment tooltip function (original style)
        function showEnvironmentTooltip(message) {
            const environmentSelect = document.getElementById('environment');
            
            // Remove any existing environment tooltip
            const existingTooltip = document.getElementById('environment-tooltip');
            if (existingTooltip) {
                existingTooltip.remove();
            }
            
            // Create tooltip-style alert matching the original environment dropdown style
            const tooltipHtml = `<div id="environment-tooltip" class="environment-validation-tooltip" style="position: absolute; top: 100%; left: 0; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 8px 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); z-index: 9999; max-width: 280px; font-size: 12px; margin-top: 5px;"><div style="display: flex; align-items: center;"><div style="width: 16px; height: 16px; background: #ffc107; border-radius: 2px; display: flex; align-items: center; justify-content: center; margin-right: 6px; flex-shrink: 0;"><span style="color: white; font-weight: bold; font-size: 10px;">!</span></div><span style="color: #495057; font-size: 11px;">${message}</span></div></div>`;
            
            // Add tooltip to the environment select's parent container
            environmentSelect.parentElement.style.position = 'relative';
            environmentSelect.parentElement.insertAdjacentHTML('beforeend', tooltipHtml);
            
            // Auto-remove after 4 seconds
            setTimeout(() => {
                const tooltip = document.getElementById('environment-tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            }, 4000);
        }



        function updateButtonText() {
            const button = document.getElementById('startBtn');
            button.innerHTML = '<i class="fas fa-play"></i> Start Migration';
        }

        // Function to manage database checking checkbox state
        function setDatabaseCheckingState(checked) {
            const testModeCheckbox = document.getElementById('testMode');
            testModeCheckbox.checked = checked;
        }

        // Test Mode workflow functions - Modified to skip confirmation page
        async function showTestSuccessAndMigration(result, env, checked) {
            // Skip the confirmation page and go directly to migration display
            // since connection string and CSV checking are already handled
                startMigrationDisplay(result.migrationId, env, checked);
        }

        function showTestFailureWarning(result) {
            const results = document.getElementById('results');
            
            let failedTestsHtml = result.failedTests.map(test => `
                <div class="col-md-6 mb-3">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">${test.status} ${test.name}</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-1">${test.message}</p>
                            ${test.details ? `<small class="text-muted">${test.details}</small>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
            
            let allTestsHtml = result.tests.map(test => {
                const isSuccess = test.status.includes('✅') || test.status.toLowerCase().includes('pass');
                const borderClass = isSuccess ? 'border-success' : 'border-danger';
                const bgClass = isSuccess ? 'bg-success' : 'bg-danger';
                
                return `
                    <div class="col-md-6 mb-2">
                        <div class="card ${borderClass}">
                            <div class="card-header ${bgClass} text-white py-1">
                                <small class="mb-0">${test.status} ${test.name}</small>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            results.innerHTML = `
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle"></i> Database Checking Failed!</h4>
                    <p><strong>Error:</strong> ${result.error}</p>
                    <p><strong>Environment:</strong> ${result.environmentName}</p>
                    
                    <h5 class="mt-4">Checking Results Overview:</h5>
                    <div class="row">
                        ${allTestsHtml}
                    </div>
                    
                    <h5 class="mt-4 text-danger">Failed Checks (Details):</h5>
                    <div class="row">
                        ${failedTestsHtml}
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <strong><i class="fas fa-shield-alt"></i> Migration Blocked for Safety</strong><br>
                        Please fix the failed database checks before proceeding with migration. 
                        You can uncheck "Run Database Checking" to skip checks and run migration anyway (not recommended).
                    </div>
                </div>
            `;
            
            results.style.display = 'block';
            results.scrollIntoView({ behavior: 'smooth' });
        }

        async function startMigrationDisplay(migrationId, env, checked) {
            // CRITICAL: Clear all previous migration state before starting new migration
            console.log("🧹 Clearing previous migration state before starting new migration");
            
            // Clear old migration ID and data
            if (currentMigrationId && connection) {
                await connection.invoke("LeaveMigrationGroup", currentMigrationId);
            }
            
            // Reset all migration state variables
            currentMigrationId = migrationId;
            window.migrationMap = {};
            window.lastProgressUpdate = null;
            window.lastSignalRUpdate = 0;
            latestReportFileName = '';
            currentReportFileName = '';
            
            // Clear any existing intervals
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
            
            console.log("✅ Previous migration state cleared, starting fresh migration:", migrationId);
            
            // Join SignalR group for this migration
            if (connection) {
                await connection.invoke("JoinMigrationGroup", currentMigrationId);
            }
            
            // Show initial progress
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="alert alert-info">
                    <h4><i class="fas fa-rocket"></i> Migration Started!</h4>
                    <p><strong>Migration ID:</strong> ${currentMigrationId}</p>
                    <p><strong>Environment:</strong> ${env}</p>
                    <p><strong>Selected Types:</strong> ${checked.map(cb => cb.value).join(', ')}</p>
                    <div class="progress mt-3" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-info" style="width: 10%">
                            Starting...
                        </div>
                    </div>
                </div>
            `;
            results.style.display = 'block';
            results.scrollIntoView({ behavior: 'smooth' });
            
            // Start periodic status checking as fallback for SignalR issues
            startPeriodicStatusCheck();
        }

        let statusCheckInterval;

        // Check current migration status and wait for completion if needed
        async function checkCurrentMigrationStatus() {
            try {
                if (currentMigrationId) {
                    console.log("Checking status for current migration:", currentMigrationId);
                    const response = await fetch(`/Home/GetProgress?migrationId=${currentMigrationId}`);
                    const result = await response.json();
                    
                    if (result.success && result.progress) {
                        console.log("Found migration status:", result.progress.status);
                        updateProgressDisplay(result.progress);
                        
                        // If migration is still running (VehicleSync), set up more frequent checking
                        if (result.progress.status === 'VehicleSync' || result.progress.status === 'Running') {
                            console.log("Migration still in progress - enabling frequent status checks");
                            startFrequentStatusChecking();
                        }
                    }
                } else {
                    // No current migration ID, check migration map for any active migrations
                    const activeMigrations = Object.keys(window.migrationMap || {});
                    if (activeMigrations.length > 0) {
                        console.log("Found active migration in migration map:", activeMigrations[0]);
                        currentMigrationId = activeMigrations[0];
                        // Recursively check this migration
                        await checkCurrentMigrationStatus();
                    } else {
                        // No active migrations, check for recent completed ones
                        console.log("No active migrations - checking for recent completed migrations");
                        await checkForCompletedMigration();
                    }
                }
            } catch (error) {
                console.error("Status check failed:", error);
            }
        }

        // Start frequent status checking when migration is in VehicleSync phase
        function startFrequentStatusChecking() {
            // Clear any existing interval
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
            
            console.log("Starting frequent status checking for VehicleSync phase");
            
            // Check every 2 seconds during VehicleSync phase
            statusCheckInterval = setInterval(async () => {
                if (currentMigrationId) {
                    try {
                        const response = await fetch(`/Home/GetProgress?migrationId=${currentMigrationId}`);
                        const result = await response.json();
                        
                        if (result.success && result.progress) {
                            console.log("Frequent check - migration status:", result.progress.status);
                            
                            // Always update if status is Completed or Failed
                            if (result.progress.status === 'Completed' || result.progress.status === 'Failed') {
                                console.log("🎯 FREQUENT CHECK: Migration finished - updating to:", result.progress.status);
                                updateProgressDisplay(result.progress);
                                clearInterval(statusCheckInterval);
                                statusCheckInterval = null;
                                return;
                            }
                            
                            // Update progress during VehicleSync
                            updateProgressDisplay(result.progress);
                        }
                    } catch (error) {
                        console.error("Frequent status check failed:", error);
                    }
                }
            }, 2000); // Check every 2 seconds
        }

        // Start periodic status checking (fallback when SignalR fails)
        function startPeriodicStatusCheck() {
            // Clear any existing interval
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
            
            // Check status every 3 seconds (more frequent for final phase detection)
            statusCheckInterval = setInterval(async () => {
                if (currentMigrationId) {
                    try {
                        const response = await fetch(`/Home/GetProgress?migrationId=${currentMigrationId}`);
                        const result = await response.json();
                        
                        if (result.success && result.progress) {
                            console.log("Periodic status check - found:", result.progress.status);
                            
                            // Simple: Update status and stop if migration finished
                            if (result.progress.status === 'Completed' || result.progress.status === 'Failed') {
                                console.log("Migration finished - updating to:", result.progress.status);
                                updateProgressDisplay(result.progress);
                                clearInterval(statusCheckInterval);
                                statusCheckInterval = null;
                            } else {
                                // For running migrations, only update if no recent SignalR activity
                                const lastSignalRUpdate = window.lastSignalRUpdate || 0;
                                const timeSinceLastSignalR = Date.now() - lastSignalRUpdate;
                                
                                if (timeSinceLastSignalR > 10000) { // 10 seconds
                                    console.log("No recent SignalR - updating from periodic check");
                                    updateProgressDisplay(result.progress);
                                }
                            }
                        } else {
                            // Migration not found in active list, might be completed
                            await checkForCompletedMigration();
                            clearInterval(statusCheckInterval);
                            statusCheckInterval = null;
                        }
                    } catch (error) {
                        console.error("Periodic status check failed:", error);
                    }
                } else {
                    // No current migration, stop checking
                    clearInterval(statusCheckInterval);
                    statusCheckInterval = null;
                }
            }, 5000); // Check every 5 seconds
        }

        // SignalR connection for real-time updates
        let connection = null;
        let currentMigrationId = null;

        async function initializeSignalR() {
            connection = new signalR.HubConnectionBuilder()
                .withUrl("/migrationHub")
                .build();

            connection.on("UpdateProgress", function (progress) {
                const timestamp = new Date().toTimeString();
                console.log(`[${timestamp}] SignalR Progress Update Received:`, progress);
                
                // Track when SignalR updates are received to prevent periodic override
                window.lastSignalRUpdate = Date.now();
                
                // Debug: Log step summaries received from backend
                if (progress.stepSummaries && Object.keys(progress.stepSummaries).length > 0) {
                    console.log(`[${timestamp}] 🔧 Frontend received step summaries:`);
                    Object.entries(progress.stepSummaries).forEach(([key, step]) => {
                        console.log(`[${timestamp}] 🔧   ${key}: Status=${step.status}, Name=${step.stepName}, Records=${step.recordsInserted || 0}`);
                    });
                    
                    // Simple: Log final status when received
                    if (progress.status === 'Completed') {
                        console.log(`[${timestamp}] ✅ Migration completed - Vehicle Settings Sync: ${progress.stepSummaries['vehicle-sync-settings']?.status || 'NOT_FOUND'}`);
                    }
                } else {
                    console.warn(`[${timestamp}] 🔧 Frontend received NO step summaries!`);
                }
                
                updateProgressDisplay(progress);
            });

            // Add connection monitoring for critical phases
            connection.onclose(async () => {
                console.warn("🔌 SignalR connection closed during migration!");
                setTimeout(async () => {
                    try {
                        await connection.start();
                        console.log("✅ SignalR reconnected successfully");
                        // SignalR reconnected successfully
                    } catch (err) {
                        console.error("❌ SignalR reconnection failed:", err);
                    }
                }, 2000);
            });

            try {
                await connection.start();
                console.log("SignalR Connected");
            } catch (err) {
                console.error("SignalR Connection Error: ", err);
            }
        }

        function updateProgressDisplay(progress) {
            console.log("Updating UI with progress:", progress.status, progress.progress + "%");
            
            // CRITICAL: Reject updates from old migrations
            if (currentMigrationId && progress.id && progress.id !== currentMigrationId) {
                console.warn(`🚫 Rejecting progress update from old migration ${progress.id} (current: ${currentMigrationId})`);
                return;
            }
            
            const results = document.getElementById('results');
            
            // Store the progress for potential later use
            window.lastProgressUpdate = progress;
            
            let statusColor = 'info';
            if (progress.status === 'Completed') statusColor = 'success';
            if (progress.status === 'Failed') statusColor = 'danger';
            
            let messagesHtml = progress.messages.map(msg => `<li>${msg}</li>`).join('');
            
            // No refresh button needed
            let refreshButton = '';

            // Generate overall summary display
            let overallSummaryHtml = '';
            
            // Debug: Log overall summary received from backend
            if (progress.overallSummary) {
                console.log(`🔧 Frontend received overallSummary:`, progress.overallSummary);
                console.log(`🔧 OverallSummary data: Processed=${progress.overallSummary.totalRecordsProcessed || 0}, Inserted=${progress.overallSummary.totalRecordsInserted || 0}, Skipped=${progress.overallSummary.totalRecordsSkipped || 0}, Errors=${progress.overallSummary.totalErrors || 0}`);
            } else {
                console.log(`🔧 Frontend overallSummary is NULL or undefined!`);
            }
            
            if (progress.overallSummary && progress.overallSummary.totalSteps > 0) {
                const completionPct = Math.round(progress.overallSummary.completionPercentage);
                overallSummaryHtml = `
                    <div class="row mt-3 mb-3">
                        <div class="col-12">
                            <h5><i class="fas fa-chart-bar"></i> Overall Migration Summary</h5>
                            <div class="row text-center">
                                <div class="col-md-3 col-sm-6 col-6">
                                    <div class="card bg-info text-white mb-2">
                                        <div class="card-body py-2">
                                            <h6 class="card-title mb-1">Processed</h6>
                                            <p class="card-text mb-0"><strong>${progress.overallSummary.totalRecordsProcessed || 0}</strong></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 col-6">
                                    <div class="card bg-success text-white mb-2">
                                        <div class="card-body py-2">
                                            <h6 class="card-title mb-1">Inserted</h6>
                                            <p class="card-text mb-0"><strong>${progress.overallSummary.totalRecordsInserted || 0}</strong></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 col-6">
                                    <div class="card bg-warning text-dark mb-2">
                                        <div class="card-body py-2">
                                            <h6 class="card-title mb-1">Skipped</h6>
                                            <p class="card-text mb-0"><strong>${progress.overallSummary.totalRecordsSkipped || 0}</strong></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 col-6">
                                    <div class="card bg-danger text-white mb-2">
                                        <div class="card-body py-2">
                                            <h6 class="card-title mb-1">Errors</h6>
                                            <p class="card-text mb-0"><strong>${progress.overallSummary.totalErrors || 0}</strong></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Generate step-by-step progress display
            let stepProgressHtml = '';
            if (progress.stepSummaries && Object.keys(progress.stepSummaries).length > 0) {
                console.log("🔧 Processing step summaries for UI update:");
                
                // CRITICAL FIX: Ensure all steps are marked as completed when migration succeeds
                if (progress.status === 'Completed') {
                    Object.keys(progress.stepSummaries).forEach(stepId => {
                        const step = progress.stepSummaries[stepId];
                        if (step.status === 'Running' || step.status === 'Pending') {
                            console.log(`🔧 FIXING: Step ${stepId} was in ${step.status} status but migration succeeded - marking as Completed`);
                            step.status = 'Completed';
                            step.endTime = new Date().toISOString();
                        }
                    });
                }
                
                stepProgressHtml = `
                    <div class="mt-3">
                        <h5><i class="fas fa-tasks"></i> Step-by-Step Progress</h5>
                        <div class="list-group">
                `;
                
                Object.values(progress.stepSummaries).forEach(step => {
                    console.log(`🔧 Processing step: ${step.stepName}, Status: ${step.status}`);
                    let stepStatusIcon = '⏳';
                    let stepStatusColor = 'light';
                    let progressWidth = 0;
                    
                    // Determine display counts with a fix for vehicle sync when counts are missing
                    let displayProcessed = step.recordsProcessed ?? 0;
                    let displayInserted = step.recordsInserted ?? 0;
                    let displaySkipped = step.recordsSkipped ?? 0;
                    if (step.stepId === 'vehicle-sync-settings' && step.status === 'Completed' && displayProcessed === 0 && displayInserted === 0 && displaySkipped === 0) {
                        try {
                            const msg = (progress.messages || []).join(' | ');
                            // Patterns that appear in backend messages
                            // e.g. "Vehicle Settings Sync completed: 9 vehicles synced" or "Vehicle Sync Settings migration completed: 9 synced, 0 failed"
                            const mVehicles = msg.match(/completed:\s*(\d+)\s*vehicles?\s*synced/i) || msg.match(/completed:\s*(\d+)\s*synced/i);
                            const mFailed = msg.match(/(\d+)\s*failed/i);
                            if (mVehicles) {
                                const synced = parseInt(mVehicles[1], 10) || 0;
                                const failed = mFailed ? (parseInt(mFailed[1], 10) || 0) : 0;
                                displayProcessed = synced + failed;
                                displayInserted = synced;
                                displaySkipped = failed;
                            }
                        } catch { /* noop */ }
                    }
                    
                    switch (step.status) {
                        case 'Completed':
                            stepStatusIcon = '✅';
                            stepStatusColor = 'success';
                            progressWidth = 100;
                            break;
                        case 'Running':
                            stepStatusIcon = '🔄';
                            stepStatusColor = 'info';
                            progressWidth = 50;
                            break;
                        case 'Failed':
                            stepStatusIcon = '❌';
                            stepStatusColor = 'danger';
                            progressWidth = 100;
                            break;
                        default:
                            stepStatusIcon = '⏳';
                            stepStatusColor = 'light';
                            progressWidth = 0;
                    }

                    stepProgressHtml += `
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${stepStatusIcon} ${step.stepName}</h6>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-${stepStatusColor}" style="width: ${progressWidth}%"></div>
                                    </div>
                                </div>
                                <div class="text-end ms-3">
                                    <small class="text-muted d-block">Status: <span class="badge bg-${stepStatusColor}">${step.status}</span></small>
                                    <small class="text-muted d-block">
                                        📊 ${displayProcessed} processed, ${displayInserted} inserted, ${displaySkipped} skipped
                                    </small>
                                    ${step.errorCount > 0 ? `<small class="text-danger d-block">⚠️ ${step.errorCount} errors</small>` : ''}
                                    ${step.status === 'Completed' ? `
                                        <div class="mt-1">
                                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                                    onclick="viewStepReport('${step.stepId}', '${step.stepName}')" 
                                                    title="View Report for ${step.stepName}">
                                                <i class="fas fa-file-text"></i> View Report
                    </button>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                stepProgressHtml += `
                        </div>
                    </div>
                `;
            }

            // Generate vehicle sync summary display
            let vehicleSyncSummaryHtml = '';
            // Only show separate vehicle sync summary if VehicleSync is NOT tracked as a step
            const hasVehicleSyncStep = progress.stepSummaries && progress.stepSummaries['vehicle-sync-settings'];
            if (!hasVehicleSyncStep && (progress.status === 'VehicleSync' || progress.status === 'Completed')) {
                // Extract vehicle sync information from messages
                const vehicleSyncMessages = progress.messages.filter(msg => 
                    msg.includes('Vehicle Settings Sync') || 
                    msg.includes('vehicles synced') || 
                    msg.includes('customers:') ||
                    msg.includes('Customer Processing Summary') ||
                    msg.includes('No vehicle sync required') ||
                    msg.includes('No customers found') ||
                    msg.includes('No vehicles found') ||
                    msg.includes('Processing completed') ||
                    msg.includes('vehicle sync') ||
                    msg.includes('Vehicle sync') ||
                    msg.includes('Starting vehicle sync check') ||
                    msg.includes('Vehicle sync completed successfully') ||
                    msg.includes('could not be synced') ||
                    msg.includes('Preparing Vehicle Settings Sync') ||
                    msg.includes('Checking vehicle sync requirements')
                );

                // Debug logging to see what messages we're working with
                console.log('DEBUG: All progress messages:', progress.messages);
                console.log('DEBUG: Filtered vehicle sync messages:', vehicleSyncMessages);

                // Always show vehicle sync summary for completed migrations
                let syncStatus = 'Completed';
                let syncStatusIcon = '✅';
                let syncStatusColor = 'success';
                let vehiclesSynced = 0;
                let customersProcessed = 0;
                let vehiclesSkipped = 0;
                let syncedCustomers = [];
                let wasSkipped = false;

                if (vehicleSyncMessages.length > 0) {
                    // Determine sync status based on messages
                    if (vehicleSyncMessages.some(msg => 
                        msg.includes('failed') || 
                        msg.includes('error') || 
                        msg.includes('Failed to authenticate') ||
                        msg.includes('Vehicle Settings Sync failed') ||
                        msg.includes('Vehicle sync error') ||
                        msg.includes('Exception during Vehicle Settings Sync') ||
                        msg.includes('No connection could be made') ||
                        msg.includes('target machine actively refused')
                    )) {
                        syncStatus = 'Failed';
                        syncStatusIcon = '❌';
                        syncStatusColor = 'danger';
                    } else if (vehicleSyncMessages.some(msg => msg.includes('No vehicle sync required') || msg.includes('No customers found') || msg.includes('No vehicles found'))) {
                        syncStatus = 'Skipped';
                        syncStatusIcon = 'ℹ️';
                        syncStatusColor = 'secondary';
                        wasSkipped = true;
                    }

                    // Extract key metrics and customer information from messages
                    vehicleSyncMessages.forEach(msg => {
                        // Fix regex patterns - remove double escaping
                        const syncedMatch = msg.match(/(\d+) vehicles synced/);
                        if (syncedMatch) vehiclesSynced = parseInt(syncedMatch[1]);
                        
                        const skippedMatch = msg.match(/(\d+) vehicles could not be synced/);
                        if (skippedMatch) vehiclesSkipped = parseInt(skippedMatch[1]);
                        
                        const customerMatch = msg.match(/(\d+) customers/);
                        if (customerMatch) customersProcessed = parseInt(customerMatch[1]);
                        
                        // Extract individual customer completion messages (accounting for leading spaces)
                        const customerCompletedMatch = msg.match(/\s*✅ ([^:]+): Processing completed/);
                        if (customerCompletedMatch) {
                            syncedCustomers.push(customerCompletedMatch[1].trim());
                        }
                        
                        // Also extract customers from the customer list (format: "• CustomerName: X vehicles")
                        const customerListMatch = msg.match(/• ([^:]+): \d+ vehicles/);
                        if (customerListMatch && !syncedCustomers.includes(customerListMatch[1].trim())) {
                            // Only add if we have confirmation of completion
                            if (syncStatus === 'Completed') {
                                syncedCustomers.push(customerListMatch[1].trim());
                            }
                        }
                        
                        // Enhanced customer name extraction patterns
                        const customerNamePatterns = [
                            // Look for "Customer:" followed by the name
                            /Customer[:\s]+([^,\n\r\t]+)/gi,
                            // Look for "Import Demo X" pattern specifically 
                            /(Import\s+Demo\s+\d+)/gi,
                            // Look for CSV pattern "Something,Customer," - capture the customer
                            /[^,\n\r]+,\s*([^,\n\r]+),/g,
                            // General customer processing messages
                            /Processing.*?([A-Za-z0-9][^,\n\r\t]{2,40})/gi
                        ];
                        
                        customerNamePatterns.forEach(pattern => {
                            const matches = msg.matchAll(pattern);
                            for (const match of matches) {
                                const customerName = match[1].trim();
                                if (customerName && 
                                    customerName.length > 3 && 
                                    !syncedCustomers.includes(customerName) &&
                                    !customerName.toLowerCase().includes('error') &&
                                    !customerName.toLowerCase().includes('unknown') &&
                                    !customerName.toLowerCase().includes('null') &&
                                    /[a-zA-Z]/.test(customerName)) {
                                    syncedCustomers.push(customerName);
                                }
                            }
                        });
                    });
                    
                    // Debug logging for extraction results
                    console.log('DEBUG: Vehicles synced:', vehiclesSynced);
                    console.log('DEBUG: Customers processed:', customersProcessed);
                    console.log('DEBUG: Synced customers:', syncedCustomers);
                } else {
                    // No specific vehicle sync messages found, determine based on migration types
                    // Check if this migration included vehicle-related types that would require sync
                    const requiresSyncTypes = ['vehicles', 'persons', 'cards', 'preop-checklist'];
                    const hasVehicleRelatedSteps = progress.stepSummaries && 
                        Object.keys(progress.stepSummaries).some(stepId => 
                            requiresSyncTypes.some(type => stepId.includes(type))
                        );
                    
                    if (!hasVehicleRelatedSteps) {
                        syncStatus = 'Skipped';
                        syncStatusIcon = 'ℹ️';
                        syncStatusColor = 'secondary';
                        wasSkipped = true;
                    } else {
                        // Try to extract customer information from CSV upload context or step messages
                        // Look for any customer-related information in the progress messages
                        const customerRelatedMessages = progress.messages.filter(msg => 
                            msg.toLowerCase().includes('customer') || 
                            msg.toLowerCase().includes('dealer') ||
                            msg.toLowerCase().includes('processed') ||
                            msg.toLowerCase().includes('imported')
                        );
                        
                        // Try to extract customer names from various message patterns
                        customerRelatedMessages.forEach(msg => {
                            // Look for patterns like "Customer: CustomerName" or similar
                            const customerPatterns = [
                                /(?:customer|dealer):\s*([^,\n\r]+)/gi,
                                /processing\s+([^:,\n\r]+):/gi,
                                /imported.*?for\s+([^,\n\r]+)/gi
                            ];
                            
                            customerPatterns.forEach(pattern => {
                                const matches = msg.matchAll(pattern);
                                for (const match of matches) {
                                    const customerName = match[1].trim();
                                    if (customerName && customerName.length > 2 && 
                                        !syncedCustomers.includes(customerName) &&
                                        !customerName.toLowerCase().includes('unknown') &&
                                        !customerName.toLowerCase().includes('error')) {
                                        syncedCustomers.push(customerName);
                                    }
                                }
                            });
                        });
                        
                        // If still no customers found but we have processed records (including skipped), try to extract more info
                        if (syncedCustomers.length === 0 && progress.overallSummary && 
                            progress.overallSummary.totalRecordsProcessed > 0) {
                            
                            // Try to extract customer names from detailed progress messages
                            const allMessages = progress.messages.join(' ');
                            
                            // Look for customer patterns in CSV data or processing messages
                            const detailedPatterns = [
                                /(?:customer|dealer|company):\s*([a-zA-Z][^,\n\r\t;]{2,30})/gi,
                                /([a-zA-Z][^,\n\r\t;]{2,30})\s*(?:customer|dealer|company)/gi,
                                /processing.*?([a-zA-Z][^,\n\r\t;]{2,30})/gi
                            ];
                            
                            detailedPatterns.forEach(pattern => {
                                const matches = allMessages.matchAll(pattern);
                                for (const match of matches) {
                                    const customerName = match[1].trim();
                                    if (customerName && 
                                        customerName.length > 2 && 
                                        customerName.length < 50 &&
                                        !syncedCustomers.includes(customerName) &&
                                        !customerName.toLowerCase().includes('unknown') &&
                                        !customerName.toLowerCase().includes('error') &&
                                        !customerName.toLowerCase().includes('step') &&
                                        !customerName.toLowerCase().includes('migration') &&
                                        !/^\d+$/.test(customerName)) { // Not just numbers
                                        syncedCustomers.push(customerName);
                                    }
                                }
                            });
                            
                            // If still no specific customers found, try to extract from CSV content or vehicle-specific messages
                            if (syncedCustomers.length === 0) {
                                // Look more aggressively for customer names in all messages
                                const enhancedPatterns = [
                                    // Look for "Import Demo X" pattern specifically
                                    /(Import\s+Demo\s+\d+)/gi,
                                    // Look for Customer: field specifically
                                    /customer[:\s]+([^,\n\r\t]+)/gi,
                                    // Look for CSV comma-separated values (capture 2nd field as customer)
                                    /[^,\n\r]+,\s*([^,\n\r]+),/g,
                                    // Look for processing messages with customer names
                                    /(?:processing|imported|found).*?([A-Za-z0-9][^,\n\r\t]{2,40})/gi
                                ];
                                
                                enhancedPatterns.forEach(pattern => {
                                    const matches = allMessages.matchAll(pattern);
                                    for (const match of matches) {
                                        const customerName = match[1].trim();
                                        // Validation for customer names including "Import Demo X" patterns
                                        if (customerName && 
                                            customerName.length >= 3 && 
                                            customerName.length < 50 &&
                                            !syncedCustomers.includes(customerName) &&
                                            !customerName.toLowerCase().includes('unknown') &&
                                            !customerName.toLowerCase().includes('error') &&
                                            !customerName.toLowerCase().includes('step') &&
                                            !customerName.toLowerCase().includes('migration') &&
                                            !customerName.toLowerCase().includes('processing') &&
                                            !customerName.toLowerCase().includes('record') &&
                                            !customerName.toLowerCase().includes('null') &&
                                            /[a-zA-Z]/.test(customerName)) { // Contains letters
                                            syncedCustomers.push(customerName);
                                        }
                                    }
                                });
                                
                                // Remove duplicates
                                syncedCustomers = [...new Set(syncedCustomers)];
                            }
                        }
                    }
                }

                // Generate customer sync status display
                let customerSyncHtml = '';
                if (syncedCustomers.length > 0 && syncStatus === 'Completed') {
                    const totalSkipped = progress.overallSummary?.totalRecordsSkipped || 0;
                    const totalProcessed = progress.overallSummary?.totalRecordsProcessed || 0;
                    
                    // Determine if this was for existing customers (all skipped) or mixed/new customers
                    const isExistingCustomersOnly = totalSkipped > 0 && totalSkipped === totalProcessed;
                    const headerText = isExistingCustomersOnly ? "Vehicle Sync for Existing Customers:" : "Successfully Synced Customers:";
                    
                    customerSyncHtml = `
                        <div class="mt-2">
                            <strong>${headerText}</strong>
                            <div class="mt-1">
                                ${syncedCustomers.map(customer => {
                                    // If we have vehicle sync count, show it with the customer name
                                    let displayText = customer;
                                    if (vehiclesSynced > 0 && syncedCustomers.length === 1) {
                                        // Single customer with vehicle count
                                        displayText = `${customer}: ${vehiclesSynced} vehicles synced`;
                                    } else if (vehiclesSynced > 0 && syncedCustomers.length > 1 && !customer.includes('Record(s)') && !customer.includes('Owner(s)')) {
                                        // Multiple customers but this is a real customer name, show proportional count
                                        displayText = `${customer}: vehicles synced`;
                                    }
                                    
                                    return `
                                        <span class="badge bg-success me-1 mb-1">
                                            <i class="fas fa-sync-alt"></i> ${displayText}
                                        </span>
                                    `;
                                }).join('')}
                            </div>
                            ${isExistingCustomersOnly ? '<small class="text-muted d-block mt-1">Records already existed, vehicles synchronized</small>' : ''}
                        </div>
                    `;
                } else if (syncStatus === 'Skipped' || wasSkipped) {
                    customerSyncHtml = `
                        <div class="mt-2">
                            <span class="badge bg-secondary">
                                <i class="fas fa-info-circle"></i> Vehicle sync not required for selected migration types
                            </span>
                        </div>
                    `;
                } else if (syncStatus === 'Failed') {
                    customerSyncHtml = `
                        <div class="mt-2">
                            <span class="badge bg-danger">
                                <i class="fas fa-exclamation-triangle"></i> Vehicle sync encountered errors - check logs
                            </span>
                        </div>
                    `;
                } else if (syncStatus === 'Completed' && vehiclesSynced === 0 && customersProcessed === 0) {
                    // Show customer information even when no specific sync metrics are available
                    if (syncedCustomers.length > 0) {
                        customerSyncHtml = `
                            <div class="mt-2">
                                <strong>Processed Customers:</strong>
                                <div class="mt-1">
                                    ${syncedCustomers.map(customer => `
                                        <span class="badge bg-success me-1 mb-1">
                                            <i class="fas fa-check"></i> ${customer}
                                        </span>
                                    `).join('')}
                                </div>
                                <small class="text-muted d-block mt-1">Vehicle sync completed for migration data</small>
                            </div>
                        `;
                    } else {
                        // Check if records were skipped - this might indicate existing customers
                        const totalSkipped = progress.overallSummary?.totalRecordsSkipped || 0;
                        const totalProcessed = progress.overallSummary?.totalRecordsProcessed || 0;
                        
                        if (totalSkipped > 0 && totalSkipped === totalProcessed) {
                            // All records were skipped - try to extract customer names and vehicle count from all messages
                            let extractedCustomer = '';
                            let extractedVehicleCount = 0;
                            
                            // Try to extract customer name and vehicle count from ALL progress messages
                            const allMessages = progress.messages.join(' ');
                            
                            // Enhanced patterns for customer extraction from CSV data
                            const customerExtractionPatterns = [
                                // Look for "Customer:" followed by the name
                                /Customer[:\s]+([^,\n\r\t]+)/gi,
                                // Look for pattern "Dealer,Customer" in CSV format - capture the customer (2nd field)
                                /[^,\n\r]+,\s*([^,\n\r]+),/g,
                                // Look for "Import Demo X" pattern specifically
                                /(Import\s+Demo\s+\d+)/gi,
                                // General customer name patterns
                                /(?:customer|client)[:\s]+([A-Za-z0-9][^,\n\r\t]{2,40})/gi
                            ];
                            
                            for (const pattern of customerExtractionPatterns) {
                                const matches = [...allMessages.matchAll(pattern)];
                                if (matches.length > 0) {
                                    const potentialCustomer = matches[0][1].trim();
                                    if (potentialCustomer && 
                                        potentialCustomer.length > 3 && 
                                        !potentialCustomer.toLowerCase().includes('error') &&
                                        !potentialCustomer.toLowerCase().includes('step') &&
                                        !potentialCustomer.toLowerCase().includes('migration') &&
                                        !potentialCustomer.toLowerCase().includes('null') &&
                                        /[a-zA-Z]/.test(potentialCustomer)) {
                                        extractedCustomer = potentialCustomer;
                                        break;
                                    }
                                }
                            }
                            
                            // Try to extract vehicle count from step summaries
                            if (progress.stepSummaries) {
                                const vehicleStep = progress.stepSummaries['vehicles'];
                                if (vehicleStep && vehicleStep.recordsProcessed > 0) {
                                    extractedVehicleCount = vehicleStep.recordsProcessed;
                                }
                            }
                            
                            // Display with extracted customer name and vehicle count if available
                            if (extractedCustomer && extractedVehicleCount > 0) {
                                customerSyncHtml = `
                                    <div class="mt-2">
                                        <span class="badge bg-success">
                                            <i class="fas fa-sync-alt"></i> ${extractedCustomer}: ${extractedVehicleCount} vehicles synced
                                        </span>
                                        <small class="text-muted d-block mt-1">Records already existed, vehicles synchronized</small>
                                    </div>
                                `;
                            } else if (extractedCustomer) {
                                customerSyncHtml = `
                                    <div class="mt-2">
                                        <span class="badge bg-success">
                                            <i class="fas fa-sync-alt"></i> ${extractedCustomer}: vehicles synced
                                        </span>
                                        <small class="text-muted d-block mt-1">Records already existed, vehicles synchronized</small>
                                    </div>
                                `;
                            } else {
                                // Fallback to generic message
                                customerSyncHtml = `
                                    <div class="mt-2">
                                        <span class="badge bg-success">
                                            <i class="fas fa-sync-alt"></i> Vehicle sync completed for ${totalSkipped} existing customer records
                                        </span>
                                        <small class="text-muted d-block mt-1">All records already existed, but vehicles were synchronized</small>
                                    </div>
                                `;
                            }
                        } else {
                            // Default completed message when no specific metrics are available
                            customerSyncHtml = `
                                <div class="mt-2">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> Vehicle sync completed successfully
                                    </span>
                                </div>
                            `;
                        }
                    }
                }

                vehicleSyncSummaryHtml = `
                    <div class="mt-3">
                        <h5><i class="fas fa-car"></i> Vehicle Sync Summary</h5>
                        <div class="card border-${syncStatusColor}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">${syncStatusIcon} Vehicle Settings Sync</h6>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-${syncStatusColor}" style="width: 100%"></div>
                                        </div>
                                    </div>
                                    <div class="text-end ms-3">
                                        <small class="text-muted d-block">Status: <span class="badge bg-${syncStatusColor}">${syncStatus}</span></small>
                                        ${vehiclesSynced > 0 ? `
                                            <small class="text-muted d-block">
                                                🚗 ${vehiclesSynced} vehicles synced${vehiclesSkipped > 0 ? `, ${vehiclesSkipped} skipped` : ''}
                                            </small>
                                        ` : ''}
                                        ${customersProcessed > 0 ? `<small class="text-muted d-block">👥 ${customersProcessed} customers processed</small>` : ''}
                                    </div>
                                </div>
                                ${customerSyncHtml}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // Determine header icon based on status
            let headerIcon = 'fas fa-cog fa-spin';
            if (progress.status === 'Completed') {
                headerIcon = 'fas fa-check-circle';
            } else if (progress.status === 'Failed') {
                headerIcon = 'fas fa-exclamation-triangle';
            }
            
            results.innerHTML = `
                <div class="alert alert-${statusColor}">
                    <h4><i class="${headerIcon}"></i> Migration Progress - ${progress.status} ${refreshButton}</h4>
                    <p><strong>Current Step:</strong> ${progress.currentStep || 'Initializing...'}</p>
                    
                    <div class="progress mt-3 mb-3" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped ${progress.status === 'Running' ? 'progress-bar-animated' : ''} bg-${statusColor}" 
                             style="width: ${progress.status === 'Completed' ? '100' : progress.progress}%">${progress.status === 'Completed' ? '100' : progress.progress}%</div>
                    </div>
                    

                    
                    ${overallSummaryHtml}
                    ${stepProgressHtml}
                    ${vehicleSyncSummaryHtml}
                    
                    <div class="mt-3" style="max-height: 200px; overflow-y: auto;">
                        <strong>Progress Log:</strong>
                        <ul class="list-unstyled mt-2" style="font-family: monospace; font-size: 0.9em;">
                            ${messagesHtml}
                        </ul>
                    </div>
                    
                    ${progress.errorMessage ? `<div class="alert alert-danger mt-3"><strong>Error:</strong> ${progress.errorMessage}</div>` : ''}
                </div>
            `;
            
            results.style.display = 'block';
            results.scrollIntoView({ behavior: 'smooth' });
            
            if (progress.status === 'Completed' || progress.status === 'Failed') {
                console.log("Migration finished with status:", progress.status, "- updating button");
                // Re-enable the button for next migration
                const startBtn = document.getElementById('startBtn');
                if (progress.status === 'Completed') {
                    console.log("Setting button to completion state");
                    startBtn.innerHTML = '<i class="fas fa-check"></i> Migration Complete - Ready for Next';
                    startBtn.className = 'btn btn-success btn-lg';
                    
                    // Report buttons are now integrated directly in the progress section
                    setTimeout(() => {
                        console.log('Migration completed, fetching latest report...');
                        fetchLatestReportForButtons();
                    }, 1000);
                } else {
                    console.log("Setting button to failed state");
                    startBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Migration Failed - Try Again';
                    startBtn.className = 'btn btn-warning btn-lg';
                }
                startBtn.disabled = false; // Re-enable for next migration
                
                // Reset button to normal state after 3 seconds
                setTimeout(() => {
                    updateButtonText();
                    startBtn.className = 'btn btn-success btn-lg';
                }, 3000);
                
                if (connection && currentMigrationId) {
                    connection.invoke("LeaveMigrationGroup", currentMigrationId);
                    currentMigrationId = null;
                }
            }
        }

        // Check if migration completed by looking for recent reports
        async function checkForCompletedMigration() {
            try {
                const response = await fetch('/Home/GetReports?currentOnly=true');
                const result = await response.json();
                
                if (result.success && result.reports && result.reports.length > 0) {
                    const latestReport = result.reports[0];
                    const reportDate = new Date(latestReport.createdDate);
                    const now = new Date();
                    const diffMinutes = (now - reportDate) / (1000 * 60);
                    
                    console.log("📅 Latest report:", latestReport.fileName);
                    console.log("⏰ Report age:", diffMinutes.toFixed(1), "minutes");
                    
                    // Increase time window to 10 minutes for better detection
                    if (diffMinutes <= 10) {
                        // Only push a "Completed" UI if backend has already reported all steps completed
                        const last = window.lastProgressUpdate;
                        const allStepsCompleted = !!(last?.stepSummaries) && Object.values(last.stepSummaries).every(s => s.status === 'Completed');
                        const backendCompleted = last?.status === 'Completed' || (last?.status === 'VehicleSync' && last?.stepSummaries?.['vehicle-sync-settings']?.status === 'Completed');

                        if (backendCompleted && allStepsCompleted) {
                            console.log("✅ Found recent report and backend reported completion - updating UI");

                            const completedProgress = {
                                status: 'Completed',
                                progress: 100,
                                currentStep: 'Migration Complete',
                                messages: [
                                    '🎉 Migration completed successfully!',
                                    'All selected data has been migrated',
                                    '📊 Migration report has been generated',
                                    `📅 Completed at: ${latestReport.createdDate}`,
                                    '📋 Migration report available for download',
                                ],
                                errorMessage: null,
                                overallSummary: last?.overallSummary || { totalSteps: 1, completedSteps: 1, completionPercentage: 100 },
                                // Preserve EXACT step summaries from backend; do not fabricate counts
                                stepSummaries: { ...(last?.stepSummaries || {}) }
                            };

                            // Only add vehicle-sync step if backend already tracked it
                            if (completedProgress.stepSummaries['vehicle-sync-settings']) {
                                completedProgress.stepSummaries['vehicle-sync-settings'].status = 'Completed';
                                completedProgress.stepSummaries['vehicle-sync-settings'].endTime = latestReport.createdDate;
                            }

                            updateProgressDisplay(completedProgress);
                            latestReportFileName = latestReport.fileName;
                            return true;
                        } else {
                            // Do not override in-progress UI; only cache latest report for buttons
                            latestReportFileName = latestReport.fileName;
                            console.log("ℹ️ Skipping forced completion UI; backend not finished with all steps yet");
                        }
                    } else {
                        console.log("⏰ Report is too old (" + diffMinutes.toFixed(1) + " minutes)");
                    }
                } else {
                    console.log("❌ No reports found or reports check failed");
                }
                
                // No recent report found - provide helpful message
                console.log("ℹ️ No recent completion detected");
                const currentStatus = window.lastProgressUpdate?.status;
                if (currentStatus && currentStatus !== 'Completed' && currentStatus !== 'Failed') {
                    console.log("📝 Migration may still be in progress. Current status:", currentStatus);
                    showInfoMessage("Migration status check: No recent completion detected. Migration may still be in progress or may require manual verification.");
                } else {
                    console.log("❓ Migration status unclear");
                    showInfoMessage("Migration status unclear. Please check the logs or start a new migration if needed.");
                }
            } catch (error) {
                console.error("💥 Error checking for completed migration:", error);
                showErrorMessage("Error checking migration completion: " + error.message);
            }
        }

        // Show error message
        function showErrorMessage(message) {
            const results = document.getElementById('results');
            const currentContent = results.innerHTML;
            results.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
                ${currentContent}
            `;
        }

        // Show info message
        function showInfoMessage(message) {
            const results = document.getElementById('results');
            const currentContent = results.innerHTML;
            results.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> ${message}
                </div>
                ${currentContent}
            `;
        }

        // Clear migration status display (used when environment changes or page loads)
        function clearMigrationStatusDisplay() {
            console.log("🧹 Clearing migration status display");
            
            // Hide the results section
            const results = document.getElementById('results');
            if (results) {
                results.style.display = 'none';
                results.innerHTML = '';
            }
            
            // Clear all migration state variables
            currentMigrationId = null;
            window.migrationMap = {};
            window.lastProgressUpdate = null;
            window.lastSignalRUpdate = 0;
            latestReportFileName = '';
            currentReportFileName = '';
            
            // Clear any existing intervals
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
            
            // Reset the start button to initial state if needed
            const startBtn = document.getElementById('startBtn');
            if (startBtn && (startBtn.innerHTML.includes('Complete') || startBtn.innerHTML.includes('Failed'))) {
                updateButtonText();
                startBtn.className = 'btn btn-success btn-lg';
                startBtn.disabled = false;
            }
            
            console.log("✅ Migration status display cleared");
        }

        // Start new migration (reset the form)
        function startNewMigration() {
            location.reload();
        }

        // Report functionality variables
        let latestReportFileName = '';
        let currentReportFileName = '';

        // Report functions for status section buttons
        async function viewLatestReport() {
            if (latestReportFileName) {
                await viewModalReport(latestReportFileName);
            } else {
                alert('No report available');
            }
        }

        function downloadLatestReport() {
            if (latestReportFileName) {
                downloadReport(latestReportFileName);
            } else {
                alert('No report available');
            }
        }

        // Check for migration status and show status section if needed
        async function checkAndShowMigrationStatus() {
            try {
                const response = await fetch('/Home/GetReports?currentOnly=true');
                const result = await response.json();
                
                if (result.success && result.reports && result.reports.length > 0) {
                    const latestReport = result.reports[0];
                    latestReportFileName = latestReport.fileName;
                    
                    // MUCH more strict: Only show if report was created in last 30 seconds AND no new migration is starting
                    const reportDate = new Date(latestReport.createdDate);
                    const now = new Date();
                    const diffSeconds = (now - reportDate) / 1000;
                    
                    console.log(`Report age: ${diffSeconds} seconds old`);
                    
                    // Only auto-show very recent reports (30 seconds) and only if user isn't starting a new migration
                    if (diffSeconds <= 30 && !currentMigrationId) {
                        console.log("Showing very recent migration report");
                        showRecentMigrationNotification(latestReport);
                    } else {
                        console.log(`Not showing old report: ${diffSeconds}s old, currentMigrationId: ${currentMigrationId}`);
                        // Store the report filename for potential manual access, but don't show it automatically
                        latestReportFileName = latestReport.fileName;
                    }
                }
            } catch (error) {
                console.log('Could not check migration status:', error);
            }
        }

        function showRecentMigrationNotification(report) {
            // Show a simple success notification at the top of the page
            const notification = document.createElement('div');
            notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';
            notification.innerHTML = `
                <strong><i class="fas fa-check-circle"></i> Migration Completed!</strong><br>
                Latest migration finished successfully. Report is available for download.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            document.body.appendChild(notification);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 10000);
        }

        // Fetch latest report filename for the integrated report buttons
        async function fetchLatestReportForButtons() {
            try {
                console.log('Fetching latest report for integrated buttons...');
                const response = await fetch('/Home/GetReports?currentOnly=true');
                const result = await response.json();
                
                console.log('GetReports response:', result);
                
                if (result.success && result.reports && result.reports.length > 0) {
                    const latestReport = result.reports[0];
                    latestReportFileName = latestReport.fileName;
                    console.log('Found latest report for buttons:', latestReport.fileName);
                } else {
                    console.log('No reports found yet, will retry...');
                    // Retry after a short delay
                    setTimeout(() => {
                        fetchLatestReportForButtons();
                    }, 2000);
                }
            } catch (error) {
                console.error('Error fetching latest report for buttons:', error);
            }
        }

        function extractStepFromCurrentMigration() {
            // Get the first selected migration type as the step name
            const checkedTypes = Array.from(document.querySelectorAll('.migration-type:checked'));
            return checkedTypes.length > 0 ? checkedTypes[0].value : 'migration';
        }

        // Reports modal functionality
        window.loadAndShowReports = async function() {
            const modal = new bootstrap.Modal(document.getElementById('reportsModal'));
            modal.show();
            await refreshModalReports();
        };

        window.refreshModalReports = async function() {
            const container = document.getElementById('modalReportsContainer');
            const loading = document.getElementById('modalReportsLoading');
            
            loading.style.display = 'block';
            container.innerHTML = '';
            
            try {
                const response = await fetch('/Home/GetReports');
                const result = await response.json();
                
                if (result.success) {
                    if (result.reports && result.reports.length > 0) {
                        displayModalReports(result.reports);
                    } else {
                        container.innerHTML = '<div class="alert alert-info">No migration reports available yet. Reports are generated after migrations complete.</div>';
                    }
                } else {
                    container.innerHTML = `<div class="alert alert-warning">Error loading reports: ${result.error}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="alert alert-danger">Failed to load reports: ${error.message}</div>`;
            } finally {
                loading.style.display = 'none';
            }
        };

        window.displayModalReports = function(reports) {
            const container = document.getElementById('modalReportsContainer');
            
            let html = '<div class="row">';
            
            reports.forEach(report => {
                const date = new Date(report.createdDate).toLocaleDateString();
                const time = new Date(report.createdDate).toLocaleTimeString();
                const sizeKB = Math.round(report.size / 1024);
                
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">${report.displayName}</h6>
                                <p class="card-text text-muted small">${date} ${time} • ${sizeKB}KB</p>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" 
                                            onclick="viewModalReport('${report.fileName}')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" 
                                            onclick="downloadReport('${report.fileName}')">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        };

        window.viewModalReport = async function(fileName) {
            try {
                const response = await fetch(`/Home/ViewReport?fileName=${encodeURIComponent(fileName)}`);
                const result = await response.json();
                
                if (result.success) {
                    currentReportFileName = fileName;
                    document.getElementById('reportModalLabel').innerHTML = 
                        `<i class="fas fa-file-text"></i> ${result.report.displayName}`;
                    document.getElementById('reportContent').textContent = result.report.content;
                    
                    // Show report modal without hiding the reports modal
                    const modal = new bootstrap.Modal(document.getElementById('reportModal'));
                    modal.show();
                } else {
                    alert('Error viewing report: ' + result.error);
                }
            } catch (error) {
                alert('Failed to load report: ' + error.message);
            }
        };

        window.downloadReport = function(fileName) {
            window.open(`/Home/DownloadReport?fileName=${encodeURIComponent(fileName)}`, '_blank');
        };

        // View report for specific migration step
        window.viewStepReport = async function(stepId, stepName) {
            try {
                // First try to get the individual step report
                const stepResponse = await fetch(`/Home/GetStepReport?stepId=${encodeURIComponent(stepId)}`);
                const stepResult = await stepResponse.json();
                
                if (stepResult.success) {
                    // Show the individual step report
                    currentReportFileName = stepResult.report.fileName;
                    
                    document.getElementById('reportModalLabel').innerHTML = 
                        `<i class="fas fa-file-text"></i> ${stepName} - Individual Report`;
                    
                    document.getElementById('reportContent').textContent = stepResult.report.content;
                    
                    const modal = new bootstrap.Modal(document.getElementById('reportModal'));
                    modal.show();
                    
                } else {
                    // Fallback to the comprehensive report if no individual step report exists
                    console.log(`No individual report found for ${stepId}, falling back to comprehensive report`);
                    
                    const response = await fetch('/Home/GetReports?currentOnly=true');
                    const result = await response.json();
                    
                    if (result.success && result.reports && result.reports.length > 0) {
                        const latestReport = result.reports[0];
                        
                        // Get the full report content
                        const reportResponse = await fetch(`/Home/ViewReport?fileName=${encodeURIComponent(latestReport.fileName)}`);
                        const reportResult = await reportResponse.json();
                        
                        if (reportResult.success) {
                            currentReportFileName = latestReport.fileName;
                            
                            // Set the modal title to indicate it's for the specific step
                            document.getElementById('reportModalLabel').innerHTML = 
                                `<i class="fas fa-file-text"></i> ${stepName} - Migration Report (Comprehensive)`;
                            
                            // Set the full report content
                            const reportContent = reportResult.report.content;
                            document.getElementById('reportContent').textContent = reportContent;
                            
                            // Show the modal
                            const modal = new bootstrap.Modal(document.getElementById('reportModal'));
                            modal.show();
                            
                            // After modal is shown, try to scroll to the relevant section
                            setTimeout(() => {
                                highlightStepSection(stepId, stepName, reportContent);
                            }, 500);
                            
                        } else {
                            alert('Error loading report content: ' + reportResult.error);
                        }
                    } else {
                        alert('No migration reports available. Please run a migration first.');
                    }
                }
            } catch (error) {
                alert('Failed to load step report: ' + error.message);
            }
        };

        // Helper function to highlight the relevant section in the report
        function highlightStepSection(stepId, stepName, reportContent) {
            const reportElement = document.getElementById('reportContent');
            
            // Create a mapping of step IDs to search terms in the report
            const stepSearchTerms = {
                'spare-modules': 'SPARE MODULE',
                'preop-checklist': 'PREOP CHECKLIST',
                'vehicles': 'VEHICLE',
                'persons': 'PERSON',
                'cards-and-vehicle-access': 'CARD',
                'supervisor-access': 'SUPERVISOR ACCESS',
                'driver-blacklist': 'DRIVER BLACKLIST',
                'website-users': 'WEBSITE USER',
                'vehicle-sync-settings': 'VEHICLE SYNC SETTINGS'
            };
            
            const searchTerm = stepSearchTerms[stepId] || stepName.toUpperCase();
            
            // Try to find and scroll to the relevant section
            const lines = reportContent.split('\n');
            let targetLineIndex = -1;
            
            for (let i = 0; i < lines.length; i++) {
                if (lines[i].toUpperCase().includes(searchTerm) && 
                    (lines[i].includes('MIGRATION') || lines[i].includes('═'))) {
                    targetLineIndex = i;
                    break;
                }
            }
            
            if (targetLineIndex >= 0) {
                // Calculate approximate scroll position
                const totalLines = lines.length;
                const scrollPercentage = targetLineIndex / totalLines;
                const scrollPosition = reportElement.scrollHeight * scrollPercentage;
                
                // Smooth scroll to the section
                reportElement.scrollTo({
                    top: scrollPosition,
                    behavior: 'smooth'
                });
                
                // Add a visual indicator (optional)
                console.log(`Scrolled to ${stepName} section at line ${targetLineIndex + 1}`);
            }
        };

        // Template viewing functionality
        window.viewTemplate = async function(migrationType) {
            try {
                const response = await fetch(`/api/template/view/${migrationType}`);
                const result = await response.json();
                
                if (response.ok) {
                    let sampleDataHtml = '';
                    if (result.sampleData && result.sampleData.length > 0) {
                        sampleDataHtml = '<h6>Sample Data Rows:</h6>';
                        result.sampleData.forEach((row, index) => {
                            sampleDataHtml += `<p><strong>Row ${index + 1}:</strong> ${row.join(', ')}</p>`;
                        });
                    }
                    
                    const modalHtml = `
                        <div class="modal fade" id="templateModal" tabindex="-1" aria-labelledby="templateModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="templateModalLabel">
                                            <i class="fas fa-file-csv"></i> ${result.fileName} Template
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="alert alert-info">
                                            <strong>Description:</strong> ${result.description}
                                        </div>
                                        
                                        <h6>Required Headers (in exact sequence):</h6>
                                        <div class="row">
                                            ${result.headers.map((header, index) => `<div class="col-md-6 mb-2"><span class="badge bg-primary me-2">${index + 1}</span><code>${header}</code></div>`).join('')}
                                        </div>
                                        
                                        ${sampleDataHtml}
                                        
                                        <div class="alert alert-warning mt-3">
                                            <strong><i class="fas fa-exclamation-triangle"></i> Critical Requirements:</strong>
                                            <ul class="mb-0 mt-2">
                                                <li><strong>Header sequence:</strong> Column order must match the numbered sequence above exactly</li>
                                                <li><strong>Header names:</strong> Must be present and match exactly (case-sensitive)</li>
                                                <li><strong>CSV formatting:</strong> Use proper quoting for fields containing commas or special characters</li>
                                                <li><strong>Data types:</strong> Ensure values match expected formats (boolean: true/false, numbers, dates)</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        <a href="/api/template/download/${migrationType}" class="btn btn-primary">
                                            <i class="fas fa-download"></i> Download Template
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Remove existing modal if present
                    const existingModal = document.getElementById('templateModal');
                    if (existingModal) {
                        existingModal.remove();
                    }
                    
                    // Add modal to body and show
                    document.body.insertAdjacentHTML('beforeend', modalHtml);
                    const modal = new bootstrap.Modal(document.getElementById('templateModal'));
                    modal.show();
                    
                    // Clean up modal when closed
                    document.getElementById('templateModal').addEventListener('hidden.bs.modal', function () {
                        this.remove();
                    });
                } else {
                    alert('Error loading template information: ' + result.message);
                }
            } catch (error) {
                alert('Failed to load template information: ' + error.message);
            }
        };

        document.addEventListener('DOMContentLoaded', async function() {
            // Initialize SignalR first
            await initializeSignalR();
            
            // Clear any previous migration status on page load
            console.log("🧹 Page loaded - clearing any previous migration status display");
            clearMigrationStatusDisplay();
            console.log("✅ Page loaded with clean state - ready for new migration");
            
            // Don't automatically check for any migrations or show any status on page load
            // Page starts completely clean - user can manually start new migrations
            
            // Add event listeners for migration type checkboxes
            document.querySelectorAll('.migration-type').forEach(cb => {
                cb.addEventListener('change', updateCsvSection);
            });
            
            // Add event listener for database checking checkbox
            document.getElementById('testMode').addEventListener('change', updateButtonText);
            
            // Add event listener for environment selection changes
            document.getElementById('environment').addEventListener('change', function() {
                console.log("🔄 Environment changed - clearing migration status");
                clearMigrationStatusDisplay();
                // Check database checking checkbox when environment changes
                setDatabaseCheckingState(true);
            });
            
            // Initialize CSV section and button text on page load
            updateCsvSection();
            updateButtonText();

            document.getElementById('migrationFormData').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const env = document.getElementById('environment').value;
                if (!env) {
                    showEnvironmentTooltip('Please select an environment');
                    return;
                }
                
                const checked = Array.from(document.querySelectorAll('.migration-type:checked'));
                if (checked.length === 0) {
                    showValidationModal('Please select at least one migration type.');
                    return;
                }
                
                // Validate CSV files before submission
                const csvInputs = document.querySelectorAll('.csv-file-input');
                let validationErrors = [];
                
                console.log(`🔍 Validating ${csvInputs.length} file inputs...`);
                
                // Check each file input individually
                csvInputs.forEach((input, index) => {
                    console.log(`📁 Checking input ${index + 1}: ${input.name}`);
                    
                    if (input.files.length > 0) {
                        const file = input.files[0];
                        const migrationType = input.name.match(/\[(.*?)\]/)[1]; // Extract migration type from input name
                        
                        console.log(`📄 File: ${file.name} (${file.size} bytes) for ${migrationType}`);
                        
                        // Check file extension
                        if (!file.name.toLowerCase().endsWith('.csv')) {
                            validationErrors.push(`❌ ${migrationType}: ${file.name} is not a CSV file. Please select a .csv file.`);
                            console.log(`❌ Extension error for ${migrationType}`);
                        }
                        
                        // Check if file is empty
                        if (file.size === 0) {
                            validationErrors.push(`❌ ${migrationType}: ${file.name} is empty. Please select a file with data.`);
                            console.log(`❌ Empty file error for ${migrationType}`);
                        }
                    } else {
                        console.log(`📁 No file selected for ${input.name}`);
                    }
                });
                
                // Check if any migration types are selected but no files uploaded
                const selectedTypes = checked.map(cb => cb.value);
                const uploadedFiles = Array.from(csvInputs).filter(input => input.files.length > 0);
                
                // Check for missing files for selected migration types
                selectedTypes.forEach(selectedType => {
                    const csvInput = Array.from(csvInputs).find(input => {
                        const migrationType = input.name.match(/\[(.*?)\]/)[1];
                        return migrationType === selectedType;
                    });
                    
                    if (csvInput && csvInput.files.length === 0) {
                        validationErrors.push(`❌ ${selectedType}: Please upload a CSV file for this migration type.`);
                        console.log(`❌ Missing file for ${selectedType}`);
                    }
                });
                
                if (validationErrors.length > 0) {
                    // Show individual tooltips for each error instead of central modal
                    validationErrors.forEach(error => {
                        console.log('Processing error:', error);
                        
                        // Handle specific field errors
                        if (error.includes('❌ ') && error.includes(': ')) {
                            // Find the first colon after the migration type
                            const errorWithoutPrefix = error.replace('❌ ', '');
                            const colonIndex = errorWithoutPrefix.indexOf(': ');
                            
                            if (colonIndex !== -1) {
                                const migrationType = errorWithoutPrefix.substring(0, colonIndex);
                                const message = errorWithoutPrefix.substring(colonIndex + 2); // +2 for ': '
                                
                                console.log('Migration type:', migrationType, 'Message:', message);
                                
                                const csvInput = Array.from(csvInputs).find(input => {
                                    const inputMigrationType = input.name.match(/\[(.*?)\]/)[1];
                                    console.log('Comparing:', inputMigrationType, 'with', migrationType);
                                    return inputMigrationType === migrationType;
                                });
                                
                                if (csvInput) {
                                    console.log('Found input for migration type:', migrationType);
                                    showFileValidationTooltip(csvInput, message);
                                } else {
                                    console.log('Could not find input for migration type:', migrationType);
                                    console.log('Available inputs:', Array.from(csvInputs).map(input => input.name.match(/\[(.*?)\]/)[1]));
                                }
                            }
                        }
                    });
                    return; // Stop submission if validation errors exist
                }
                
                // No success alert - just proceed with migration silently
                
                const formData = new FormData();
                formData.append('environment', env);
                
                // Add migration types
                checked.forEach(cb => {
                    formData.append('migrationTypes', cb.value);
                });
                
                // Add test mode flag
                const testMode = document.getElementById('testMode').checked;
                formData.append('testMode', testMode);
                
                // Add CSV files (reuse the csvInputs variable)
                csvInputs.forEach(input => {
                    if (input.files.length > 0) {
                        formData.append(input.name, input.files[0]);
                    }
                });
                
                // Clear previous results and hide results section
                const results = document.getElementById('results');
                results.style.display = 'none';
                results.innerHTML = ''; // Clear all previous content
                
                // Show initial status in progress section instead of disabling button
                const actionText = testMode ? 'Running Database Checking...' : 'Starting Migration...';
                
                results.innerHTML = `
                    <div class="alert alert-info">
                        <h4><i class="fas fa-spinner fa-spin"></i> ${actionText}</h4>
                        <p><strong>Environment:</strong> ${env}</p>
                        <p><strong>Selected Types:</strong> ${checked.map(cb => cb.value).join(', ')}</p>
                        <div class="progress mt-3" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-info" style="width: 10%">
                                Initializing...
                            </div>
                        </div>
                    </div>
                `;
                results.style.display = 'block';
                results.scrollIntoView({ behavior: 'smooth' });
                
                try {
                    const response = await fetch('/Home/StartMigration', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // Uncheck database checking after migration operation starts
                        setDatabaseCheckingState(false);
                        
                        if (result.testsRan) {
                            // Test mode: Tests passed, migration started
                            showTestSuccessAndMigration(result, env, checked);
                        } else {
                            // Normal mode: Migration started directly
                            startMigrationDisplay(result.migrationId, env, checked);
                        }
                    } else {
                        // Uncheck database checking after any operation completes (even failures)
                        setDatabaseCheckingState(false);
                        
                        if (result.isTestMode && result.failedTests) {
                            // Test mode: Tests failed, show detailed warnings
                            showTestFailureWarning(result);
                        } else {
                            // General error
                            alert('Error starting migration: ' + result.error);
                            // Clear the progress section on error
                            const results = document.getElementById('results');
                            results.style.display = 'none';
                            results.innerHTML = '';
                        }
                    }
                } catch (error) {
                    // Uncheck database checking after network error
                    setDatabaseCheckingState(false);
                    
                    alert('Network error: ' + error.message);
                    // Clear the progress section on error
                    const results = document.getElementById('results');
                    results.style.display = 'none';
                    results.innerHTML = '';
                }
            });

            // Download current report from modal
            document.getElementById('downloadCurrentModalReport').addEventListener('click', function() {
                if (currentReportFileName) {
                    downloadReport(currentReportFileName);
                }
            });
        });
    </script>
}
