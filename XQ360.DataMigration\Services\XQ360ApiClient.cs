using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using XQ360.DataMigration.Models;
using System.Linq; // Added for Count()

namespace XQ360.DataMigration.Services
{
    public class XQ360ApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<XQ360ApiClient> _logger;
        private readonly MigrationConfiguration _config;
        
        private string _csrfToken = string.Empty;
        private string _authToken = string.Empty;
        private string _appToken = string.Empty;
        private bool _isAuthenticated = false;

        public XQ360ApiClient(HttpClient httpClient, ILogger<XQ360ApiClient> logger, IOptions<MigrationConfiguration> config)
        {
            _httpClient = httpClient;
            _logger = logger;
            _config = config.Value;
            
            // Add User-Agent for better compatibility with the API server (safely)
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("User-Agent", "XQ360-DataMigration/1.0");
        }

        public virtual async Task<bool> AuthenticateAsync()
        {
            try
            {
                _logger.LogInformation("Starting XQ360 API authentication...");

                // Step 1: Get CSRF token
                _csrfToken = await GetCsrfTokenAsync();

                // Step 2: Authenticate to get auth and app tokens
                var (authToken, appToken) = await AuthenticateWithCredentialsAsync(_csrfToken);
                _authToken = authToken;
                _appToken = appToken;

                _isAuthenticated = true;
                _logger.LogInformation("XQ360 API authentication successful");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "XQ360 API authentication failed");
                _isAuthenticated = false;
                return false;
            }
        }

        public async Task<bool> RefreshCsrfTokenAsync()
        {
            try
            {
                _logger.LogInformation("Refreshing CSRF token...");
                _csrfToken = await GetCsrfTokenAsync();
                _logger.LogInformation("CSRF token refreshed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh CSRF token");
                return false;
            }
        }

        public virtual async Task<bool> TestAuthenticationAsync()
        {
            try
            {
                _logger.LogInformation("Testing authentication status...");
                
                // Try a simple API call to test authentication
                var url = $"{_config.ApiBaseUrl}dataset/api/goservices/csrf-token";
                var request = new HttpRequestMessage(HttpMethod.Get, url);
                request.Headers.Add("Accept", "application/json");
                request.Headers.Add("Authorization", $"Bearer {_authToken}");
                request.Headers.Add("Authorization-Application", $"Bearer {_appToken}");
                request.Headers.Add("X-CSRF-TOKEN", _csrfToken);
                request.Headers.Add("X-Requested-With", "XMLHttpRequest");

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                _logger.LogInformation($"Authentication test result: {response.StatusCode}");
                _logger.LogDebug($"Authentication test response: {responseContent}");
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Authentication test failed");
                return false;
            }
        }

        private async Task<string> GetCsrfTokenAsync()
        {
            _logger.LogInformation("Fetching CSRF token...");

            var url = $"{_config.ApiBaseUrl}dataset/api/goservices/csrf-token";
            var request = new HttpRequestMessage(HttpMethod.Get, url);
            request.Headers.Add("Accept", "application/json");
            request.Headers.Add("X-Requested-With", "XMLHttpRequest");

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var csrfResponse = JsonConvert.DeserializeObject<CsrfTokenResponse>(responseContent);

            if (csrfResponse == null)
            {
                _logger.LogError("Failed to deserialize CSRF token response");
                throw new InvalidOperationException("CSRF token request returned null response");
            }

            _logger.LogInformation("CSRF token received successfully");
            return csrfResponse.CsrfToken ?? string.Empty;
        }

        private async Task<(string authToken, string appToken)> AuthenticateWithCredentialsAsync(string csrfToken)
        {
            _logger.LogInformation("Authenticating with credentials...");

            var url = $"{_config.ApiBaseUrl}dataset/api/gosecurityprovider/authenticate";
            
            var formData = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("userName", _config.ApiUsername),
                new KeyValuePair<string, string>("password", _config.ApiPassword)
            };

            var request = new HttpRequestMessage(HttpMethod.Post, url);
            request.Headers.Add("Accept", "application/json");
            request.Headers.Add("X-CSRF-TOKEN", csrfToken);
            request.Content = new FormUrlEncodedContent(formData);
            // Explicitly set Content-Type to match API documentation
            request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var authResponse = JsonConvert.DeserializeObject<AuthenticationResponse>(responseContent);

            var tokensDataSet = authResponse?.ObjectsDataSet?.GOSecurityTokensObjectsDataSet?.GOSecurityTokensObjects;
            if (tokensDataSet == null)
            {
                throw new InvalidOperationException("Authentication response tokens data set is null");
            }
            var firstKey = GetFirstDictionaryKey(tokensDataSet);
            var tokens = tokensDataSet[firstKey];

            _logger.LogInformation("Authentication successful, tokens acquired");
            return (tokens?.AuthenticationToken ?? string.Empty, tokens?.ApplicationToken ?? string.Empty);
        }

        public virtual async Task<ApiResult<T>> PostFormAsync<T>(string endpoint, Dictionary<string, string> formData) where T : class
        {
            if (!_isAuthenticated)
            {
                var authResult = await AuthenticateAsync();
                if (!authResult)
                {
                    return new ApiResult<T> { Success = false, ErrorMessage = "Authentication failed" };
                }
            }

            try
            {
                var url = $"{_config.ApiBaseUrl}api/{endpoint}";
                
                var request = new HttpRequestMessage(HttpMethod.Post, url);
                request.Headers.Add("Accept", "application/json");
                request.Headers.Add("Authorization", $"Bearer {_authToken}");
                request.Headers.Add("Authorization-Application", $"Bearer {_appToken}");
                request.Headers.Add("X-CSRF-TOKEN", _csrfToken);
                request.Headers.Add("X-Requested-With", "XMLHttpRequest");
                
                // Create form content from dictionary
                var formContent = formData.Select(kvp => new KeyValuePair<string, string>(kvp.Key, kvp.Value)).ToList();
                request.Content = new FormUrlEncodedContent(formContent);
                request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

                _logger.LogDebug($"Making API request to {url}");
                _logger.LogInformation($"Request headers: Authorization=Bearer {_authToken?.Substring(0, Math.Min(10, _authToken.Length))}..., X-CSRF-TOKEN={_csrfToken?.Substring(0, Math.Min(10, _csrfToken?.Length ?? 0))}...");
                _logger.LogInformation($"Content-Type: {request.Content.Headers.ContentType}");
                _logger.LogInformation($"Form data: {string.Join(", ", formData.Select(kvp => $"{kvp.Key}={kvp.Value}"))}");

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var data = JsonConvert.DeserializeObject<T>(responseContent);
                    return new ApiResult<T> { Success = true, Data = data };
                }
                else
                {
                    _logger.LogError($"API call failed. Status: {response.StatusCode}, Response: {responseContent}");
                    _logger.LogError($"API request URL: {url}");
                    return new ApiResult<T> 
                    { 
                        Success = false, 
                        ErrorMessage = $"API call failed: {response.StatusCode} - {responseContent}",
                        StatusCode = (int)response.StatusCode
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "API request failed with exception");
                return new ApiResult<T> 
                { 
                    Success = false, 
                    ErrorMessage = ex.Message 
                };
            }
        }

        public async Task<ApiResult<T>> PostAsync<T>(string endpoint, object data) where T : class
        {
            if (!_isAuthenticated)
            {
                var authResult = await AuthenticateAsync();
                if (!authResult)
                {
                    return new ApiResult<T> { Success = false, ErrorMessage = "Authentication failed" };
                }
            }

            try
            {
                var url = $"{_config.ApiBaseUrl}api/{endpoint}";
                
                // Convert object to JSON for the entity parameter
                var entityJson = JsonConvert.SerializeObject(data);
                
                // Try form-encoded with entity parameter (POST might be different from GET)
                var formData = new List<KeyValuePair<string, string>>();
                formData.Add(new KeyValuePair<string, string>("entity", entityJson));
                formData.Add(new KeyValuePair<string, string>("include", "")); // Add required include parameter as shown in Swagger

                var request = new HttpRequestMessage(HttpMethod.Post, url);
                // Use working headers from Python example
                request.Headers.Add("Accept", "application/json");
                request.Headers.Add("Authorization", $"Bearer {_authToken}");
                request.Headers.Add("Authorization-Application", $"Bearer {_appToken}");
                request.Headers.Add("X-CSRF-TOKEN", _csrfToken);
                request.Headers.Add("X-Requested-With", "XMLHttpRequest");
                
                // Form-encoded content for POST requests (matching Swagger documentation)
                request.Content = new FormUrlEncodedContent(formData);
                // Explicitly ensure Content-Type matches Swagger documentation
                request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

                _logger.LogDebug($"Making API request to {url}");
                _logger.LogInformation($"Request headers: Authorization=Bearer {_authToken?.Substring(0, Math.Min(10, _authToken.Length))}..., X-CSRF-TOKEN={_csrfToken?.Substring(0, Math.Min(10, _csrfToken?.Length ?? 0))}...");
                _logger.LogInformation($"Content-Type: {request.Content.Headers.ContentType}");
                _logger.LogInformation($"Request content (form-encoded): entity={entityJson}");

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                // If form-encoded fails, try JSON body as fallback
                if (response.StatusCode == System.Net.HttpStatusCode.UnsupportedMediaType)
                {
                    _logger.LogWarning("Form-encoded request failed with UnsupportedMediaType, trying JSON body...");
                    
                    request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Accept", "application/json");
                    request.Headers.Add("Authorization", $"Bearer {_authToken}");
                    request.Headers.Add("Authorization-Application", $"Bearer {_appToken}");
                    request.Headers.Add("X-CSRF-TOKEN", _csrfToken);
                    request.Headers.Add("X-Requested-With", "XMLHttpRequest");
                    request.Content = new StringContent(entityJson, System.Text.Encoding.UTF8, "application/json");

                    _logger.LogDebug("Retrying with JSON body");
                    response = await _httpClient.SendAsync(request);
                    responseContent = await response.Content.ReadAsStringAsync();
                    
                    // If JSON POST also fails, try PUT method
                    if (response.StatusCode == System.Net.HttpStatusCode.UnsupportedMediaType)
                    {
                        _logger.LogWarning("JSON POST failed with UnsupportedMediaType, trying PUT method...");
                        
                        request = new HttpRequestMessage(HttpMethod.Put, url);
                        request.Headers.Add("Accept", "application/json");
                        request.Headers.Add("Authorization", $"Bearer {_authToken}");
                        request.Headers.Add("Authorization-Application", $"Bearer {_appToken}");
                        request.Headers.Add("X-CSRF-TOKEN", _csrfToken);
                        request.Headers.Add("X-Requested-With", "XMLHttpRequest");
                        request.Content = new StringContent(entityJson, System.Text.Encoding.UTF8, "application/json");

                        _logger.LogDebug("Retrying with PUT method and JSON body");
                        response = await _httpClient.SendAsync(request);
                        responseContent = await response.Content.ReadAsStringAsync();
                    }
                }

                // Check if we got a CSRF token error and retry with fresh authentication
                if (response.StatusCode == System.Net.HttpStatusCode.InternalServerError && 
                    responseContent.Contains("CSRF token"))
                {
                    _logger.LogWarning("CSRF token error detected, refreshing authentication and retrying...");
                    
                    // Re-authenticate to get fresh tokens
                    _isAuthenticated = false;
                    var reAuthResult = await AuthenticateAsync();
                    if (!reAuthResult)
                    {
                        return new ApiResult<T> { Success = false, ErrorMessage = "Re-authentication failed after CSRF error" };
                    }

                    // Retry the request with fresh tokens (using form-encoded)
                    formData = new List<KeyValuePair<string, string>>();
                    formData.Add(new KeyValuePair<string, string>("entity", entityJson));
                    formData.Add(new KeyValuePair<string, string>("include", "")); // Add required include parameter as shown in Swagger
                    
                    request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Accept", "application/json");
                    request.Headers.Add("Authorization", $"Bearer {_authToken}");
                    request.Headers.Add("Authorization-Application", $"Bearer {_appToken}");
                    request.Headers.Add("X-CSRF-TOKEN", _csrfToken);
                    request.Headers.Add("X-Requested-With", "XMLHttpRequest");
                    request.Content = new FormUrlEncodedContent(formData);
                    request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

                    _logger.LogDebug("Retrying API request with fresh authentication tokens");
                    response = await _httpClient.SendAsync(request);
                    responseContent = await response.Content.ReadAsStringAsync();
                }

                if (response.IsSuccessStatusCode)
                {
                    // Log the response for debugging
                    _logger.LogDebug($"API Response for {endpoint}: {responseContent}");

                    // Add null-safe deserialization with better error handling
                    try
                    {
                        // Check if response content is valid JSON
                        if (string.IsNullOrEmpty(responseContent) || responseContent.Trim() == "null")
                        {
                            _logger.LogWarning($"API returned null or empty response for endpoint: {endpoint}");
                            return new ApiResult<T> 
                            { 
                                Success = false, 
                                ErrorMessage = "API returned null or empty response",
                                StatusCode = (int)response.StatusCode
                            };
                        }

                        // Configure JsonSerializer settings for better null handling
                        var jsonSettings = new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore,
                            MissingMemberHandling = MissingMemberHandling.Ignore,
                            Error = (sender, args) =>
                            {
                                _logger.LogError($"JSON deserialization error for {endpoint}: {args.ErrorContext.Error.Message}");
                                args.ErrorContext.Handled = true;
                            }
                        };

                        var result = JsonConvert.DeserializeObject<T>(responseContent, jsonSettings);
                        
                        // Additional null checking for the result
                        if (result == null)
                        {
                            _logger.LogWarning($"Deserialization resulted in null object for endpoint: {endpoint}");
                            return new ApiResult<T> 
                            { 
                                Success = false, 
                                ErrorMessage = "Deserialization resulted in null object",
                                StatusCode = (int)response.StatusCode
                            };
                        }

                    return new ApiResult<T> 
                    { 
                        Success = true, 
                        Data = result,
                        StatusCode = (int)response.StatusCode
                    };
                    }
                    catch (JsonException jsonEx)
                    {
                        _logger.LogError(jsonEx, $"JSON deserialization failed for endpoint: {endpoint}. Response: {responseContent}");
                        return new ApiResult<T> 
                        { 
                            Success = false, 
                            ErrorMessage = $"JSON deserialization failed: {jsonEx.Message}",
                            StatusCode = (int)response.StatusCode
                        };
                    }
                }
                else
                {
                    _logger.LogError($"API call failed. Status: {response.StatusCode}, Response: {responseContent}");
                    _logger.LogError($"API request URL: {url}");
                    _logger.LogError($"API request entity: {entityJson}");
                    
                    return new ApiResult<T> 
                    { 
                        Success = false, 
                        ErrorMessage = $"API call failed: {response.StatusCode} - {responseContent}",
                        StatusCode = (int)response.StatusCode
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error calling API endpoint: {endpoint}");
                return new ApiResult<T> 
                { 
                    Success = false, 
                    ErrorMessage = ex.Message 
                };
            }
        }

        private string GetFirstDictionaryKey<TKey, TValue>(Dictionary<TKey, TValue> dictionary) where TKey : notnull
        {
            foreach (var key in dictionary.Keys)
            {
                return key.ToString() ?? string.Empty;
            }
            throw new InvalidOperationException("Dictionary is empty");
        }
    }

    // Response models for API calls
    public class CsrfTokenResponse
    {
        [JsonProperty("csrfToken")]
        public string? CsrfToken { get; set; }
    }

    public class AuthenticationResponse
    {
        [JsonProperty("ObjectsDataSet")]
        public AuthObjectsDataSet? ObjectsDataSet { get; set; }
    }

    public class AuthObjectsDataSet
    {
        [JsonProperty("GOSecurityTokensObjectsDataSet")]
        public GOSecurityTokensObjectsDataSet? GOSecurityTokensObjectsDataSet { get; set; }
    }

    public class GOSecurityTokensObjectsDataSet
    {
        [JsonProperty("GOSecurityTokensObjects")]
        public Dictionary<string, SecurityTokens>? GOSecurityTokensObjects { get; set; }
    }

    public class SecurityTokens
    {
        [JsonProperty("AuthenticationToken")]
        public string? AuthenticationToken { get; set; }

        [JsonProperty("ApplicationToken")]
        public string? ApplicationToken { get; set; }
    }

    public class ApiResult<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? ErrorMessage { get; set; }
        public int? StatusCode { get; set; }
    }
} 