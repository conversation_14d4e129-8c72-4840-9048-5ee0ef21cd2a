using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service interface for async/await optimization operations
/// Implementation of Phase 3.1.2: Async/Await Optimization
/// </summary>
public interface IAsyncOptimizationService
{
    /// <summary>
    /// Optimizes a single async operation with <PERSON>figure<PERSON>wait(false) and proper cancellation
    /// </summary>
    /// <typeparam name="T">Return type of the operation</typeparam>
    /// <param name="operation">The async operation to optimize</param>
    /// <param name="options">Optimization options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Optimization result</returns>
    Task<AsyncOptimizationResult> OptimizeAsyncOperationAsync<T>(
        Func<CancellationToken, Task<T>> operation,
        AsyncOptimizationOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Optimizes a batch of async operations
    /// </summary>
    /// <typeparam name="T">Return type of operations</typeparam>
    /// <param name="operations">Collection of async operations</param>
    /// <param name="options">Optimization options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of optimization results</returns>
    Task<IEnumerable<AsyncOptimizationResult>> OptimizeBatchAsyncOperationsAsync<T>(
        IEnumerable<Func<CancellationToken, Task<T>>> operations,
        AsyncOptimizationOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes an async stream with memory-efficient buffering and ConfigureAwait optimization
    /// </summary>
    /// <typeparam name="TInput">Input stream type</typeparam>
    /// <typeparam name="TOutput">Output type</typeparam>
    /// <param name="inputStream">Input async stream</param>
    /// <param name="processor">Processing function</param>
    /// <param name="options">Stream processing options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Stream processing result</returns>
    Task<AsyncStreamProcessingResult<TOutput>> ProcessAsyncStreamAsync<TInput, TOutput>(
        IAsyncEnumerable<TInput> inputStream,
        Func<TInput, CancellationToken, Task<TOutput>> processor,
        AsyncStreamProcessingOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Optimizes cancellation token propagation throughout operation chain
    /// </summary>
    /// <param name="operation">Operation to execute with optimized cancellation</param>
    /// <param name="timeout">Operation timeout</param>
    /// <param name="cancellationToken">External cancellation token</param>
    /// <returns>Cancellation optimization result</returns>
    Task<CancellationTokenOptimizationResult> OptimizeCancellationTokenPropagationAsync(
        Func<CancellationToken, Task> operation,
        TimeSpan timeout,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Optimizes thread pool settings for high-throughput async scenarios
    /// </summary>
    /// <returns>Thread pool optimization result</returns>
    Task<ThreadPoolOptimizationResult> OptimizeThreadPoolUsageAsync();
}
