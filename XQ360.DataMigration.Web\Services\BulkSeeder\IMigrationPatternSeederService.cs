using XQ360.DataMigration.Models;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Enhanced seeder service that integrates migration patterns (Phase 2 implementation)
/// Provides Person/Driver creation via API and complex entity sequences
/// </summary>
public interface IMigrationPatternSeederService : IBulkSeederService
{
    /// <summary>
    /// Creates Person/Driver records using XQ360 API following migration patterns
    /// Implements rate limiting, batching, and error recovery
    /// </summary>
    /// <param name="options">Enhanced seeder options with API configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Results with API operation details</returns>
    Task<SeederResult> ExecutePersonDriverSeederAsync(MigrationPatternSeederOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates Vehicles with complete dependency chain following migration patterns
    /// ChecklistSettings → VehicleOtherSettings → Vehicle → Module allocation
    /// </summary>
    /// <param name="options">Enhanced seeder options with entity configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Results with vehicle creation details</returns>
    Task<SeederResult> ExecuteVehicleSeederAsync(MigrationPatternSeederOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates Cards and Access permissions following migration patterns
    /// Card → Driver link → 4-tier access permissions (Site/Department/Model/Vehicle)
    /// </summary>
    /// <param name="options">Enhanced seeder options with access configuration</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Results with card and access creation details</returns>
    Task<SeederResult> ExecuteCardAccessSeederAsync(MigrationPatternSeederOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes full migration pattern sequence: Person/Driver → Vehicle → Card/Access
    /// Follows proper dependency ordering and error recovery
    /// </summary>
    /// <param name="options">Complete seeder options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Results with complete operation details</returns>
    Task<SeederResult> ExecuteFullMigrationPatternAsync(MigrationPatternSeederOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates API connectivity and migration pattern prerequisites
    /// </summary>
    /// <returns>Validation result with detailed status</returns>
    Task<MigrationPatternValidationResult> ValidateMigrationPatternPrerequisitesAsync();
}

/// <summary>
/// Enhanced seeder options that support migration patterns
/// </summary>
public class MigrationPatternSeederOptions : SeederOptions
{
    /// <summary>
    /// Enable API-based Person/Driver creation (follows PersonMigration pattern)
    /// </summary>
    public bool UseApiForPersonCreation { get; set; } = true;

    /// <summary>
    /// Enable complex Vehicle creation sequence (follows VehicleMigration pattern)
    /// </summary>
    public bool UseComplexVehicleCreation { get; set; } = true;

    /// <summary>
    /// Enable Card and Access permission creation (follows VehicleAccessMigration pattern)
    /// </summary>
    public bool CreateCardAccessPermissions { get; set; } = true;

    /// <summary>
    /// Access level for card permissions
    /// </summary>
    public AccessLevel DefaultAccessLevel { get; set; } = AccessLevel.Department;

    /// <summary>
    /// Percentage of drivers that should be supervisors (0-100)
    /// </summary>
    public int SupervisorPercentage { get; set; } = 10;

    /// <summary>
    /// Percentage of drivers that should have website access (0-100)
    /// </summary>
    public int WebsiteAccessPercentage { get; set; } = 25;

    /// <summary>
    /// Vehicle checklist type preference
    /// </summary>
    public ChecklistType VehicleChecklistType { get; set; } = ChecklistType.TimeBased;

    /// <summary>
    /// Validate module availability before allocation
    /// </summary>
    public bool ValidateModuleAvailability { get; set; } = true;

    /// <summary>
    /// API call batch size (max 100 for rate limiting)
    /// </summary>
    public int ApiBatchSize { get; set; } = 50;

    /// <summary>
    /// Maximum API calls per second (max 50 per Phase 2 spec)
    /// </summary>
    public int ApiRateLimit { get; set; } = 25;
}

/// <summary>
/// Validation result for migration pattern prerequisites
/// </summary>
public class MigrationPatternValidationResult
{
    public bool IsValid { get; set; }
    public bool ApiConnectivityValid { get; set; }
    public bool DatabaseSchemaValid { get; set; }
    public bool MigrationDataValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object> ValidationDetails { get; set; } = new();
}
