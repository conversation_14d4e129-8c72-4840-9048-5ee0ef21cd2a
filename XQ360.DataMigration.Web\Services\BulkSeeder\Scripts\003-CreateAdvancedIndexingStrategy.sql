-- =================================================================
-- Advanced Indexing Strategy Script - Phase 3.2.1
-- Implements covering indexes for frequent lookups and filtered indexes for staging tables
-- =================================================================

USE [XQ360_DEV_Migration_Test];
GO

PRINT 'Starting Advanced Indexing Strategy implementation...';

-- =================================================================
-- SECTION 1: Covering Indexes for Production Tables
-- =================================================================

PRINT 'Creating covering indexes for production tables...';

-- Covering index for Vehicle lookups by SerialNo (includes frequently accessed columns)
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_Vehicle_SerialNo_Covering' AND object_id = OBJECT_ID('dbo.Vehicle'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Vehicle_SerialNo_Covering]
    ON [dbo].[Vehicle] ([SerialNo])
    INCLUDE ([Id], [HireNo], [ModelId], [SiteId], [DepartmentId], [CustomerId], [ModuleId1], [OnHire], [Created], [Updated])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created covering index: IX_Vehicle_SerialNo_Covering';
END
ELSE
    PRINT 'Covering index IX_Vehicle_SerialNo_Covering already exists';

-- Covering index for Person lookups by name and site
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_Person_Name_Site_Covering' AND object_id = OBJECT_ID('dbo.Person'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Person_Name_Site_Covering]
    ON [dbo].[Person] ([FirstName], [LastName], [SiteId])
    INCLUDE ([Id], [CustomerId], [DepartmentId], [IsDriver], [Active], [Language], [Created], [Updated])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created covering index: IX_Person_Name_Site_Covering';
END
ELSE
    PRINT 'Covering index IX_Person_Name_Site_Covering already exists';

-- Covering index for Driver lookups
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_Driver_PersonId_Covering' AND object_id = OBJECT_ID('dbo.Driver'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Driver_PersonId_Covering]
    ON [dbo].[Driver] ([PersonId])
    INCLUDE ([Id], [CardDetailsId], [IsActiveDriver], [VehicleAccess], [Created], [Updated])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created covering index: IX_Driver_PersonId_Covering';
END
ELSE
    PRINT 'Covering index IX_Driver_PersonId_Covering already exists';

-- Covering index for Module lookups by IoTDevice
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_Module_IoTDevice_Covering' AND object_id = OBJECT_ID('dbo.Module'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Module_IoTDevice_Covering]
    ON [dbo].[Module] ([IoTDevice])
    INCLUDE ([Id], [VehicleId], [SiteId], [CustomerId], [ModuleTypesId], [Created], [Updated])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created covering index: IX_Module_IoTDevice_Covering';
END
ELSE
    PRINT 'Covering index IX_Module_IoTDevice_Covering already exists';

-- Covering index for Card lookups by Weigand number
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_Card_Weigand_Covering' AND object_id = OBJECT_ID('dbo.Card'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Card_Weigand_Covering]
    ON [dbo].[Card] ([Weigand])
    INCLUDE ([Id], [WeigandIssue], [WeigandFacility], [Created], [Updated])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created covering index: IX_Card_Weigand_Covering';
END
ELSE
    PRINT 'Covering index IX_Card_Weigand_Covering already exists';

-- =================================================================
-- SECTION 2: Filtered Indexes for Staging Tables
-- =================================================================

PRINT 'Creating filtered indexes for staging tables...';

-- Filtered index for active staging sessions
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_StagingDriver_SessionId_Active' AND object_id = OBJECT_ID('dbo.StagingDriver'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_StagingDriver_SessionId_Active]
    ON [dbo].[StagingDriver] ([SessionId])
    INCLUDE ([FirstName], [LastName], [SiteId], [DepartmentId], [CustomerId])
    WHERE [ProcessingStatus] = 'Pending' OR [ProcessingStatus] = 'Processing'
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 80
    );
    PRINT 'Created filtered index: IX_StagingDriver_SessionId_Active';
END
ELSE
    PRINT 'Filtered index IX_StagingDriver_SessionId_Active already exists';

-- Filtered index for active staging vehicles
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_StagingVehicle_SessionId_Active' AND object_id = OBJECT_ID('dbo.StagingVehicle'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_StagingVehicle_SessionId_Active]
    ON [dbo].[StagingVehicle] ([SessionId])
    INCLUDE ([SerialNo], [HireNo], [ModelId], [SiteId], [DepartmentId])
    WHERE [ProcessingStatus] = 'Pending' OR [ProcessingStatus] = 'Processing'
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 80
    );
    PRINT 'Created filtered index: IX_StagingVehicle_SessionId_Active';
END
ELSE
    PRINT 'Filtered index IX_StagingVehicle_SessionId_Active already exists';

-- =================================================================
-- SECTION 3: Performance Indexes for Frequent Join Operations
-- =================================================================

PRINT 'Creating performance indexes for frequent join operations...';

-- Index for Vehicle access permission joins
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_SiteVehicleNormalCardAccess_Performance' AND object_id = OBJECT_ID('dbo.SiteVehicleNormalCardAccess'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_SiteVehicleNormalCardAccess_Performance]
    ON [dbo].[SiteVehicleNormalCardAccess] ([SiteId], [CardId])
    INCLUDE ([PermissionId])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created performance index: IX_SiteVehicleNormalCardAccess_Performance';
END
ELSE
    PRINT 'Performance index IX_SiteVehicleNormalCardAccess_Performance already exists';

-- Index for Department vehicle access permission joins
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_DepartmentVehicleNormalCardAccess_Performance' AND object_id = OBJECT_ID('dbo.DepartmentVehicleNormalCardAccess'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_DepartmentVehicleNormalCardAccess_Performance]
    ON [dbo].[DepartmentVehicleNormalCardAccess] ([DepartmentId], [CardId])
    INCLUDE ([PermissionId])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created performance index: IX_DepartmentVehicleNormalCardAccess_Performance';
END
ELSE
    PRINT 'Performance index IX_DepartmentVehicleNormalCardAccess_Performance already exists';

-- Index for Model vehicle access permission joins
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_ModelVehicleNormalCardAccess_Performance' AND object_id = OBJECT_ID('dbo.ModelVehicleNormalCardAccess'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_ModelVehicleNormalCardAccess_Performance]
    ON [dbo].[ModelVehicleNormalCardAccess] ([ModelId], [CardId], [DepartmentId])
    INCLUDE ([PermissionId])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created performance index: IX_ModelVehicleNormalCardAccess_Performance';
END
ELSE
    PRINT 'Performance index IX_ModelVehicleNormalCardAccess_Performance already exists';

-- Index for Per-vehicle access permission joins
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_PerVehicleNormalCardAccess_Performance' AND object_id = OBJECT_ID('dbo.PerVehicleNormalCardAccess'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_PerVehicleNormalCardAccess_Performance]
    ON [dbo].[PerVehicleNormalCardAccess] ([VehicleId], [CardId])
    INCLUDE ([PermissionId])
    WITH (
        PAD_INDEX = OFF,
        STATISTICS_NORECOMPUTE = OFF,
        SORT_IN_TEMPDB = ON,
        DROP_EXISTING = OFF,
        ONLINE = OFF,
        ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON,
        FILLFACTOR = 90
    );
    PRINT 'Created performance index: IX_PerVehicleNormalCardAccess_Performance';
END
ELSE
    PRINT 'Performance index IX_PerVehicleNormalCardAccess_Performance already exists';

-- =================================================================
-- SECTION 4: Statistics Optimization
-- =================================================================

PRINT 'Updating statistics on key tables...';

-- Update statistics on frequently accessed tables
UPDATE STATISTICS [dbo].[Vehicle] WITH FULLSCAN;
PRINT 'Updated statistics for Vehicle table';

UPDATE STATISTICS [dbo].[Person] WITH FULLSCAN;
PRINT 'Updated statistics for Person table';

UPDATE STATISTICS [dbo].[Driver] WITH FULLSCAN;
PRINT 'Updated statistics for Driver table';

UPDATE STATISTICS [dbo].[Module] WITH FULLSCAN;
PRINT 'Updated statistics for Module table';

UPDATE STATISTICS [dbo].[Card] WITH FULLSCAN;
PRINT 'Updated statistics for Card table';

-- Update statistics on staging tables
IF OBJECT_ID('dbo.StagingDriver') IS NOT NULL
BEGIN
    UPDATE STATISTICS [dbo].[StagingDriver] WITH FULLSCAN;
    PRINT 'Updated statistics for StagingDriver table';
END

IF OBJECT_ID('dbo.StagingVehicle') IS NOT NULL
BEGIN
    UPDATE STATISTICS [dbo].[StagingVehicle] WITH FULLSCAN;
    PRINT 'Updated statistics for StagingVehicle table';
END

-- =================================================================
-- SECTION 5: Index Maintenance Settings
-- =================================================================

PRINT 'Configuring automatic index maintenance settings...';

-- Enable automatic statistics updates for better query performance
EXEC sp_configure 'auto update statistics', 1;
RECONFIGURE;
PRINT 'Enabled automatic statistics updates';

-- Enable automatic statistics creation
EXEC sp_configure 'auto create statistics', 1;
RECONFIGURE;
PRINT 'Enabled automatic statistics creation';

-- =================================================================
-- SECTION 6: Query Store Configuration (if available)
-- =================================================================

-- Enable Query Store for performance monitoring (SQL Server 2016+)
IF EXISTS (SELECT 1 FROM sys.database_query_store_options WHERE actual_state = 0)
BEGIN
    ALTER DATABASE [XQ360_DEV_Migration_Test] SET QUERY_STORE = ON
    (
        OPERATION_MODE = READ_WRITE,
        CLEANUP_POLICY = (STALE_QUERY_THRESHOLD_DAYS = 30),
        DATA_FLUSH_INTERVAL_SECONDS = 900,
        INTERVAL_LENGTH_MINUTES = 60,
        MAX_STORAGE_SIZE_MB = 1000,
        QUERY_CAPTURE_MODE = AUTO,
        SIZE_BASED_CLEANUP_MODE = AUTO
    );
    PRINT 'Enabled Query Store for performance monitoring';
END
ELSE
    PRINT 'Query Store is already enabled or not supported';

-- =================================================================
-- SECTION 7: Index Usage Monitoring Views
-- =================================================================

PRINT 'Creating index usage monitoring views...';

-- Create view for monitoring index usage statistics
IF OBJECT_ID('dbo.vw_IndexUsageStats') IS NOT NULL
    DROP VIEW dbo.vw_IndexUsageStats;
GO

CREATE VIEW dbo.vw_IndexUsageStats
AS
SELECT 
    t.name AS TableName,
    i.name AS IndexName,
    i.type_desc AS IndexType,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates,
    s.last_user_seek,
    s.last_user_scan,
    s.last_user_lookup,
    s.last_user_update,
    CASE 
        WHEN s.user_seeks + s.user_scans + s.user_lookups = 0 THEN 'UNUSED'
        WHEN s.user_updates > (s.user_seeks + s.user_scans + s.user_lookups) * 5 THEN 'HIGH_MAINTENANCE'
        ELSE 'ACTIVE'
    END AS IndexStatus
FROM sys.dm_db_index_usage_stats s
    INNER JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
    INNER JOIN sys.tables t ON s.object_id = t.object_id
WHERE s.database_id = DB_ID()
    AND i.type > 0; -- Exclude heaps
GO

PRINT 'Created index usage monitoring view: vw_IndexUsageStats';

-- =================================================================
-- SECTION 8: Index Fragmentation Monitoring
-- =================================================================

-- Create stored procedure for index fragmentation analysis
IF OBJECT_ID('dbo.sp_AnalyzeIndexFragmentation') IS NOT NULL
    DROP PROCEDURE dbo.sp_AnalyzeIndexFragmentation;
GO

CREATE PROCEDURE dbo.sp_AnalyzeIndexFragmentation
    @FragmentationThreshold FLOAT = 10.0
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        t.name AS TableName,
        i.name AS IndexName,
        i.type_desc AS IndexType,
        ips.avg_fragmentation_in_percent,
        ips.page_count,
        CASE 
            WHEN ips.avg_fragmentation_in_percent > 30 THEN 'REBUILD RECOMMENDED'
            WHEN ips.avg_fragmentation_in_percent > @FragmentationThreshold THEN 'REORGANIZE RECOMMENDED'
            ELSE 'OK'
        END AS Recommendation
    FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'SAMPLED') ips
        INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
        INNER JOIN sys.tables t ON ips.object_id = t.object_id
    WHERE ips.avg_fragmentation_in_percent > @FragmentationThreshold
        AND ips.page_count > 1000 -- Only analyze indexes with significant page count
        AND i.type > 0 -- Exclude heaps
    ORDER BY ips.avg_fragmentation_in_percent DESC;
END;
GO

PRINT 'Created index fragmentation analysis procedure: sp_AnalyzeIndexFragmentation';

-- =================================================================
-- COMPLETION
-- =================================================================

PRINT '';
PRINT '=================================================================';
PRINT 'Advanced Indexing Strategy implementation completed successfully!';
PRINT '=================================================================';
PRINT '';
PRINT 'Summary of created indexes:';
PRINT '- IX_Vehicle_SerialNo_Covering: Covering index for vehicle lookups';
PRINT '- IX_Person_Name_Site_Covering: Covering index for person lookups';
PRINT '- IX_Driver_PersonId_Covering: Covering index for driver lookups';
PRINT '- IX_Module_IoTDevice_Covering: Covering index for module lookups';
PRINT '- IX_Card_Weigand_Covering: Covering index for card lookups';
PRINT '- IX_StagingDriver_SessionId_Active: Filtered index for active staging';
PRINT '- IX_StagingVehicle_SessionId_Active: Filtered index for active staging';
PRINT '- Performance indexes for access permission tables';
PRINT '';
PRINT 'Additional features:';
PRINT '- Updated statistics on all key tables';
PRINT '- Configured automatic statistics updates';
PRINT '- Enabled Query Store for performance monitoring';
PRINT '- Created index usage monitoring view: vw_IndexUsageStats';
PRINT '- Created fragmentation analysis procedure: sp_AnalyzeIndexFragmentation';
PRINT '';
PRINT 'Performance improvements expected:';
PRINT '- Faster lookups for Vehicle, Person, Driver, Module, and Card entities';
PRINT '- Improved staging table query performance';
PRINT '- Better join performance for access permission queries';
PRINT '- Enhanced query plan optimization through updated statistics';
PRINT '';
