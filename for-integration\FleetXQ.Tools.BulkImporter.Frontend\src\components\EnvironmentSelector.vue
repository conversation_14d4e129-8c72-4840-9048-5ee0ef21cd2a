<template>
  <div class="environment-selector">
    <div class="mb-2">
      <label for="environment" class="form-label">Environment *</label>
      <select 
        id="environment" 
        class="form-select" 
        :class="{ 'is-invalid': validationError }"
        v-model="selectedEnvironmentName"
        @change="onEnvironmentChange"
        :disabled="loading"
        required
      >
        <option value="">Select an environment...</option>
        <option 
          v-for="env in environments" 
          :key="env.name" 
          :value="env.name"
        >
          {{ env.name.toUpperCase() }}
        </option>
      </select>
      
      <div v-if="validationError" class="invalid-feedback">
        {{ validationError }}
      </div>
      
      <div v-if="loading" class="form-text">
        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        Loading environments...
      </div>
    </div>

    <!-- Environment Status Display -->
    <div v-if="currentEnvironment" class="environment-status">
      <div class="alert" :class="environmentAlertClass">
        <div class="d-flex align-items-center mb-2">
          <i :class="environmentIcon" class="me-2"></i>
          <strong>{{ currentEnvironment.name.toUpperCase() }}</strong> environment selected
        </div>
        
        <div class="environment-details">
          <p class="mb-1">{{ currentEnvironment.description }}</p>
          
          <!-- Production Warning -->
          <div v-if="isProduction" class="text-muted fw-bold mt-2">
            <i class="fas fa-exclamation-triangle me-1"></i>
            Production environment requires additional approval
          </div>
          
          <!-- Maintenance Window Warning -->
          <div v-if="isInMaintenanceWindow" class="text-danger fw-bold mt-2">
            <i class="fas fa-tools me-1"></i>
            System is currently in maintenance window
          </div>
          
          <!-- Operation Limits -->
          <div class="operation-limits mt-2">
            <small class="text-muted">
              <i class="fas fa-info-circle me-1"></i>
              Max operation size: {{ maxOperationSize.toLocaleString() }} records
            </small>
          </div>
          
          <!-- Maintenance Windows Info -->
          <div v-if="currentEnvironment.maintenanceWindows?.length" class="maintenance-info mt-2">
            <small class="text-muted">
              <strong>Maintenance Windows:</strong>
              <div v-for="window in currentEnvironment.maintenanceWindows" :key="window.start" class="ms-2">
                {{ window.start }} - {{ window.end }} {{ window.timeZone }}
                <span v-if="window.description" class="text-muted">({{ window.description }})</span>
              </div>
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useEnvironmentStore } from '@/stores/environment'
import type { Environment } from '@/types/environment'

// Store
const environmentStore = useEnvironmentStore()

// Props
const props = defineProps<{
  modelValue?: Environment | null
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Environment | null]
  'validation-change': [isValid: boolean]
}>()

// Local state
const selectedEnvironmentName = ref('')
const validationError = ref('')

// Computed properties
const environments = computed(() => environmentStore.environments)
const loading = computed(() => environmentStore.loading)
const currentEnvironment = computed(() => environmentStore.currentEnvironment)
const isProduction = computed(() => environmentStore.isProduction)
const maxOperationSize = computed(() => environmentStore.maxOperationSize)
const isInMaintenanceWindow = computed(() => environmentStore.isInMaintenanceWindow)

const environmentAlertClass = computed(() => {
  if (isInMaintenanceWindow.value) return 'alert-secondary'
  if (isProduction.value) return 'alert-light'
  return 'alert-light'
})

const environmentIcon = computed(() => {
  if (isInMaintenanceWindow.value) return 'fas fa-exclamation-triangle'
  if (isProduction.value) return 'fas fa-shield-alt'
  return 'fas fa-server'
})

const isValid = computed(() => {
  return currentEnvironment.value !== null && !validationError.value
})

// Methods
const onEnvironmentChange = () => {
  validationError.value = ''
  
  if (!selectedEnvironmentName.value) {
    environmentStore.clearEnvironment()
    emit('update:modelValue', null)
    return
  }

  const environment = environments.value.find(env => env.name === selectedEnvironmentName.value)
  if (environment) {
    environmentStore.setCurrentEnvironment(environment)
    emit('update:modelValue', environment)
    
    // Validate environment constraints
    if (isInMaintenanceWindow.value) {
      validationError.value = 'Cannot perform operations during maintenance window'
    }
  } else {
    validationError.value = 'Selected environment not found'
    emit('update:modelValue', null)
  }
}

const validateEnvironment = () => {
  if (!currentEnvironment.value) {
    validationError.value = 'Environment selection is required'
    return false
  }
  
  if (isInMaintenanceWindow.value) {
    validationError.value = 'Cannot perform operations during maintenance window'
    return false
  }
  
  validationError.value = ''
  return true
}

// Watchers
watch(isValid, (newValue) => {
  emit('validation-change', newValue)
})

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedEnvironmentName.value = newValue.name
    environmentStore.setCurrentEnvironment(newValue)
  } else {
    selectedEnvironmentName.value = ''
    environmentStore.clearEnvironment()
  }
})

// Lifecycle
onMounted(async () => {
  // Load saved environment
  environmentStore.loadSavedEnvironment()
  
  // Fetch environments if not already loaded
  if (environments.value.length === 0) {
    await environmentStore.fetchEnvironments()
  }
  
  // Set initial value if there's a saved environment
  if (currentEnvironment.value) {
    selectedEnvironmentName.value = currentEnvironment.value.name
    emit('update:modelValue', currentEnvironment.value)
  }
})

// Expose validation method for parent components
defineExpose({
  validate: validateEnvironment,
  isValid
})
</script>

<style scoped>
.environment-selector {
  .spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
  }
  
  .environment-status {
    .alert {
      border-left: 4px solid;
    }
    
    .alert-info {
      border-left-color: var(--bs-info);
    }
    
    .alert-warning {
      border-left-color: var(--bs-warning);
    }
    
    .alert-danger {
      border-left-color: var(--bs-danger);
    }
  }
  
  .operation-limits {
    padding: 0.5rem;
    background-color: rgba(var(--bs-secondary-rgb), 0.1);
    border-radius: 0.25rem;
  }
  
  .maintenance-info {
    padding: 0.5rem;
    background-color: rgba(var(--bs-info-rgb), 0.1);
    border-radius: 0.25rem;
    border: 1px solid rgba(var(--bs-info-rgb), 0.3);
  }
}
</style>
