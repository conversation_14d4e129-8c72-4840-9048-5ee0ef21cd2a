{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ_Dev;Trusted_Connection=true;MultipleActiveResultSets=true;", "FleetXQConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ_Dev;Trusted_Connection=true;MultipleActiveResultSets=true;"}, "BulkImporter": {"DefaultDriversCount": 1000, "DefaultVehiclesCount": 500, "DefaultBatchSize": 500, "MaxBatchSize": 10000, "BulkCopyTimeout": 300, "CommandTimeout": 120, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": false, "UseTempTables": true, "TempTableMode": "SessionScoped", "TempTableBatchSize": 1000, "LogTempTableOperations": true}, "DataGeneration": {"OutputDirectory": "OutputFiles", "ArchiveDirectory": "Archive", "ErrorDirectory": "Errors", "EnableSyntheticDataGeneration": true, "RandomSeed": 42, "GenerationBatchSize": 1000, "ValidateGeneratedData": true, "MaxMemoryUsageMB": 500}, "Environment": {"Name": "Development", "Description": "Local development environment", "RequiresApproval": false, "MaxOperationSize": 10000, "NotificationWebhooks": [], "MaintenanceWindows": []}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:8080", "https://localhost:3000", "https://localhost:5173", "https://localhost:8080"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true}, "RateLimiting": {"EnableEndpointRateLimiting": false, "StackBlockedRequests": false, "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 1000}]}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] [{Environment}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/bulkimporter-webapi-dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] [{Environment}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Properties": {"Application": "FleetXQ.BulkImporter.WebApi", "Environment": "Development"}}}