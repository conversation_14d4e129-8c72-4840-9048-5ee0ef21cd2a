using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using XQ360.DataMigration.Implementations;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Services
{
    public class MigrationOrchestrator
    {
        private readonly ILogger<MigrationOrchestrator> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        
        // Define the CRITICAL dependency order
        private readonly List<MigrationStep> _migrationSteps = new List<MigrationStep>
        {
            new MigrationStep
            {
                Id = "spare-modules",
                Name = "Spare Module Migration",
                Description = "API Import - handles IoT hub integration automatically",
                Type = MigrationType.Api,
                CsvFileName = "SPARE_MODEL_IMPORT_TEMPLATE.csv",
                Order = 1,
                Dependencies = new List<string>(), // No dependencies
                IsRequired = true
            },
            new MigrationStep
            {
                Id = "preop-checklist",
                Name = "Department Checklist + PreOp Questions Migration",
                Description = "SQL Import - creates department-model mappings and questions with business rule validation",
                Type = MigrationType.Sql,
                CsvFileName = "PREOP_CHECKLIST_IMPORT.csv",
                Order = 2,
                Dependencies = new List<string> { "spare-modules" },
                IsRequired = true
            },
            new MigrationStep
            {
                Id = "vehicles",
                Name = "Vehicle Migration",
                Description = "SQL Import - complex but manageable",
                Type = MigrationType.Sql,
                CsvFileName = "VEHICLE_IMPORT.csv",
                Order = 3,
                Dependencies = new List<string> { "preop-checklist" },
                IsRequired = true
            },
            new MigrationStep
            {
                Id = "persons",
                Name = "Person Migration",
                Description = "API Import - creates Driver entities and language settings automatically",
                Type = MigrationType.Api,
                CsvFileName = "PERSON_IMPORT_TEMPLATE.csv",
                Order = 4,
                Dependencies = new List<string> { "vehicles" },
                IsRequired = true
            },
            new MigrationStep
            {
                Id = "cards-and-vehicle-access",
                Name = "Card + Vehicle Access Migration",
                Description = "SQL Import - creates cards and assigns access permissions",
                Type = MigrationType.Sql,
                CsvFileName = "CARD_IMPORT.csv",
                Order = 5,
                Dependencies = new List<string> { "persons" },
                IsRequired = true
            },
            new MigrationStep
            {
                Id = "supervisor-access",
                Name = "Supervisor Access Migration",
                Description = "SQL Import - enhanced permissions for supervisors",
                Type = MigrationType.Sql,
                CsvFileName = "SUPERVISOR_ACCESS_IMPORT.csv",
                Order = 6,
                Dependencies = new List<string> { "cards-and-vehicle-access" },
                IsRequired = true
            },
            new MigrationStep
            {
                Id = "driver-blacklist",
                Name = "Driver Blacklist Migration",
                Description = "SQL Import - removes vehicle access rights for blacklisted drivers",
                Type = MigrationType.Sql,
                CsvFileName = "DRIVER_BLACKLIST_IMPORT.csv",
                Order = 7,
                Dependencies = new List<string> { "supervisor-access" },
                IsRequired = true
            },
            new MigrationStep
            {
                Id = "website-users",
                Name = "Website User Migration",
                Description = "API Import - creates website users with role assignments automatically",
                Type = MigrationType.Api,
                CsvFileName = "WEBSITE_USER_IMPORT.csv",
                Order = 8,
                Dependencies = new List<string> { "driver-blacklist" },
                IsRequired = false
            },
            new MigrationStep
            {
                Id = "vehicle-sync-settings",
                Name = "Vehicle Sync Settings",
                Description = "API Import - synchronizes vehicle IoT device settings via API",
                Type = MigrationType.Api,
                CsvFileName = "VEHICLE_IMPORT.csv", // Reads Device IDs from vehicle CSV
                Order = 9,
                Dependencies = new List<string> { "website-users" },
                IsRequired = true
            }
        };

        public MigrationOrchestrator(ILogger<MigrationOrchestrator> logger, IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task<MigrationOrchestrationResult> ExecuteAllMigrationsAsync()
        {
            var result = new MigrationOrchestrationResult();
            var startTime = DateTime.UtcNow;
            
            try
            {
                _logger.LogInformation("=== STARTING BULLETPROOF ORDERED MIGRATION PROCESS ===");
                _logger.LogInformation("CRITICAL dependency order GUARANTEED:");
                
                foreach (var step in _migrationSteps.OrderBy(s => s.Order))
                {
                    _logger.LogInformation($"{step.Order}. {step.Name} - {step.Description}");
                }
                _logger.LogInformation("");

                // Enable orchestration mode to suppress individual migration reports
                using var scope = _serviceScopeFactory.CreateScope();
                var reportingService = scope.ServiceProvider.GetRequiredService<MigrationReportingService>();
                reportingService.SetOrchestrationMode(true);
                _logger.LogInformation("📋 Orchestration mode enabled - only comprehensive Full Migration Report will be generated");

                // Step 1: Validate all dependencies and prerequisites
                await ValidatePrerequisitesAsync();

                // Step 2: Execute migrations in strict order
                foreach (var step in _migrationSteps.OrderBy(s => s.Order))
                {
                    var stepResult = await ExecuteStepWithValidationAsync(step, result);
                    
                    if (!stepResult.Success)
                    {
                        _logger.LogCritical($"MIGRATION FAILED at step {step.Order}: {step.Name}");
                        _logger.LogCritical("ALL SUBSEQUENT STEPS WILL BE SKIPPED to maintain data integrity");
                        
                        result.Success = false;
                        result.FailedStep = step;
                        result.CompletedSteps = result.StepResults.Where(r => r.Success).Select(r => r.StepId).ToList();
                        break;
                    }
                    
                    result.CompletedSteps.Add(step.Id);
                    _logger.LogInformation($"✅ Step {step.Order} completed successfully: {step.Name}");
                }

                result.Success = result.CompletedSteps.Count == _migrationSteps.Count;
                result.Duration = DateTime.UtcNow - startTime;

                // Generate comprehensive orchestration report
                using var reportScope = _serviceScopeFactory.CreateScope();
                var reportService = reportScope.ServiceProvider.GetRequiredService<MigrationReportingService>();
                reportService.GenerateOrchestrationReport(result);
                
                // Disable orchestration mode
                reportService.SetOrchestrationMode(false);
                
                // Inform user where to find comprehensive results
                _logger.LogInformation("📋 COMPREHENSIVE REPORTING COMPLETE:");
                _logger.LogInformation("   📄 Full Migration Report: Contains ALL detailed record information across all steps");
                _logger.LogInformation("   📄 Developer Logs: Technical progress and debugging information");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "CRITICAL ERROR in migration orchestration");
                
                // Ensure orchestration mode is disabled even on error
                try
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var reportingService = scope.ServiceProvider.GetRequiredService<MigrationReportingService>();
                    reportingService.SetOrchestrationMode(false);
                }
                catch (Exception reportEx)
                {
                    _logger.LogWarning(reportEx, "Failed to disable orchestration mode after error");
                }
                
                result.Success = false;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors.Add($"Orchestration failed: {ex.Message}");
                return result;
            }
        }

        public async Task<MigrationOrchestrationResult> ExecuteStepAsync(string stepId)
        {
            var step = _migrationSteps.FirstOrDefault(s => s.Id == stepId);
            if (step == null)
            {
                throw new ArgumentException($"Unknown migration step: {stepId}");
            }

            var result = new MigrationOrchestrationResult();
            
            // Ensure orchestration mode is disabled for individual migrations
            using var scope = _serviceScopeFactory.CreateScope();
            var reportingService = scope.ServiceProvider.GetRequiredService<MigrationReportingService>();
            reportingService.SetOrchestrationMode(false);
            _logger.LogInformation("📋 Individual migration - generating Full Migration Report");
            
            // Validate dependencies before executing
            await ValidateDependenciesAsync(step);
            
            var stepResult = await ExecuteStepWithValidationAsync(step, result);
            
            result.Success = stepResult.Success;
            result.CompletedSteps = stepResult.Success ? new List<string> { stepId } : new List<string>();
            
            return result;
        }

        private Task ValidatePrerequisitesAsync()
        {
            _logger.LogInformation("🔍 Validating prerequisites and CSV files...");
            
            var errors = new List<string>();
            
            foreach (var step in _migrationSteps)
            {
                var csvPath = GetCsvFilePath(step.CsvFileName);
                if (!File.Exists(csvPath))
                {
                    errors.Add($"CSV file not found for {step.Name}: {csvPath}");
                }
            }
            
            if (errors.Any())
            {
                var errorMessage = "Prerequisites validation failed:\n" + string.Join("\n", errors);
                _logger.LogCritical(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }
            
            _logger.LogInformation("✅ All prerequisites validated successfully");
            return Task.CompletedTask;
        }

        private Task ValidateDependenciesAsync(MigrationStep step)
        {
            _logger.LogInformation($"🔍 Validating dependencies for {step.Name}...");
            
            foreach (var dependencyId in step.Dependencies)
            {
                var dependency = _migrationSteps.FirstOrDefault(s => s.Id == dependencyId);
                if (dependency == null)
                {
                    throw new InvalidOperationException($"Unknown dependency '{dependencyId}' for step '{step.Id}'");
                }
                
                // Here you could add database checks to verify the dependency actually completed
                // For now, we rely on the sequential execution order
                _logger.LogDebug($"✅ Dependency validated: {dependency.Name}");
            }
            return Task.CompletedTask;
        }

        private async Task<MigrationStepResult> ExecuteStepWithValidationAsync(MigrationStep step, MigrationOrchestrationResult orchestrationResult)
        {
            _logger.LogInformation($"🚀 Executing Step {step.Order}: {step.Name}");
            _logger.LogInformation($"   Type: {step.Type}");
            _logger.LogInformation($"   CSV: {step.CsvFileName}");
            _logger.LogInformation($"   Dependencies: {string.Join(", ", step.Dependencies)}");
            
            var stepResult = new MigrationStepResult
            {
                StepId = step.Id,
                StepName = step.Name,
                StartTime = DateTime.UtcNow
            };
            
            try
            {
                var csvPath = GetCsvFilePath(step.CsvFileName);
                MigrationResult? migrationResult = null;
                
                using var scope = _serviceScopeFactory.CreateScope();
                var serviceProvider = scope.ServiceProvider;
                
                switch (step.Id)
                {
                    case "spare-modules":
                        var spareModuleMigration = serviceProvider.GetRequiredService<SpareModuleMigration>();
                        migrationResult = await spareModuleMigration.ExecuteAsync(csvPath);
                        break;
                        
                    case "preop-checklist":
                        var preopMigration = serviceProvider.GetRequiredService<PreOpChecklistMigration>();
                        migrationResult = await preopMigration.ExecuteAsync(csvPath);
                        break;
                        
                    case "vehicles":
                        var vehicleMigration = serviceProvider.GetRequiredService<VehicleMigration>();
                        migrationResult = await vehicleMigration.ExecuteAsync(csvPath);
                        break;
                        
                    case "persons":
                        var personMigration = serviceProvider.GetRequiredService<PersonMigration>();
                        migrationResult = await personMigration.ExecuteAsync(csvPath);
                        break;
                        
                    case "cards-and-vehicle-access":
                        // Execute both Card and Vehicle Access migrations as a single atomic transaction
                        migrationResult = await ExecuteCoordinatedCardAndVehicleAccessAsync(csvPath);
                        break;
                        
                    case "supervisor-access":
                        var supervisorAccessMigration = serviceProvider.GetRequiredService<SupervisorAccessMigration>();
                        migrationResult = await supervisorAccessMigration.ExecuteAsync(csvPath);
                        break;
                        
                    case "driver-blacklist":
                        var driverBlacklistMigration = serviceProvider.GetRequiredService<DriverBlacklistMigration>();
                        migrationResult = await driverBlacklistMigration.ExecuteAsync(csvPath);
                        break;
                        
                    case "website-users":
                        var websiteUserMigration = serviceProvider.GetRequiredService<WebsiteUserMigration>();
                        migrationResult = await websiteUserMigration.ExecuteAsync(csvPath);
                        break;
                        
                    case "vehicle-sync-settings":
                        var vehicleSyncMigration = serviceProvider.GetRequiredService<VehicleSyncMigration>();
                        var vehicleCsvPath = GetCsvFilePath("VEHICLE_IMPORT.csv");
                        migrationResult = await vehicleSyncMigration.ExecuteAsync(vehicleCsvPath);
                        break;
                        
                    default:
                        throw new InvalidOperationException($"Unknown migration step: {step.Id}");
                }
                
                stepResult.Success = migrationResult.Success;
                stepResult.RecordsProcessed = migrationResult.RecordsProcessed;
                stepResult.RecordsInserted = migrationResult.RecordsInserted;
                stepResult.RecordsSkipped = migrationResult.RecordsSkipped;
                stepResult.Duration = migrationResult.Duration;
                stepResult.Errors = migrationResult.Errors;
                stepResult.Warnings = migrationResult.Warnings;
                
                // Capture detailed record-level information for comprehensive reporting
                stepResult.DetailedErrors = migrationResult.DetailedErrors;
                stepResult.DetailedWarnings = migrationResult.DetailedWarnings;
                
                if (migrationResult.Success)
                {
                    _logger.LogInformation($"✅ {step.Name} completed successfully");
                    _logger.LogInformation($"   Records: {migrationResult.RecordsProcessed} processed, {migrationResult.RecordsInserted} inserted, {migrationResult.RecordsSkipped} skipped");
                }
                else
                {
                    _logger.LogError($"❌ {step.Name} failed");
                    foreach (var error in migrationResult.Errors)
                    {
                        _logger.LogError($"   Error: {error}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ {step.Name} threw exception");
                stepResult.Success = false;
                stepResult.Errors.Add(ex.Message);
            }
            
            stepResult.EndTime = DateTime.UtcNow;
            orchestrationResult.StepResults.Add(stepResult);
            
            return stepResult;
        }

        /// <summary>
        /// Execute Card and Vehicle Access migrations as a single atomic transaction
        /// to prevent data inconsistency
        /// </summary>
        private async Task<MigrationResult> ExecuteCoordinatedCardAndVehicleAccessAsync(string csvPath)
        {
            _logger.LogInformation("🔗 Starting COORDINATED Card + Vehicle Access Migration with shared transaction");
            var startTime = DateTime.UtcNow;

            using var scope = _serviceScopeFactory.CreateScope();
            var serviceProvider = scope.ServiceProvider;
            
            var cardMigration = serviceProvider.GetRequiredService<CardMigration>();
            var vehicleAccessMigration = serviceProvider.GetRequiredService<VehicleAccessMigration>();

            using var connection = new Microsoft.Data.SqlClient.SqlConnection();
            
            // Get connection string from CardMigration via reflection or DI
            var config = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<XQ360.DataMigration.Models.MigrationConfiguration>>();
            connection.ConnectionString = config.Value.DatabaseConnection;
            
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                _logger.LogInformation("   📄 Executing Card Migration (Part 1 of 2) with shared transaction...");
                var cardResult = await cardMigration.ExecuteWithTransactionAsync(csvPath, connection, transaction);
                
                if (!cardResult.Success)
                {
                    _logger.LogError("❌ Card Migration failed - aborting transaction");
                    await transaction.RollbackAsync();
                    return cardResult;
                }

                _logger.LogInformation("   🚗 Executing Vehicle Access Migration (Part 2 of 2) with shared transaction...");
                var vehicleAccessResult = await vehicleAccessMigration.ExecuteWithTransactionAsync(csvPath, connection, transaction);
                
                if (!vehicleAccessResult.Success)
                {
                    _logger.LogError("❌ Vehicle Access Migration failed - rolling back entire transaction");
                    await transaction.RollbackAsync();
                    return vehicleAccessResult;
                }

                // Both migrations succeeded - commit the transaction
                await transaction.CommitAsync();
                _logger.LogInformation("✅ COORDINATED transaction committed successfully");

                // Clean up duplicates after successful transaction
                try
                {
                    await vehicleAccessMigration.CleanupDuplicateAccessRecordsAsync();
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogWarning($"Warning: Duplicate cleanup failed: {cleanupEx.Message}");
                    // Don't fail the entire migration for cleanup issues
                }

                // Combine results - treat as single logical operation per CSV record
                var csvRecordCount = cardResult.RecordsProcessed; // Both process same CSV records
                
                // Calculate logical CSV record success: count records that had NO skipped warnings
                var totalSkippedRecords = (cardResult.DetailedWarnings?.Count ?? 0) + (vehicleAccessResult.DetailedWarnings?.Count ?? 0);
                var logicalRecordsImported = totalSkippedRecords == 0 ? vehicleAccessResult.RecordsInserted : 0;
                var logicalRecordsSkipped = totalSkippedRecords > 0 ? csvRecordCount - logicalRecordsImported : 0;
                
                var combinedResult = new MigrationResult
                {
                    Success = true,
                    RecordsProcessed = csvRecordCount, // Logical CSV record count (not double-counted)
                    RecordsInserted = logicalRecordsImported, // Records that achieved their intended purpose without warnings
                    RecordsSkipped = logicalRecordsSkipped, // Records that appear in detailed warnings (skipped)
                    Duration = DateTime.UtcNow - startTime,
                    Errors = cardResult.Errors.Concat(vehicleAccessResult.Errors).ToList(),
                    Warnings = cardResult.Warnings.Concat(vehicleAccessResult.Warnings).ToList()
                };

                // Merge detailed errors and warnings with corrected row numbering
                combinedResult.DetailedErrors.AddRange(cardResult.DetailedErrors);
                combinedResult.DetailedErrors.AddRange(vehicleAccessResult.DetailedErrors);
                
                // Add card warnings first (keep original row numbers)
                combinedResult.DetailedWarnings.AddRange(cardResult.DetailedWarnings);
                
                // Add vehicle access warnings with offset row numbers to avoid collisions with card warnings
                var cardWarningCount = combinedResult.DetailedWarnings.Count;
                foreach (var vehicleWarning in vehicleAccessResult.DetailedWarnings ?? new List<DetailedWarning>())
                {
                    // Simple offset: add the number of existing card warnings to avoid any row number collision
                    var adjustedWarning = new DetailedWarning
                    {
                        RowNumber = cardWarningCount + vehicleWarning.RowNumber,
                        WarningType = vehicleWarning.WarningType,
                        WarningMessage = vehicleWarning.WarningMessage,
                        FieldName = vehicleWarning.FieldName,
                        FieldValue = vehicleWarning.FieldValue,
                        Recommendation = vehicleWarning.Recommendation,
                        Context = vehicleWarning.Context
                    };
                    combinedResult.DetailedWarnings.Add(adjustedWarning);
                }

                                _logger.LogInformation($"🎉 COORDINATED Card + Vehicle Access Migration completed successfully:");
                _logger.LogInformation($"   📊 Total Records: {combinedResult.RecordsProcessed} processed, {combinedResult.RecordsInserted} inserted, {combinedResult.RecordsSkipped} skipped");
                _logger.LogInformation($"   ⏱️  Duration: {combinedResult.Duration}");
                
                // Generate comprehensive report for coordinated migration
                using var reportScope = _serviceScopeFactory.CreateScope();
                var reportingService = reportScope.ServiceProvider.GetRequiredService<MigrationReportingService>();
                reportingService.GenerateMigrationReport(combinedResult, "Card + Vehicle Access Migration");
                
                return combinedResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 CRITICAL ERROR in coordinated migration - rolling back transaction");
                try
                {
                    await transaction.RollbackAsync();
                    _logger.LogInformation("🔙 Transaction rolled back successfully");
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "❌ Failed to rollback transaction");
                }

                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { $"Coordinated migration failed: {ex.Message}" },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }
        
        /// <summary>
        /// Gets the correct absolute path for CSV files, accounting for different execution contexts
        /// </summary>
        private string GetCsvFilePath(string fileName)
        {
            var currentDirectory = Directory.GetCurrentDirectory();
            
            // Check if we're running from IIS deployment (XQ360Migration)
            if (currentDirectory.EndsWith("XQ360Migration"))
            {
                // When deployed to IIS, CSV_Input is in the same directory as the application
                return Path.Combine(currentDirectory, "CSV_Input", fileName);
            }
            
            // Check if we're running from the web application directory during development
            if (currentDirectory.EndsWith("XQ360.DataMigration.Web"))
            {
                // Navigate to the parent directory and then to the main migration project
                return Path.Combine(currentDirectory, "..", "XQ360.DataMigration", "CSV_Input", fileName);
            }
            
            // Otherwise, assume we're running from the main migration project directory
            return Path.Combine(currentDirectory, "CSV_Input", fileName);
        }
    }

    public enum MigrationType
    {
        Api,
        Sql
    }

    public class MigrationStep
    {
        public required string Id { get; set; }
        public required string Name { get; set; }
        public required string Description { get; set; }
        public MigrationType Type { get; set; }
        public required string CsvFileName { get; set; }
        public int Order { get; set; }
        public List<string> Dependencies { get; set; } = new List<string>();
        public bool IsRequired { get; set; }
    }

    public class MigrationOrchestrationResult
    {
        public bool Success { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> CompletedSteps { get; set; } = new List<string>();
        public MigrationStep? FailedStep { get; set; }
        public List<MigrationStepResult> StepResults { get; set; } = new List<MigrationStepResult>();
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
    }

    public class MigrationStepResult
    {
        public required string StepId { get; set; }
        public required string StepName { get; set; }
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsInserted { get; set; }
        public int RecordsSkipped { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        
        // Enhanced detailed record information for comprehensive reporting
        public List<DetailedError> DetailedErrors { get; set; } = new List<DetailedError>();
        public List<DetailedWarning> DetailedWarnings { get; set; } = new List<DetailedWarning>();
    }
} 