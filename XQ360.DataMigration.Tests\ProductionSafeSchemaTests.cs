using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// PRODUCTION-SAFE schema validation tests
    /// These tests ONLY READ schema information - NO DATA MODIFICATION
    /// Safe to run against production databases
    /// </summary>
    public class ProductionSafeSchemaTests
    {
        private readonly string _connectionString;

        public ProductionSafeSchemaTests()
        {
            _connectionString = TestConfigurationHelper.GetTestConnectionString();
        }

        [Fact]
        public async Task Database_ShouldHaveRequiredTables()
        {
            // These are the actual tables used by migration implementations
            var requiredTables = new[] { "Vehicle", "Card", "Driver", "Site", "Customer", "Department", "Module" };
            
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            foreach (var tableName in requiredTables)
            {
                var sql = @"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_NAME = @TableName AND TABLE_TYPE = 'BASE TABLE'";
                
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@TableName", tableName);
                
                var result = await cmd.ExecuteScalarAsync();
                var count = result != null ? (int)result : 0;
                Assert.True(count > 0, $"Required table '{tableName}' does not exist in database");
            }
        }

        [Fact]
        public async Task VehicleTable_ShouldHaveRequiredColumns()
        {
            // These are the actual columns used by VehicleMigration (based on INSERT statement)
            var requiredColumns = new[] { "Id", "SerialNo", "HireNo", "ModelId", "SiteId", "DepartmentId" };
            
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            foreach (var columnName in requiredColumns)
            {
                var sql = @"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'Vehicle' AND COLUMN_NAME = @ColumnName";
                
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@ColumnName", columnName);
                
                var result = await cmd.ExecuteScalarAsync();
                var count = result != null ? (int)result : 0;
                Assert.True(count > 0, $"Required column 'Vehicle.{columnName}' does not exist");
            }
        }

        [Fact]
        public async Task Database_ShouldBeAccessible()
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            
            var sql = "SELECT 1";
            using var cmd = new SqlCommand(sql, connection);
            var result = await cmd.ExecuteScalarAsync();
            
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task Database_ShouldHaveWritePermissions()
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Test write permission without actually writing
            var sql = @"
                SELECT HAS_PERMS_BY_NAME(DB_NAME(), 'DATABASE', 'INSERT') as CanInsert,
                       HAS_PERMS_BY_NAME(DB_NAME(), 'DATABASE', 'UPDATE') as CanUpdate";
            
            using var cmd = new SqlCommand(sql, connection);
            using var reader = await cmd.ExecuteReaderAsync();
            
            Assert.True(reader.Read(), "Could not check database permissions");
            
            // HAS_PERMS_BY_NAME returns int (1 = true, 0 = false, null = unknown)
            var canInsertValue = reader["CanInsert"];
            var canUpdateValue = reader["CanUpdate"];
            
            var canInsert = canInsertValue != DBNull.Value && Convert.ToInt32(canInsertValue) == 1;
            var canUpdate = canUpdateValue != DBNull.Value && Convert.ToInt32(canUpdateValue) == 1;
            
            Assert.True(canInsert, "Database user does not have INSERT permissions");
            Assert.True(canUpdate, "Database user does not have UPDATE permissions");
        }
    }
} 