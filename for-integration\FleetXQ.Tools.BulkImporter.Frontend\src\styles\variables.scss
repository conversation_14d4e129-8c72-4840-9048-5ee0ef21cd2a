// Minimalistic Neutral Color Scheme
$primary: #6C757D; // Medium gray for primary actions
$secondary: #DEE2E6; // Light gray for secondary elements
$success: #495057; // Dark gray for success states
$info: #ADB5BD; // Medium-light gray for info
$warning: #6C757D; // Same as primary for consistency
$danger: #495057; // Dark gray for danger states
$light: #F8F9FA; // Very light gray
$dark: #495057; // Dark gray for text

// Neutral Color Palette
$white: #FFFFFF; // Pure white
$gray-100: #F8F9FA; // Lightest gray
$gray-200: #E9ECEF; // Light gray
$gray-300: #DEE2E6; // Medium-light gray
$gray-400: #CED4DA; // Medium gray
$gray-500: #ADB5BD; // Medium gray
$gray-600: #6C757D; // Medium-dark gray
$gray-700: #495057; // Dark gray
$gray-800: #343A40; // Darker gray
$gray-900: #212529; // Darkest gray

// Typography
$font-family-base: 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
$font-size-base: 0.875rem; // 14px
$line-height-base: 1.5;

// Spacing
$spacer: 1rem;
$spacers: (
    0: 0,
    1: $spacer * 0.25,
    2: $spacer * 0.5,
    3: $spacer,
    4: $spacer * 1.5,
    5: $spacer * 3,
);

// Border radius
$border-radius: 0.375rem;
$border-radius-sm: 0.25rem;
$border-radius-lg: 0.5rem;

// Typography - Compact sizes
$font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-size-base: 0.8rem; // Reduced from default 1rem
$font-weight-base: 400;
$line-height-base: 1.4; // Slightly tighter line height

// Headings - Reduced by 15-20%
$h1-font-size: 2rem; // Reduced from 2.5rem
$h2-font-size: 1.6rem; // Reduced from 2rem
$h3-font-size: 1.4rem; // Reduced from 1.75rem
$h4-font-size: 1.2rem; // Reduced from 1.5rem
$h5-font-size: 1rem; // Reduced from 1.25rem
$h6-font-size: 0.875rem; // Reduced from 1rem

// Small text sizes
$small-font-size: 0.75rem; // For labels and secondary text
$badge-font-size: 0.65rem; // For badges and status indicators

// Shadows
$box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
$box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
$box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

// Component specific variables - Compact sizes
$navbar-brand-font-size: 1.2rem; // Reduced from 1.5rem
$card-border-width: 0;
$card-box-shadow: $box-shadow-sm;

// Form controls
$input-border-radius: $border-radius;
$input-focus-border-color: $gray-600;
$input-focus-box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.15);

// Buttons
$btn-border-radius: $border-radius;
$btn-font-weight: 500;

// Progress bars
$progress-height: 1rem;
$progress-border-radius: $border-radius;

// Alerts
$alert-border-radius: $border-radius;

// Cards
$card-border-radius: $border-radius;
$card-inner-border-radius: calc($border-radius - 1px);

// Compact design variables - More compact
$compact-padding-y: 0.25rem; // Reduced from 0.375rem
$compact-padding-x: 0.5rem; // Reduced from 0.75rem
$compact-margin: 0.375rem; // Reduced from 0.5rem

// Button compact sizes
$btn-padding-y-sm: 0.2rem; // Extra small button padding
$btn-padding-x-sm: 0.4rem; // Extra small button padding
$btn-font-size-sm: 0.75rem; // Small button font size

// Mobile-friendly touch targets
$touch-target-size: 44px;
$touch-target-spacing: 8px;