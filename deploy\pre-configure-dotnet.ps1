# XQ360 Data Migration - .NET Pre-configuration Script
# This script pre-configures .NET to prevent first-time setup issues when running tests

param(
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration"
)

Write-Host "XQ360 Data Migration - .NET Pre-configuration Script" -ForegroundColor Green
Write-Host "Pre-configuring .NET to prevent system folder access issues..." -ForegroundColor Yellow

# Step 1: Check if running as Administrator
Write-Host "`nStep 1: Checking administrator privileges..." -ForegroundColor Cyan
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "✅ Running as Administrator" -ForegroundColor Green
}

# Step 2: Pre-configure .NET CLI
Write-Host "`nStep 2: Pre-configuring .NET CLI..." -ForegroundColor Cyan
try {
    # Set environment variables to skip first-time experience
    $env:DOTNET_SKIP_FIRST_TIME_EXPERIENCE = "1"
    $env:DOTNET_NOLOGO = "1"
    $env:DOTNET_CLI_TELEMETRY_OPTOUT = "1"
    
    # Run dotnet --info to trigger configuration
    Write-Host "Running dotnet --info..." -ForegroundColor Yellow
    $dotnetInfo = dotnet --info 2>&1
    Write-Host "✅ .NET CLI pre-configuration completed" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Failed to pre-configure .NET CLI: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 3: Pre-configure test runner
Write-Host "`nStep 3: Pre-configuring test runner..." -ForegroundColor Cyan
try {
    # Navigate to the deployment directory
    Push-Location $DeployPath
    
    # Run a simple test command to configure the test runner
    Write-Host "Running dotnet test --help..." -ForegroundColor Yellow
    $testHelp = dotnet test --help 2>&1
    Write-Host "✅ Test runner pre-configuration completed" -ForegroundColor Green
    
    # Try to run a simple test to ensure everything is configured
    Write-Host "Running a simple test to verify configuration..." -ForegroundColor Yellow
    if (Test-Path "XQ360.DataMigration.Tests.csproj") {
        $simpleTest = dotnet test --list-tests --verbosity quiet 2>&1
        Write-Host "✅ Test configuration verified" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Test project not found in deployment directory" -ForegroundColor Yellow
    }
    
    Pop-Location
    
} catch {
    Write-Host "❌ Failed to pre-configure test runner: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 4: Set up user profile for IIS
Write-Host "`nStep 4: Setting up user profile for IIS..." -ForegroundColor Cyan
try {
    # Create a user profile directory for the application pool
    $appPoolName = "XQ360MigrationPool"
    $profilePath = "C:\inetpub\wwwroot\XQ360Migration\App_Data\.dotnet"
    
    if (-not (Test-Path $profilePath)) {
        New-Item -ItemType Directory -Path $profilePath -Force | Out-Null
        Write-Host "✅ Created .NET profile directory: $profilePath" -ForegroundColor Green
    } else {
        Write-Host "✅ .NET profile directory already exists: $profilePath" -ForegroundColor Green
    }
    
    # Set permissions on the profile directory
    icacls $profilePath /grant "IIS_IUSRS:(OI)(CI)(F)" /T
    icacls $profilePath /grant "IIS AppPool\$appPoolName:(OI)(CI)(F)" /T
    Write-Host "✅ Set permissions on .NET profile directory" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Failed to set up user profile: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 5: Create a test script for the web application
Write-Host "`nStep 5: Creating test execution script..." -ForegroundColor Cyan
try {
    $testScript = @"
@echo off
REM XQ360 Migration Test Execution Script
REM This script runs tests with proper environment variables

set DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
set DOTNET_NOLOGO=1
set DOTNET_CLI_TELEMETRY_OPTOUT=1
set DOTNET_MULTILEVEL_LOOKUP=0

cd /d "$DeployPath"
dotnet test XQ360.DataMigration.Tests.csproj --verbosity quiet --logger console
"@

    $testScriptPath = Join-Path $DeployPath "run-tests.bat"
    $testScript | Out-File -FilePath $testScriptPath -Encoding ASCII
    Write-Host "✅ Created test execution script: $testScriptPath" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Failed to create test script: $($_.Exception.Message)" -ForegroundColor Red
}

# Summary
Write-Host "`n.NET Pre-configuration Summary:" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green
Write-Host "✅ .NET CLI pre-configured" -ForegroundColor White
Write-Host "✅ Test runner pre-configured" -ForegroundColor White
Write-Host "✅ User profile directory created" -ForegroundColor White
Write-Host "✅ Test execution script created" -ForegroundColor White
Write-Host "`nThe web application should now be able to run tests without system folder access issues." -ForegroundColor Green 