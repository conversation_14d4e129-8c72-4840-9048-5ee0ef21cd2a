﻿// Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
// for details on configuring this project to bundle and minify static web assets.

// Write your JavaScript code.

// Test Bootstrap JavaScript loading
document.addEventListener('DOMContentLoaded', function() {
    console.log('Site.js loaded');
    
    // Test if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        console.log('✅ Bootstrap JavaScript loaded successfully');
        console.log('Bootstrap version:', bootstrap.VERSION || 'Unknown');
    } else {
        console.error('❌ Bootstrap JavaScript not loaded');
    }
    
    // Test if jQuery is available
    if (typeof $ !== 'undefined') {
        console.log('✅ jQuery loaded successfully');
        console.log('jQuery version:', $.fn.jquery);
    } else {
        console.error('❌ jQuery not loaded');
    }
    
    // Test Bootstrap components
    try {
        // Test if Bootstrap tooltip is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            console.log('✅ Bootstrap Tooltip component available');
        }
        
        // Test if Bootstrap collapse is available
        if (typeof bootstrap !== 'undefined' && bootstrap.Collapse) {
            console.log('✅ Bootstrap Collapse component available');
        }
        
        // Test navbar toggle functionality
        const navbarToggler = document.querySelector('.navbar-toggler');
        if (navbarToggler) {
            console.log('✅ Navbar toggler found');
            navbarToggler.addEventListener('click', function() {
                console.log('Navbar toggler clicked');
            });
        }
        
    } catch (error) {
        console.error('❌ Error testing Bootstrap components:', error);
    }
});
