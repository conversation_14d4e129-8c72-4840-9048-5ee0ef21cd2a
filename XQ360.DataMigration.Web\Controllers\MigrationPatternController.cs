using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// Controller for Phase 2: Migration Pattern Integration
/// Provides endpoints for API-based Person/Driver creation and complex entity sequences
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class MigrationPatternController : ControllerBase
{
    private readonly ILogger<MigrationPatternController> _logger;
    private readonly IMigrationPatternSeederService _migrationPatternSeeder;

    public MigrationPatternController(
        ILogger<MigrationPatternController> logger,
        IMigrationPatternSeederService migrationPatternSeeder)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _migrationPatternSeeder = migrationPatternSeeder ?? throw new ArgumentNullException(nameof(migrationPatternSeeder));
    }

    /// <summary>
    /// Validates migration pattern prerequisites (API connectivity, database schema, reference data)
    /// </summary>
    /// <returns>Validation result with detailed status</returns>
    [HttpGet("validate")]
    public async Task<ActionResult<MigrationPatternValidationResult>> ValidatePrerequisites()
    {
        try
        {
            _logger.LogInformation("Starting migration pattern prerequisites validation");

            var result = await _migrationPatternSeeder.ValidateMigrationPatternPrerequisitesAsync();

            if (result.IsValid)
            {
                _logger.LogInformation("Migration pattern prerequisites validation successful");
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Migration pattern prerequisites validation failed: {Errors}", 
                    string.Join(", ", result.Errors));
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Migration pattern prerequisites validation failed with exception");
            return StatusCode(500, new MigrationPatternValidationResult
            {
                IsValid = false,
                Errors = new List<string> { $"Validation failed: {ex.Message}" }
            });
        }
    }

    /// <summary>
    /// Creates Person/Driver records using XQ360 API following migration patterns
    /// </summary>
    /// <param name="options">Migration pattern seeder options</param>
    /// <returns>Seeder result with API operation details</returns>
    [HttpPost("person-driver")]
    public async Task<ActionResult<SeederResult>> CreatePersonDriverRecords([FromBody] MigrationPatternSeederOptions options)
    {
        try
        {
            _logger.LogInformation("Starting Person/Driver creation via migration patterns: {DriversCount} drivers", 
                options.DriversCount);

            var result = await _migrationPatternSeeder.ExecutePersonDriverSeederAsync(options, HttpContext.RequestAborted);

            if (result.Success)
            {
                _logger.LogInformation("Person/Driver creation completed successfully: {ProcessedRows} rows", 
                    result.ProcessedRows);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Person/Driver creation completed with errors: {Errors}", 
                    string.Join(", ", result.Errors));
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Person/Driver creation failed with exception");
            return StatusCode(500, new SeederResult
            {
                Success = false,
                Errors = new List<string> { $"Person/Driver creation failed: {ex.Message}" },
                Summary = $"Person/Driver creation failed: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Creates Vehicles with complete dependency chain following migration patterns
    /// </summary>
    /// <param name="options">Migration pattern seeder options</param>
    /// <returns>Seeder result with vehicle creation details</returns>
    [HttpPost("vehicles")]
    public async Task<ActionResult<SeederResult>> CreateVehicleRecords([FromBody] MigrationPatternSeederOptions options)
    {
        try
        {
            _logger.LogInformation("Starting Vehicle creation via migration patterns: {VehiclesCount} vehicles", 
                options.VehiclesCount);

            var result = await _migrationPatternSeeder.ExecuteVehicleSeederAsync(options, HttpContext.RequestAborted);

            if (result.Success)
            {
                _logger.LogInformation("Vehicle creation completed successfully: {ProcessedRows} rows", 
                    result.ProcessedRows);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Vehicle creation completed with errors: {Errors}", 
                    string.Join(", ", result.Errors));
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Vehicle creation failed with exception");
            return StatusCode(500, new SeederResult
            {
                Success = false,
                Errors = new List<string> { $"Vehicle creation failed: {ex.Message}" },
                Summary = $"Vehicle creation failed: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Creates Cards and Access permissions following migration patterns
    /// </summary>
    /// <param name="options">Migration pattern seeder options</param>
    /// <returns>Seeder result with card and access creation details</returns>
    [HttpPost("card-access")]
    public async Task<ActionResult<SeederResult>> CreateCardAccessRecords([FromBody] MigrationPatternSeederOptions options)
    {
        try
        {
            _logger.LogInformation("Starting Card/Access creation via migration patterns: {DriversCount} drivers", 
                options.DriversCount);

            var result = await _migrationPatternSeeder.ExecuteCardAccessSeederAsync(options, HttpContext.RequestAborted);

            if (result.Success)
            {
                _logger.LogInformation("Card/Access creation completed successfully: {ProcessedRows} rows", 
                    result.ProcessedRows);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Card/Access creation completed with errors: {Errors}", 
                    string.Join(", ", result.Errors));
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Card/Access creation failed with exception");
            return StatusCode(500, new SeederResult
            {
                Success = false,
                Errors = new List<string> { $"Card/Access creation failed: {ex.Message}" },
                Summary = $"Card/Access creation failed: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Executes full migration pattern sequence: Person/Driver → Vehicle → Card/Access
    /// </summary>
    /// <param name="options">Complete migration pattern seeder options</param>
    /// <returns>Seeder result with complete operation details</returns>
    [HttpPost("full-pattern")]
    public async Task<ActionResult<SeederResult>> ExecuteFullMigrationPattern([FromBody] MigrationPatternSeederOptions options)
    {
        try
        {
            _logger.LogInformation("Starting full migration pattern: {DriversCount} drivers, {VehiclesCount} vehicles", 
                options.DriversCount, options.VehiclesCount);

            var result = await _migrationPatternSeeder.ExecuteFullMigrationPatternAsync(options, HttpContext.RequestAborted);

            if (result.Success)
            {
                _logger.LogInformation("Full migration pattern completed successfully: {ProcessedRows} rows in {Duration}s", 
                    result.ProcessedRows, result.Duration.TotalSeconds);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Full migration pattern completed with errors: {Errors}", 
                    string.Join(", ", result.Errors));
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Full migration pattern failed with exception");
            return StatusCode(500, new SeederResult
            {
                Success = false,
                Errors = new List<string> { $"Full migration pattern failed: {ex.Message}" },
                Summary = $"Full migration pattern failed: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Gets default migration pattern options based on current configuration
    /// </summary>
    /// <returns>Default migration pattern seeder options</returns>
    [HttpGet("default-options")]
    public ActionResult<MigrationPatternSeederOptions> GetDefaultOptions()
    {
        try
        {
            var defaultOptions = new MigrationPatternSeederOptions
            {
                // Seeder defaults
                DriversCount = 100,
                VehiclesCount = 50,
                BatchSize = 1000,
                DryRun = false,
                GenerateData = true,

                // Migration pattern defaults
                UseApiForPersonCreation = true,
                UseComplexVehicleCreation = true,
                CreateCardAccessPermissions = true,
                DefaultAccessLevel = AccessLevel.Department,
                SupervisorPercentage = 10,
                WebsiteAccessPercentage = 25,
                VehicleChecklistType = ChecklistType.TimeBased,
                ValidateModuleAvailability = true,
                ApiBatchSize = 50,
                ApiRateLimit = 25
            };

            return Ok(defaultOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get default migration pattern options");
            return StatusCode(500, new { error = $"Failed to get default options: {ex.Message}" });
        }
    }

    /// <summary>
    /// Gets current API orchestration status and metrics
    /// </summary>
    /// <returns>API orchestration status</returns>
    [HttpGet("api-status")]
    public async Task<ActionResult<object>> GetApiOrchestrationStatus()
    {
        try
        {
            var validation = await _migrationPatternSeeder.ValidateMigrationPatternPrerequisitesAsync();

            var status = new
            {
                ApiConnectivity = validation.ApiConnectivityValid,
                DatabaseSchema = validation.DatabaseSchemaValid,
                MigrationData = validation.MigrationDataValid,
                OverallValid = validation.IsValid,
                Timestamp = DateTime.UtcNow,
                Errors = validation.Errors,
                Warnings = validation.Warnings
            };

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get API orchestration status");
            return StatusCode(500, new { error = $"Failed to get API status: {ex.Message}" });
        }
    }
}
