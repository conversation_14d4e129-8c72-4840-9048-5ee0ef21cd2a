version: '3.8'

services:
  # Web API Service
  bulkimporter-api:
    build:
      context: .
      dockerfile: FleetXQ.Tools.BulkImporter.WebApi/Dockerfile
    container_name: fleetxq-bulkimporter-api
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80;https://+:443
      - ASPNETCORE_Kestrel__Certificates__Default__Password=password
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
    volumes:
      - ./certs:/https:ro
      - bulkimporter-logs:/app/logs
      - bulkimporter-data:/app/data
      - bulkimporter-temp:/app/temp
    networks:
      - fleetxq-network
    depends_on:
      - sql-server
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Service
  bulkimporter-frontend:
    build:
      context: FleetXQ.Tools.BulkImporter.Frontend
      dockerfile: Dockerfile
    container_name: fleetxq-bulkimporter-frontend
    ports:
      - "3000:8080"
    networks:
      - fleetxq-network
    depends_on:
      - bulkimporter-api
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # SQL Server (for development)
  sql-server:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: fleetxq-sql-server
    ports:
      - "1433:1433"
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Developer
    volumes:
      - sql-data:/var/opt/mssql
      - ./Sql:/docker-entrypoint-initdb.d:ro
    networks:
      - fleetxq-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis (for caching and SignalR backplane)
  redis:
    image: redis:7-alpine
    container_name: fleetxq-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - fleetxq-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  sql-data:
    driver: local
  redis-data:
    driver: local
  bulkimporter-logs:
    driver: local
  bulkimporter-data:
    driver: local
  bulkimporter-temp:
    driver: local

networks:
  fleetxq-network:
    driver: bridge
    name: fleetxq-bulkimporter
