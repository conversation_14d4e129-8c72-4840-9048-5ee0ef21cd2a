# Simple Deployment Guide for XQ360 Data Migration

This guide provides step-by-step instructions for deploying the XQ360 Data Migration application to a remote Windows Server with IIS.

## Prerequisites

### On the Remote Windows Server:
1. **Windows Server** (2016, 2019, or 2022)
2. **IIS** with ASP.NET Core Hosting Bundle
3. **Administrator access** for deployment
4. **Port 80** (or your chosen port) available

### On Your Development Machine:
1. **PowerShell** (for running deployment script)
2. **Network access** to the remote server
3. **Administrator privileges** (for some operations)

## Quick Deployment Steps

### Step 1: Prepare Your Local Environment
```powershell
# Navigate to your project directory
cd C:\FleetXQ\XQ360.DataMigration

# Test the deployment script locally (build only)
.\deploy\deploy-remote.ps1 -ServerName "localhost" -Port 80 -SkipIISConfig
```

### Step 2: Deploy to Remote Server

#### Option A: Using the Automated Script (Recommended)
```powershell
# Run as Administrator on the remote server
.\deploy\deploy-remote.ps1 -ServerName "*************" -Port 80
```

#### Option B: Manual Deployment
1. **Build and publish applications locally:**
   ```powershell
   dotnet publish XQ360.DataMigration.Web -c Release -o "C:\temp\Web"
   dotnet publish XQ360.DataMigration -c Release -o "C:\temp\Main"
   Copy-Item "XQ360.DataMigration.Tests" -Destination "C:\temp\Tests" -Recurse
   ```

2. **Copy files to remote server:**
   ```powershell
   # Create deployment directory on remote server
   New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration" -Force
   
   # Copy web application files
   Copy-Item "C:\temp\Web\*" -Destination "C:\inetpub\wwwroot\XQ360Migration" -Recurse
   
   # Copy main application (for CLI operations)
   Copy-Item "C:\temp\Main\*" -Destination "C:\inetpub\wwwroot\XQ360Migration\bin" -Recurse
   
   # Copy test application (for database checking)
   Copy-Item "C:\temp\Tests\*" -Destination "C:\inetpub\wwwroot\XQ360Migration\tests" -Recurse
   
   # Copy production configuration
   Copy-Item "deploy\production-appsettings.json" -Destination "C:\inetpub\wwwroot\XQ360Migration\appsettings.json"
   ```

3. **Configure IIS manually:**
   ```powershell
   # Import WebAdministration module
   Import-Module WebAdministration
   
   # Create Application Pool
   New-WebAppPool -Name "XQ360MigrationPool"
   Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "managedRuntimeVersion" -Value ""
   
   # Create website
   New-Website -Name "XQ360Migration" -PhysicalPath "C:\inetpub\wwwroot\XQ360Migration" -ApplicationPool "XQ360MigrationPool" -Port 80
   
   # Start website
   Start-Website -Name "XQ360Migration"
   ```

## Configuration

### Update Production Settings
Edit `deploy\production-appsettings.json` with your real credentials:

```json
{
  "Migration": {
    "Environments": {
      "US": {
        "DatabaseConnection": "Server=your-us-server;Database=your-database;User Id=your-user;Password=your-password;",
        "ApiBaseUrl": "https://your-us-api.com/",
        "ApiUsername": "your-api-user",
        "ApiPassword": "your-api-password"
      },
      "UK": {
        "DatabaseConnection": "Server=your-uk-server;Database=your-database;User Id=your-user;Password=your-password;",
        "ApiBaseUrl": "https://your-uk-api.com/",
        "ApiUsername": "your-api-user",
        "ApiPassword": "your-api-password"
      }
      // ... other environments
    }
  }
}
```

## Troubleshooting

### Common Issues:

1. **"WebAdministration module not available"**
   - Install IIS with ASP.NET Core Hosting Bundle
   - Run: `Install-WindowsFeature -Name Web-Server -IncludeManagementTools`

2. **"Access denied" errors**
   - Run PowerShell as Administrator
   - Check file permissions on deployment directory

3. **"Port already in use"**
   - Change port in deployment script: `-Port 8080`
   - Or stop existing service using the port

4. **"Firewall rule creation failed"**
   - Run as Administrator
   - Or configure firewall manually

### Testing Deployment:

1. **Local access:** `http://localhost`
2. **Remote access:** `http://*************`
3. **Test database checking:** Use the web UI to test environment selection

## Security Considerations

1. **Update credentials** in `production-appsettings.json`
2. **Configure SSL** for production use
3. **Set up monitoring** and logging
4. **Restrict network access** as needed
5. **Regular backups** of configuration

## Support

For detailed instructions, see: `deploy\README-REMOTE-DEPLOYMENT.md`

For quick deployment, use: `deploy\QUICK-DEPLOYMENT-GUIDE.md` 