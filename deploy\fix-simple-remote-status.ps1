# XQ360 Data Migration - Simple Remote Server Status Fix
# This script fixes the status update problems with minimal configuration

param(
    [string]$ServerName = "localhost",
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [string]$AppPoolName = "XQ360MigrationPool"
)

Write-Host "XQ360 Data Migration - Simple Remote Server Status Fix" -ForegroundColor Green
Write-Host "Target Server: $ServerName" -ForegroundColor Yellow
Write-Host "Deploy Path: $DeployPath" -ForegroundColor Yellow

# Step 1: Create logs directory if it doesn't exist
Write-Host "Creating logs directory..." -ForegroundColor Yellow
$logsPath = Join-Path $DeployPath "logs"
if (-not (Test-Path $logsPath)) {
    New-Item -ItemType Directory -Path $logsPath -Force
    Write-Host "✅ Created logs directory: $logsPath" -ForegroundColor Green
} else {
    Write-Host "✅ Logs directory already exists: $logsPath" -ForegroundColor Green
}

# Step 2: Backup current web.config
Write-Host "Backing up current web.config..." -ForegroundColor Yellow
$webConfigPath = Join-Path $DeployPath "web.config"
$backupPath = Join-Path $DeployPath "web.config.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"

if (Test-Path $webConfigPath) {
    Copy-Item $webConfigPath $backupPath
    Write-Host "✅ Backed up web.config to: $backupPath" -ForegroundColor Green
} else {
    Write-Host "⚠️  No existing web.config found at: $webConfigPath" -ForegroundColor Yellow
}

# Step 3: Copy the simple web.config
Write-Host "Updating web.config with essential SignalR and logging fixes..." -ForegroundColor Yellow
$simpleWebConfig = Join-Path $PSScriptRoot "simple-remote-web.config"

if (Test-Path $simpleWebConfig) {
    Copy-Item $simpleWebConfig $webConfigPath -Force
    Write-Host "✅ Updated web.config with essential fixes" -ForegroundColor Green
} else {
    Write-Host "❌ Simple web.config not found at: $simpleWebConfig" -ForegroundColor Red
    Write-Host "Please ensure the simple-remote-web.config file exists in the deploy directory." -ForegroundColor Yellow
    exit 1
}

# Step 4: Set basic permissions on logs directory
Write-Host "Setting basic permissions on logs directory..." -ForegroundColor Yellow
try {
    $acl = Get-Acl $logsPath
    $rule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "Modify", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($rule)
    Set-Acl $logsPath $acl
    Write-Host "✅ Set basic permissions on logs directory" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not set permissions on logs directory: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "You may need to set permissions manually in IIS Manager." -ForegroundColor Yellow
}

# Step 5: Restart Application Pool
Write-Host "Restarting application pool..." -ForegroundColor Yellow
try {
    Import-Module WebAdministration
    Restart-WebAppPool -Name $AppPoolName
    Write-Host "✅ Application pool '$AppPoolName' restarted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restart application pool: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please restart the application pool manually in IIS Manager." -ForegroundColor Yellow
}

# Step 6: Test basic connectivity
Write-Host "Testing basic connectivity..." -ForegroundColor Yellow
Start-Sleep -Seconds 5  # Wait for app pool to restart

try {
    $testUrl = "http://$ServerName/"
    $response = Invoke-WebRequest -Uri $testUrl -Method GET -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✅ Application is accessible" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Application test failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "This might be normal if the application requires specific configuration." -ForegroundColor Yellow
}

# Step 7: Check logs directory permissions
Write-Host "Verifying logs directory permissions..." -ForegroundColor Yellow
if (Test-Path $logsPath) {
    try {
        $testFile = Join-Path $logsPath "test-write.tmp"
        "Test write access" | Out-File -FilePath $testFile -Encoding UTF8
        Remove-Item $testFile -Force
        Write-Host "✅ Logs directory is writable" -ForegroundColor Green
    } catch {
        Write-Host "❌ Logs directory is not writable: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please check IIS_IUSRS permissions on: $logsPath" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Logs directory does not exist: $logsPath" -ForegroundColor Red
}

# Step 8: Display summary
Write-Host "`n=== SIMPLE FIX SUMMARY ===" -ForegroundColor Cyan
Write-Host "✅ Updated web.config with essential SignalR and logging fixes" -ForegroundColor Green
Write-Host "✅ Created logs directory with basic permissions" -ForegroundColor Green
Write-Host "✅ Restarted application pool" -ForegroundColor Green
Write-Host "`nKey changes made:" -ForegroundColor Yellow
Write-Host "  • Enabled stdoutLogEnabled='true'" -ForegroundColor White
Write-Host "  • Changed hostingModel to 'outofprocess'" -ForegroundColor White
Write-Host "  • Enabled WebSocket support for SignalR" -ForegroundColor White
Write-Host "  • Removed complex security headers to avoid permission issues" -ForegroundColor White
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test the migration status updates in the web interface" -ForegroundColor White
Write-Host "2. Check the logs directory for any error messages" -ForegroundColor White
Write-Host "3. If issues persist, check IIS Manager for additional configuration" -ForegroundColor White

Write-Host "`n✅ Simple remote server status fix completed!" -ForegroundColor Green 