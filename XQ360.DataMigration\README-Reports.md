# 📋 Migration Reports Guide

The XQ360 Data Migration tool now generates **two types of reports** to serve different audiences:

## 🔧 Developer Logs (Technical Details)
**Location**: `Logs/developer-migration-YYYYMMDD.log` and Console output
**Purpose**: Technical debugging, application process information, detailed error traces
**Audience**: Developers and system administrators

### What's included:
- ✅ Step-by-step migration process details
- 🔍 Database connection information  
- ⚙️  SQL execution details
- 🐛 Technical error messages and stack traces
- 📊 Performance metrics and timing information
- 🔗 API authentication and communication logs

---

## 📋 User Reports (Actionable Results)
**Location**: `Reports/Migration-Report-YYYYMMDD-HHMMSS.txt`
**Purpose**: Clean, actionable guidance for fixing CSV files and completing migrations
**Audience**: End users, data analysts, business users

### What's included:
- ✅ **Clear Success Summary** - What was imported successfully
- ❌ **Failed Records Table** - Exact row numbers, record names, and specific fixes needed
- ⏭️  **Skipped Records Table** - What was skipped and why (usually duplicates)
- 🚀 **Next Steps** - Specific actions to complete the migration
- 📝 **CSV Fix Guide** - How to correct your data files

### Example User Report Format:
```
═══════════════════════════════════════════════════════════════════════════════════════════════
✅ CARD MIGRATION - COMPLETED SUCCESSFULLY
═══════════════════════════════════════════════════════════════════════════════════════════════
📅 Date: 2025-01-23 14:30:15
⏱️  Duration: 00:00:03
📊 Records Processed: 25
✅ Successfully Imported: 20 (80.0%)
⚠️  Skipped/Failed: 5 (20.0%)

❌ FAILED RECORDS - NEED CSV FIXES:
─────────────────────────────────────────────────────────────────────────────────────────────
Row   | Record                  | Issue                          | Fix Required
─────────────────────────────────────────────────────────────────────────────────────────────
003   | John Smith              | Person not found in dept      | Run Person migration first
007   | Card 12345              | Duplicate Weigand number       | Use unique Weigand number
012   | Jane Doe                | Missing department access      | Verify site-dept hierarchy

🚀 NEXT STEPS TO COMPLETE MIGRATION:
─────────────────────────────────────────────────────────────────────────────────────────────
1. Fix the failed records in your CSV file using the details above
2. Run Person migration first if you have missing person errors  
3. Check for duplicate card numbers in your CSV
4. Re-run the migration: dotnet run --migrate-cards
```

---

## 🎯 How to Use This System

### For End Users:
1. **Run your migration**:
   - `dotnet run --migrate-all` → Generates **Full Migration Report** (comprehensive, all steps)
   - `dotnet run --migrate-[type]` → Generates **Full Migration Report** (single step)
2. **Check the console** for basic progress updates
3. **Open the Full Migration Report** in the `Reports/` folder for detailed results
4. **Fix your CSV files** based on the specific guidance in the report
5. **Re-run the migration** to import the remaining records

### For Developers:
1. **Monitor console output** for real-time progress and immediate issues
2. **Check developer log files** for detailed technical information
3. **Review user reports** to understand data quality issues
4. **Use both logs together** for comprehensive troubleshooting

---

## 📁 File Locations

```
XQ360.DataMigration/
├── Logs/                           # Developer logs (always generated)
│   └── developer-migration-YYYYMMDD.log
├── Reports/                        # User reports  
│   └── Full-Migration-Report-YYYYMMDD-HHMMSS.txt # All migrations (single or orchestrated)
└── CSV_Input/                      # Your data files
    ├── CARD_IMPORT.csv
    ├── PERSON_IMPORT_TEMPLATE.csv
    └── ...
```

### Report Generation Logic:
- **`--migrate-all`**: Generates **ONE** comprehensive **Full Migration Report** (all steps) + **Developer Logs**
- **`--migrate-[individual]`**: Generates **Full Migration Report** (single migration) + **Developer Logs**

**Important**: 
- During `--migrate-all`, only ONE Full Migration Report is generated at the end with comprehensive details from all migration steps
- Individual migration reports are suppressed during orchestration to avoid duplication
- The old individual `Migration-Report-YYYYMMDD-HHMMSS.txt` files are no longer generated

---

## 💡 Pro Tips

### For Faster CSV Fixing:
- **Sort by error type** - Fix all missing persons first, then duplicates
- **Use row numbers** - Easy to find specific records in Excel/CSV editors  
- **Check context info** - Shows related dealer/customer/site information
- **Follow fix suggestions** - Each error includes specific remediation steps

### For Multiple Migrations:
- **Run step-by-step** - Easier to fix issues migration by migration
- **Check dependencies** - Some migrations require others to run first
- **Use full orchestration** - `--migrate-all` runs everything in correct order

---

## 🔧 Configuration

Edit `appsettings.json` to customize reporting:

```json
"Reporting": {
    "GenerateUserReports": true,           // Enable/disable user reports
    "UserReportDirectory": "./Reports",    // Where to save user reports
    "IncludeDetailedRecords": true,        // Show individual record details
    "MaxRecordsPerErrorType": 50           // Limit records per error type
}
```

---

## 📞 Support

- **User Report Issues**: Share the user report file from `Reports/` folder
- **Technical Issues**: Share both the user report AND developer log files
- **CSV Questions**: User reports include specific fixing guidance
- **System Issues**: Developer logs contain technical details for troubleshooting 