using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Hosted service that orchestrates the bulk import operation
/// </summary>
public class BulkImportHostedService : BackgroundService
{
    private readonly ILogger<BulkImportHostedService> _logger;
    private readonly IBulkImportService _bulkImportService;
    private readonly ICommandLineService _commandLineService;
    private readonly IInteractiveService _interactiveService;
    private readonly IHostApplicationLifetime _appLifetime;
    private readonly string[] _args;
    private int _exitCode = 0;

    public BulkImportHostedService(
        ILogger<BulkImportHostedService> logger,
        IBulkImportService bulkImportService,
        ICommandLineService commandLineService,
        IInteractiveService interactiveService,
        IHostApplicationLifetime appLifetime)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _bulkImportService = bulkImportService ?? throw new ArgumentNullException(nameof(bulkImportService));
        _commandLineService = commandLineService ?? throw new ArgumentNullException(nameof(commandLineService));
        _interactiveService = interactiveService ?? throw new ArgumentNullException(nameof(interactiveService));
        _appLifetime = appLifetime ?? throw new ArgumentNullException(nameof(appLifetime));
        
        // Get command line args from environment
        _args = Environment.GetCommandLineArgs().Skip(1).ToArray();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Starting bulk import hosted service");

            // Parse command line arguments
            var options = _commandLineService.ParseArguments(_args);

            // If interactive mode, prompt for missing options
            if (options.Interactive)
            {
                options = await _interactiveService.PromptForOptionsAsync(options);

                // Confirm operation
                if (!_interactiveService.ConfirmOperation(options))
                {
                    _logger.LogInformation("Operation cancelled by user");
                    _exitCode = 0;
                    return;
                }
            }

            // Execute the bulk import
            var result = await _bulkImportService.ExecuteImportAsync(options, stoppingToken);

            // Display results
            DisplayResults(result);

            _exitCode = result.Success ? 0 : 1;
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Operation was cancelled");
            _exitCode = 130; // Standard exit code for SIGINT
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Bulk import service failed with unhandled exception");
            _exitCode = 1;
        }
        finally
        {
            // Stop the application
            _appLifetime.StopApplication();
        }
    }

    private void DisplayResults(ImportResult result)
    {
        Console.WriteLine();
        Console.WriteLine("=== Import Results ===");
        Console.WriteLine($"Session ID: {result.SessionId}");
        Console.WriteLine($"Status: {(result.Success ? "SUCCESS" : "FAILED")}");
        Console.WriteLine($"Duration: {result.Duration.TotalSeconds:F2} seconds");
        Console.WriteLine($"Total rows: {result.TotalRows:N0}");
        Console.WriteLine($"Processed rows: {result.ProcessedRows:N0}");
        Console.WriteLine($"Successful rows: {result.SuccessfulRows:N0}");
        Console.WriteLine($"Failed rows: {result.FailedRows:N0}");

        if (result.Errors.Any())
        {
            Console.WriteLine();
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine("Errors:");
            foreach (var error in result.Errors)
            {
                Console.WriteLine($"  - {error}");
            }
            Console.ResetColor();
        }

        if (result.Warnings.Any())
        {
            Console.WriteLine();
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("Warnings:");
            foreach (var warning in result.Warnings)
            {
                Console.WriteLine($"  - {warning}");
            }
            Console.ResetColor();
        }

        Console.WriteLine();
        Console.WriteLine(result.Summary);

        if (result.TotalRows > 0)
        {
            var throughput = result.ProcessedRows / result.Duration.TotalSeconds;
            Console.WriteLine($"Throughput: {throughput:F0} rows/second");
        }

        _logger.LogInformation("Import results displayed to console");
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Bulk import hosted service stopping");
        await base.StopAsync(cancellationToken);
        
        // Set exit code for the application
        Environment.ExitCode = _exitCode;
    }
}