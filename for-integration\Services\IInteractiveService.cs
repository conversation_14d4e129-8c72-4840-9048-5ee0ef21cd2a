using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Configuration;

namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Service for interactive user prompts
/// </summary>
public interface IInteractiveService
{
    /// <summary>
    /// Prompts user for import options if not already specified
    /// </summary>
    /// <param name="options">Current import options</param>
    /// <returns>Updated import options</returns>
    Task<ImportOptions> PromptForOptionsAsync(ImportOptions options);

    /// <summary>
    /// Prompts user for confirmation before proceeding
    /// </summary>
    /// <param name="options">Import options to confirm</param>
    /// <returns>True if user confirms, false otherwise</returns>
    bool ConfirmOperation(ImportOptions options);
}

/// <summary>
/// Interactive service implementation
/// </summary>
public class InteractiveService : IInteractiveService
{
    private readonly BulkImporterOptions _defaultOptions;

    public InteractiveService(IOptions<BulkImporterOptions> options)
    {
        _defaultOptions = options?.Value ?? throw new ArgumentNullException(nameof(options));
    }

    public async Task<ImportOptions> PromptForOptionsAsync(ImportOptions options)
    {
        Console.WriteLine();
        Console.WriteLine("=== FleetXQ Bulk Importer ===");
        Console.WriteLine();

        // Prompt for drivers count if not specified
        if (!options.DriversCount.HasValue)
        {
            options.DriversCount = PromptForInteger(
                "Number of drivers to process", 
                _defaultOptions.DefaultDriversCount, 
                1, 
                1000000);
        }

        // Prompt for vehicles count if not specified
        if (!options.VehiclesCount.HasValue)
        {
            options.VehiclesCount = PromptForInteger(
                "Number of vehicles to process", 
                _defaultOptions.DefaultVehiclesCount, 
                1, 
                1000000);
        }

        // Prompt for batch size if not specified
        if (!options.BatchSize.HasValue)
        {
            options.BatchSize = PromptForInteger(
                "Batch size for bulk operations", 
                _defaultOptions.DefaultBatchSize, 
                100, 
                _defaultOptions.MaxBatchSize);
        }

        // Prompt for operation mode
        if (!options.GenerateData && options.InputFiles.Length == 0)
        {
            Console.WriteLine();
            Console.Write("Data source [1=Generate synthetic data, 2=Read from files]: ");
            var choice = Console.ReadLine();
            
            if (choice == "1" || choice?.ToLowerInvariant().StartsWith("g") == true)
            {
                options.GenerateData = true;
            }
            else
            {
                // For now, default to generate data since file processing is not implemented
                Console.WriteLine("File processing not yet implemented. Using synthetic data generation.");
                options.GenerateData = true;
            }
        }

        // Prompt for dry run
        Console.WriteLine();
        Console.Write("Dry run mode (validate only, no changes) [y/N]: ");
        var dryRunChoice = Console.ReadLine();
        if (dryRunChoice?.ToLowerInvariant().StartsWith("y") == true)
        {
            options.DryRun = true;
        }

        await Task.CompletedTask;
        return options;
    }

    public bool ConfirmOperation(ImportOptions options)
    {
        Console.WriteLine();
        Console.WriteLine("=== Operation Summary ===");
        Console.WriteLine($"Drivers to process: {options.DriversCount:N0}");
        Console.WriteLine($"Vehicles to process: {options.VehiclesCount:N0}");
        Console.WriteLine($"Batch size: {options.BatchSize:N0}");
        Console.WriteLine($"Data source: {(options.GenerateData ? "Generated synthetic data" : "Input files")}");
        Console.WriteLine($"Mode: {(options.DryRun ? "DRY RUN (validation only)" : "LIVE (will modify database)")}");
        
        if (!options.DryRun)
        {
            Console.WriteLine();
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("WARNING: This operation will modify the database!");
            Console.ResetColor();
        }

        Console.WriteLine();
        Console.Write("Proceed with this operation? [y/N]: ");
        var confirmation = Console.ReadLine();
        
        return confirmation?.ToLowerInvariant().StartsWith("y") == true;
    }

    private int PromptForInteger(string prompt, int defaultValue, int min, int max)
    {
        while (true)
        {
            Console.Write($"{prompt} (default {defaultValue:N0}): ");
            var input = Console.ReadLine();

            if (string.IsNullOrWhiteSpace(input))
            {
                return defaultValue;
            }

            if (int.TryParse(input, out var value))
            {
                if (value >= min && value <= max)
                {
                    return value;
                }
                Console.WriteLine($"Please enter a value between {min:N0} and {max:N0}.");
            }
            else
            {
                Console.WriteLine("Please enter a valid number.");
            }
        }
    }
}