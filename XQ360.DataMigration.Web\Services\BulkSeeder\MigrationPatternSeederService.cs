using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.SignalR;
using System.Diagnostics;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Enhanced seeder service implementing Phase 2: Migration Pattern Integration
/// Provides Person/Driver creation via API and complex entity sequences
/// </summary>
public class MigrationPatternSeederService : BulkSeederService, IMigrationPatternSeederService
{
    private readonly ILogger<MigrationPatternSeederService> _logger;
    private readonly IApiOrchestrationService _apiOrchestrationService;
    private readonly IComplexEntityCreationService _complexEntityService;
    private readonly IStagingSchemaService _stagingSchemaService;
    private readonly IHubContext<MigrationHub> _migrationHubContext;

    public MigrationPatternSeederService(
        ILogger<MigrationPatternSeederService> logger,
        IOptions<BulkSeederConfiguration> options,
        ISqlDataGenerationService sqlDataService,
        IEnvironmentConfigurationService environmentService,
        IHubContext<MigrationHub> hubContext,
        IApiOrchestrationService apiOrchestrationService,
        IComplexEntityCreationService complexEntityService,
        IStagingSchemaService stagingSchemaService)
        : base(logger, options, sqlDataService, environmentService, hubContext, apiOrchestrationService, complexEntityService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _apiOrchestrationService = apiOrchestrationService ?? throw new ArgumentNullException(nameof(apiOrchestrationService));
        _complexEntityService = complexEntityService ?? throw new ArgumentNullException(nameof(complexEntityService));
        _stagingSchemaService = stagingSchemaService ?? throw new ArgumentNullException(nameof(stagingSchemaService));
        _migrationHubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));
    }

    public async Task<SeederResult> ExecutePersonDriverSeederAsync(
        MigrationPatternSeederOptions options,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid().ToString("N")[..8];
        var stopwatch = Stopwatch.StartNew();
        
        _logger.LogInformation("Starting Person/Driver seeding using migration patterns {SessionId}", sessionId);

        var result = new SeederResult { SessionId = sessionId };

        try
        {
            // Validate prerequisites
            var validation = await ValidateMigrationPatternPrerequisitesAsync();
            if (!validation.IsValid)
            {
                result.Success = false;
                result.Errors.AddRange(validation.Errors);
                return result;
            }

            await NotifyProgress(sessionId, "Starting", 0, "Initializing Person/Driver API creation...");

            // Generate Person creation requests
            var personRequests = await GeneratePersonCreateRequestsAsync(options, cancellationToken);
            
            if (personRequests.Count == 0)
            {
                result.Success = true;
                result.Summary = "No Person/Driver records to create";
                return result;
            }

            await NotifyProgress(sessionId, "Running", 25, $"Generated {personRequests.Count} Person creation requests...");

            // Execute API-based Person/Driver creation
            var apiResult = await _apiOrchestrationService.CreatePersonDriverBatchAsync(
                personRequests, options.ApiBatchSize, cancellationToken);

            result.ProcessedRows = apiResult.TotalRequests;
            result.Success = apiResult.Success;
            result.Duration = stopwatch.Elapsed;
            result.Errors.AddRange(apiResult.Errors);
            result.Summary = $"Person/Driver creation completed: {apiResult.SuccessfulRequests}/{apiResult.TotalRequests} successful";

            await NotifyProgress(sessionId, "Completed", 100, result.Summary);

            _logger.LogInformation("Person/Driver seeding completed {SessionId}: {Successful}/{Total} in {Duration}ms",
                sessionId, apiResult.SuccessfulRequests, apiResult.TotalRequests, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Person/Driver seeding failed {SessionId}", sessionId);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.Errors.Add($"Person/Driver seeding failed: {ex.Message}");
            result.Summary = $"Person/Driver seeding failed: {ex.Message}";
            
            await NotifyProgress(sessionId, "Failed", 0, result.Summary);
            return result;
        }
    }

    public async Task<SeederResult> ExecuteVehicleSeederAsync(
        MigrationPatternSeederOptions options,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid().ToString("N")[..8];
        var stopwatch = Stopwatch.StartNew();
        
        _logger.LogInformation("Starting Vehicle seeding using migration patterns {SessionId}", sessionId);

        var result = new SeederResult { SessionId = sessionId };

        try
        {
            // Create database session for complex entity operations
            var dbSessionId = Guid.NewGuid();
            
            await NotifyProgress(sessionId, "Starting", 0, "Initializing Vehicle creation with dependencies...");

            // Generate Vehicle creation requests
            var vehicleRequests = await GenerateVehicleCreateRequestsAsync(options, cancellationToken);
            
            if (vehicleRequests.Count == 0)
            {
                result.Success = true;
                result.Summary = "No Vehicle records to create";
                return result;
            }

            await NotifyProgress(sessionId, "Running", 25, $"Generated {vehicleRequests.Count} Vehicle creation requests...");

            // Execute complex Vehicle creation sequence
            var entityResult = await _complexEntityService.CreateVehicleBatchAsync(
                dbSessionId, vehicleRequests, cancellationToken);

            result.ProcessedRows = entityResult.TotalRequests;
            result.Success = entityResult.Success;
            result.Duration = stopwatch.Elapsed;
            result.Errors.AddRange(entityResult.Errors);
            result.Warnings.AddRange(entityResult.Warnings);
            result.Summary = $"Vehicle creation completed: {entityResult.SuccessfulRequests}/{entityResult.TotalRequests} successful";

            await NotifyProgress(sessionId, "Completed", 100, result.Summary);

            _logger.LogInformation("Vehicle seeding completed {SessionId}: {Successful}/{Total} in {Duration}ms",
                sessionId, entityResult.SuccessfulRequests, entityResult.TotalRequests, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Vehicle seeding failed {SessionId}", sessionId);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.Errors.Add($"Vehicle seeding failed: {ex.Message}");
            result.Summary = $"Vehicle seeding failed: {ex.Message}";
            
            await NotifyProgress(sessionId, "Failed", 0, result.Summary);
            return result;
        }
    }

    public async Task<SeederResult> ExecuteCardAccessSeederAsync(
        MigrationPatternSeederOptions options,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid().ToString("N")[..8];
        var stopwatch = Stopwatch.StartNew();
        
        _logger.LogInformation("Starting Card/Access seeding using migration patterns {SessionId}", sessionId);

        var result = new SeederResult { SessionId = sessionId };

        try
        {
            var dbSessionId = Guid.NewGuid();
            
            await NotifyProgress(sessionId, "Starting", 0, "Initializing Card and Access permission creation...");

            // Generate Card/Access creation requests
            var accessRequests = await GenerateCardAccessCreateRequestsAsync(options, cancellationToken);
            
            if (accessRequests.Count == 0)
            {
                result.Success = true;
                result.Summary = "No Card/Access records to create";
                return result;
            }

            await NotifyProgress(sessionId, "Running", 25, $"Generated {accessRequests.Count} Card/Access creation requests...");

            // Execute Card and Access permission creation
            var entityResult = await _complexEntityService.CreateCardAccessPermissionsBatchAsync(
                dbSessionId, accessRequests, cancellationToken);

            result.ProcessedRows = entityResult.TotalRequests;
            result.Success = entityResult.Success;
            result.Duration = stopwatch.Elapsed;
            result.Errors.AddRange(entityResult.Errors);
            result.Warnings.AddRange(entityResult.Warnings);
            result.Summary = $"Card/Access creation completed: {entityResult.SuccessfulRequests}/{entityResult.TotalRequests} successful";

            await NotifyProgress(sessionId, "Completed", 100, result.Summary);

            _logger.LogInformation("Card/Access seeding completed {SessionId}: {Successful}/{Total} in {Duration}ms",
                sessionId, entityResult.SuccessfulRequests, entityResult.TotalRequests, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Card/Access seeding failed {SessionId}", sessionId);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.Errors.Add($"Card/Access seeding failed: {ex.Message}");
            result.Summary = $"Card/Access seeding failed: {ex.Message}";
            
            await NotifyProgress(sessionId, "Failed", 0, result.Summary);
            return result;
        }
    }

    public async Task<SeederResult> ExecuteFullMigrationPatternAsync(
        MigrationPatternSeederOptions options,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid().ToString("N")[..8];
        var stopwatch = Stopwatch.StartNew();
        
        _logger.LogInformation("Starting full migration pattern seeding {SessionId}", sessionId);

        var result = new SeederResult { SessionId = sessionId };

        try
        {
            await NotifyProgress(sessionId, "Starting", 0, "Executing full migration pattern sequence...");

            var allResults = new List<SeederResult>();

            // Step 1: Person/Driver creation via API (if enabled)
            if (options.UseApiForPersonCreation && (options.DriversCount ?? 0) > 0)
            {
                await NotifyProgress(sessionId, "Running", 20, "Creating Person/Driver records via API...");
                var personResult = await ExecutePersonDriverSeederAsync(options, cancellationToken);
                allResults.Add(personResult);
                
                if (!personResult.Success)
                {
                    result.Errors.Add("Person/Driver creation failed - stopping migration pattern");
                    result.Success = false;
                    return result;
                }
            }

            // Step 2: Vehicle creation with dependencies (if enabled)
            if (options.UseComplexVehicleCreation && (options.VehiclesCount ?? 0) > 0)
            {
                await NotifyProgress(sessionId, "Running", 50, "Creating Vehicles with dependency chain...");
                var vehicleResult = await ExecuteVehicleSeederAsync(options, cancellationToken);
                allResults.Add(vehicleResult);
                
                if (!vehicleResult.Success)
                {
                    result.Errors.Add("Vehicle creation failed - stopping migration pattern");
                    result.Success = false;
                    return result;
                }
            }

            // Step 3: Card and Access permission creation (if enabled)
            if (options.CreateCardAccessPermissions && (options.DriversCount ?? 0) > 0)
            {
                await NotifyProgress(sessionId, "Running", 80, "Creating Cards and Access permissions...");
                var cardResult = await ExecuteCardAccessSeederAsync(options, cancellationToken);
                allResults.Add(cardResult);
                
                if (!cardResult.Success)
                {
                    result.Warnings.Add("Card/Access creation had issues but continuing");
                }
            }

            // Aggregate results
            result.ProcessedRows = allResults.Sum(r => r.ProcessedRows);
            result.Success = allResults.All(r => r.Success);
            result.Duration = stopwatch.Elapsed;
            result.Errors.AddRange(allResults.SelectMany(r => r.Errors));
            result.Warnings.AddRange(allResults.SelectMany(r => r.Warnings));
            result.Summary = $"Full migration pattern completed: {allResults.Count} operations in {result.Duration.TotalSeconds:F2}s";

            await NotifyProgress(sessionId, "Completed", 100, result.Summary);

            _logger.LogInformation("Full migration pattern completed {SessionId}: {ProcessedRows} rows in {Duration}ms",
                sessionId, result.ProcessedRows, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Full migration pattern failed {SessionId}", sessionId);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.Errors.Add($"Full migration pattern failed: {ex.Message}");
            result.Summary = $"Full migration pattern failed: {ex.Message}";
            
            await NotifyProgress(sessionId, "Failed", 0, result.Summary);
            return result;
        }
    }

    public async Task<MigrationPatternValidationResult> ValidateMigrationPatternPrerequisitesAsync()
    {
        var result = new MigrationPatternValidationResult();

        try
        {
            _logger.LogInformation("Validating migration pattern prerequisites...");

            // Validate API connectivity
            result.ApiConnectivityValid = await _apiOrchestrationService.ValidateApiConnectivityAsync();
            if (!result.ApiConnectivityValid)
            {
                result.Errors.Add("XQ360 API connectivity validation failed");
            }

            // Validate database schema (check for required tables and permissions)
            result.DatabaseSchemaValid = await ValidateDatabaseSchemaAsync();
            if (!result.DatabaseSchemaValid)
            {
                result.Errors.Add("Database schema validation failed - missing required tables or permissions");
            }

            // Validate migration data prerequisites (Customer, Site, Department, etc.)
            result.MigrationDataValid = await ValidateMigrationDataAsync();
            if (!result.MigrationDataValid)
            {
                result.Warnings.Add("Migration data validation warnings - some reference data may be missing");
            }

            result.IsValid = result.ApiConnectivityValid && result.DatabaseSchemaValid;

            _logger.LogInformation("Migration pattern validation completed: {IsValid} (API: {ApiValid}, DB: {DbValid}, Data: {DataValid})",
                result.IsValid, result.ApiConnectivityValid, result.DatabaseSchemaValid, result.MigrationDataValid);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Migration pattern validation failed with exception");
            result.IsValid = false;
            result.Errors.Add($"Validation failed: {ex.Message}");
            return result;
        }
    }

    private async Task<List<PersonCreateRequest>> GeneratePersonCreateRequestsAsync(
        MigrationPatternSeederOptions options,
        CancellationToken cancellationToken)
    {
        var requests = new List<PersonCreateRequest>();
        
        // Get default site/department/customer for generated persons
        // This would integrate with staging schema service to get valid FK references
        
        var driversCount = options.DriversCount ?? 0;
        for (int i = 0; i < driversCount; i++)
        {
            var request = new PersonCreateRequest
            {
                FirstName = $"Driver{i:D4}",
                LastName = "Generated",
                IsDriver = true,
                IsSupervisor = Random.Shared.Next(100) < options.SupervisorPercentage,
                WebsiteAccess = Random.Shared.Next(100) < options.WebsiteAccessPercentage,
                SendDenyMessage = true,
                VORActivateDeactivate = false,
                NormalDriverAccess = true,
                CanUnlockVehicle = true,
                // TODO: Get actual site/department/customer IDs from configuration or staging
                SiteId = Guid.NewGuid(), // Placeholder
                DepartmentId = Guid.NewGuid(), // Placeholder
                CustomerId = Guid.NewGuid() // Placeholder
            };
            
            requests.Add(request);
        }

        return requests;
    }

    private async Task<List<VehicleCreateRequest>> GenerateVehicleCreateRequestsAsync(
        MigrationPatternSeederOptions options,
        CancellationToken cancellationToken)
    {
        var requests = new List<VehicleCreateRequest>();
        
        var vehiclesCount = options.VehiclesCount ?? 0;
        for (int i = 0; i < vehiclesCount; i++)
        {
            var request = new VehicleCreateRequest
            {
                SerialNo = $"VEH{i:D6}",
                HireNo = $"H{i:D4}",
                IdleTimer = 15,
                OnHire = true,
                ImpactLockout = false,
                TimeoutEnabled = true,
                IsCanbus = Random.Shared.Next(2) == 1,
                ChecklistType = options.VehicleChecklistType,
                // TODO: Get actual model/site/department/customer IDs from configuration or staging
                ModelId = Guid.NewGuid(), // Placeholder
                SiteId = Guid.NewGuid(), // Placeholder
                DepartmentId = Guid.NewGuid(), // Placeholder
                CustomerId = Guid.NewGuid(), // Placeholder
                ModuleIoTDevice = $"MOD{i:D6}" // Placeholder
            };
            
            requests.Add(request);
        }

        return requests;
    }

    private async Task<List<CardAccessCreateRequest>> GenerateCardAccessCreateRequestsAsync(
        MigrationPatternSeederOptions options,
        CancellationToken cancellationToken)
    {
        var requests = new List<CardAccessCreateRequest>();
        
        var driversCount = options.DriversCount ?? 0;
        for (int i = 0; i < driversCount; i++)
        {
            var request = new CardAccessCreateRequest
            {
                DriverId = Guid.NewGuid(), // TODO: Get from actual created drivers
                WeigandNumber = $"{10000 + i}",
                AccessLevel = options.DefaultAccessLevel,
                SiteId = Guid.NewGuid(), // Placeholder
                DepartmentId = Guid.NewGuid(), // Placeholder
                VehicleIds = new List<Guid>(), // TODO: Get from actual created vehicles
                ModelIds = new List<Guid>() // TODO: Get from configuration
            };
            
            requests.Add(request);
        }

        return requests;
    }

    private async Task<bool> ValidateDatabaseSchemaAsync()
    {
        try
        {
            // Check if required tables exist for migration patterns
            // This would use staging schema service to validate
            return true; // Placeholder
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> ValidateMigrationDataAsync()
    {
        try
        {
            // Check if required reference data exists (Customer, Site, Department, Model, etc.)
            // This would use FK lookup cache service to validate
            return true; // Placeholder
        }
        catch
        {
            return false;
        }
    }

    private async Task NotifyProgress(string sessionId, string status, int progressPercentage, string message)
    {
        try
        {
            await _migrationHubContext.Clients.All.SendAsync("ProgressUpdate", new
            {
                SessionId = sessionId,
                Status = status,
                Progress = progressPercentage,
                Message = message,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send progress notification for session {SessionId}", sessionId);
        }
    }
}
