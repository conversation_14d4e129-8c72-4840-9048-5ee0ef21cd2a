{"Migration": {"Environments": {"US": {"Name": "United States Production", "Description": "US Production Environment", "DatabaseConnection": "Server=us-fleetxqdb.database.windows.net,1433;Database=FleetXQ.US.Production;User Id=us-fleetxqdb;Password=YOUR_US_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false", "ApiBaseUrl": "https://us-api.xq360.com/", "ApiUsername": "us-migration-user", "ApiPassword": "YOUR_US_API_PASSWORD"}, "UK": {"Name": "United Kingdom Production", "Description": "UK Production Environment", "DatabaseConnection": "Server=uk-fleetxqdb.database.windows.net,1433;Database=FleetXQ.UK.Production;User Id=uk-fleetxqdb;Password=YOUR_UK_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false", "ApiBaseUrl": "https://uk-api.xq360.com/", "ApiUsername": "uk-migration-user", "ApiPassword": "YOUR_UK_API_PASSWORD"}, "AU": {"Name": "Australia Production", "Description": "AU Production Environment", "DatabaseConnection": "Server=au-fleetxqdb.database.windows.net,1433;Database=FleetXQ.AU.Production;User Id=au-fleetxqdb;Password=YOUR_AU_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false", "ApiBaseUrl": "https://au-api.xq360.com/", "ApiUsername": "au-migration-user", "ApiPassword": "YOUR_AU_API_PASSWORD"}, "Pilot": {"Name": "Pilot Testing Environment", "Description": "Pilot testing and validation environment", "DatabaseConnection": "Server=fleetxqdb.database.windows.net,1433;Database=fleetXQ.Application-8735218d-3aeb-4563-bccb-8cdfcdf1188f;User Id=fleetxqdb;Password=*"wUvWCEKUxaHZK)6w*";TrustServerCertificate=true;", "ApiBaseUrl": "https://godev.collectiveintelligence.com.au/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/", "ApiUsername": "Admin", "ApiPassword": "Admin"}, "Development": {"Name": "Development Environment", "Description": "Development and testing environment", "DatabaseConnection": "Server=DELLLAPTOP-HUI**SQLEXPRESS01;Database=FleetXQProd20250701;Integrated Security=true;TrustServerCertificate=true;", "ApiBaseUrl": "https://localhost:53052/", "ApiUsername": "Admin", "ApiPassword": "Admin"}}, "BatchSize": 100, "MaxRetryAttempts": 3, "BackupEnabled": true, "ValidateBeforeMigration": true, "ContinueOnError": false}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "./Logs/developer-migration-{Date}.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}"}}]}, "Reporting": {"GenerateUserReports": true, "UserReportDirectory": "./Reports", "IncludeDetailedRecords": true, "MaxRecordsPerErrorType": 50}}