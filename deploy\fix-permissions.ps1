# XQ360 Data Migration - Permission Fix Script
# This script fixes permission issues on remote server deployment

param(
    [string]$DeployPath = "C:\inetpub\wwwroot\XQ360Migration",
    [string]$AppPoolName = "XQ360MigrationPool",
    [switch]$FixAll = $false
)

Write-Host "XQ360 Data Migration - Permission Fix Script" -ForegroundColor Green
Write-Host "This script will fix permission issues on the remote server" -ForegroundColor Yellow
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}
Write-Host "✅ Running as Administrator" -ForegroundColor Green

# Step 1: Fix CSV_Input Directory Permissions
Write-Host "`nStep 1: Fixing CSV_Input Directory Permissions..." -ForegroundColor Cyan

$csvInputPath = "$DeployPath\CSV_Input"
if (Test-Path $csvInputPath) {
    Write-Host "Found CSV_Input directory at: $csvInputPath" -ForegroundColor Green
    
    # Grant full permissions to IIS_IUSRS
    Write-Host "Setting IIS_IUSRS permissions..." -ForegroundColor Yellow
    try {
        icacls $csvInputPath /grant "IIS_IUSRS:(OI)(CI)(F)" /T
        Write-Host "✅ IIS_IUSRS permissions set successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set IIS_IUSRS permissions: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Grant full permissions to Application Pool Identity
    Write-Host "Setting Application Pool permissions..." -ForegroundColor Yellow
    try {
        icacls $csvInputPath /grant "IIS AppPool\$AppPoolName:(OI)(CI)(F)" /T
        Write-Host "✅ Application Pool permissions set successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set Application Pool permissions: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Grant permissions to NETWORK SERVICE (alternative)
    Write-Host "Setting NETWORK SERVICE permissions..." -ForegroundColor Yellow
    try {
        icacls $csvInputPath /grant "NETWORK SERVICE:(OI)(CI)(F)" /T
        Write-Host "✅ NETWORK SERVICE permissions set successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set NETWORK SERVICE permissions: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Grant permissions to Everyone (temporary fix for testing)
    Write-Host "Setting Everyone permissions (temporary fix)..." -ForegroundColor Yellow
    try {
        icacls $csvInputPath /grant "Everyone:(OI)(CI)(F)" /T
        Write-Host "✅ Everyone permissions set successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set Everyone permissions: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ CSV_Input directory not found at: $csvInputPath" -ForegroundColor Red
    Write-Host "Creating CSV_Input directory..." -ForegroundColor Yellow
    try {
        New-Item -ItemType Directory -Path $csvInputPath -Force
        Write-Host "✅ CSV_Input directory created successfully!" -ForegroundColor Green
        
        # Set permissions on newly created directory
        icacls $csvInputPath /grant "IIS_IUSRS:(OI)(CI)(F)" /T
        icacls $csvInputPath /grant "IIS AppPool\$AppPoolName:(OI)(CI)(F)" /T
        icacls $csvInputPath /grant "NETWORK SERVICE:(OI)(CI)(F)" /T
        icacls $csvInputPath /grant "Everyone:(OI)(CI)(F)" /T
        Write-Host "✅ Permissions set on newly created CSV_Input directory!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to create CSV_Input directory: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 2: Fix Logs Directory Permissions
Write-Host "`nStep 2: Fixing Logs Directory Permissions..." -ForegroundColor Cyan

$logsPath = "$DeployPath\Logs"
if (Test-Path $logsPath) {
    Write-Host "Found Logs directory at: $logsPath" -ForegroundColor Green
    
    # Grant full permissions to all IIS identities
    try {
        icacls $logsPath /grant "IIS_IUSRS:(OI)(CI)(F)" /T
        icacls $logsPath /grant "IIS AppPool\$AppPoolName:(OI)(CI)(F)" /T
        icacls $logsPath /grant "NETWORK SERVICE:(OI)(CI)(F)" /T
        icacls $logsPath /grant "Everyone:(OI)(CI)(F)" /T
        Write-Host "✅ Logs directory permissions set successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set Logs directory permissions: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Creating Logs directory..." -ForegroundColor Yellow
    try {
        New-Item -ItemType Directory -Path $logsPath -Force
        icacls $logsPath /grant "IIS_IUSRS:(OI)(CI)(F)" /T
        icacls $logsPath /grant "IIS AppPool\$AppPoolName:(OI)(CI)(F)" /T
        icacls $logsPath /grant "NETWORK SERVICE:(OI)(CI)(F)" /T
        icacls $logsPath /grant "Everyone:(OI)(CI)(F)" /T
        Write-Host "✅ Logs directory created and permissions set!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to create Logs directory: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 3: Fix Main Application Directory Permissions
Write-Host "`nStep 3: Fixing Main Application Directory Permissions..." -ForegroundColor Cyan

if (Test-Path $DeployPath) {
    Write-Host "Setting permissions on main application directory..." -ForegroundColor Yellow
    try {
        # Grant read and execute permissions to the entire application directory
        icacls $DeployPath /grant "IIS_IUSRS:(OI)(CI)(RX)" /T
        icacls $DeployPath /grant "IIS AppPool\$AppPoolName:(OI)(CI)(RX)" /T
        icacls $DeployPath /grant "NETWORK SERVICE:(OI)(CI)(RX)" /T
        
        # Grant modify permissions for specific directories that need write access
        icacls "$DeployPath\CSV_Input" /grant "IIS_IUSRS:(OI)(CI)(M)" /T
        icacls "$DeployPath\CSV_Input" /grant "IIS AppPool\$AppPoolName:(OI)(CI)(M)" /T
        icacls "$DeployPath\CSV_Input" /grant "NETWORK SERVICE:(OI)(CI)(M)" /T
        
        icacls "$DeployPath\Logs" /grant "IIS_IUSRS:(OI)(CI)(M)" /T
        icacls "$DeployPath\Logs" /grant "IIS AppPool\$AppPoolName:(OI)(CI)(M)" /T
        icacls "$DeployPath\Logs" /grant "NETWORK SERVICE:(OI)(CI)(M)" /T
        
        Write-Host "✅ Main application directory permissions set successfully!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set main application directory permissions: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Main application directory not found at: $DeployPath" -ForegroundColor Red
}

# Step 4: Restart Application Pool
Write-Host "`nStep 4: Restarting Application Pool..." -ForegroundColor Cyan

try {
    Import-Module WebAdministration -ErrorAction Stop
    
    # Stop the application pool
    Write-Host "Stopping application pool..." -ForegroundColor Yellow
    Stop-WebAppPool -Name $AppPoolName -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
    
    # Start the application pool
    Write-Host "Starting application pool..." -ForegroundColor Yellow
    Start-WebAppPool -Name $AppPoolName
    
    Write-Host "✅ Application pool restarted successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to restart application pool: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please restart the application pool manually in IIS Manager" -ForegroundColor Yellow
}

# Step 5: Test File Access
Write-Host "`nStep 5: Testing File Access..." -ForegroundColor Cyan

$testFile = "$csvInputPath\test.txt"
try {
    # Try to create a test file
    "Test file created by permission fix script" | Out-File -FilePath $testFile -Encoding UTF8
    Write-Host "✅ Successfully created test file: $testFile" -ForegroundColor Green
    
    # Try to read the test file
    $content = Get-Content -Path $testFile -ErrorAction Stop
    Write-Host "✅ Successfully read test file" -ForegroundColor Green
    
    # Clean up test file
    Remove-Item -Path $testFile -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Test file cleaned up" -ForegroundColor Green
    
} catch {
    Write-Host "❌ File access test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Permissions may still need manual adjustment" -ForegroundColor Yellow
}

# Step 6: Alternative Permission Methods (if FixAll is specified)
if ($FixAll) {
    Write-Host "`nStep 6: Applying Alternative Permission Methods..." -ForegroundColor Cyan
    
    # Method 1: Grant permissions to the entire wwwroot directory
    Write-Host "Setting permissions on parent wwwroot directory..." -ForegroundColor Yellow
    try {
        $wwwrootPath = Split-Path -Parent $DeployPath
        icacls $wwwrootPath /grant "IIS_IUSRS:(OI)(CI)(RX)" /T
        icacls $wwwrootPath /grant "IIS AppPool\$AppPoolName:(OI)(CI)(RX)" /T
        Write-Host "✅ Parent directory permissions set!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set parent directory permissions: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Method 2: Create a test file in CSV_Input to ensure directory is writable
    Write-Host "Creating test files in CSV_Input..." -ForegroundColor Yellow
    try {
        $testFiles = @(
            "test_permission.txt",
            "CARD_IMPORT.csv",
            "DRIVER_BLACKLIST_IMPORT.csv",
            "PERSON_IMPORT_TEMPLATE.csv",
            "PREOP_CHECKLIST_IMPORT.csv",
            "SPARE_MODEL_IMPORT_TEMPLATE.csv",
            "SUPERVISOR_ACCESS_IMPORT.csv",
            "VEHICLE_IMPORT.csv",
            "WEBSITE_USER_IMPORT.csv"
        )
        
        foreach ($file in $testFiles) {
            $testPath = Join-Path $csvInputPath $file
            if (-not (Test-Path $testPath)) {
                "Test file: $file" | Out-File -FilePath $testPath -Encoding UTF8
                Write-Host "✅ Created test file: $file" -ForegroundColor Green
            }
        }
    } catch {
        Write-Host "❌ Failed to create test files: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 7: Pre-configure .NET to prevent first-time setup issues
Write-Host "`nStep 7: Pre-configuring .NET..." -ForegroundColor Cyan
try {
    # Run dotnet --info to trigger first-time setup with proper permissions
    Write-Host "Running dotnet --info to pre-configure .NET..." -ForegroundColor Yellow
    $dotnetInfo = dotnet --info 2>&1
    Write-Host "✅ .NET pre-configuration completed" -ForegroundColor Green
    
    # Also run a simple test to ensure test runner is configured
    Write-Host "Pre-configuring test runner..." -ForegroundColor Yellow
    $testConfig = dotnet test --help 2>&1
    Write-Host "✅ Test runner pre-configuration completed" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Failed to pre-configure .NET: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "This may cause issues when running tests from the web interface" -ForegroundColor Yellow
}

# Summary
Write-Host "`nPermission Fix Summary:" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green
Write-Host "✅ CSV_Input directory permissions fixed" -ForegroundColor White
Write-Host "✅ Logs directory permissions fixed" -ForegroundColor White
Write-Host "✅ Main application directory permissions fixed" -ForegroundColor White
Write-Host "✅ Application pool restarted" -ForegroundColor White
Write-Host "✅ File access tested" -ForegroundColor White

if ($FixAll) {
    Write-Host "✅ Alternative permission methods applied" -ForegroundColor White
}

Write-Host "" -ForegroundColor White
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "   1. Test the web application again" -ForegroundColor White
Write-Host "   2. Try uploading a CSV file" -ForegroundColor White
Write-Host "   3. Check if the permission error is resolved" -ForegroundColor White
Write-Host "   4. If issues persist, check IIS logs for more details" -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host "Permission fix completed!" -ForegroundColor Green 