using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Interfaces;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Implementations
{
    public class SupervisorAccessMigration
    {
        private readonly ILogger<SupervisorAccessMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly string _connectionString;
        private readonly IPermissionService _permissionService;
        private readonly MigrationReportingService _reportingService;

        public SupervisorAccessMigration(
            ILogger<SupervisorAccessMigration> logger,
            IOptions<MigrationConfiguration> config,
            IPermissionService permissionService,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _connectionString = _config.DatabaseConnection;
            _permissionService = permissionService;
            _reportingService = reportingService;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Supervisor Authorization migration - updating Person table supervisor settings");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Execute SQL migration
                var migrationResult = await ExecuteSupervisorAccessSqlAsync(data);

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;

                _logger.LogInformation($"Supervisor Authorization migration completed: {result.RecordsInserted} updated, {result.RecordsSkipped} skipped, Duration: {result.Duration}");

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Supervisor Access Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Supervisor Authorization migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private async Task<List<SupervisorAccessImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Supervisor Authorization CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096, useAsync: true);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<SupervisorAccessImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return records;
        }

        private async Task<MigrationResult> ExecuteSupervisorAccessSqlAsync(List<SupervisorAccessImportModel> data)
        {
            _logger.LogInformation("Executing Supervisor Authorization SQL migration...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                // Validate that SupervisorPermissionId exists in dbo.Permission
                var supervisorPermissionId = await _permissionService.GetSupervisorPermissionIdAsync();
                var permissionExists = await ValidateSupervisorPermissionExistsAsync(supervisorPermissionId, connection, transaction);
                if (!permissionExists)
                {
                    throw new InvalidOperationException($"Supervisor Permission ID '{supervisorPermissionId}' does not exist in dbo.Permission table");
                }

                var migrationResult = new MigrationResult();
                var totalInserted = 0;
                var warnings = new List<string>();

                foreach (var record in data)
                {
                    // Parse supervisor authorization (format: "CanUnlockVehicle&NormalDriverAccess&VORActivateDeactivate")
                    var authParts = record.SupervisorAuthorization.Split('&');
                    if (authParts.Length != 3)
                    {
                        _reportingService.AddDetailedWarning(migrationResult, data.IndexOf(record) + 1, WarningTypes.VALIDATION_WARNING,
                            $"Invalid supervisor authorization format: {record.SupervisorAuthorization}",
                            "SupervisorAuthorization", record.SupervisorAuthorization,
                            "Use format: 'CanUnlockVehicle&NormalDriverAccess&VORActivateDeactivate'",
                            new Dictionary<string, string> 
                            {
                                { "FirstName", record.FirstName },
                                { "LastName", record.LastName },
                                { "Weigand", record.Weigand },
                                { "ExpectedFormat", "1&2&4 or 0&0&0" }
                            });
                        warnings.Add($"Invalid supervisor authorization format for {record.FirstName} {record.LastName}: {record.SupervisorAuthorization}");
                        continue;
                    }

                    // Parse authorization values
                    if (!int.TryParse(authParts[0], out var canUnlockValue) ||
                        !int.TryParse(authParts[1], out var normalDriverValue) ||
                        !int.TryParse(authParts[2], out var vorValue))
                    {
                        warnings.Add($"Invalid supervisor authorization values for {record.FirstName} {record.LastName}: {record.SupervisorAuthorization}");
                        continue;
                    }

                    var canUnlockVehicle = canUnlockValue == 1;
                    var normalDriverAccess = normalDriverValue == 2;
                    var vorActivateDeactivate = vorValue == 4;

                    // Get supervisor using full context from CSV
                    var supervisorInfo = await GetSupervisorInfoAsync(record, connection, transaction);
                    if (supervisorInfo == null)
                    {
                        warnings.Add($"Supervisor not found: {record.FirstName} {record.LastName} in {record.PersonDealer}/{record.PersonCustomer}/{record.PersonSite}/{record.PersonDepartment} with weigand {record.Weigand}");
                        continue;
                    }

                    // Validate that base vehicle access exists for this card
                    var baseAccessExists = await ValidateBaseVehicleAccessExistsAsync(supervisorInfo.CardId, connection, transaction);
                    if (!baseAccessExists)
                    {
                        warnings.Add($"Base vehicle access not found for supervisor: {record.FirstName} {record.LastName} (CardId: {supervisorInfo.CardId}). Card must have existing vehicle access before supervisor access can be granted.");
                        continue;
                    }

                    // Validate vehicle exists if GMTP ID is provided
                    Guid? vehicleId = null;
                    if (!string.IsNullOrEmpty(record.GMTPID))
                    {
                        vehicleId = await GetVehicleIdByGMTPAsync(record.GMTPID, connection, transaction);
                        if (vehicleId == null)
                        {
                            warnings.Add($"Vehicle not found for GMTP ID: {record.GMTPID}");
                            continue;
                        }
                    }

                    // Update Person table with supervisor authorization settings
                    var updated = await UpdatePersonSupervisorAuthorizationAsync(
                        supervisorInfo.PersonId, 
                        canUnlockVehicle, 
                        normalDriverAccess, 
                        vorActivateDeactivate, 
                        connection, 
                        transaction);

                    if (updated)
                    {
                        totalInserted++;
                        _logger.LogInformation($"✅ Updated supervisor authorization for {record.FirstName} {record.LastName}: Supervisor=TRUE, CanUnlock={canUnlockVehicle}, NormalDriver={normalDriverAccess}, VOR={vorActivateDeactivate}");

                        // Create vehicle access records for the specific vehicle if GMTP ID is provided
                        if (vehicleId.HasValue)
                        {
                            var vehicleAccessCount = await CreateSupervisorVehicleAccessAsync(
                                supervisorInfo.CardId, 
                                vehicleId.Value, 
                                supervisorInfo.SiteId, 
                                supervisorInfo.DepartmentId, 
                                connection, 
                                transaction);
                            
                            _logger.LogInformation($"✅ Created {vehicleAccessCount} supervisor vehicle access records for {record.FirstName} {record.LastName} on vehicle {record.GMTPID}");
                        }
                    }
                }

                await transaction.CommitAsync();

                _logger.LogInformation($"Successfully updated {totalInserted} supervisor authorization records");

                // Clean up duplicates from all vehicle access tables
                await CleanupDuplicateVehicleAccessRecordsAsync();

                var result = new MigrationResult
                {
                    Success = true,
                    RecordsInserted = totalInserted,
                    Warnings = warnings
                };

                return result;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Supervisor Authorization SQL migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<SupervisorInfo> GetSupervisorInfoAsync(SupervisorAccessImportModel record, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                SELECT 
                    p.Id as PersonId,
                    d.Id as DriverId,
                    c.Id as CardId,
                    p.SiteId,
                    p.DepartmentId
                FROM [dbo].[Person] p
                JOIN [dbo].[Driver] d ON p.DriverId = d.Id
                JOIN [dbo].[Card] c ON d.CardDetailsId = c.Id
                JOIN [dbo].[Site] s ON p.SiteId = s.Id
                JOIN [dbo].[Department] dept ON p.DepartmentId = dept.Id
                JOIN [dbo].[Customer] cust ON s.CustomerId = cust.Id
                JOIN [dbo].[Dealer] dealer ON cust.DealerId = dealer.Id
                WHERE p.FirstName = @FirstName 
                AND p.LastName = @LastName
                AND c.Weigand = @Weigand
                AND dealer.Name = @DealerName
                AND cust.CompanyName = @CustomerName
                AND s.Name = @SiteName
                AND dept.Name = @DepartmentName";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@FirstName", record.FirstName);
            cmd.Parameters.AddWithValue("@LastName", record.LastName);
            cmd.Parameters.AddWithValue("@Weigand", record.Weigand);
            cmd.Parameters.AddWithValue("@DealerName", record.PersonDealer);
            cmd.Parameters.AddWithValue("@CustomerName", record.PersonCustomer);
            cmd.Parameters.AddWithValue("@SiteName", record.PersonSite);
            cmd.Parameters.AddWithValue("@DepartmentName", record.PersonDepartment);

            using var reader = await cmd.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new SupervisorInfo
                {
                    PersonId = reader.GetGuid(0),
                    DriverId = reader.GetGuid(1),
                    CardId = reader.GetGuid(2),
                    SiteId = reader.GetGuid(3),
                    DepartmentId = reader.GetGuid(4)
                };
            }

            return null;
        }

        private async Task<bool> ValidateBaseVehicleAccessExistsAsync(Guid cardId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                SELECT COUNT(*)
                FROM (
                    SELECT CardId FROM [dbo].[SiteVehicleNormalCardAccess] WHERE CardId = @CardId
                    UNION
                    SELECT CardId FROM [dbo].[DepartmentVehicleNormalCardAccess] WHERE CardId = @CardId
                    UNION
                    SELECT CardId FROM [dbo].[ModelVehicleNormalCardAccess] WHERE CardId = @CardId
                    UNION
                    SELECT CardId FROM [dbo].[PerVehicleNormalCardAccess] WHERE CardId = @CardId
                ) AS VehicleAccess";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CardId", cardId);

            var count = (int)await cmd.ExecuteScalarAsync();
            return count > 0;
        }

        private async Task<bool> ValidateSupervisorPermissionExistsAsync(Guid supervisorPermissionId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = "SELECT COUNT(*) FROM [dbo].[Permission] WHERE Id = @PermissionId";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@PermissionId", supervisorPermissionId);

            var count = (int)await cmd.ExecuteScalarAsync();
            return count > 0;
        }

        private async Task<Guid?> GetVehicleIdByGMTPAsync(string gmtpId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                SELECT v.Id 
                FROM [dbo].[Vehicle] v
                INNER JOIN [dbo].[Module] m ON v.ModuleId1 = m.Id
                WHERE m.IoTDevice = @GMTPID";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@GMTPID", gmtpId);

            var result = await cmd.ExecuteScalarAsync();
            return result as Guid?;
        }

        private async Task<bool> UpdatePersonSupervisorAuthorizationAsync(Guid personId, bool canUnlockVehicle, bool normalDriverAccess, bool vorActivateDeactivate, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                UPDATE [dbo].[Person] 
                SET 
                    Supervisor = 1,
                    CanUnlockVehicle = @CanUnlockVehicle,
                    NormalDriverAccess = @NormalDriverAccess,
                    VORActivateDeactivate = @VORActivateDeactivate
                WHERE Id = @PersonId";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@PersonId", personId);
            cmd.Parameters.AddWithValue("@CanUnlockVehicle", canUnlockVehicle);
            cmd.Parameters.AddWithValue("@NormalDriverAccess", normalDriverAccess);
            cmd.Parameters.AddWithValue("@VORActivateDeactivate", vorActivateDeactivate);

            var rowsAffected = await cmd.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        private async Task<int> CreateSupervisorVehicleAccessAsync(Guid cardId, Guid vehicleId, Guid siteId, Guid departmentId, SqlConnection connection, SqlTransaction transaction)
        {
            var totalInserted = 0;
            var supervisorPermissionId = await _permissionService.GetSupervisorPermissionIdAsync();

            // Get the ModelId for this vehicle
            var modelId = await GetVehicleModelIdAsync(vehicleId, connection, transaction);
            if (!modelId.HasValue)
            {
                _logger.LogWarning($"Cannot create model access for vehicle {vehicleId} - ModelId not found");
                return 0;
            }

            // 1. Create SiteVehicleNormalCardAccess
            totalInserted += await CreateSupervisorSiteAccessAsync(cardId, siteId, supervisorPermissionId, connection, transaction);

            // 2. Create DepartmentVehicleNormalCardAccess  
            totalInserted += await CreateSupervisorDepartmentAccessAsync(cardId, departmentId, supervisorPermissionId, connection, transaction);

            // 3. Create ModelVehicleNormalCardAccess for the specific vehicle's model
            totalInserted += await CreateSupervisorModelAccessAsync(cardId, modelId.Value, departmentId, supervisorPermissionId, connection, transaction);

            // 4. Create PerVehicleNormalCardAccess for the specific vehicle
            totalInserted += await CreateSupervisorPerVehicleAccessAsync(cardId, vehicleId, supervisorPermissionId, connection, transaction);

            return totalInserted;
        }

        private async Task<Guid?> GetVehicleModelIdAsync(Guid vehicleId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = "SELECT ModelId FROM [dbo].[Vehicle] WHERE Id = @VehicleId";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@VehicleId", vehicleId);

            var result = await cmd.ExecuteScalarAsync();
            return result as Guid?;
        }

        private async Task<int> CreateSupervisorSiteAccessAsync(Guid cardId, Guid siteId, Guid permissionId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                INSERT INTO [dbo].[SiteVehicleNormalCardAccess] (Id, SiteId, PermissionId, CardId)
                SELECT NEWID(), @SiteId, @PermissionId, @CardId
                WHERE NOT EXISTS (
                    SELECT 1 FROM [dbo].[SiteVehicleNormalCardAccess] 
                    WHERE SiteId = @SiteId AND CardId = @CardId AND PermissionId = @PermissionId
                )";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@SiteId", siteId);
            cmd.Parameters.AddWithValue("@PermissionId", permissionId);
            cmd.Parameters.AddWithValue("@CardId", cardId);

            return await cmd.ExecuteNonQueryAsync();
        }

        private async Task<int> CreateSupervisorDepartmentAccessAsync(Guid cardId, Guid departmentId, Guid permissionId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                INSERT INTO [dbo].[DepartmentVehicleNormalCardAccess] (Id, DepartmentId, PermissionId, CardId)
                SELECT NEWID(), @DepartmentId, @PermissionId, @CardId
                WHERE NOT EXISTS (
                    SELECT 1 FROM [dbo].[DepartmentVehicleNormalCardAccess] 
                    WHERE DepartmentId = @DepartmentId AND CardId = @CardId AND PermissionId = @PermissionId
                )";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@DepartmentId", departmentId);
            cmd.Parameters.AddWithValue("@PermissionId", permissionId);
            cmd.Parameters.AddWithValue("@CardId", cardId);

            return await cmd.ExecuteNonQueryAsync();
        }

        private async Task<int> CreateSupervisorModelAccessAsync(Guid cardId, Guid modelId, Guid departmentId, Guid permissionId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                INSERT INTO [dbo].[ModelVehicleNormalCardAccess] (Id, ModelId, PermissionId, CardId, DepartmentId)
                SELECT NEWID(), @ModelId, @PermissionId, @CardId, @DepartmentId
                WHERE NOT EXISTS (
                    SELECT 1 FROM [dbo].[ModelVehicleNormalCardAccess] 
                    WHERE ModelId = @ModelId AND CardId = @CardId AND DepartmentId = @DepartmentId AND PermissionId = @PermissionId
                )";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@ModelId", modelId);
            cmd.Parameters.AddWithValue("@PermissionId", permissionId);
            cmd.Parameters.AddWithValue("@CardId", cardId);
            cmd.Parameters.AddWithValue("@DepartmentId", departmentId);

            return await cmd.ExecuteNonQueryAsync();
        }

        private async Task<int> CreateSupervisorPerVehicleAccessAsync(Guid cardId, Guid vehicleId, Guid permissionId, SqlConnection connection, SqlTransaction transaction)
        {
            var sql = @"
                INSERT INTO [dbo].[PerVehicleNormalCardAccess] (Id, VehicleId, PermissionId, CardId)
                SELECT NEWID(), @VehicleId, @PermissionId, @CardId
                WHERE NOT EXISTS (
                    SELECT 1 FROM [dbo].[PerVehicleNormalCardAccess] 
                    WHERE VehicleId = @VehicleId AND CardId = @CardId AND PermissionId = @PermissionId
                )";

            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@VehicleId", vehicleId);
            cmd.Parameters.AddWithValue("@PermissionId", permissionId);
            cmd.Parameters.AddWithValue("@CardId", cardId);

            return await cmd.ExecuteNonQueryAsync();
        }

        private async Task CleanupDuplicateVehicleAccessRecordsAsync()
        {
            _logger.LogInformation("Cleaning up duplicate vehicle access records...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                // Clean up SiteVehicleNormalCardAccess duplicates
                var cleanupSiteSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, SiteId, CardId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [SiteVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [SiteVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(cleanupSiteSql, connection, transaction))
                {
                    var deletedSite = await cmd.ExecuteNonQueryAsync();
                    if (deletedSite > 0)
                        _logger.LogInformation($"Removed {deletedSite} duplicate SiteVehicleNormalCardAccess records");
                }

                // Clean up DepartmentVehicleNormalCardAccess duplicates
                var cleanupDeptSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY CardId, DepartmentId, PermissionId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [DepartmentVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [DepartmentVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(cleanupDeptSql, connection, transaction))
                {
                    var deletedDept = await cmd.ExecuteNonQueryAsync();
                    if (deletedDept > 0)
                        _logger.LogInformation($"Removed {deletedDept} duplicate DepartmentVehicleNormalCardAccess records");
                }

                // Clean up ModelVehicleNormalCardAccess duplicates
                var cleanupModelSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, CardId, DepartmentId, ModelId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [ModelVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [ModelVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(cleanupModelSql, connection, transaction))
                {
                    var deletedModel = await cmd.ExecuteNonQueryAsync();
                    if (deletedModel > 0)
                        _logger.LogInformation($"Removed {deletedModel} duplicate ModelVehicleNormalCardAccess records");
                }

                // Clean up PerVehicleNormalCardAccess duplicates
                var cleanupVehicleSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, VehicleId, CardId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [PerVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [PerVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(cleanupVehicleSql, connection, transaction))
                {
                    var deletedVehicle = await cmd.ExecuteNonQueryAsync();
                    if (deletedVehicle > 0)
                        _logger.LogInformation($"Removed {deletedVehicle} duplicate PerVehicleNormalCardAccess records");
                }

                await transaction.CommitAsync();
                _logger.LogInformation("Duplicate vehicle access cleanup completed successfully");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to clean up duplicate vehicle access records");
                throw;
            }
        }



        private class SupervisorInfo
        {
            public Guid PersonId { get; set; }
            public Guid DriverId { get; set; }
            public Guid CardId { get; set; }
            public Guid SiteId { get; set; }
            public Guid DepartmentId { get; set; }
        }
    }
} 