# Fix script for CSV_Input directory and permissions issues
# Run this on your remote server to fix the DirectoryNotFoundException errors

Write-Host "=== Fixing CSV_Input Directory and Permissions ===" -ForegroundColor Cyan
Write-Host ""

# Define paths
$appRoot = "C:\inetpub\wwwroot\XQ360Migration"
$csvInputPath = "$appRoot\CSV_Input"
$csvTemplatePath = "$appRoot\CSV_Template"
$logsPath = "$appRoot\Logs"

Write-Host "1. Creating CSV_Input directory..." -ForegroundColor Yellow
if (-not (Test-Path $csvInputPath)) {
    New-Item -ItemType Directory -Path $csvInputPath -Force
    Write-Host "   ✓ Created CSV_Input directory" -ForegroundColor Green
} else {
    Write-Host "   ✓ CSV_Input directory already exists" -ForegroundColor Green
}

Write-Host ""

Write-Host "2. Copying CSV template files to CSV_Input..." -ForegroundColor Yellow
if (Test-Path $csvTemplatePath) {
    $templateFiles = Get-ChildItem $csvTemplatePath -Filter "*.csv"
    if ($templateFiles) {
        Copy-Item "$csvTemplatePath\*.csv" $csvInputPath -Force
        Write-Host "   ✓ Copied $($templateFiles.Count) CSV files to CSV_Input" -ForegroundColor Green
        foreach ($file in $templateFiles) {
            Write-Host "      - $($file.Name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "   ⚠️ No CSV template files found in CSV_Template" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ❌ CSV_Template directory not found!" -ForegroundColor Red
    Write-Host "   💡 You may need to copy CSV files manually" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "3. Setting permissions for CSV_Input directory..." -ForegroundColor Yellow
try {
    # Set permissions for IIS_IUSRS
    icacls $csvInputPath /grant "IIS_IUSRS:(OI)(CI)F" /T
    Write-Host "   ✓ Set FullControl permissions for IIS_IUSRS" -ForegroundColor Green
    
    # Also set for NETWORK SERVICE (alternative IIS identity)
    icacls $csvInputPath /grant "NETWORK SERVICE:(OI)(CI)F" /T
    Write-Host "   ✓ Set FullControl permissions for NETWORK SERVICE" -ForegroundColor Green
    
    # Set for the application pool identity (if different)
    $appPoolName = "XQ360Migration"
    icacls $csvInputPath /grant "IIS AppPool\$appPoolName:(OI)(CI)F" /T
    Write-Host "   ✓ Set FullControl permissions for IIS AppPool\$appPoolName" -ForegroundColor Green
    
} catch {
    Write-Host "   ❌ Error setting permissions: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

Write-Host "4. Ensuring Logs directory exists and has proper permissions..." -ForegroundColor Yellow
if (-not (Test-Path $logsPath)) {
    New-Item -ItemType Directory -Path $logsPath -Force
    Write-Host "   ✓ Created Logs directory" -ForegroundColor Green
} else {
    Write-Host "   ✓ Logs directory already exists" -ForegroundColor Green
}

# Set permissions for Logs directory
try {
    icacls $logsPath /grant "IIS_IUSRS:(OI)(CI)F" /T
    icacls $logsPath /grant "NETWORK SERVICE:(OI)(CI)F" /T
    icacls $logsPath /grant "IIS AppPool\XQ360Migration:(OI)(CI)F" /T
    Write-Host "   ✓ Set permissions for Logs directory" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Error setting Logs permissions: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

Write-Host "5. Verifying file structure..." -ForegroundColor Yellow
$csvFiles = Get-ChildItem $csvInputPath -Filter "*.csv" -ErrorAction SilentlyContinue
if ($csvFiles) {
    Write-Host "   ✓ CSV_Input directory now contains $($csvFiles.Count) files:" -ForegroundColor Green
    foreach ($file in $csvFiles) {
        Write-Host "      - $($file.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "   ⚠️ CSV_Input directory is empty - you may need to copy CSV files manually" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "6. Testing write access..." -ForegroundColor Yellow
try {
    $testFile = "$csvInputPath\test-write.tmp"
    "Test write access $(Get-Date)" | Out-File -FilePath $testFile -Encoding UTF8
    if (Test-Path $testFile) {
        Remove-Item $testFile -Force
        Write-Host "   ✓ Write access test successful" -ForegroundColor Green
    }
} catch {
    Write-Host "   ❌ Write access test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

Write-Host "=== FIX COMPLETE ===" -ForegroundColor Green
Write-Host "The CSV_Input directory has been created and configured." -ForegroundColor Cyan
Write-Host "You should now be able to run migrations without DirectoryNotFoundException errors." -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart the IIS application pool if needed" -ForegroundColor Gray
Write-Host "2. Try running a migration to test the fix" -ForegroundColor Gray
Write-Host "3. Run the diagnostic script to verify everything is working" -ForegroundColor Gray 