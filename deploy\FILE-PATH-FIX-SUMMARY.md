# File Path Fix Summary

## Problem
The application was failing to read and write CSV files because of a folder name mismatch:

- **Deployment folder:** `C:\inetpub\wwwroot\XQ360Migration`
- **Application folder name:** `XQ360.DataMigration`
- **Issue:** <PERSON> was looking for CSV files in paths that included `XQ360.DataMigration`, but the deployed application was running from `XQ360Migration`

## Root Cause
The file path logic in the code was hardcoded to look for CSV files in paths that assumed the application was running from a directory named `XQ360.DataMigration`, but when deployed to IIS, the application runs from `XQ360Migration`.

## Solution
Updated the file path logic to detect the execution environment and use the correct paths:

### 1. MigrationOrchestrator.cs - GetCsvFilePath Method
```csharp
private string GetCsvFilePath(string fileName)
{
    var currentDirectory = Directory.GetCurrentDirectory();
    
    // Check if we're running from IIS deployment (XQ360Migration)
    if (currentDirectory.EndsWith("XQ360Migration"))
    {
        // When deployed to IIS, CSV_Input is in the same directory as the application
        return Path.Combine(currentDirectory, "CSV_Input", fileName);
    }
    
    // Check if we're running from the web application directory during development
    if (currentDirectory.EndsWith("XQ360.DataMigration.Web"))
    {
        // Navigate to the parent directory and then to the main migration project
        return Path.Combine(currentDirectory, "..", "XQ360.DataMigration", "CSV_Input", fileName);
    }
    
    // Otherwise, assume we're running from the main migration project directory
    return Path.Combine(currentDirectory, "CSV_Input", fileName);
}
```

### 2. IMigrationService.cs - Added GetCsvInputPath Helper Method
```csharp
private string GetCsvInputPath()
{
    var currentDirectory = Directory.GetCurrentDirectory();
    
    // Check if we're running from IIS deployment (XQ360Migration)
    if (currentDirectory.EndsWith("XQ360Migration"))
    {
        // When deployed to IIS, CSV_Input is in the same directory as the application
        return Path.Combine(currentDirectory, "CSV_Input");
    }
    else if (currentDirectory.EndsWith("XQ360.DataMigration.Web"))
    {
        // During development, navigate to the main migration project
        return Path.Combine(currentDirectory, "..", "XQ360.DataMigration", "CSV_Input");
    }
    else
    {
        // Fallback for other scenarios
        return Path.Combine(currentDirectory, "CSV_Input");
    }
}
```

### 3. Updated All CSV Path References
Updated the following methods to use the new helper method:
- `SaveUploadedFilesAsync`
- `BuildVehicleSyncListAsync`
- `CollectDistinctCustomersFromCsvFilesAsync`

### 4. Fixed CSV_Template Path Issues
Updated the following files to handle CSV_Template paths correctly:

#### TemplateController.cs - GetTemplatePath Method
```csharp
private string GetTemplatePath(string fileName)
{
    var currentDirectory = Directory.GetCurrentDirectory();
    
    // Check if we're running from IIS deployment (XQ360Migration)
    if (currentDirectory.EndsWith("XQ360Migration"))
    {
        // When deployed to IIS, CSV_Template is in the same directory as the application
        return Path.Combine(currentDirectory, "CSV_Template", fileName);
    }
    else if (currentDirectory.EndsWith("XQ360.DataMigration.Web"))
    {
        // During development, navigate to the main migration project
        return Path.Combine(currentDirectory, "..", "XQ360.DataMigration", "CSV_Template", fileName);
    }
    else
    {
        // Fallback for other scenarios
        return Path.Combine(currentDirectory, "CSV_Template", fileName);
    }
}
```

#### CsvFormatValidator.cs - GetRequiredHeadersAsync Method
Updated the template path resolution to handle both development and deployment environments correctly.

## Deployment Instructions

### Option 1: Use the Deployment Script
```powershell
.\deploy\fix-file-paths-and-deploy.ps1
```

### Option 2: Manual Deployment
1. **Stop IIS Application Pool:**
   ```powershell
   Stop-WebAppPool -Name "XQ360Migration"
   ```

2. **Deploy the Application:**
   ```powershell
   Copy-Item "C:\FleetXQ\XQ360.DataMigration\publish\*" "C:\inetpub\wwwroot\XQ360Migration" -Recurse -Force
   ```

3. **Create CSV_Input Directory:**
   ```powershell
   New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" -Force
   ```

4. **Set Permissions:**
   ```powershell
   icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "IIS_IUSRS:(OI)(CI)(F)" /T
   icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "NETWORK SERVICE:(OI)(CI)(F)" /T
   icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input" /grant "IIS AppPool\XQ360Migration:(OI)(CI)(F)" /T
   ```

5. **Start IIS Application Pool:**
   ```powershell
   Start-WebAppPool -Name "XQ360Migration"
   ```

## Testing the Fix

### 1. Verify CSV File Access
- Upload a CSV file through the web interface
- Check that the file appears in `C:\inetpub\wwwroot\XQ360Migration\CSV_Input`
- Verify the migration can read the uploaded files

### 2. Test Migration Process
- Start a migration with uploaded CSV files
- Verify the migration completes successfully
- Check that progress updates work correctly

### 3. Verify File Operations
- Check that the application can read existing CSV files
- Verify that uploaded files are saved correctly
- Ensure migration reports are generated properly

## What This Fixes

✅ **CSV File Upload:** Files uploaded through the web interface are now saved to the correct location

✅ **CSV File Reading:** The migration process can now read CSV files from the correct path

✅ **CSV Template Access:** Template files can now be accessed and downloaded correctly in both development and deployment environments

✅ **CSV Validation:** CSV format validation now works correctly by reading template files from the proper location

✅ **File Path Consistency:** All file operations now use the correct paths for both development and deployment environments

✅ **Cross-Environment Compatibility:** The application now works correctly in both development and production environments

## Expected Behavior After Fix

- **File Upload:** CSV files uploaded through the web interface will be saved to `C:\inetpub\wwwroot\XQ360Migration\CSV_Input`
- **File Reading:** The migration process will read CSV files from the same directory
- **Template Access:** CSV template files can be downloaded from `C:\inetpub\wwwroot\XQ360Migration\CSV_Template`
- **CSV Validation:** CSV format validation will work correctly by reading template files
- **Progress Updates:** Real-time progress updates should work correctly
- **Migration Completion:** Migrations should complete successfully without file path errors

## Troubleshooting

If you still encounter issues:

1. **Check Directory Permissions:**
   ```powershell
   icacls "C:\inetpub\wwwroot\XQ360Migration\CSV_Input"
   ```

2. **Verify Application Pool Identity:**
   - Open IIS Manager
   - Check the identity of the XQ360Migration application pool
   - Ensure it has read/write access to the CSV_Input directory

3. **Test File Access:**
   ```powershell
   $testFile = "C:\inetpub\wwwroot\XQ360Migration\CSV_Input\test.txt"
   "Test" | Out-File -FilePath $testFile
   Remove-Item $testFile
   ```

4. **Check Application Logs:**
   - Look for any file path errors in the application logs
   - Verify the current working directory is correct 