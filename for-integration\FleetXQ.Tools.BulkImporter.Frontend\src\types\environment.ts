export interface Environment {
    name: string
    displayName: string
    description: string
    requiresApproval: boolean
    maxOperationSize: number
    notificationWebhooks: string[]
    maintenanceWindows: MaintenanceWindow[]
    isInMaintenanceWindow: boolean
}

export interface MaintenanceWindow {
    start: string
    end: string
    timeZone: string
    description: string
}

export interface EnvironmentValidation {
    isValid: boolean
    errorMessage?: string
    requiresApproval: boolean
    warnings: string[]
    maxOperationSize: number
}
