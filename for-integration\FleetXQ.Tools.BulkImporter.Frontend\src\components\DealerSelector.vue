<template>
  <div class="dealer-selector">
    <div class="mb-3">
      <label for="dealer-search" class="form-label">Dealer *</label>
      <input 
        type="text" 
        id="dealer-search" 
        class="form-control" 
        :class="{ 'is-invalid': validationError }"
        v-model="searchQuery"
        @input="onSearchInput"
        @focus="showResults = true"
        @blur="onBlur"
        placeholder="Search for dealer by name or subdomain..."
        :disabled="loading"
        autocomplete="off"
        required
      >
      
      <div v-if="validationError" class="invalid-feedback">
        {{ validationError }}
      </div>
      
      <div v-if="loading" class="form-text">
        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        Searching dealers...
      </div>
    </div>

    <!-- Search Results Dropdown -->
    <div 
      v-if="showResults && (searchResults.length > 0 || searchQuery.length > 0)" 
      class="search-results dropdown-menu show w-100"
      style="position: relative; transform: none; margin-top: -0.5rem;"
    >
      <!-- No Results Found -->
      <div v-if="searchQuery.length > 0 && searchResults.length === 0 && !loading" class="dropdown-item-text text-muted">
        <i class="fas fa-search me-2"></i>
        No dealers found matching "{{ searchQuery }}"
      </div>
      
      <!-- Search Results -->
      <button
        v-for="dealer in searchResults"
        :key="dealer.id"
        type="button"
        class="dropdown-item d-flex justify-content-between align-items-start"
        @mousedown.prevent="selectDealer(dealer)"
      >
        <div class="dealer-info flex-grow-1">
          <div class="fw-bold">{{ dealer.name }}</div>
          <div class="text-muted small">{{ dealer.subdomain }}</div>
          <div v-if="dealer.contactEmail" class="text-muted small">
            <i class="fas fa-envelope me-1"></i>
            {{ dealer.contactEmail }}
          </div>
        </div>
        <div class="dealer-status">
          <span
            class="badge"
            :class="dealer.isActive ? 'bg-dark text-white' : 'bg-secondary'"
          >
            {{ dealer.isActive ? 'Active' : 'Inactive' }}
          </span>
        </div>
      </button>
      
      <!-- Show More Results -->
      <div v-if="hasMoreResults" class="dropdown-item-text text-center">
        <button 
          type="button" 
          class="btn btn-link btn-sm"
          @click="loadMoreResults"
          :disabled="loading"
        >
          <i v-if="loading" class="fas fa-spinner fa-spin me-1"></i>
          Load more results
        </button>
      </div>
    </div>

    <!-- Selected Dealer Display -->
    <div v-if="selectedDealer" class="selected-dealer mt-3">
      <div class="alert alert-light border">
        <div class="d-flex align-items-center mb-2">
          <i class="fas fa-check-circle me-2"></i>
          <strong>Dealer Selected</strong>
        </div>
        
        <div class="dealer-details">
          <div class="row">
            <div class="col-md-8">
              <h6 class="mb-1">{{ selectedDealer.name }}</h6>
              <p class="text-muted mb-1">
                <i class="fas fa-globe me-1"></i>
                {{ selectedDealer.subdomain }}
              </p>
              <p v-if="selectedDealer.contactEmail" class="text-muted mb-1">
                <i class="fas fa-envelope me-1"></i>
                {{ selectedDealer.contactEmail }}
              </p>
              <p v-if="selectedDealer.contactPhone" class="text-muted mb-0">
                <i class="fas fa-phone me-1"></i>
                {{ selectedDealer.contactPhone }}
              </p>
            </div>
            <div class="col-md-4 text-end">
              <span
                class="badge fs-6"
                :class="selectedDealer.isActive ? 'bg-dark text-white' : 'bg-secondary'"
              >
                {{ selectedDealer.isActive ? 'Active' : 'Inactive' }}
              </span>
              <div class="mt-2">
                <small class="text-muted">ID: {{ selectedDealer.id }}</small>
              </div>
            </div>
          </div>
          
          <div v-if="selectedDealer.address" class="dealer-address mt-2 pt-2 border-top">
            <small class="text-muted">
              <i class="fas fa-map-marker-alt me-1"></i>
              {{ formatAddress(selectedDealer.address) }}
            </small>
          </div>
          
          <div class="dealer-actions mt-2 pt-2 border-top">
            <button 
              type="button" 
              class="btn btn-outline-secondary btn-sm"
              @click="clearSelection"
            >
              <i class="fas fa-times me-1"></i>
              Change Dealer
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Warning for non-existing dealer -->
    <div v-if="searchQuery && !selectedDealer && searchResults.length === 0 && !loading" class="alert alert-light border mt-3">
      <i class="fas fa-exclamation-triangle me-2"></i>
      <strong>Important:</strong> Please select an existing dealer. New dealers cannot be created through this interface.
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useDealerStore } from '@/stores/dealer'
import type { Dealer } from '@/types/dealer'

// Store
const dealerStore = useDealerStore()

// Props
const props = defineProps<{
  modelValue?: Dealer | null
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Dealer | null]
  'validation-change': [isValid: boolean]
}>()

// Local state
const searchQuery = ref('')
const showResults = ref(false)
const validationError = ref('')
const searchTimeout = ref<NodeJS.Timeout>()

// Computed properties
const loading = computed(() => dealerStore.loading)
const selectedDealer = computed(() => dealerStore.selectedDealer)
const searchResults = computed(() => dealerStore.searchResults?.dealers || [])
const hasMoreResults = computed(() => dealerStore.searchResults?.hasMore || false)

const isValid = computed(() => {
  return selectedDealer.value !== null && selectedDealer.value.isActive && !validationError.value
})

// Methods
const onSearchInput = () => {
  validationError.value = ''
  showResults.value = true
  
  // Clear previous timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  // Debounce search
  searchTimeout.value = setTimeout(() => {
    if (searchQuery.value.trim().length >= 2) {
      performSearch()
    } else {
      dealerStore.searchResults = null
    }
  }, 300)
}

const performSearch = async () => {
  try {
    await dealerStore.searchDealers(searchQuery.value)
  } catch (error) {
    validationError.value = 'Failed to search dealers. Please try again.'
    console.error('Error searching dealers:', error)
  }
}

const selectDealer = async (dealer: Dealer) => {
  if (!dealer.isActive) {
    validationError.value = 'Selected dealer is inactive and cannot be used'
    return
  }
  
  searchQuery.value = dealer.name
  showResults.value = false
  dealerStore.setSelectedDealer(dealer)
  emit('update:modelValue', dealer)
  
  // Validate dealer exists and is active
  const isValid = await dealerStore.validateDealer(dealer.id)
  if (!isValid) {
    validationError.value = 'Selected dealer is not valid or accessible'
  }
}

const clearSelection = () => {
  searchQuery.value = ''
  dealerStore.clearDealer()
  emit('update:modelValue', null)
  validationError.value = ''
  showResults.value = false
}

const onBlur = () => {
  // Delay hiding results to allow for clicks
  setTimeout(() => {
    showResults.value = false
  }, 150)
}

const loadMoreResults = () => {
  if (hasMoreResults.value && !loading.value) {
    performSearch()
  }
}

const formatAddress = (address: any) => {
  if (!address) return ''
  return `${address.street}, ${address.city}, ${address.state} ${address.postalCode}, ${address.country}`
}

const validateDealer = () => {
  if (!selectedDealer.value) {
    validationError.value = 'Dealer selection is required'
    return false
  }
  
  if (!selectedDealer.value.isActive) {
    validationError.value = 'Selected dealer must be active'
    return false
  }
  
  validationError.value = ''
  return true
}

// Watchers
watch(isValid, (newValue) => {
  emit('validation-change', newValue)
})

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    searchQuery.value = newValue.name
    dealerStore.setSelectedDealer(newValue)
  } else {
    searchQuery.value = ''
    dealerStore.clearDealer()
  }
})

// Lifecycle
onMounted(() => {
  // Load saved dealer
  dealerStore.loadSavedDealer()
  
  // Set initial value if there's a saved dealer
  if (selectedDealer.value) {
    searchQuery.value = selectedDealer.value.name
    emit('update:modelValue', selectedDealer.value)
  }
})

// Expose validation method for parent components
defineExpose({
  validate: validateDealer,
  isValid
})
</script>

<style scoped>
.dealer-selector {
  position: relative;
  
  .spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
  }
  
  .search-results {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    
    .dropdown-item {
      padding: 0.75rem 1rem;
      border-bottom: 1px solid var(--bs-border-color);
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background-color: var(--bs-light);
      }
      
      .dealer-info {
        .fw-bold {
          color: var(--bs-primary);
        }
      }
    }
  }
  
  .selected-dealer {
    .alert {
      border-left: 4px solid var(--bs-success);
    }
    
    .dealer-details {
      .badge {
        font-size: 0.75rem;
      }
    }
  }
}
</style>
