# XQ360 Data Migration - Cleanup Script
# This script removes all deployment artifacts for a clean deployment

param(
    [switch]$Force = $false
)

Write-Host "XQ360 Data Migration - Cleanup Script" -ForegroundColor Green
Write-Host "This will remove all deployment artifacts for a clean deployment." -ForegroundColor Yellow

if (-not $Force) {
    Write-Host "`nAre you sure you want to continue? (Y/N)" -ForegroundColor Red
    $response = Read-Host
    if ($response -ne "Y" -and $response -ne "y") {
        Write-Host "Cleanup cancelled." -ForegroundColor Yellow
        exit 0
    }
}

Write-Host "`nStarting cleanup..." -ForegroundColor Cyan

# Step 1: Import WebAdministration module
try {
    Import-Module WebAdministration -ErrorAction SilentlyContinue
    if (Get-Module WebAdministration) {
        Write-Host "✅ WebAdministration module loaded" -ForegroundColor Green
    } else {
        Write-Host "⚠️ WebAdministration module not available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Could not load WebAdministration module: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 2: Stop and remove website
Write-Host "`nStep 1: Stopping and removing website..." -ForegroundColor Cyan
try {
    $website = Get-Website -Name "XQ360Migration" -ErrorAction SilentlyContinue
    if ($website) {
        Write-Host "Found website 'XQ360Migration', stopping..." -ForegroundColor Yellow
        Stop-Website -Name "XQ360Migration" -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 2
        
        Write-Host "Removing website..." -ForegroundColor Yellow
        Remove-Website -Name "XQ360Migration" -ErrorAction SilentlyContinue
        Write-Host "✅ Website removed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Website 'XQ360Migration' not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Error removing website: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 3: Remove application pool
Write-Host "`nStep 2: Removing application pool..." -ForegroundColor Cyan
try {
    $appPool = Get-WebAppPool -Name "XQ360MigrationPool" -ErrorAction SilentlyContinue
    if ($appPool) {
        Write-Host "Found application pool 'XQ360MigrationPool', removing..." -ForegroundColor Yellow
        Remove-WebAppPool -Name "XQ360MigrationPool" -ErrorAction SilentlyContinue
        Write-Host "✅ Application pool removed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Application pool 'XQ360MigrationPool' not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Error removing application pool: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 4: Remove deployment directory
Write-Host "`nStep 3: Removing deployment directory..." -ForegroundColor Cyan
$DeployPath = "C:\inetpub\wwwroot\XQ360Migration"
if (Test-Path $DeployPath) {
    try {
        Write-Host "Found deployment directory, removing..." -ForegroundColor Yellow
        Remove-Item $DeployPath -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "✅ Deployment directory removed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Error removing deployment directory: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "You may need to manually delete: $DeployPath" -ForegroundColor Red
    }
} else {
    Write-Host "Deployment directory not found: $DeployPath" -ForegroundColor Yellow
}

# Step 5: Remove temporary files
Write-Host "`nStep 4: Removing temporary files..." -ForegroundColor Cyan
$tempPath = "C:\temp\XQ360Deploy"
if (Test-Path $tempPath) {
    try {
        Write-Host "Found temporary directory, removing..." -ForegroundColor Yellow
        Remove-Item $tempPath -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "✅ Temporary files removed successfully!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Error removing temporary files: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "Temporary directory not found: $tempPath" -ForegroundColor Yellow
}

# Step 6: Remove firewall rule (optional)
Write-Host "`nStep 5: Removing firewall rule..." -ForegroundColor Cyan
try {
    $firewallRule = Get-NetFirewallRule -DisplayName "XQ360 Migration Web" -ErrorAction SilentlyContinue
    if ($firewallRule) {
        Write-Host "Found firewall rule, removing..." -ForegroundColor Yellow
        Remove-NetFirewallRule -DisplayName "XQ360 Migration Web" -ErrorAction SilentlyContinue
        Write-Host "✅ Firewall rule removed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Firewall rule 'XQ360 Migration Web' not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Error removing firewall rule: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Summary
Write-Host "`nCleanup Summary:" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green
Write-Host "✅ Website stopped and removed" -ForegroundColor White
Write-Host "✅ Application pool removed" -ForegroundColor White
Write-Host "✅ Deployment directory removed" -ForegroundColor White
Write-Host "✅ Temporary files removed" -ForegroundColor White
Write-Host "✅ Firewall rule removed" -ForegroundColor White

Write-Host "`nCleanup completed! You can now run a fresh deployment." -ForegroundColor Green
Write-Host "Run: .\deploy\deploy-remote.ps1 -ServerName '*************' -Port 80" -ForegroundColor Cyan 