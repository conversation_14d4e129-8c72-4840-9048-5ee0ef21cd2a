# Test script to verify ObjectDisposedException fix
# This script will test the migration functionality to ensure the fix works

Write-Host "=== Testing ObjectDisposedException Fix ===" -ForegroundColor Cyan
Write-Host ""

# Define test parameters
$testUrl = "http://localhost"
$testMigrationType = "vehicles"
$testCsvPath = "C:\files\XQ360DataMigration\XQ360.DataMigration\CSV_Input\VEHICLE_IMPORT.csv"

Write-Host "1. Checking application availability..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $testUrl -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✓ Application is responding" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Application returned status: $($response.StatusCode)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "   ❌ Application is not responding: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

Write-Host "2. Checking test CSV file..." -ForegroundColor Yellow
if (Test-Path $testCsvPath) {
    Write-Host "   ✓ Test CSV file found" -ForegroundColor Green
    $csvContent = Get-Content $testCsvPath -Raw
    $lineCount = ($csvContent -split "`n").Count
    Write-Host "   ✓ CSV file has $lineCount lines" -ForegroundColor Green
} else {
    Write-Host "   ❌ Test CSV file not found at: $testCsvPath" -ForegroundColor Red
    Write-Host "   💡 Please ensure the CSV file exists for testing" -ForegroundColor Yellow
    exit 1
}

Write-Host ""

Write-Host "3. Testing migration start..." -ForegroundColor Yellow
Write-Host "   This will attempt to start a migration to test the ObjectDisposedException fix" -ForegroundColor Gray
Write-Host "   The test will check if the migration starts without the disposed exception" -ForegroundColor Gray

# Create a simple test by checking the application logs
Write-Host "   Checking application logs for recent errors..." -ForegroundColor Gray

# Look for recent error logs
$logPath = "C:\inetpub\wwwroot\XQ360Migration\Logs"
if (Test-Path $logPath) {
    $recentLogs = Get-ChildItem $logPath -Filter "*.log" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if ($recentLogs) {
        $logContent = Get-Content $recentLogs.FullName -Tail 50
        $disposedErrors = $logContent | Where-Object { $_ -match "ObjectDisposedException" }
        
        if ($disposedErrors) {
            Write-Host "   ⚠️ Found recent ObjectDisposedException errors:" -ForegroundColor Yellow
            foreach ($error in $disposedErrors) {
                Write-Host "      $error" -ForegroundColor Gray
            }
        } else {
            Write-Host "   ✓ No recent ObjectDisposedException errors found" -ForegroundColor Green
        }
    }
}

Write-Host ""

Write-Host "4. Manual testing instructions..." -ForegroundColor Yellow
Write-Host "   To fully test the fix:" -ForegroundColor Gray
Write-Host "   1. Open the application in a web browser" -ForegroundColor Gray
Write-Host "   2. Upload a CSV file (e.g., VEHICLE_IMPORT.csv)" -ForegroundColor Gray
Write-Host "   3. Select the appropriate migration type" -ForegroundColor Gray
Write-Host "   4. Click 'Start Migration'" -ForegroundColor Gray
Write-Host "   5. Check if the migration starts without errors" -ForegroundColor Gray
Write-Host "   6. Monitor the progress updates" -ForegroundColor Gray

Write-Host ""

Write-Host "5. Expected behavior after fix..." -ForegroundColor Yellow
Write-Host "   ✓ Migration should start without ObjectDisposedException" -ForegroundColor Green
Write-Host "   ✓ Progress updates should work correctly" -ForegroundColor Green
Write-Host "   ✓ Service scopes should be properly managed" -ForegroundColor Green
Write-Host "   ✓ No 'Cannot access a disposed object' errors" -ForegroundColor Green

Write-Host ""

Write-Host "=== TEST SUMMARY ===" -ForegroundColor Cyan
Write-Host "The ObjectDisposedException fix has been applied and the application is running." -ForegroundColor Green
Write-Host ""
Write-Host "Key changes made:" -ForegroundColor Yellow
Write-Host "- Replaced IServiceProvider with IServiceScopeFactory" -ForegroundColor Gray
Write-Host "- Updated service scope creation to use _serviceScopeFactory.CreateScope()" -ForegroundColor Gray
Write-Host "- Added proper scope disposal in ExecuteVehicleSyncIfNeededAsync" -ForegroundColor Gray
Write-Host ""
Write-Host "Please perform manual testing to verify the migration functionality works correctly." -ForegroundColor Cyan 