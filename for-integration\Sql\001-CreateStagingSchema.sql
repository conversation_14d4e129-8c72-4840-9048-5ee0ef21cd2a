-- =============================================
-- FleetXQ Bulk Importer - Staging Schema Setup
-- Creates staging schema and core infrastructure
-- =============================================

-- Create staging schema if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Staging')
BEGIN
    EXEC('CREATE SCHEMA [Staging]')
    PRINT 'Created [Staging] schema'
END
ELSE
BEGIN
    PRINT '[Staging] schema already exists'
END
GO

-- Create import session tracking table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE schema_id = SCHEMA_ID('Staging') AND name = 'ImportSession')
BEGIN
    CREATE TABLE [Staging].[ImportSession] (
        [Id] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [SessionName] NVARCHAR(100) NOT NULL,
        [StartTime] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [EndTime] DATETIME2 NULL,
        [Status] NVARCHAR(20) NOT NULL DEFAULT 'Running', -- Running, Completed, Failed, RolledBack
        [TotalRows] INT NOT NULL DEFAULT 0,
        [ProcessedRows] INT NOT NULL DEFAULT 0,
        [SuccessfulRows] INT NOT NULL DEFAULT 0,
        [FailedRows] INT NOT NULL DEFAULT 0,
        [ErrorSummary] NVARCHAR(MAX) NULL,
        [ConfigurationSnapshot] NVARCHAR(MAX) NULL,
        [CreatedBy] NVARCHAR(100) NOT NULL DEFAULT SYSTEM_USER,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        -- Indexes for performance
        INDEX IX_ImportSession_Status ([Status]),
        INDEX IX_ImportSession_StartTime ([StartTime] DESC)
    )
    
    PRINT 'Created [Staging].[ImportSession] table'
END
ELSE
BEGIN
    PRINT '[Staging].[ImportSession] table already exists'
END
GO