using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Data.SqlClient;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using Newtonsoft.Json; // Added for JsonConvert

namespace XQ360.DataMigration.Implementations
{
    public class SpareModuleMigration
    {
        private readonly ILogger<SpareModuleMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly XQ360ApiClient _apiClient;
        private readonly MigrationReportingService _reportingService;
        private readonly string _connectionString;

        // Cache for database lookups
        private readonly Dictionary<string, Guid> _dealerCache = new Dictionary<string, Guid>();

        public SpareModuleMigration(
            ILogger<SpareModuleMigration> logger,
            IOptions<MigrationConfiguration> config,
            XQ360ApiClient apiClient,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _apiClient = apiClient;
            _reportingService = reportingService;
            _connectionString = _config.DatabaseConnection;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Spare Module migration using XQ360 API");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Authenticate with API
                var authResult = await _apiClient.AuthenticateAsync();
                if (!authResult)
                {
                    return new MigrationResult
                    {
                        Success = false,
                        Errors = new List<string> { "Failed to authenticate with XQ360 API" },
                        Duration = DateTime.UtcNow - startTime
                    };
                }

                // Step 2.5: Test authentication to ensure it's working
                var authTestResult = await _apiClient.TestAuthenticationAsync();
                if (!authTestResult)
                {
                    _logger.LogWarning("Authentication test failed, but proceeding with migration...");
                }
                else
                {
                    _logger.LogInformation("Authentication test successful");
                }

                // Step 2.6: Add small delay to ensure tokens are stable
                await Task.Delay(1000);
                _logger.LogInformation("Starting module API calls after authentication delay...");

                // Step 3: Execute API migration
                result.RecordsProcessed = data.Count;
                var migrationResult = await ExecuteSpareModuleApiAsync(data, result);

                result.Success = migrationResult.Success;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;

                _logger.LogInformation($"Spare Module migration completed: {result.RecordsInserted} inserted, {result.RecordsSkipped} skipped, Duration: {result.Duration}");

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Spare Module Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Spare Module migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private Task<List<SpareModuleImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Spare Module CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<SpareModuleImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return Task.FromResult(records);
        }

        private async Task<MigrationResult> ExecuteSpareModuleApiAsync(List<SpareModuleImportModel> data, MigrationResult sharedResult)
        {
            _logger.LogInformation("Executing Spare Module API migration...");

            var insertedCount = 0;
            var errors = new List<string>();
            var warnings = new List<string>();

            foreach (var record in data)
            {
                try
                {
                    // Validate the record data first
                    if (string.IsNullOrEmpty(record.IoTDeviceID))
                    {
                        _logger.LogWarning($"Skipping record with empty IoTDeviceID");
                        continue;
                    }
                    
                    if (string.IsNullOrEmpty(record.Dealer))
                    {
                        _logger.LogWarning($"Record {record.IoTDeviceID} has empty Dealer, continuing anyway...");
                    }

                    // Check if module with this IoTDevice already exists
                    var existingModuleId = await CheckIfModuleExistsAsync(record.IoTDeviceID);
                    if (existingModuleId != null)
                    {
                        _logger.LogWarning($"Module with IoTDevice '{record.IoTDeviceID}' already exists with ID: {existingModuleId}. Skipping creation.");
                        _reportingService.AddDetailedWarning(sharedResult, data.IndexOf(record) + 1, WarningTypes.EXISTING_RECORD_SKIPPED,
                            $"Spare module '{record.IoTDeviceID}' already exists in database", 
                            "IoTDeviceID", record.IoTDeviceID,
                            "Remove duplicate from CSV or verify this module assignment",
                            new Dictionary<string, string> 
                            {
                                { "Dealer", record.Dealer ?? "Unknown" },
                                { "CCID", record.CCID.ToString() }
                            });
                        warnings.Add($"Module '{record.IoTDeviceID}' already exists - skipped");
                        continue;
                    }

                    // Get dealer ID from dealer name
                    var dealerId = await GetDealerIdAsync(record.Dealer ?? "Unknown");
                    if (dealerId == null)
                    {
                        var errorMsg = $"Dealer '{record.Dealer}' not found for module {record.IoTDeviceID}";
                        errors.Add(errorMsg);
                        _logger.LogWarning(errorMsg);
                        continue;
                    }

                    // Create the entity object with ALL required fields
                    // NOTE: Do NOT include Id field - let server generate it for new records
                    var moduleEntity = new
                    {
                        IsNew = true,
                        // Basic fields from your data
                        IoTDevice = record.IoTDeviceID?.Trim(),
                        DealerId = dealerId.Value,
                        CCID = record.CCID.ToString().Trim(),
                        RANumber = record.RANumber.ToString().Trim(),
                        TechNumber = record.TechNumber.ToString().Trim(),
                        
                        // Required fields with correct values
                        IsSimCardExpired = false,
                        Status = 0, // ModuleStatusEnum.Spare
                        FSSXMulti = 1.0, // CRITICAL: Must be 1, not 0!
                        IsAllocatedToVehicle = false, // ✅ FALSE for spare modules
                        ModuleType = 0, // ModuleTypeEnum.MK3
                        Calibration = 0, // Default value for spare modules
                        FSSSBase = 0.0, // Default value for spare modules
                        
                        // Impact values (will be calculated by ModuleDataProviderExtension)
                        AmberImpact = 0.0,
                        BlueImpact = 0.0,
                        RedImpact = 0.0
                    };

                    // Serialize to JSON string for the entity parameter
                    var entityJson = JsonConvert.SerializeObject(moduleEntity);

                    // Create form data
                    var formData = new Dictionary<string, string>
                    {
                        ["entity"] = entityJson,
                        ["include"] = "Dealer" // Optional: include dealer info in response
                    };

                    _logger.LogDebug($"Creating module: {record.IoTDeviceID}");
                    
                    // Post as form data
                    var apiResult = await _apiClient.PostFormAsync<ModuleApiResponse>("module", formData);

                    if (apiResult.Success)
                    {
                        // Additional validation for the API response
                        if (apiResult.Data == null)
                        {
                            var errorMsg = $"API returned null data for module {record.IoTDeviceID}";
                            errors.Add(errorMsg);
                            _logger.LogWarning(errorMsg);
                        }
                        else if (!apiResult.Data.IsObjectsDataSetValid())
                        {
                            // ObjectsDataSet is null but we can still consider this a success
                            // as the module might have been created despite the null dataset
                            _logger.LogWarning($"Module {record.IoTDeviceID} created but ObjectsDataSet is null in response");
                            insertedCount++;
                            _logger.LogDebug($"Successfully created module: {record.IoTDeviceID} (with null ObjectsDataSet)");
                        }
                        else
                        {
                            insertedCount++;
                            _logger.LogDebug($"Successfully created module: {record.IoTDeviceID}");
                        }
                    }
                    else
                    {
                        var errorMsg = $"Failed to create module {record.IoTDeviceID}: {apiResult.ErrorMessage}";
                        errors.Add(errorMsg);
                        _logger.LogWarning(errorMsg);
                    }
                }
                catch (Exception ex)
                {
                    var errorMsg = $"Error processing module {record.IoTDeviceID}: {ex.Message}";
                    errors.Add(errorMsg);
                    _logger.LogError(ex, errorMsg);
                }

                // Add small delay to avoid overwhelming the API
                await Task.Delay(100);
            }

            _logger.LogInformation($"Successfully created {insertedCount} spare modules via API");

            return new MigrationResult
            {
                Success = errors.Count == 0,
                RecordsInserted = insertedCount,
                Errors = errors,
                Warnings = warnings
            };
        }

        private async Task<Guid?> CheckIfModuleExistsAsync(string iotDeviceId)
        {
            if (string.IsNullOrEmpty(iotDeviceId))
                return null;

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = "SELECT Id FROM dbo.Module WHERE IoTDevice = @IoTDevice";
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@IoTDevice", iotDeviceId);

                var result = await cmd.ExecuteScalarAsync();
                if (result != null)
                {
                    var moduleId = (Guid)result;
                    return moduleId;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to check if module exists for IoTDevice '{iotDeviceId}'");
                return null;
            }
        }

        private async Task<Guid?> GetDealerIdAsync(string dealerName)
        {
            if (string.IsNullOrEmpty(dealerName))
                return null;

            if (_dealerCache.ContainsKey(dealerName))
            {
                return _dealerCache[dealerName];
            }

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var sql = "SELECT Id FROM dbo.Dealer WHERE Name = @Name";
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@Name", dealerName);

                var result = await cmd.ExecuteScalarAsync();
                if (result != null)
                {
                    var dealerId = (Guid)result;
                    _dealerCache[dealerName] = dealerId;
                    return dealerId;
                }
                else
                {
                    _logger.LogWarning($"Dealer '{dealerName}' not found in database");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to lookup dealer '{dealerName}'");
                return null;
            }
        }
    }

    // API entity models for Module API - matches database schema
    public class ModuleApiEntity
    {
        // Core required fields only
        public required string IoTDevice { get; set; }
        public required string CCID { get; set; }
        public required string RANumber { get; set; }
        public required string TechNumber { get; set; }
        public int Status { get; set; }
        public bool IsAllocatedToVehicle { get; set; }
        public int Calibration { get; set; }
        public float BlueImpact { get; set; }
        public float FSSXMulti { get; set; }
        public float FSSBase { get; set; }
        public float AmberImpact { get; set; }
        public float RedImpact { get; set; }
        // Additional fields that might be required
        public int ModuleType { get; set; }
        public Guid? DealerId { get; set; }
        public required string Dealer { get; set; } // Try dealer name instead of GUID
        public required string Note { get; set; }
    }

    public class ModuleApiResponse
    {
        public int InternalObjectId { get; set; }
        public required string PrimaryKey { get; set; }
        
        private object _objectsDataSet = new object();
        public object ObjectsDataSet 
        { 
            get => _objectsDataSet; 
            set => _objectsDataSet = value;
        }
        
        // Helper method to check if ObjectsDataSet is valid
        public bool IsObjectsDataSetValid()
        {
            return ObjectsDataSet != null;
        }
    }
} 