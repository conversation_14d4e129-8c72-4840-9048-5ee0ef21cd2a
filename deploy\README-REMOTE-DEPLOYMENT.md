# Remote Windows Server Deployment Guide

## 🎯 Overview

This guide covers deploying all three XQ360 Data Migration applications to a remote Windows Server with IIS:
- **Web Application** → IIS (for remote UI access)
- **Main Application** → Console app (for CLI operations)  
- **Test Application** → Available for database checking

## 📋 Prerequisites

### **Remote Server Requirements:**
- ✅ Windows Server 2019/2022
- ✅ IIS 10.0+ installed
- ✅ .NET 9.0 Runtime installed
- ✅ SQL Server (if using local database)
- ✅ Network access to target databases (US, UK, AU, etc.)
- ✅ Firewall rules for web access

### **Network Requirements:**
- ✅ Port 80/443 open for web access
- ✅ Network access to target database servers
- ✅ Network access to target API endpoints

## 🚀 Step-by-Step Deployment

### **Step 1: Prepare the Remote Server**

#### **1.1 Install .NET 9.0 Runtime**
```powershell
# Download and install .NET 9.0 Runtime
# https://dotnet.microsoft.com/download/dotnet/9.0
```

#### **1.2 Install IIS Features**
```powershell
# Enable IIS with ASP.NET Core Hosting Bundle
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServer
Enable-WindowsOptionalFeature -Online -FeatureName IIS-CommonHttpFeatures
Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpErrors
Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpLogging
Enable-WindowsOptionalFeature -Online -FeatureName IIS-RequestFiltering
Enable-WindowsOptionalFeature -Online -FeatureName IIS-StaticContent
Enable-WindowsOptionalFeature -Online -FeatureName IIS-DefaultDocument
Enable-WindowsOptionalFeature -Online -FeatureName IIS-DirectoryBrowsing
Enable-WindowsOptionalFeature -Online -FeatureName IIS-ASPNET45
```

#### **1.3 Install ASP.NET Core Hosting Bundle**
```powershell
# Download and install ASP.NET Core Hosting Bundle
# https://dotnet.microsoft.com/download/dotnet/9.0
```

### **Step 2: Build and Prepare Applications**

#### **2.1 Build All Applications**
```powershell
# Build main application
dotnet build XQ360.DataMigration -c Release

# Build web application  
dotnet build XQ360.DataMigration.Web -c Release

# Build test application
dotnet build XQ360.DataMigration.Tests -c Release
```

#### **2.2 Publish Applications**
```powershell
# Publish web application
dotnet publish XQ360.DataMigration.Web -c Release -o C:\temp\XQ360Web

# Publish main application
dotnet publish XQ360.DataMigration -c Release -o C:\temp\XQ360Main

# Copy test application
Copy-Item "XQ360.DataMigration.Tests" -Destination "C:\temp\XQ360Tests" -Recurse
```

### **Step 3: Configure IIS**

#### **3.1 Create Application Pool**
```powershell
# Create Application Pool
Import-Module WebAdministration
New-WebAppPool -Name "XQ360MigrationPool"
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "managedRuntimeVersion" -Value ""
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
```

#### **3.2 Create Website**
```powershell
# Create website
New-Website -Name "XQ360Migration" -PhysicalPath "C:\inetpub\wwwroot\XQ360Migration" -ApplicationPool "XQ360MigrationPool" -Port 80
```

#### **3.3 Configure Application**
```powershell
# Copy web application files
Copy-Item "C:\temp\XQ360Web\*" -Destination "C:\inetpub\wwwroot\XQ360Migration" -Recurse -Force

# Copy main application (for CLI operations)
Copy-Item "C:\temp\XQ360Main\*" -Destination "C:\inetpub\wwwroot\XQ360Migration\bin" -Recurse -Force

# Copy test application (for database checking)
Copy-Item "C:\temp\XQ360Tests\*" -Destination "C:\inetpub\wwwroot\XQ360Migration\tests" -Recurse -Force
```

### **Step 4: Configure Application Settings**

#### **4.1 Update Production Configuration**
```powershell
# Copy production appsettings
Copy-Item "deploy\production-appsettings.json" -Destination "C:\inetpub\wwwroot\XQ360Migration\appsettings.json" -Force
```

#### **4.2 Configure Environment Variables**
```powershell
# Set ASPNETCORE_ENVIRONMENT
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "environmentVariables" -Value @{
    "ASPNETCORE_ENVIRONMENT" = "Production"
}
```

### **Step 5: Set Permissions**

#### **5.1 Set File Permissions**
```powershell
# Set permissions for IIS_IUSRS
icacls "C:\inetpub\wwwroot\XQ360Migration" /grant "IIS_IUSRS:(OI)(CI)(RX)" /T
icacls "C:\inetpub\wwwroot\XQ360Migration" /grant "IIS_IUSRS:(OI)(CI)(M)" /T

# Set permissions for Application Pool Identity
icacls "C:\inetpub\wwwroot\XQ360Migration" /grant "IIS AppPool\XQ360MigrationPool:(OI)(CI)(RX)" /T
icacls "C:\inetpub\wwwroot\XQ360Migration" /grant "IIS AppPool\XQ360MigrationPool:(OI)(CI)(M)" /T
```

#### **5.2 Set Log Directory Permissions**
```powershell
# Create and set permissions for logs directory
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration\Logs" -Force
icacls "C:\inetpub\wwwroot\XQ360Migration\Logs" /grant "IIS_IUSRS:(OI)(CI)(F)" /T
icacls "C:\inetpub\wwwroot\XQ360Migration\Logs" /grant "IIS AppPool\XQ360MigrationPool:(OI)(CI)(F)" /T
```

### **Step 6: Configure Network Access**

#### **6.1 Configure Firewall**
```powershell
# Allow HTTP traffic
New-NetFirewallRule -DisplayName "XQ360 Migration Web" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow

# Allow HTTPS traffic (if using SSL)
New-NetFirewallRule -DisplayName "XQ360 Migration Web HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
```

#### **6.2 Configure DNS/Network**
- Add DNS entry pointing to your server IP
- Configure reverse proxy if needed
- Set up SSL certificate if using HTTPS

### **Step 7: Test Deployment**

#### **7.1 Start the Website**
```powershell
# Start the website
Start-Website -Name "XQ360Migration"
```

#### **7.2 Test Web Access**
```powershell
# Test local access
Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing

# Test remote access (replace with your server IP)
Invoke-WebRequest -Uri "http://YOUR_SERVER_IP" -UseBasicParsing
```

#### **7.3 Test Database Checking**
1. Open web browser to `http://YOUR_SERVER_IP`
2. Select an environment (e.g., "Development")
3. Check "Run Database Checking"
4. Click "Start Migration"
5. Verify tests run successfully

## 🔧 Configuration Files

### **Production appsettings.json**
Update `deploy\production-appsettings.json` with your production environment settings:

```json
{
  "Migration": {
    "Environments": {
      "US": {
        "DatabaseConnection": "Server=us-fleetxqdb.database.windows.net,1433;Database=FleetXQ.US.Production;User Id=us-fleetxqdb;Password=YOUR_ACTUAL_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false",
        "ApiBaseUrl": "https://us-api.xq360.com/",
        "ApiUsername": "us-migration-user",
        "ApiPassword": "YOUR_ACTUAL_API_PASSWORD"
      },
      "UK": {
        "DatabaseConnection": "Server=uk-fleetxqdb.database.windows.net,1433;Database=FleetXQ.UK.Production;User Id=uk-fleetxqdb;Password=YOUR_ACTUAL_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false",
        "ApiBaseUrl": "https://uk-api.xq360.com/",
        "ApiUsername": "uk-migration-user", 
        "ApiPassword": "YOUR_ACTUAL_API_PASSWORD"
      },
      "AU": {
        "DatabaseConnection": "Server=au-fleetxqdb.database.windows.net,1433;Database=FleetXQ.AU.Production;User Id=au-fleetxqdb;Password=YOUR_ACTUAL_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false",
        "ApiBaseUrl": "https://au-api.xq360.com/",
        "ApiUsername": "au-migration-user",
        "ApiPassword": "YOUR_ACTUAL_API_PASSWORD"
      },
      "Pilot": {
        "DatabaseConnection": "Server=pilot-fleetxqdb.database.windows.net,1433;Database=FleetXQ.Pilot.Testing;User Id=pilot-fleetxqdb;Password=YOUR_ACTUAL_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false",
        "ApiBaseUrl": "https://pilot-api.xq360.com/",
        "ApiUsername": "pilot-migration-user",
        "ApiPassword": "YOUR_ACTUAL_API_PASSWORD"
      },
      "Development": {
        "DatabaseConnection": "Server=YOUR_SERVER_SQL;Database=FleetXQProd;User Id=YOUR_USER;Password=YOUR_PASSWORD;TrustServerCertificate=true;",
        "ApiBaseUrl": "https://localhost:53052/",
        "ApiUsername": "Admin",
        "ApiPassword": "Admin"
      }
    }
  }
}
```

## 🚨 Troubleshooting

### **Common Issues:**

#### **1. Application Pool Identity Issues**
```powershell
# Check Application Pool identity
Get-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.identityType"

# Set to ApplicationPoolIdentity if needed
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
```

#### **2. Permission Issues**
```powershell
# Grant full permissions to Application Pool
icacls "C:\inetpub\wwwroot\XQ360Migration" /grant "IIS AppPool\XQ360MigrationPool:(OI)(CI)(F)" /T
```

#### **3. Network Connectivity Issues**
```powershell
# Test database connectivity
Test-NetConnection -ComputerName "us-fleetxqdb.database.windows.net" -Port 1433

# Test API connectivity  
Test-NetConnection -ComputerName "us-api.xq360.com" -Port 443
```

#### **4. Log Analysis**
```powershell
# Check application logs
Get-Content "C:\inetpub\wwwroot\XQ360Migration\Logs\developer-migration-*.log" -Tail 50

# Check IIS logs
Get-Content "C:\inetpub\logs\LogFiles\W3SVC1\*.log" -Tail 50
```

## 🔒 Security Considerations

### **1. SSL/HTTPS Setup**
```powershell
# Install SSL certificate
# Configure HTTPS binding in IIS
```

### **2. Network Security**
- Use firewall rules to restrict access
- Consider VPN for secure access
- Implement IP whitelisting if needed

### **3. Credential Management**
- Use Azure Key Vault for production passwords
- Implement secure credential rotation
- Monitor credential usage

## 📊 Monitoring

### **1. Application Monitoring**
- Monitor application logs
- Set up alerts for errors
- Monitor database connectivity

### **2. Performance Monitoring**
- Monitor IIS performance counters
- Track database connection usage
- Monitor API response times

## 🎯 Access URLs

After deployment, access the application at:
- **Local**: `http://localhost`
- **Remote**: `http://YOUR_SERVER_IP`
- **DNS**: `http://your-domain.com` (if configured)

## ✅ Verification Checklist

- [ ] IIS website starts successfully
- [ ] Web UI loads remotely
- [ ] Environment selection works
- [ ] Database checking functionality works
- [ ] Migration operations work
- [ ] Logs are being written
- [ ] Network connectivity to target databases
- [ ] API connectivity to target endpoints
- [ ] File permissions are correct
- [ ] SSL certificate installed (if using HTTPS) 

## Step 8: Pre-configure .NET (IMPORTANT - Prevents Test Issues)

### 8.1 Run .NET Pre-configuration Script
```powershell
# Run as Administrator to prevent system folder access issues
.\deploy\pre-configure-dotnet.ps1
```

This step is **CRITICAL** to prevent the error:
```
System.UnauthorizedAccessException: Access to the path 'C:\Windows\system32\config\systemprofile' is denied.
```

### 8.2 What This Fixes
- Prevents .NET first-time setup from trying to access system folders
- Configures test runner to work with IIS application pool identity
- Creates proper user profile directories for the application
- Sets up environment variables to skip telemetry and first-time experience

### 8.3 Alternative Manual Fix
If the script doesn't work, manually run these commands as Administrator:
```powershell
# Set environment variables
$env:DOTNET_SKIP_FIRST_TIME_EXPERIENCE = "1"
$env:DOTNET_NOLOGO = "1"
$env:DOTNET_CLI_TELEMETRY_OPTOUT = "1"

# Pre-configure .NET
dotnet --info
dotnet test --help

# Create profile directory
New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\XQ360Migration\App_Data\.dotnet" -Force
``` 