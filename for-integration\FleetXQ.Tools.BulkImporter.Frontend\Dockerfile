# Build stage
FROM node:18-alpine AS build

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine AS final

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=build /app/dist /usr/share/nginx/html

# Create non-root user
RUN addgroup -g 1001 -S fleetxq && \
    adduser -S fleetxq -u 1001

# Set permissions
RUN chown -R fleetxq:fleetxq /usr/share/nginx/html && \
    chown -R fleetxq:fleetxq /var/cache/nginx && \
    chown -R fleetxq:fleetxq /var/log/nginx && \
    chown -R fleetxq:fleetxq /etc/nginx/conf.d

# Create nginx pid directory
RUN touch /var/run/nginx.pid && \
    chown -R fleetxq:fleetxq /var/run/nginx.pid

# Switch to non-root user
USER fleetxq

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
