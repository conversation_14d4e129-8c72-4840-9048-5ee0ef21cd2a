using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FleetXQ.Tools.BulkImporter.Core.Configuration;
using FleetXQ.Tools.BulkImporter.Core.Logging;
using System.Data;

namespace FleetXQ.Tools.BulkImporter.Core.Services;

/// <summary>
/// SQL-based data generation service that replaces CSV file operations
/// </summary>
public class SqlDataGenerationService : ISqlDataGenerationService
{
    private readonly ILogger<SqlDataGenerationService> _logger;
    private readonly BulkImporterOptions _options;
    private readonly DataGenerationOptions _generationOptions;
    private readonly ConnectionStringOptions _connectionOptions;

    public SqlDataGenerationService(
        ILogger<SqlDataGenerationService> logger,
        IOptions<BulkImporterOptions> options,
        IOptions<DataGenerationOptions> generationOptions,
        IOptions<ConnectionStringOptions> connectionOptions)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _generationOptions = generationOptions?.Value ?? throw new ArgumentNullException(nameof(generationOptions));
        _connectionOptions = connectionOptions?.Value ?? throw new ArgumentNullException(nameof(connectionOptions));
    }

    public async Task<Guid> CreateImportSessionAsync(string sessionName, CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();

        using var connection = new SqlConnection(_connectionOptions.FleetXQConnection);
        await connection.OpenAsync(cancellationToken);

        const string sql = @"
            INSERT INTO [Staging].[ImportSession] 
            ([Id], [SessionName], [StartTime], [Status], [CreatedBy])
            VALUES (@SessionId, @SessionName, @StartTime, @Status, @CreatedBy)";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@SessionName", sessionName);
        command.Parameters.AddWithValue("@StartTime", DateTime.UtcNow);
        command.Parameters.AddWithValue("@Status", "Running");
        command.Parameters.AddWithValue("@CreatedBy", Environment.UserName);

        await command.ExecuteNonQueryAsync(cancellationToken);

        _logger.LogInformation("Created import session {SessionId} with name '{SessionName}'", sessionId, sessionName);
        return sessionId;
    }

    public async Task<DataGenerationResult> GenerateDriverDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new DataGenerationResult();

        try
        {
            _logger.LogInformation("Generating {Count} driver records for session {SessionId}", count, sessionId);

            using var connection = new SqlConnection(_connectionOptions.FleetXQConnection);
            await connection.OpenAsync(cancellationToken);

            // Generate driver data in batches using SQL
            var batchSize = _generationOptions.GenerationBatchSize;
            var totalGenerated = 0;

            for (int offset = 0; offset < count; offset += batchSize)
            {
                var currentBatchSize = Math.Min(batchSize, count - offset);
                await GenerateDriverBatchAsync(connection, sessionId, offset, currentBatchSize, cancellationToken);
                totalGenerated += currentBatchSize;

                _logger.LogInformation("Generated {CurrentGenerated}/{TotalCount} driver records", totalGenerated, count);
            }

            result.Success = true;
            result.GeneratedRows = totalGenerated;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Successfully generated {totalGenerated} driver records";

            _logger.LogInformation("Driver data generation completed: {GeneratedRows} records in {Duration}",
                result.GeneratedRows, result.Duration);

            return result;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Driver data generation failed: {ex.Message}";

            _logger.LogError(ex, "Driver data generation failed for session {SessionId}", sessionId);
            throw;
        }
    }

    public async Task<DataGenerationResult> GenerateVehicleDataAsync(Guid sessionId, int count, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new DataGenerationResult();

        try
        {
            _logger.LogInformation("Generating {Count} vehicle records for session {SessionId}", count, sessionId);

            using var connection = new SqlConnection(_connectionOptions.FleetXQConnection);
            await connection.OpenAsync(cancellationToken);

            // Generate vehicle data in batches using SQL
            var batchSize = _generationOptions.GenerationBatchSize;
            var totalGenerated = 0;

            for (int offset = 0; offset < count; offset += batchSize)
            {
                var currentBatchSize = Math.Min(batchSize, count - offset);
                await GenerateVehicleBatchAsync(connection, sessionId, offset, currentBatchSize, cancellationToken);
                totalGenerated += currentBatchSize;

                _logger.LogInformation("Generated {CurrentGenerated}/{TotalCount} vehicle records", totalGenerated, count);
            }

            result.Success = true;
            result.GeneratedRows = totalGenerated;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Successfully generated {totalGenerated} vehicle records";

            _logger.LogInformation("Vehicle data generation completed: {GeneratedRows} records in {Duration}",
                result.GeneratedRows, result.Duration);

            return result;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Vehicle data generation failed: {ex.Message}";

            _logger.LogError(ex, "Vehicle data generation failed for session {SessionId}", sessionId);
            throw;
        }
    }

    private async Task GenerateDriverBatchAsync(SqlConnection connection, Guid sessionId, int offset, int batchSize, CancellationToken cancellationToken)
    {
        const string sql = @"
            WITH NumberSequence AS (
                SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset AS RowNum
                FROM master.dbo.spt_values s1
                CROSS JOIN master.dbo.spt_values s2
                WHERE s1.type = 'P' AND s2.type = 'P'
                    AND ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) <= @BatchSize
            ),
            RandomData AS (
                SELECT
                    RowNum,
                    'EXT_DRV_' + RIGHT('000000' + CAST(RowNum AS VARCHAR), 6) AS ExternalDriverId,
                    CASE ABS(CHECKSUM(NEWID())) % 20
                        WHEN 0 THEN 'John' WHEN 1 THEN 'Jane' WHEN 2 THEN 'Michael' WHEN 3 THEN 'Sarah'
                        WHEN 4 THEN 'David' WHEN 5 THEN 'Lisa' WHEN 6 THEN 'Robert' WHEN 7 THEN 'Emily'
                        WHEN 8 THEN 'James' WHEN 9 THEN 'Jessica' WHEN 10 THEN 'William' WHEN 11 THEN 'Ashley'
                        WHEN 12 THEN 'Richard' WHEN 13 THEN 'Amanda' WHEN 14 THEN 'Charles' WHEN 15 THEN 'Stephanie'
                        WHEN 16 THEN 'Thomas' WHEN 17 THEN 'Jennifer' WHEN 18 THEN 'Christopher' ELSE 'Michelle'
                    END AS FirstName,
                    CASE ABS(CHECKSUM(NEWID())) % 20
                        WHEN 0 THEN 'Smith' WHEN 1 THEN 'Johnson' WHEN 2 THEN 'Williams' WHEN 3 THEN 'Brown'
                        WHEN 4 THEN 'Jones' WHEN 5 THEN 'Garcia' WHEN 6 THEN 'Miller' WHEN 7 THEN 'Davis'
                        WHEN 8 THEN 'Rodriguez' WHEN 9 THEN 'Martinez' WHEN 10 THEN 'Hernandez' WHEN 11 THEN 'Lopez'
                        WHEN 12 THEN 'Gonzalez' WHEN 13 THEN 'Wilson' WHEN 14 THEN 'Anderson' WHEN 15 THEN 'Thomas'
                        WHEN 16 THEN 'Taylor' WHEN 17 THEN 'Moore' WHEN 18 THEN 'Jackson' ELSE 'Martin'
                    END AS LastName,
                    CASE ABS(CHECKSUM(NEWID())) % 3
                        WHEN 0 THEN 'FleetXQ Corp' WHEN 1 THEN 'Transport Solutions' ELSE 'Logistics Inc'
                    END AS CustomerName,
                    CASE ABS(CHECKSUM(NEWID())) % 5
                        WHEN 0 THEN 'Main Site' WHEN 1 THEN 'North Branch' WHEN 2 THEN 'South Branch'
                        WHEN 3 THEN 'East Depot' ELSE 'West Terminal'
                    END AS SiteName,
                    CASE ABS(CHECKSUM(NEWID())) % 4
                        WHEN 0 THEN 'Operations' WHEN 1 THEN 'Maintenance' WHEN 2 THEN 'Logistics' ELSE 'Administration'
                    END AS DepartmentName,
                    CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 9 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS DriverActive,
                    ABS(CHECKSUM(NEWID())) % 3 AS DriverLicenseMode,
                    CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 8 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS DriverVehicleAccess
                FROM NumberSequence
            )
            INSERT INTO [Staging].[DriverImport] (
                [ImportSessionId], [RowNumber], [ExternalDriverId], [PersonFirstName], [PersonLastName],
                [PersonEmail], [DriverActive], [DriverLicenseMode], [DriverVehicleAccess],
                [PersonIsActiveDriver], [PersonHasLicense], [PersonLicenseActive], [PersonVehicleAccess],
                [PersonCanUnlockVehicle], [PersonNormalDriverAccess], [CustomerName], [SiteName], [DepartmentName]
            )
            SELECT
                @SessionId,
                RowNum,
                ExternalDriverId,
                FirstName,
                LastName,
                LOWER(FirstName + '.' + LastName + '@' + REPLACE(CustomerName, ' ', '') + '.com'),
                DriverActive,
                DriverLicenseMode,
                DriverVehicleAccess,
                DriverActive,
                DriverActive,
                DriverActive,
                DriverVehicleAccess,
                DriverVehicleAccess,
                DriverVehicleAccess,
                CustomerName,
                SiteName,
                DepartmentName
            FROM RandomData";

        using var command = new SqlCommand(sql, connection);
        command.CommandTimeout = _options.CommandTimeout;
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@Offset", offset);
        command.Parameters.AddWithValue("@BatchSize", batchSize);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    private async Task GenerateVehicleBatchAsync(SqlConnection connection, Guid sessionId, int offset, int batchSize, CancellationToken cancellationToken)
    {
        const string sql = @"
            WITH NumberSequence AS (
                SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) + @Offset AS RowNum
                FROM master.dbo.spt_values s1
                CROSS JOIN master.dbo.spt_values s2
                WHERE s1.type = 'P' AND s2.type = 'P'
                    AND ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) <= @BatchSize
            ),
            RandomData AS (
                SELECT
                    RowNum,
                    'EXT_VEH_' + RIGHT('000000' + CAST(RowNum AS VARCHAR), 6) AS ExternalVehicleId,
                    'FLT' + RIGHT('00000' + CAST(RowNum AS VARCHAR), 5) AS HireNo,
                    'SN' + RIGHT('00000' + CAST(RowNum + 50000 AS VARCHAR), 5) AS SerialNo,
                    CASE ABS(CHECKSUM(NEWID())) % 5
                        WHEN 0 THEN 'Forklift - Warehouse' WHEN 1 THEN 'Reach Truck - High Bay'
                        WHEN 2 THEN 'Pallet Truck - Floor Level' WHEN 3 THEN 'Order Picker - Multi Level'
                        ELSE 'Counterbalance - Outdoor'
                    END AS Description,
                    CASE ABS(CHECKSUM(NEWID())) % 3
                        WHEN 0 THEN 'FleetXQ Corp' WHEN 1 THEN 'Transport Solutions' ELSE 'Logistics Inc'
                    END AS CustomerName,
                    CASE ABS(CHECKSUM(NEWID())) % 5
                        WHEN 0 THEN 'Main Site' WHEN 1 THEN 'North Branch' WHEN 2 THEN 'South Branch'
                        WHEN 3 THEN 'East Depot' ELSE 'West Terminal'
                    END AS SiteName,
                    CASE ABS(CHECKSUM(NEWID())) % 4
                        WHEN 0 THEN 'Operations' WHEN 1 THEN 'Maintenance' WHEN 2 THEN 'Logistics' ELSE 'Administration'
                    END AS DepartmentName,
                    CASE ABS(CHECKSUM(NEWID())) % 4
                        WHEN 0 THEN 'Linde H25' WHEN 1 THEN 'Toyota 8FB' WHEN 2 THEN 'Crown FC5200'
                        ELSE 'Hyster J2.0XN'
                    END AS ModelName,
                    CASE ABS(CHECKSUM(NEWID())) % 4
                        WHEN 0 THEN 'Linde' WHEN 1 THEN 'Toyota' WHEN 2 THEN 'Crown' ELSE 'Hyster'
                    END AS ManufacturerName,
                    'MOD' + RIGHT('00000' + CAST(RowNum + 10000 AS VARCHAR), 5) AS ModuleSerialNumber,
                    CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 9 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS OnHire,
                    CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 2 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS ImpactLockout,
                    CASE WHEN ABS(CHECKSUM(NEWID())) % 10 < 8 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS IsCanbus
                FROM NumberSequence
            )
            INSERT INTO [Staging].[VehicleImport] (
                [ImportSessionId], [RowNumber], [ExternalVehicleId], [HireNo], [SerialNo], [Description],
                [OnHire], [ImpactLockout], [IsCanbus], [TimeoutEnabled], [ModuleIsConnected], [IDLETimer],
                [CustomerName], [SiteName], [DepartmentName], [ModelName], [ManufacturerName], [ModuleSerialNumber]
            )
            SELECT
                @SessionId,
                RowNum,
                ExternalVehicleId,
                HireNo,
                SerialNo,
                Description,
                OnHire,
                ImpactLockout,
                IsCanbus,
                CAST(1 AS BIT),
                OnHire,
                CASE WHEN IsCanbus = 1 THEN 300 ELSE 600 END,
                CustomerName,
                SiteName,
                DepartmentName,
                ModelName,
                ManufacturerName,
                ModuleSerialNumber
            FROM RandomData";

        using var command = new SqlCommand(sql, connection);
        command.CommandTimeout = _options.CommandTimeout;
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@Offset", offset);
        command.Parameters.AddWithValue("@BatchSize", batchSize);

        await command.ExecuteNonQueryAsync(cancellationToken);
    }

    public async Task UpdateImportSessionAsync(Guid sessionId, string status, int totalRows, int successfulRows, int failedRows, CancellationToken cancellationToken = default)
    {
        using var connection = new SqlConnection(_connectionOptions.FleetXQConnection);
        await connection.OpenAsync(cancellationToken);

        const string sql = @"
            UPDATE [Staging].[ImportSession]
            SET [Status] = @Status,
                [EndTime] = CASE WHEN @Status IN ('Completed', 'Failed', 'RolledBack') THEN GETUTCDATE() ELSE [EndTime] END,
                [TotalRows] = @TotalRows,
                [ProcessedRows] = @TotalRows,
                [SuccessfulRows] = @SuccessfulRows,
                [FailedRows] = @FailedRows
            WHERE [Id] = @SessionId";

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.Parameters.AddWithValue("@Status", status);
        command.Parameters.AddWithValue("@TotalRows", totalRows);
        command.Parameters.AddWithValue("@SuccessfulRows", successfulRows);
        command.Parameters.AddWithValue("@FailedRows", failedRows);

        await command.ExecuteNonQueryAsync(cancellationToken);

        _logger.LogInformation("Updated import session {SessionId}: Status={Status}, Total={TotalRows}, Success={SuccessfulRows}, Failed={FailedRows}",
            sessionId, status, totalRows, successfulRows, failedRows);
    }

    public async Task<ValidationResult> ValidateStagedDataAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult();

        using var connection = new SqlConnection(_connectionOptions.FleetXQConnection);
        await connection.OpenAsync(cancellationToken);

        // Call validation stored procedures (these would be implemented in the SQL scripts)
        using var command = new SqlCommand("EXEC [Staging].[ValidateImportData] @SessionId", connection);
        command.CommandTimeout = _options.CommandTimeout;
        command.Parameters.AddWithValue("@SessionId", sessionId);

        using var reader = await command.ExecuteReaderAsync(cancellationToken);

        if (await reader.ReadAsync(cancellationToken))
        {
            result.ValidRows = reader.GetInt32("ValidRows");
            result.InvalidRows = reader.GetInt32("InvalidRows");
            result.Success = result.InvalidRows == 0;
            result.Summary = $"Validation completed: {result.ValidRows} valid, {result.InvalidRows} invalid";
        }

        _logger.LogInformation("Data validation completed for session {SessionId}: {ValidRows} valid, {InvalidRows} invalid",
            sessionId, result.ValidRows, result.InvalidRows);

        return result;
    }

    public async Task<ProcessingResult> ProcessStagedDataAsync(Guid sessionId, bool dryRun = false, CancellationToken cancellationToken = default)
    {
        var result = new ProcessingResult();

        using var connection = new SqlConnection(_connectionOptions.FleetXQConnection);
        await connection.OpenAsync(cancellationToken);

        // Call merge stored procedures (these would be implemented in the SQL scripts)
        var procedureName = dryRun ? "[Staging].[ProcessImportDataDryRun]" : "[Staging].[ProcessImportData]";
        using var command = new SqlCommand($"EXEC {procedureName} @SessionId", connection);
        command.CommandTimeout = _options.CommandTimeout;
        command.Parameters.AddWithValue("@SessionId", sessionId);

        using var reader = await command.ExecuteReaderAsync(cancellationToken);

        if (await reader.ReadAsync(cancellationToken))
        {
            result.ProcessedRows = reader.GetInt32("ProcessedRows");
            result.InsertedRows = reader.GetInt32("InsertedRows");
            result.UpdatedRows = reader.GetInt32("UpdatedRows");
            result.SkippedRows = reader.GetInt32("SkippedRows");
            result.Success = true;
            result.Summary = $"Processing completed: {result.InsertedRows} inserted, {result.UpdatedRows} updated, {result.SkippedRows} skipped";
        }

        _logger.LogInformation("Data processing completed for session {SessionId}: {InsertedRows} inserted, {UpdatedRows} updated, {SkippedRows} skipped",
            sessionId, result.InsertedRows, result.UpdatedRows, result.SkippedRows);

        return result;
    }
}
