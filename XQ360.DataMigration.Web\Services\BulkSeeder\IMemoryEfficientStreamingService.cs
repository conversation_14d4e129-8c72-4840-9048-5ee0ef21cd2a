using Microsoft.Data.SqlClient;
using System.Runtime.CompilerServices;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service interface for memory-efficient data streaming operations
/// Implementation of Phase 3.1.3: Memory-Efficient Data Streaming
/// </summary>
public interface IMemoryEfficientStreamingService
{
    /// <summary>
    /// Streams data from database using IAsyncEnumerable to avoid loading large datasets into memory
    /// </summary>
    /// <typeparam name="T">Type of data to stream</typeparam>
    /// <param name="query">SQL query to execute</param>
    /// <param name="mapper">Function to map SqlDataReader to object</param>
    /// <param name="options">Streaming options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Async enumerable of data</returns>
    IAsyncEnumerable<T> StreamDataFromDatabaseAsync<T>(
        string query,
        Func<SqlDataReader, T> mapper,
        StreamingOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Streams and transforms data using yield return patterns for memory efficiency
    /// </summary>
    /// <typeparam name="TInput">Input data type</typeparam>
    /// <typeparam name="TOutput">Output data type</typeparam>
    /// <param name="inputStream">Input async stream</param>
    /// <param name="transformer">Transformation function</param>
    /// <param name="options">Transform options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Async enumerable of transformed data</returns>
    IAsyncEnumerable<TOutput> StreamTransformDataAsync<TInput, TOutput>(
        IAsyncEnumerable<TInput> inputStream,
        Func<TInput, CancellationToken, Task<TOutput>> transformer,
        StreamingTransformOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes data in batches using streaming patterns
    /// </summary>
    /// <typeparam name="T">Data type</typeparam>
    /// <param name="inputStream">Input stream</param>
    /// <param name="batchProcessor">Batch processing function</param>
    /// <param name="batchSize">Size of each batch</param>
    /// <param name="options">Batch processing options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Async enumerable of processed data</returns>
    IAsyncEnumerable<T> StreamBatchProcessingAsync<T>(
        IAsyncEnumerable<T> inputStream,
        Func<IEnumerable<T>, CancellationToken, Task<IEnumerable<T>>> batchProcessor,
        int batchSize,
        StreamingBatchOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Streams data with memory pressure monitoring and automatic optimization
    /// </summary>
    /// <typeparam name="T">Data type</typeparam>
    /// <param name="inputStream">Input stream</param>
    /// <param name="options">Memory pressure options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Memory-optimized async enumerable</returns>
    IAsyncEnumerable<T> StreamWithMemoryPressureMonitoringAsync<T>(
        IAsyncEnumerable<T> inputStream,
        MemoryPressureOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current streaming performance metrics
    /// </summary>
    /// <returns>Streaming metrics</returns>
    Task<StreamingMetrics> GetStreamingMetricsAsync();
}
