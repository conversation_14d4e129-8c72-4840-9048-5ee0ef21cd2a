import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Dealer, DealerSearchFilter, DealerSearchResult } from '@/types/dealer'
import { getDealerService } from '@/services'
import type { DealerListRequest, DealerSearchRequest, DealerValidationResponse } from '@/services'

export const useDealerStore = defineStore('dealer', () => {
    // Services
    const dealerService = getDealerService()

    // State
    const dealers = ref<Dealer[]>([])
    const selectedDealer = ref<Dealer | null>(null)
    const searchResults = ref<DealerSearchResult | null>(null)
    const recentDealers = ref<Dealer[]>([])
    const loading = ref(false)
    const error = ref<string | null>(null)
    const lastFetch = ref<Date | null>(null)
    const validationCache = ref<Map<string, DealerValidationResponse>>(new Map())

    // Getters
    const activeDealers = computed(() =>
        dealers.value.filter(dealer => dealer.active)
    )

    const dealerById = computed(() => (id: string) =>
        dealers.value.find(dealer => dealer.id.toString() === id)
    )

    const hasSelectedDealer = computed(() =>
        selectedDealer.value !== null
    )

    const selectedDealerName = computed(() =>
        selectedDealer.value?.name || ''
    )

    const selectedDealerSubdomain = computed(() =>
        selectedDealer.value?.subDomain || ''
    )

    const isDataStale = computed(() => {
        if (!lastFetch.value) return true
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)
        return lastFetch.value < tenMinutesAgo
    })

    // Actions
    const fetchDealers = async (request: DealerListRequest = {}, force = false) => {
        if (!force && !isDataStale.value && dealers.value.length > 0) {
            return
        }

        loading.value = true
        error.value = null

        try {
            const response = await dealerService.getDealers(request)

            // Convert to our internal format
            const result: DealerSearchResult = {
                dealers: response.dealers,
                total: response.totalCount,
                totalCount: response.totalCount,
                pageNumber: response.pageNumber,
                pageSize: response.pageSize,
                totalPages: response.totalPages,
                hasMore: response.pageNumber < response.totalPages
            }

            searchResults.value = result
            lastFetch.value = new Date()

            // Update dealers array with unique entries
            const existingIds = new Set(dealers.value.map(d => d.id))
            const newDealers = response.dealers.filter(d => !existingIds.has(d.id))
            dealers.value = [...dealers.value, ...newDealers]

        } catch (err: any) {
            error.value = err.message || 'Failed to fetch dealers'
            console.error('Error fetching dealers:', err)
        } finally {
            loading.value = false
        }
    }

    const searchDealers = async (query: string, limit = 10) => {
        if (!query.trim()) {
            searchResults.value = null
            return
        }

        loading.value = true
        error.value = null

        try {
            const request: DealerSearchRequest = {
                query: query.trim(),
                limit,
                activeOnly: true
            }

            const searchedDealers = await dealerService.searchDealers(request)

            // Convert to search result format
            searchResults.value = {
                dealers: searchedDealers,
                total: searchedDealers.length,
                totalCount: searchedDealers.length,
                pageNumber: 1,
                pageSize: limit,
                totalPages: 1,
                hasMore: false
            }

            // Update dealers array with unique entries
            const existingIds = new Set(dealers.value.map(d => d.id))
            const newDealers = searchedDealers.filter(d => !existingIds.has(d.id))
            dealers.value = [...dealers.value, ...newDealers]

        } catch (err: any) {
            error.value = err.message || 'Failed to search dealers'
            console.error('Error searching dealers:', err)
        } finally {
            loading.value = false
        }
    }

    const getDealerById = async (id: string): Promise<Dealer | null> => {
        // Check if already in store
        const cached = dealerById.value(id)
        if (cached) {
            return cached
        }

        loading.value = true
        error.value = null

        try {
            const dealer = await dealerService.getDealerById(id)

            // Add to store
            const index = dealers.value.findIndex(d => d.id === dealer.id)
            if (index >= 0) {
                dealers.value[index] = dealer
            } else {
                dealers.value.push(dealer)
            }

            return dealer

        } catch (err: any) {
            error.value = err.message || 'Failed to fetch dealer'
            console.error('Error fetching dealer:', err)
            return null
        } finally {
            loading.value = false
        }
    }

    const validateDealer = async (id: string): Promise<DealerValidationResponse> => {
        // Check cache first
        if (validationCache.value.has(id)) {
            return validationCache.value.get(id)!
        }

        try {
            const validation = await dealerService.validateDealer(id)

            // Cache the result for 5 minutes
            validationCache.value.set(id, validation)
            setTimeout(() => {
                validationCache.value.delete(id)
            }, 5 * 60 * 1000)

            return validation
        } catch (err: any) {
            console.error('Error validating dealer:', err)
            throw err
        }
    }

    const getDealerBySubdomain = async (subdomain: string): Promise<Dealer | null> => {
        try {
            return await dealerService.getDealerBySubdomain(subdomain)
        } catch (err: any) {
            console.error('Error fetching dealer by subdomain:', err)
            return null
        }
    }

    const setSelectedDealer = (dealer: Dealer | null) => {
        selectedDealer.value = dealer
        if (dealer) {
            localStorage.setItem('selectedDealer', JSON.stringify(dealer))
            dealerService.addToRecentDealers(dealer.id.toString())
        } else {
            localStorage.removeItem('selectedDealer')
        }

        // Clear validation cache when dealer changes
        validationCache.value.clear()
    }

    const loadSavedDealer = () => {
        const saved = localStorage.getItem('selectedDealer')
        if (saved) {
            try {
                selectedDealer.value = JSON.parse(saved)
            } catch (err) {
                console.error('Error loading saved dealer:', err)
                localStorage.removeItem('selectedDealer')
            }
        }
    }

    const clearDealer = () => {
        selectedDealer.value = null
        localStorage.removeItem('selectedDealer')
        validationCache.value.clear()
    }

    const clearCache = () => {
        dealers.value = []
        searchResults.value = null
        recentDealers.value = []
        error.value = null
        validationCache.value.clear()
        lastFetch.value = null
    }

    const loadRecentDealers = async () => {
        try {
            recentDealers.value = await dealerService.getRecentDealers(5)
        } catch (err: any) {
            console.error('Error loading recent dealers:', err)
        }
    }

    const checkDealerExists = async (nameOrSubdomain: string): Promise<boolean> => {
        try {
            return await dealerService.dealerExists(nameOrSubdomain)
        } catch (err: any) {
            console.error('Error checking if dealer exists:', err)
            return false
        }
    }

    const getDealerStats = async (dealerId: string) => {
        try {
            return await dealerService.getDealerStats(dealerId)
        } catch (err: any) {
            console.error('Error getting dealer stats:', err)
            return {
                totalCustomers: 0,
                activeCustomers: 0,
                totalVehicles: 0,
                totalDrivers: 0
            }
        }
    }

    return {
        // State
        dealers,
        selectedDealer,
        searchResults,
        recentDealers,
        loading,
        error,
        lastFetch,
        validationCache,

        // Getters
        activeDealers,
        dealerById,
        hasSelectedDealer,
        selectedDealerName,
        selectedDealerSubdomain,
        isDataStale,

        // Actions
        fetchDealers,
        searchDealers,
        getDealerById,
        validateDealer,
        getDealerBySubdomain,
        setSelectedDealer,
        loadSavedDealer,
        clearDealer,
        clearCache,
        loadRecentDealers,
        checkDealerExists,
        getDealerStats
    }
})
