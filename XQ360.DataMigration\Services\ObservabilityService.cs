using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Services
{
    /// <summary>
    /// Service to manage conditional observability features based on configuration
    /// </summary>
    public interface IObservabilityService
    {
        /// <summary>
        /// Check if logging is enabled
        /// </summary>
        bool IsLoggingEnabled { get; }

        /// <summary>
        /// Check if performance logging is enabled
        /// </summary>
        bool IsPerformanceLoggingEnabled { get; }

        /// <summary>
        /// Check if error logging is enabled
        /// </summary>
        bool IsErrorLoggingEnabled { get; }

        /// <summary>
        /// Check if debug logging is enabled
        /// </summary>
        bool IsDebugLoggingEnabled { get; }

        /// <summary>
        /// Check if monitoring is enabled
        /// </summary>
        bool IsMonitoringEnabled { get; }

        /// <summary>
        /// Check if metrics collection is enabled
        /// </summary>
        bool IsMetricsEnabled { get; }

        /// <summary>
        /// Check if health checks are enabled
        /// </summary>
        bool IsHealthChecksEnabled { get; }

        /// <summary>
        /// Check if tracing is enabled
        /// </summary>
        bool IsTracingEnabled { get; }

        /// <summary>
        /// Check if alerting is enabled
        /// </summary>
        bool IsAlertingEnabled { get; }

        /// <summary>
        /// Check if audit trail is enabled
        /// </summary>
        bool IsAuditEnabled { get; }

        /// <summary>
        /// Check if reporting is enabled
        /// </summary>
        bool IsReportingEnabled { get; }

        /// <summary>
        /// Log information conditionally based on configuration
        /// </summary>
        void LogInformation(ILogger logger, string message, params object[] args);

        /// <summary>
        /// Log warning conditionally based on configuration
        /// </summary>
        void LogWarning(ILogger logger, string message, params object[] args);

        /// <summary>
        /// Log error conditionally based on configuration
        /// </summary>
        void LogError(ILogger logger, Exception? exception, string message, params object[] args);

        /// <summary>
        /// Log debug conditionally based on configuration
        /// </summary>
        void LogDebug(ILogger logger, string message, params object[] args);

        /// <summary>
        /// Execute an action only if observability feature is enabled
        /// </summary>
        void ExecuteIfEnabled(ObservabilityFeature feature, Action action);

        /// <summary>
        /// Execute an async action only if observability feature is enabled
        /// </summary>
        Task ExecuteIfEnabledAsync(ObservabilityFeature feature, Func<Task> action);

        /// <summary>
        /// Get the observability configuration
        /// </summary>
        ObservabilityConfiguration Configuration { get; }
    }

    /// <summary>
    /// Enumeration of observability features
    /// </summary>
    public enum ObservabilityFeature
    {
        Logging,
        Monitoring,
        Metrics,
        HealthChecks,
        Tracing,
        Alerting,
        Audit,
        Reporting
    }

    /// <summary>
    /// Implementation of the observability service
    /// </summary>
    public class ObservabilityService : IObservabilityService
    {
        private readonly ObservabilityConfiguration _config;

        public ObservabilityService(IOptions<ObservabilityConfiguration> config)
        {
            _config = config.Value;
        }

        public ObservabilityConfiguration Configuration => _config;

        public bool IsLoggingEnabled => _config.Logging.Enabled;
        public bool IsPerformanceLoggingEnabled => _config.Logging.Enabled && _config.Logging.EnablePerformanceLogging;
        public bool IsErrorLoggingEnabled => _config.Logging.Enabled && _config.Logging.EnableErrorLogging;
        public bool IsDebugLoggingEnabled => _config.Logging.Enabled && _config.Logging.EnableDebugLogging;
        public bool IsMonitoringEnabled => _config.Monitoring.Enabled;
        public bool IsMetricsEnabled => _config.Metrics.Enabled;
        public bool IsHealthChecksEnabled => _config.HealthChecks.Enabled;
        public bool IsTracingEnabled => _config.Tracing.Enabled;
        public bool IsAlertingEnabled => _config.Alerting.Enabled;
        public bool IsAuditEnabled => _config.Audit.Enabled;
        public bool IsReportingEnabled => _config.Reporting.Enabled;

        public void LogInformation(ILogger logger, string message, params object[] args)
        {
            if (IsLoggingEnabled)
            {
                logger.LogInformation(message, args);
            }
        }

        public void LogWarning(ILogger logger, string message, params object[] args)
        {
            if (IsLoggingEnabled)
            {
                logger.LogWarning(message, args);
            }
        }

        public void LogError(ILogger logger, Exception? exception, string message, params object[] args)
        {
            if (IsErrorLoggingEnabled)
            {
                if (exception != null)
                {
                    logger.LogError(exception, message, args);
                }
                else
                {
                    logger.LogError(message, args);
                }
            }
        }

        public void LogDebug(ILogger logger, string message, params object[] args)
        {
            if (IsDebugLoggingEnabled)
            {
                logger.LogDebug(message, args);
            }
        }

        public void ExecuteIfEnabled(ObservabilityFeature feature, Action action)
        {
            if (IsFeatureEnabled(feature))
            {
                try
                {
                    action();
                }
                catch (Exception ex)
                {
                    // Silently handle observability failures to prevent them from breaking the main application flow
                    if (IsErrorLoggingEnabled)
                    {
                        // Use a minimal logger to avoid recursion
                        System.Diagnostics.Debug.WriteLine($"Observability feature {feature} failed: {ex.Message}");
                    }
                }
            }
        }

        public async Task ExecuteIfEnabledAsync(ObservabilityFeature feature, Func<Task> action)
        {
            if (IsFeatureEnabled(feature))
            {
                try
                {
                    await action();
                }
                catch (Exception ex)
                {
                    // Silently handle observability failures to prevent them from breaking the main application flow
                    if (IsErrorLoggingEnabled)
                    {
                        // Use a minimal logger to avoid recursion
                        System.Diagnostics.Debug.WriteLine($"Observability feature {feature} failed: {ex.Message}");
                    }
                }
            }
        }

        private bool IsFeatureEnabled(ObservabilityFeature feature)
        {
            return feature switch
            {
                ObservabilityFeature.Logging => IsLoggingEnabled,
                ObservabilityFeature.Monitoring => IsMonitoringEnabled,
                ObservabilityFeature.Metrics => IsMetricsEnabled,
                ObservabilityFeature.HealthChecks => IsHealthChecksEnabled,
                ObservabilityFeature.Tracing => IsTracingEnabled,
                ObservabilityFeature.Alerting => IsAlertingEnabled,
                ObservabilityFeature.Audit => IsAuditEnabled,
                ObservabilityFeature.Reporting => IsReportingEnabled,
                _ => false
            };
        }
    }
}
