<template>
  <div class="customer-selector">
    <div class="mb-3">
      <label for="customer" class="form-label">Customer</label>
      <select 
        id="customer" 
        class="form-select" 
        :class="{ 'is-invalid': validationError }"
        v-model="selectedCustomerId"
        @change="onCustomerChange"
        :disabled="loading || !dealerId"
      >
        <option value="">Select a customer...</option>
        <option 
          v-for="customer in availableCustomers" 
          :key="customer.id" 
          :value="customer.id"
        >
          {{ customer.name }}
        </option>
        <option value="new">+ Create New Customer</option>
      </select>
      
      <div v-if="validationError" class="invalid-feedback">
        {{ validationError }}
      </div>
      
      <div v-if="loading" class="form-text">
        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        Loading customers...
      </div>
      
      <div v-if="!dealerId" class="form-text text-muted">
        Please select a dealer first to load customers.
      </div>
    </div>

    <!-- No Customers Found State -->
    <div v-if="!loading && dealerId && availableCustomers.length === 0" class="alert alert-light border">
      <div class="d-flex align-items-center mb-2">
        <i class="fas fa-info-circle me-2"></i>
        <strong>No Customers Found</strong>
      </div>
      <p class="mb-2">No customers were found for the selected dealer. You can create a new customer to proceed.</p>
      <button 
        type="button" 
        class="btn btn-dark btn-sm"
        @click="showCreateForm"
      >
        <i class="fas fa-plus me-1"></i>
        Create New Customer
      </button>
    </div>

    <!-- Create New Customer Form -->
    <div v-if="selectedCustomerId === 'new'" class="create-customer-form mt-3">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
              <i class="fas fa-plus me-2"></i>
              Create New Customer
            </h6>
            <button 
              type="button" 
              class="btn-close"
              @click="cancelCreate"
              aria-label="Close"
            ></button>
          </div>
        </div>
        <div class="card-body">
          <form @submit.prevent="createCustomer">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="customerName" class="form-label">Customer Name *</label>
                <input 
                  type="text" 
                  id="customerName" 
                  class="form-control" 
                  :class="{ 'is-invalid': formErrors.name }"
                  v-model="newCustomer.name"
                  required
                  :disabled="creating"
                >
                <div v-if="formErrors.name" class="invalid-feedback">
                  {{ formErrors.name }}
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="contactName" class="form-label">Contact Name *</label>
                <input 
                  type="text" 
                  id="contactName" 
                  class="form-control" 
                  :class="{ 'is-invalid': formErrors.contactName }"
                  v-model="newCustomer.contactName"
                  required
                  :disabled="creating"
                >
                <div v-if="formErrors.contactName" class="invalid-feedback">
                  {{ formErrors.contactName }}
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="contactEmail" class="form-label">Contact Email *</label>
                <input 
                  type="email" 
                  id="contactEmail" 
                  class="form-control" 
                  :class="{ 'is-invalid': formErrors.contactEmail }"
                  v-model="newCustomer.contactEmail"
                  required
                  :disabled="creating"
                >
                <div v-if="formErrors.contactEmail" class="invalid-feedback">
                  {{ formErrors.contactEmail }}
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="contactPhone" class="form-label">Contact Phone</label>
                <input 
                  type="tel" 
                  id="contactPhone" 
                  class="form-control" 
                  v-model="newCustomer.contactPhone"
                  :disabled="creating"
                >
              </div>
            </div>
            
            <!-- Address Section -->
            <div class="address-section mb-3">
              <h6 class="border-bottom pb-2 mb-3">Address Information (Optional)</h6>
              
              <div class="row">
                <div class="col-12 mb-3">
                  <label for="street" class="form-label">Street Address</label>
                  <input 
                    type="text" 
                    id="street" 
                    class="form-control" 
                    v-model="newCustomer.address.street"
                    :disabled="creating"
                  >
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-4 mb-3">
                  <label for="city" class="form-label">City</label>
                  <input 
                    type="text" 
                    id="city" 
                    class="form-control" 
                    v-model="newCustomer.address.city"
                    :disabled="creating"
                  >
                </div>
                
                <div class="col-md-4 mb-3">
                  <label for="state" class="form-label">State</label>
                  <input 
                    type="text" 
                    id="state" 
                    class="form-control" 
                    v-model="newCustomer.address.state"
                    :disabled="creating"
                  >
                </div>
                
                <div class="col-md-4 mb-3">
                  <label for="postalCode" class="form-label">Postal Code</label>
                  <input 
                    type="text" 
                    id="postalCode" 
                    class="form-control" 
                    v-model="newCustomer.address.postalCode"
                    :disabled="creating"
                  >
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="country" class="form-label">Country</label>
                  <input 
                    type="text" 
                    id="country" 
                    class="form-control" 
                    v-model="newCustomer.address.country"
                    :disabled="creating"
                    placeholder="e.g., United States"
                  >
                </div>
              </div>
            </div>
            
            <div class="form-actions d-flex justify-content-end gap-2">
              <button 
                type="button" 
                class="btn btn-outline-secondary"
                @click="cancelCreate"
                :disabled="creating"
              >
                Cancel
              </button>
              <button 
                type="submit" 
                class="btn btn-dark"
                :disabled="creating || !canCreateCustomer"
              >
                <span v-if="creating" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                <i v-else class="fas fa-save me-1"></i>
                {{ creating ? 'Creating...' : 'Create Customer' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Selected Customer Display -->
    <div v-if="selectedCustomer && selectedCustomerId !== 'new'" class="selected-customer mt-3">
      <div class="alert alert-light border">
        <div class="d-flex align-items-center mb-2">
          <i class="fas fa-check-circle me-2"></i>
          <strong>Customer Selected</strong>
        </div>
        
        <div class="customer-details">
          <div class="row">
            <div class="col-md-8">
              <h6 class="mb-1">{{ selectedCustomer.name }}</h6>
              <p class="text-muted mb-1">
                <i class="fas fa-user me-1"></i>
                {{ selectedCustomer.contactName }}
              </p>
              <p class="text-muted mb-1">
                <i class="fas fa-envelope me-1"></i>
                {{ selectedCustomer.contactEmail }}
              </p>
              <p v-if="selectedCustomer.contactPhone" class="text-muted mb-0">
                <i class="fas fa-phone me-1"></i>
                {{ selectedCustomer.contactPhone }}
              </p>
            </div>
            <div class="col-md-4 text-end">
              <span 
                class="badge fs-6" 
                :class="selectedCustomer.isActive ? 'bg-dark text-white' : 'bg-secondary'"
              >
                {{ selectedCustomer.isActive ? 'Active' : 'Inactive' }}
              </span>
              <div class="mt-2">
                <small class="text-muted">ID: {{ selectedCustomer.id }}</small>
              </div>
            </div>
          </div>
          
          <div v-if="selectedCustomer.address" class="customer-address mt-2 pt-2 border-top">
            <small class="text-muted">
              <i class="fas fa-map-marker-alt me-1"></i>
              {{ formatAddress(selectedCustomer.address) }}
            </small>
          </div>
          
          <div class="customer-actions mt-2 pt-2 border-top">
            <button 
              type="button" 
              class="btn btn-outline-secondary btn-sm"
              @click="clearSelection"
            >
              <i class="fas fa-times me-1"></i>
              Change Customer
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useCustomerStore } from '@/stores/customer'
import type { Customer, CreateCustomerRequest } from '@/types/customer'

// Store
const customerStore = useCustomerStore()

// Props
const props = defineProps<{
  modelValue?: Customer | null
  dealerId?: number | null
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Customer | null]
  'validation-change': [isValid: boolean]
}>()

// Local state
const selectedCustomerId = ref<string | number>('')
const validationError = ref('')
const formErrors = ref<Record<string, string>>({})
const newCustomer = ref<CreateCustomerRequest & { address: any }>({
  dealerId: 0,
  name: '',
  contactName: '',
  contactEmail: '',
  contactPhone: '',
  address: {
    street: '',
    city: '',
    state: '',
    postalCode: '',
    country: ''
  }
})

// Computed properties
const loading = computed(() => customerStore.loading)
const creating = computed(() => customerStore.creating)
const selectedCustomer = computed(() => customerStore.selectedCustomer)
const availableCustomers = computed(() => {
  if (!props.dealerId) return []
  return customerStore.customersByDealer(props.dealerId)
})

const dealerId = computed(() => props.dealerId)

const canCreateCustomer = computed(() => {
  return newCustomer.value.name.trim() !== '' &&
         newCustomer.value.contactName.trim() !== '' &&
         newCustomer.value.contactEmail.trim() !== '' &&
         isValidEmail(newCustomer.value.contactEmail)
})

const isValid = computed(() => {
  if (selectedCustomerId.value === 'new') {
    return canCreateCustomer.value
  }
  return selectedCustomer.value !== null && !validationError.value
})

// Methods
const onCustomerChange = () => {
  validationError.value = ''
  
  if (selectedCustomerId.value === '') {
    customerStore.clearCustomer()
    emit('update:modelValue', null)
    return
  }
  
  if (selectedCustomerId.value === 'new') {
    customerStore.clearCustomer()
    emit('update:modelValue', null)
    resetNewCustomerForm()
    return
  }
  
  const customer = availableCustomers.value.find(c => c.id === Number(selectedCustomerId.value))
  if (customer) {
    customerStore.setSelectedCustomer(customer)
    emit('update:modelValue', customer)
  }
}

const showCreateForm = () => {
  selectedCustomerId.value = 'new'
  resetNewCustomerForm()
}

const cancelCreate = () => {
  selectedCustomerId.value = ''
  resetNewCustomerForm()
  customerStore.clearCustomer()
  emit('update:modelValue', null)
}

const resetNewCustomerForm = () => {
  if (props.dealerId) {
    newCustomer.value.dealerId = props.dealerId
  }
  newCustomer.value.name = ''
  newCustomer.value.contactName = ''
  newCustomer.value.contactEmail = ''
  newCustomer.value.contactPhone = ''
  newCustomer.value.address = {
    street: '',
    city: '',
    state: '',
    postalCode: '',
    country: ''
  }
  formErrors.value = {}
}

const createCustomer = async () => {
  if (!validateForm()) return
  
  try {
    const customerData: CreateCustomerRequest = {
      dealerId: props.dealerId!,
      name: newCustomer.value.name.trim(),
      contactName: newCustomer.value.contactName.trim(),
      contactEmail: newCustomer.value.contactEmail.trim(),
      contactPhone: newCustomer.value.contactPhone?.trim() || undefined,
      address: hasAddress() ? {
        street: newCustomer.value.address.street.trim(),
        city: newCustomer.value.address.city.trim(),
        state: newCustomer.value.address.state.trim(),
        postalCode: newCustomer.value.address.postalCode.trim(),
        country: newCustomer.value.address.country.trim()
      } : undefined
    }
    
    const createdCustomer = await customerStore.createCustomer(customerData)
    
    if (createdCustomer) {
      selectedCustomerId.value = createdCustomer.id
      customerStore.setSelectedCustomer(createdCustomer)
      emit('update:modelValue', createdCustomer)
    } else {
      validationError.value = 'Failed to create customer. Please try again.'
    }
  } catch (error) {
    validationError.value = 'Failed to create customer. Please check your input and try again.'
    console.error('Error creating customer:', error)
  }
}

const validateForm = () => {
  formErrors.value = {}
  
  if (!newCustomer.value.name.trim()) {
    formErrors.value.name = 'Customer name is required'
  }
  
  if (!newCustomer.value.contactName.trim()) {
    formErrors.value.contactName = 'Contact name is required'
  }
  
  if (!newCustomer.value.contactEmail.trim()) {
    formErrors.value.contactEmail = 'Contact email is required'
  } else if (!isValidEmail(newCustomer.value.contactEmail)) {
    formErrors.value.contactEmail = 'Please enter a valid email address'
  }
  
  return Object.keys(formErrors.value).length === 0
}

const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const hasAddress = () => {
  return newCustomer.value.address.street.trim() !== '' ||
         newCustomer.value.address.city.trim() !== '' ||
         newCustomer.value.address.state.trim() !== '' ||
         newCustomer.value.address.postalCode.trim() !== '' ||
         newCustomer.value.address.country.trim() !== ''
}

const clearSelection = () => {
  selectedCustomerId.value = ''
  customerStore.clearCustomer()
  emit('update:modelValue', null)
  validationError.value = ''
}

const formatAddress = (address: any) => {
  if (!address) return ''
  const parts = [address.street, address.city, address.state, address.postalCode, address.country]
  return parts.filter(part => part && part.trim()).join(', ')
}

const validateCustomer = () => {
  if (selectedCustomerId.value === 'new') {
    return canCreateCustomer.value
  }
  
  if (!selectedCustomer.value) {
    validationError.value = 'Customer selection is required'
    return false
  }
  
  validationError.value = ''
  return true
}

// Watchers
watch(isValid, (newValue) => {
  emit('validation-change', newValue)
})

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedCustomerId.value = newValue.id
    customerStore.setSelectedCustomer(newValue)
  } else {
    selectedCustomerId.value = ''
    customerStore.clearCustomer()
  }
})

watch(() => props.dealerId, async (newDealerId) => {
  if (newDealerId) {
    // Reset selection when dealer changes
    clearSelection()
    
    // Load customers for new dealer
    await customerStore.fetchCustomersByDealer(newDealerId)
    
    // Update new customer form dealer ID
    newCustomer.value.dealerId = newDealerId
  } else {
    clearSelection()
    customerStore.clearCache()
  }
})

// Lifecycle
onMounted(() => {
  // Load saved customer
  customerStore.loadSavedCustomer()
  
  // Set initial value if there's a saved customer
  if (selectedCustomer.value) {
    selectedCustomerId.value = selectedCustomer.value.id
    emit('update:modelValue', selectedCustomer.value)
  }
  
  // Load customers if dealer is already selected
  if (props.dealerId) {
    customerStore.fetchCustomersByDealer(props.dealerId)
    newCustomer.value.dealerId = props.dealerId
  }
})

// Expose validation method for parent components
defineExpose({
  validate: validateCustomer,
  isValid
})
</script>

<style scoped>
.customer-selector {
  .spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
  }
  
  .create-customer-form {
    .card {
      border-left: 4px solid var(--bs-primary);
    }
    
    .address-section {
      background-color: rgba(var(--bs-secondary-rgb), 0.05);
      padding: 1rem;
      border-radius: 0.25rem;
    }
    
    .form-actions {
      padding-top: 1rem;
      border-top: 1px solid var(--bs-border-color);
    }
  }
  
  .selected-customer {
    .alert {
      border-left: 4px solid var(--bs-success);
    }
    
    .customer-details {
      .badge {
        font-size: 0.75rem;
      }
    }
  }
}
</style>
