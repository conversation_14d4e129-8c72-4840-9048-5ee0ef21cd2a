# FleetXQ BulkImporter Docker Image
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app

# Install SQL Server tools for database connectivity
RUN apt-get update \
    && apt-get install -y curl gnupg2 \
    && curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql18 mssql-tools18 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create application directories
RUN mkdir -p /app/data/output \
    && mkdir -p /app/data/archive \
    && mkdir -p /app/data/errors \
    && mkdir -p /app/logs

# Create non-root user for security
RUN groupadd -r fleetxq && useradd -r -g fleetxq -d /app -s /bin/bash fleetxq \
    && chown -R fleetxq:fleetxq /app

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src

# Copy project file and restore dependencies
COPY ["BulkImporter.csproj", "."]
RUN dotnet restore "BulkImporter.csproj"

# Copy source code and build
COPY . .
RUN dotnet build "BulkImporter.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "BulkImporter.csproj" -c Release -o /app/publish --self-contained false

FROM base AS final
WORKDIR /app

# Copy published application
COPY --from=publish /app/publish .

# Set ownership
RUN chown -R fleetxq:fleetxq /app

# Switch to non-root user
USER fleetxq

# Environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Entry point
ENTRYPOINT ["dotnet", "FleetXQ.Tools.BulkImporter.dll"]
