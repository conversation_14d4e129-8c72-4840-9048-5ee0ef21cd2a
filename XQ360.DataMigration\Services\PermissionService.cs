using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using XQ360.DataMigration.Interfaces;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Services
{
    public class PermissionService : IPermissionService
    {
        private readonly string _connectionString;
        private readonly ILogger<PermissionService> _logger;
        private Guid? _normalDriverPermissionId;
        private Guid? _supervisorPermissionId;
        private readonly object _lockObject = new object();

        public PermissionService(IOptions<MigrationConfiguration> config, ILogger<PermissionService> logger)
        {
            _connectionString = config.Value.DatabaseConnection;
            _logger = logger;
        }

        public async Task<Guid> GetNormalDriverPermissionIdAsync()
        {
            if (_normalDriverPermissionId.HasValue)
                return _normalDriverPermissionId.Value;

            lock (_lockObject)
            {
                if (_normalDriverPermissionId.HasValue)
                    return _normalDriverPermissionId.Value;
            }

            var permissionId = await FetchPermissionIdByDescriptionAsync("Normal driver");
            
            lock (_lockObject)
            {
                _normalDriverPermissionId = permissionId;
            }

            _logger.LogInformation($"Retrieved Normal Driver Permission ID: {permissionId}");
            return permissionId;
        }

        public async Task<Guid> GetSupervisorPermissionIdAsync()
        {
            if (_supervisorPermissionId.HasValue)
                return _supervisorPermissionId.Value;

            lock (_lockObject)
            {
                if (_supervisorPermissionId.HasValue)
                    return _supervisorPermissionId.Value;
            }

            var permissionId = await FetchPermissionIdByDescriptionAsync("Master");
            
            lock (_lockObject)
            {
                _supervisorPermissionId = permissionId;
            }

            _logger.LogInformation($"Retrieved Supervisor Permission ID: {permissionId}");
            return permissionId;
        }

        private async Task<Guid> FetchPermissionIdByDescriptionAsync(string description)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var sql = "SELECT Id FROM [dbo].[Permission] WHERE Description = @Description";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@Description", description);

            var result = await cmd.ExecuteScalarAsync();
            
            if (result == null)
            {
                throw new InvalidOperationException($"Permission with description '{description}' not found in dbo.Permission table");
            }

            return (Guid)result;
        }
    }
} 