﻿using System;
using System.Collections.Generic;

namespace XQ360.DataMigration.Models
{
    public class MigrationConfiguration
    {
        public required string DatabaseConnection { get; set; }
        public required string ApiBaseUrl { get; set; }
        public required string ApiUsername { get; set; }
        public required string ApiPassword { get; set; }
        public int BatchSize { get; set; } = 100;
        public int MaxRetryAttempts { get; set; } = 3;
        public bool BackupEnabled { get; set; } = true;
        public bool ValidateBeforeMigration { get; set; } = true;
        public bool ContinueOnError { get; set; } = false;
    }

    public class EnvironmentConfiguration
    {
        public Dictionary<string, EnvironmentSettings> Environments { get; set; } = new();
        public int BatchSize { get; set; } = 100;
        public int MaxRetryAttempts { get; set; } = 3;
        public bool BackupEnabled { get; set; } = true;
        public bool ValidateBeforeMigration { get; set; } = true;
        public bool ContinueOnError { get; set; } = false;
    }

    public class EnvironmentSettings
    {
        public required string Name { get; set; }
        public required string DatabaseConnection { get; set; }
        public required string ApiBaseUrl { get; set; }
        public required string ApiUsername { get; set; }
        public required string ApiPassword { get; set; }
        public string? Description { get; set; }
    }

    public class MigrationResult
    {
        public bool Success { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsInserted { get; set; }
        public int RecordsUpdated { get; set; }
        public int RecordsSkipped { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        // Enhanced reporting features
        public List<DetailedError> DetailedErrors { get; set; } = new List<DetailedError>();
        public List<DetailedWarning> DetailedWarnings { get; set; } = new List<DetailedWarning>();
        public MigrationSummary Summary { get; set; } = new MigrationSummary();
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    public class DetailedError
    {
        public int RowNumber { get; set; }
        public required string ErrorType { get; set; }
        public required string ErrorMessage { get; set; }
        public required string FieldName { get; set; }
        public required string FieldValue { get; set; }
        public required string Suggestion { get; set; }
        public Dictionary<string, string> Context { get; set; } = new Dictionary<string, string>();
    }

    public class DetailedWarning
    {
        public int RowNumber { get; set; }
        public required string WarningType { get; set; }
        public required string WarningMessage { get; set; }
        public required string FieldName { get; set; }
        public required string FieldValue { get; set; }
        public required string Recommendation { get; set; }
        public Dictionary<string, string> Context { get; set; } = new Dictionary<string, string>();
    }

    public class MigrationSummary
    {
        public Dictionary<string, int> SuccessBreakdown { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> ErrorBreakdown { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> WarningBreakdown { get; set; } = new Dictionary<string, int>();
        public List<string> CreatedRelationships { get; set; } = new List<string>();
        public List<string> UpdatedRecords { get; set; } = new List<string>();
        public List<string> SkippedReasons { get; set; } = new List<string>();
    }

    // Error type constants for consistent reporting
    public static class ErrorTypes
    {
        public const string MISSING_PERSON = "MISSING_PERSON";
        public const string DUPLICATE_CARD = "DUPLICATE_CARD";
        public const string INVALID_DEALER = "INVALID_DEALER";
        public const string INVALID_CUSTOMER = "INVALID_CUSTOMER";
        public const string INVALID_SITE = "INVALID_SITE";
        public const string INVALID_DEPARTMENT = "INVALID_DEPARTMENT";
        public const string INVALID_MODEL = "INVALID_MODEL";
        public const string MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD";
        public const string INVALID_DATA_FORMAT = "INVALID_DATA_FORMAT";
        public const string BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION";
        public const string DUPLICATE_RECORD = "DUPLICATE_RECORD";
        public const string FOREIGN_KEY_CONSTRAINT = "FOREIGN_KEY_CONSTRAINT";
        public const string DATABASE_ERROR = "DATABASE_ERROR";
    }

    public static class WarningTypes
    {
        public const string EXISTING_RECORD_SKIPPED = "EXISTING_RECORD_SKIPPED";
        public const string AUTO_GENERATED_VALUE = "AUTO_GENERATED_VALUE";
        public const string DEFAULT_VALUE_USED = "DEFAULT_VALUE_USED";
        public const string RELATIONSHIP_CREATED = "RELATIONSHIP_CREATED";
        public const string PARTIAL_MATCH = "PARTIAL_MATCH";
        public const string DEPRECATED_FIELD = "DEPRECATED_FIELD";
        public const string MISSING_DEPENDENCY = "MISSING_DEPENDENCY";
        public const string VALIDATION_WARNING = "VALIDATION_WARNING";
    }

    /// <summary>
    /// Configuration for observability features (logging, monitoring, metrics, health checks, tracing)
    /// Each feature can be enabled/disabled independently
    /// </summary>
    public class ObservabilityConfiguration
    {
        /// <summary>
        /// Enable/disable logging features
        /// </summary>
        public LoggingConfiguration Logging { get; set; } = new();

        /// <summary>
        /// Enable/disable monitoring features
        /// </summary>
        public MonitoringConfiguration Monitoring { get; set; } = new();

        /// <summary>
        /// Enable/disable metrics collection
        /// </summary>
        public MetricsConfiguration Metrics { get; set; } = new();

        /// <summary>
        /// Enable/disable health check features
        /// </summary>
        public HealthCheckConfiguration HealthChecks { get; set; } = new();

        /// <summary>
        /// Enable/disable tracing features
        /// </summary>
        public TracingConfiguration Tracing { get; set; } = new();

        /// <summary>
        /// Enable/disable alerting features
        /// </summary>
        public AlertingConfiguration Alerting { get; set; } = new();

        /// <summary>
        /// Enable/disable audit trail features
        /// </summary>
        public AuditConfiguration Audit { get; set; } = new();

        /// <summary>
        /// Enable/disable reporting features
        /// </summary>
        public ReportingConfiguration Reporting { get; set; } = new();
    }

    /// <summary>
    /// Configuration for logging features
    /// </summary>
    public class LoggingConfiguration
    {
        /// <summary>
        /// Enable/disable all logging features
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Enable/disable console logging
        /// </summary>
        public bool EnableConsoleLogging { get; set; } = true;

        /// <summary>
        /// Enable/disable file logging
        /// </summary>
        public bool EnableFileLogging { get; set; } = true;

        /// <summary>
        /// Enable/disable structured logging
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;

        /// <summary>
        /// Enable/disable performance logging
        /// </summary>
        public bool EnablePerformanceLogging { get; set; } = true;

        /// <summary>
        /// Enable/disable error logging
        /// </summary>
        public bool EnableErrorLogging { get; set; } = true;

        /// <summary>
        /// Enable/disable debug logging
        /// </summary>
        public bool EnableDebugLogging { get; set; } = false;
    }

    /// <summary>
    /// Configuration for monitoring features
    /// </summary>
    public class MonitoringConfiguration
    {
        /// <summary>
        /// Enable/disable all monitoring features
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Enable/disable performance monitoring
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// Enable/disable resource monitoring (CPU, Memory, etc.)
        /// </summary>
        public bool EnableResourceMonitoring { get; set; } = true;

        /// <summary>
        /// Enable/disable database monitoring
        /// </summary>
        public bool EnableDatabaseMonitoring { get; set; } = true;

        /// <summary>
        /// Enable/disable operation monitoring
        /// </summary>
        public bool EnableOperationMonitoring { get; set; } = true;

        /// <summary>
        /// Monitoring collection interval in seconds
        /// </summary>
        public int CollectionIntervalSeconds { get; set; } = 5;
    }

    /// <summary>
    /// Configuration for metrics collection
    /// </summary>
    public class MetricsConfiguration
    {
        /// <summary>
        /// Enable/disable all metrics collection
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Enable/disable throughput metrics
        /// </summary>
        public bool EnableThroughputMetrics { get; set; } = true;

        /// <summary>
        /// Enable/disable duration metrics
        /// </summary>
        public bool EnableDurationMetrics { get; set; } = true;

        /// <summary>
        /// Enable/disable error rate metrics
        /// </summary>
        public bool EnableErrorRateMetrics { get; set; } = true;

        /// <summary>
        /// Enable/disable success rate metrics
        /// </summary>
        public bool EnableSuccessRateMetrics { get; set; } = true;

        /// <summary>
        /// Maximum number of metrics to keep in memory
        /// </summary>
        public int MaxHistorySize { get; set; } = 10000;
    }

    /// <summary>
    /// Configuration for health check features
    /// </summary>
    public class HealthCheckConfiguration
    {
        /// <summary>
        /// Enable/disable all health check features
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Enable/disable database health checks
        /// </summary>
        public bool EnableDatabaseHealthChecks { get; set; } = true;

        /// <summary>
        /// Enable/disable system resource health checks
        /// </summary>
        public bool EnableSystemHealthChecks { get; set; } = true;

        /// <summary>
        /// Enable/disable service health checks
        /// </summary>
        public bool EnableServiceHealthChecks { get; set; } = true;

        /// <summary>
        /// Enable/disable network health checks
        /// </summary>
        public bool EnableNetworkHealthChecks { get; set; } = true;

        /// <summary>
        /// Health check interval in seconds
        /// </summary>
        public int CheckIntervalSeconds { get; set; } = 30;
    }

    /// <summary>
    /// Configuration for tracing features
    /// </summary>
    public class TracingConfiguration
    {
        /// <summary>
        /// Enable/disable all tracing features
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// Enable/disable distributed tracing
        /// </summary>
        public bool EnableDistributedTracing { get; set; } = false;

        /// <summary>
        /// Enable/disable operation tracing
        /// </summary>
        public bool EnableOperationTracing { get; set; } = false;

        /// <summary>
        /// Enable/disable database tracing
        /// </summary>
        public bool EnableDatabaseTracing { get; set; } = false;

        /// <summary>
        /// Sampling rate for tracing (0.0 to 1.0)
        /// </summary>
        public double SamplingRate { get; set; } = 0.1;
    }

    /// <summary>
    /// Configuration for alerting features
    /// </summary>
    public class AlertingConfiguration
    {
        /// <summary>
        /// Enable/disable all alerting features
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Enable/disable performance alerts
        /// </summary>
        public bool EnablePerformanceAlerts { get; set; } = true;

        /// <summary>
        /// Enable/disable error alerts
        /// </summary>
        public bool EnableErrorAlerts { get; set; } = true;

        /// <summary>
        /// Enable/disable resource alerts
        /// </summary>
        public bool EnableResourceAlerts { get; set; } = true;

        /// <summary>
        /// Enable/disable auto-resolution of alerts
        /// </summary>
        public bool EnableAutoResolution { get; set; } = true;

        /// <summary>
        /// Enable/disable alert escalation
        /// </summary>
        public bool EnableEscalation { get; set; } = true;

        /// <summary>
        /// Enable/disable alert deduplication
        /// </summary>
        public bool EnableDeduplication { get; set; } = true;
    }

    /// <summary>
    /// Configuration for audit trail features
    /// </summary>
    public class AuditConfiguration
    {
        /// <summary>
        /// Enable/disable all audit trail features
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Enable/disable operation auditing
        /// </summary>
        public bool EnableOperationAuditing { get; set; } = true;

        /// <summary>
        /// Enable/disable data change auditing
        /// </summary>
        public bool EnableDataChangeAuditing { get; set; } = true;

        /// <summary>
        /// Enable/disable security auditing
        /// </summary>
        public bool EnableSecurityAuditing { get; set; } = true;

        /// <summary>
        /// Enable/disable automatic cleanup of old audit entries
        /// </summary>
        public bool EnableAutomaticCleanup { get; set; } = true;

        /// <summary>
        /// Enable/disable masking of sensitive data in audit logs
        /// </summary>
        public bool MaskSensitiveData { get; set; } = true;

        /// <summary>
        /// Default retention period for audit entries
        /// </summary>
        public TimeSpan DefaultRetentionPeriod { get; set; } = TimeSpan.FromDays(90);
    }

    /// <summary>
    /// Configuration for reporting features
    /// </summary>
    public class ReportingConfiguration
    {
        /// <summary>
        /// Enable/disable all reporting features
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Enable/disable migration reports
        /// </summary>
        public bool EnableMigrationReports { get; set; } = true;

        /// <summary>
        /// Enable/disable performance reports
        /// </summary>
        public bool EnablePerformanceReports { get; set; } = true;

        /// <summary>
        /// Enable/disable error reports
        /// </summary>
        public bool EnableErrorReports { get; set; } = true;

        /// <summary>
        /// Enable/disable detailed record reports
        /// </summary>
        public bool EnableDetailedRecordReports { get; set; } = true;

        /// <summary>
        /// Generate user-friendly reports
        /// </summary>
        public bool GenerateUserReports { get; set; } = true;

        /// <summary>
        /// Directory for user reports
        /// </summary>
        public string UserReportDirectory { get; set; } = "./Reports";

        /// <summary>
        /// Include detailed records in reports
        /// </summary>
        public bool IncludeDetailedRecords { get; set; } = true;

        /// <summary>
        /// Maximum records per error type in reports
        /// </summary>
        public int MaxRecordsPerErrorType { get; set; } = 50;
    }
}