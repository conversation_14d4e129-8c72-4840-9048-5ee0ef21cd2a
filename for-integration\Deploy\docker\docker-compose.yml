version: '3.8'

services:
  fleetxq-bulkimporter:
    build:
      context: ../..
      dockerfile: Deploy/docker/Dockerfile
    image: fleetxq/bulkimporter:${VERSION:-latest}
    container_name: fleetxq-bulkimporter-${ENVIRONMENT:-staging}
    environment:
      - ASPNETCORE_ENVIRONMENT=${ENVIRONMENT:-Staging}
      - FLEETXQ_BULKIMPORTER_ConnectionStrings__FleetXQConnection=${DB_CONNECTION_STRING}
      - FLEETXQ_BULKIMPORTER_Environment__NotificationWebhooks__0=${NOTIFICATION_WEBHOOK}
    volumes:
      - bulkimporter-data:/app/data
      - bulkimporter-logs:/app/logs
      - ./secrets:/app/secrets:ro
    networks:
      - fleetxq-network
    restart: unless-stopped
    depends_on:
      - sql-server
    profiles:
      - staging
      - pilot
      - production

  # SQL Server for development/testing
  sql-server:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: fleetxq-sql-${ENVIRONMENT:-staging}
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=${SQL_SA_PASSWORD:-FleetXQ123!}
      - MSSQL_PID=Developer
    ports:
      - "${SQL_PORT:-1433}:1433"
    volumes:
      - sql-data:/var/opt/mssql
    networks:
      - fleetxq-network
    profiles:
      - development
      - staging

volumes:
  bulkimporter-data:
    driver: local
  bulkimporter-logs:
    driver: local
  sql-data:
    driver: local

networks:
  fleetxq-network:
    driver: bridge
