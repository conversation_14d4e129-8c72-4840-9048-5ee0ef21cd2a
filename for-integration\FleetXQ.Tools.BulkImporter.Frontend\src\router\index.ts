import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      title: 'Dashboard'
    }
  },
  {
    path: '/import',
    name: 'Import',
    component: () => import('@/views/ImportWizardView.vue'),
    meta: {
      title: 'Bulk Import Wizard'
    }
  },
  {
    path: '/sessions',
    name: 'Sessions',
    component: () => import('@/views/SessionsView.vue'),
    meta: {
      title: 'Import Sessions'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/SettingsView.vue'),
    meta: {
      title: 'Settings'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue'),
    meta: {
      title: 'Page Not Found'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// Global navigation guard for setting page titles
router.beforeEach((to) => {
  document.title = to.meta?.title 
    ? `${to.meta.title} - FleetXQ Bulk Importer` 
    : 'FleetXQ Bulk Importer'
})

export default router
