using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Service interface for optimized MERGE operations
/// Implementation of Phase 3.2.2: Optimized MERGE Operations
/// </summary>
public interface IOptimizedMergeService
{
    /// <summary>
    /// Executes bulk MERGE operation for vehicles using optimized stored procedures
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="options">MERGE operation options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>MERGE operation result</returns>
    Task<MergeOperationResult> ExecuteBulkMergeVehiclesAsync(
        Guid sessionId,
        MergeOperationOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes bulk MERGE operation for persons/drivers using optimized stored procedures
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="options">MERGE operation options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>MERGE operation result</returns>
    Task<MergeOperationResult> ExecuteBulkMergePersonsAsync(
        Guid sessionId,
        MergeOperationOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes bulk MERGE operation for cards and access permissions using optimized stored procedures
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="options">MERGE operation options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>MERGE operation result</returns>
    Task<MergeOperationResult> ExecuteBulkMergeCardAccessAsync(
        Guid sessionId,
        MergeOperationOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes comprehensive MERGE operation for all entity types in proper sequence
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="options">Comprehensive merge options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Comprehensive merge result</returns>
    Task<ComprehensiveMergeResult> ExecuteComprehensiveMergeAsync(
        Guid sessionId,
        ComprehensiveMergeOptions options,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance metrics for MERGE operations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of performance metrics</returns>
    Task<List<MergePerformanceMetric>> GetMergePerformanceMetricsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets session statistics for MERGE operations
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of session statistics</returns>
    Task<List<SessionStatistic>> GetSessionStatisticsAsync(
        Guid sessionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Optimizes MERGE performance through statistics updates and index maintenance
    /// </summary>
    /// <param name="options">Optimization options</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Optimization result</returns>
    Task<MergeOperationResult> OptimizeMergePerformanceAsync(
        OptimizationOptions options,
        CancellationToken cancellationToken = default);
}
