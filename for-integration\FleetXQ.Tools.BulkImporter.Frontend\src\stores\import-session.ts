import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
    ImportSession,
    CreateImportSessionRequest,
    ImportSessionFilter,
    ImportSessionSearchResult,
    ImportSessionStatus
} from '@/types/import-session'
import { getBulkImportService, getSignalRService } from '@/services'
import type { ImportProgressResponse, ImportResultResponse } from '@/services'

export const useImportSessionStore = defineStore('importSession', () => {
    // Services
    const bulkImportService = getBulkImportService()
    const signalRService = getSignalRService()

    // State
    const sessions = ref<ImportSession[]>([])
    const currentSession = ref<ImportSession | null>(null)
    const searchResults = ref<ImportSessionSearchResult | null>(null)
    const progressUpdates = ref<Map<string, ImportProgressResponse>>(new Map())
    const sessionResults = ref<Map<string, ImportResultResponse>>(new Map())
    const loading = ref(false)
    const creating = ref(false)
    const executing = ref(false)
    const error = ref<string | null>(null)
    const lastFetch = ref<Date | null>(null)

    // Getters
    const activeSessions = computed(() =>
        sessions.value.filter(session =>
            ['queued', 'processing'].includes(session.status)
        )
    )

    const completedSessions = computed(() =>
        sessions.value.filter(session => session.status === 'completed')
    )

    const failedSessions = computed(() =>
        sessions.value.filter(session => session.status === 'failed')
    )

    const cancelledSessions = computed(() =>
        sessions.value.filter(session => session.status === 'cancelled')
    )

    const sessionById = computed(() => (id: string) =>
        sessions.value.find(session => session.id === id)
    )

    const hasCurrentSession = computed(() =>
        currentSession.value !== null
    )

    const currentSessionProgress = computed(() => {
        if (!currentSession.value) return null
        return progressUpdates.value.get(currentSession.value.id) || null
    })

    const isSessionActive = computed(() => (sessionId: string) => {
        const session = sessionById.value(sessionId)
        return session && ['queued', 'processing'].includes(session.status)
    })

    const getSessionProgress = computed(() => (sessionId: string) => {
        return progressUpdates.value.get(sessionId) || null
    })

    const getSessionResult = computed(() => (sessionId: string) => {
        return sessionResults.value.get(sessionId) || null
    })

    const isDataStale = computed(() => {
        if (!lastFetch.value) return true
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
        return lastFetch.value < fiveMinutesAgo
    })

    const sessionsByDealer = computed(() => (dealerId: string) =>
        sessions.value.filter(session => session.dealerId.toString() === dealerId)
    )

    const recentSessions = computed(() =>
        [...sessions.value]
            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
            .slice(0, 10)
    )

    // Actions
    const fetchSessions = async (dealerId?: string, limit = 20, offset = 0, force = false) => {
        if (!force && !isDataStale.value && sessions.value.length > 0) {
            return
        }

        loading.value = true
        error.value = null

        try {
            const response = await bulkImportService.getSessions(dealerId, limit, offset)

            // Convert to our internal format
            const result: ImportSessionSearchResult = {
                sessions: response.sessions,
                total: response.totalCount,
                totalCount: response.totalCount,
                hasMore: response.hasMore
            }

            searchResults.value = result
            lastFetch.value = new Date()

            // Update sessions array with unique entries
            const existingIds = new Set(sessions.value.map(s => s.id))
            const newSessions = response.sessions.filter(s => !existingIds.has(s.id))
            sessions.value = [...sessions.value, ...newSessions]

        } catch (err: any) {
            error.value = err.message || 'Failed to fetch import sessions'
            console.error('Error fetching import sessions:', err)
        } finally {
            loading.value = false
        }
    }

    const getSessionById = async (id: string): Promise<ImportSession | null> => {
        // Check if already in store
        const cached = sessionById.value(id)
        if (cached) {
            return cached
        }

        loading.value = true
        error.value = null

        try {
            const session = await bulkImportService.getSession(id)

            // Add to store
            const index = sessions.value.findIndex(s => s.id === session.id)
            if (index >= 0) {
                sessions.value[index] = session
            } else {
                sessions.value.push(session)
            }

            return session

        } catch (err: any) {
            error.value = err.message || 'Failed to fetch import session'
            console.error('Error fetching import session:', err)
            return null
        } finally {
            loading.value = false
        }
    }

    const createSession = async (request: CreateImportSessionRequest): Promise<ImportSession | null> => {
        creating.value = true
        error.value = null

        try {
            const response = await bulkImportService.createSession(request)

            // Add to store
            sessions.value.push(response.session)
            currentSession.value = response.session

            return response.session

        } catch (err: any) {
            error.value = err.message || 'Failed to create import session'
            console.error('Error creating import session:', err)
            return null
        } finally {
            creating.value = false
        }
    }

    const executeSession = async (sessionId: string, confirmExecution = true): Promise<boolean> => {
        executing.value = true
        error.value = null

        try {
            const response = await bulkImportService.executeSession({ sessionId, confirmExecution })

            // Update progress
            progressUpdates.value.set(sessionId, response)

            // Refresh session data
            await getSessionById(sessionId)

            return true

        } catch (err: any) {
            error.value = err.message || 'Failed to execute import session'
            console.error('Error executing import session:', err)
            return false
        } finally {
            executing.value = false
        }
    }

    const cancelSession = async (sessionId: string): Promise<boolean> => {
        try {
            await bulkImportService.cancelSession(sessionId)

            // Update session status in store
            const session = sessionById.value(sessionId)
            if (session) {
                session.status = 'cancelled'
            }

            return true
        } catch (err: any) {
            error.value = err.message || 'Failed to cancel session'
            console.error('Error cancelling session:', err)
            return false
        }
    }

    const fetchSessionProgress = async (sessionId: string): Promise<ImportProgressResponse | null> => {
        try {
            const progress = await bulkImportService.getSessionProgress(sessionId)
            progressUpdates.value.set(sessionId, progress)
            return progress
        } catch (err: any) {
            console.error('Error getting session progress:', err)
            return null
        }
    }

    const getSessionResults = async (sessionId: string): Promise<ImportResultResponse | null> => {
        try {
            const results = await bulkImportService.getSessionResults(sessionId)
            sessionResults.value.set(sessionId, results)
            return results
        } catch (err: any) {
            console.error('Error getting session results:', err)
            return null
        }
    }

    const updateSessionStatus = (sessionId: string, status: ImportSessionStatus, session?: Partial<ImportSession>) => {
        const index = sessions.value.findIndex(s => s.id === sessionId)
        if (index >= 0) {
            sessions.value[index] = {
                ...sessions.value[index],
                status,
                ...session
            }
        }

        if (currentSession.value?.id === sessionId) {
            currentSession.value = {
                ...currentSession.value,
                status,
                ...session
            }
        }
    }

    const setCurrentSession = (session: ImportSession | null) => {
        currentSession.value = session
    }

    const clearSessions = () => {
        sessions.value = []
        searchResults.value = null
        currentSession.value = null
        progressUpdates.value.clear()
        sessionResults.value.clear()
        error.value = null
        lastFetch.value = null
    }

    const refreshCurrentSession = async () => {
        if (currentSession.value) {
            await getSessionById(currentSession.value.id)
        }
    }

    const loadActiveSessions = async () => {
        try {
            const activeSessions = await bulkImportService.getActiveSessions()

            // Update store with active sessions
            activeSessions.forEach(session => {
                const index = sessions.value.findIndex(s => s.id === session.id)
                if (index >= 0) {
                    sessions.value[index] = session
                } else {
                    sessions.value.push(session)
                }
            })
        } catch (err: any) {
            console.error('Error loading active sessions:', err)
        }
    }

    const validateImportParameters = async (request: Omit<CreateImportSessionRequest, 'description'>) => {
        try {
            return await bulkImportService.validateImportParameters(request)
        } catch (err: any) {
            console.error('Error validating import parameters:', err)
            throw err
        }
    }

    return {
        // State
        sessions,
        currentSession,
        searchResults,
        progressUpdates,
        sessionResults,
        loading,
        creating,
        executing,
        error,
        lastFetch,

        // Getters
        activeSessions,
        completedSessions,
        failedSessions,
        cancelledSessions,
        sessionById,
        hasCurrentSession,
        currentSessionProgress,
        isSessionActive,
        getSessionProgress,
        getSessionResult,
        isDataStale,
        sessionsByDealer,
        recentSessions,

        // Actions
        fetchSessions,
        getSessionById,
        createSession,
        executeSession,
        cancelSession,
        fetchSessionProgress: fetchSessionProgress,
        getSessionResults,
        updateSessionStatus,
        setCurrentSession,
        clearSessions,
        refreshCurrentSession,
        loadActiveSessions,
        validateImportParameters
    }
})
