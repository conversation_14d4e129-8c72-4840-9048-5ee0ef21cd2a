# Observability Configuration Guide

## Overview

The XQ360 Data Migration system now supports configurable observability features. All observability features (logging, monitoring, metrics, health checks, tracing, alerting, audit trails, and reporting) can be independently enabled or disabled through configuration settings.

## Configuration Structure

The observability configuration is located in the `Observability` section of `appsettings.json`:

```json
{
  "Observability": {
    "Logging": { ... },
    "Monitoring": { ... },
    "Metrics": { ... },
    "HealthChecks": { ... },
    "Tracing": { ... },
    "Alerting": { ... },
    "Audit": { ... },
    "Reporting": { ... }
  }
}
```

## Feature Categories

### 1. Logging Configuration

Controls all logging-related features:

```json
"Logging": {
  "Enabled": true,                    // Master switch for all logging
  "EnableConsoleLogging": true,       // Console output
  "EnableFileLogging": true,          // File-based logging
  "EnableStructuredLogging": true,    // Structured log format
  "EnablePerformanceLogging": true,   // Performance-related logs
  "EnableErrorLogging": true,         // Error logging
  "EnableDebugLogging": false         // Debug-level logging
}
```

### 2. Monitoring Configuration

Controls performance and resource monitoring:

```json
"Monitoring": {
  "Enabled": true,                      // Master switch for monitoring
  "EnablePerformanceMonitoring": true, // Performance metrics collection
  "EnableResourceMonitoring": true,    // CPU, Memory, Disk monitoring
  "EnableDatabaseMonitoring": true,    // Database performance monitoring
  "EnableOperationMonitoring": true,   // Operation-specific monitoring
  "CollectionIntervalSeconds": 5       // How often to collect metrics
}
```

### 3. Metrics Configuration

Controls metrics collection and storage:

```json
"Metrics": {
  "Enabled": true,                    // Master switch for metrics
  "EnableThroughputMetrics": true,    // Records/second metrics
  "EnableDurationMetrics": true,      // Operation duration tracking
  "EnableErrorRateMetrics": true,     // Error rate calculations
  "EnableSuccessRateMetrics": true,   // Success rate calculations
  "MaxHistorySize": 10000             // Maximum metrics to keep in memory
}
```

### 4. Health Checks Configuration

Controls health check operations:

```json
"HealthChecks": {
  "Enabled": true,                        // Master switch for health checks
  "EnableDatabaseHealthChecks": true,     // Database connectivity checks
  "EnableSystemHealthChecks": true,       // System resource checks
  "EnableServiceHealthChecks": true,      // Service health validation
  "EnableNetworkHealthChecks": true,      // Network connectivity checks
  "CheckIntervalSeconds": 30              // Health check frequency
}
```

### 5. Tracing Configuration

Controls distributed tracing (typically disabled for performance):

```json
"Tracing": {
  "Enabled": false,                   // Master switch for tracing
  "EnableDistributedTracing": false,  // Cross-service tracing
  "EnableOperationTracing": false,    // Individual operation tracing
  "EnableDatabaseTracing": false,     // Database operation tracing
  "SamplingRate": 0.1                 // Percentage of operations to trace
}
```

### 6. Alerting Configuration

Controls alert generation and management:

```json
"Alerting": {
  "Enabled": true,                    // Master switch for alerting
  "EnablePerformanceAlerts": true,    // Performance-based alerts
  "EnableErrorAlerts": true,          // Error-based alerts
  "EnableResourceAlerts": true,       // Resource utilization alerts
  "EnableAutoResolution": true,       // Automatic alert resolution
  "EnableEscalation": true,           // Alert escalation
  "EnableDeduplication": true         // Prevent duplicate alerts
}
```

### 7. Audit Configuration

Controls audit trail functionality:

```json
"Audit": {
  "Enabled": true,                      // Master switch for auditing
  "EnableOperationAuditing": true,      // Audit migration operations
  "EnableDataChangeAuditing": true,     // Audit data modifications
  "EnableSecurityAuditing": true,       // Audit security events
  "EnableAutomaticCleanup": true,       // Auto-cleanup old audit entries
  "MaskSensitiveData": true,           // Mask sensitive information
  "DefaultRetentionPeriod": "90.00:00:00" // How long to keep audit entries
}
```

### 8. Reporting Configuration

Controls report generation:

```json
"Reporting": {
  "Enabled": true,                      // Master switch for reporting
  "EnableMigrationReports": true,       // Migration summary reports
  "EnablePerformanceReports": true,     // Performance analysis reports
  "EnableErrorReports": true,           // Error analysis reports
  "EnableDetailedRecordReports": true,  // Detailed record-level reports
  "GenerateUserReports": true,          // User-friendly reports
  "UserReportDirectory": "./Reports",   // Where to save reports
  "IncludeDetailedRecords": true,       // Include record details
  "MaxRecordsPerErrorType": 50          // Limit records per error type
}
```

## Configuration Examples

### High Observability (Production)

Full observability for production environments:

```json
"Observability": {
  "Logging": { "Enabled": true, "EnableDebugLogging": false },
  "Monitoring": { "Enabled": true, "CollectionIntervalSeconds": 5 },
  "Metrics": { "Enabled": true, "MaxHistorySize": 50000 },
  "HealthChecks": { "Enabled": true, "CheckIntervalSeconds": 30 },
  "Tracing": { "Enabled": false },
  "Alerting": { "Enabled": true },
  "Audit": { "Enabled": true, "DefaultRetentionPeriod": "365.00:00:00" },
  "Reporting": { "Enabled": true }
}
```

### Minimal Observability (Development)

Minimal overhead for development:

```json
"Observability": {
  "Logging": { "Enabled": true, "EnableFileLogging": false, "EnableDebugLogging": true },
  "Monitoring": { "Enabled": false },
  "Metrics": { "Enabled": false },
  "HealthChecks": { "Enabled": false },
  "Tracing": { "Enabled": false },
  "Alerting": { "Enabled": false },
  "Audit": { "Enabled": false },
  "Reporting": { "Enabled": true, "EnableDetailedRecordReports": false }
}
```

### Performance-Focused (Testing)

Optimized for performance testing:

```json
"Observability": {
  "Logging": { "Enabled": false },
  "Monitoring": { "Enabled": false },
  "Metrics": { "Enabled": true, "EnableThroughputMetrics": true, "EnableDurationMetrics": true },
  "HealthChecks": { "Enabled": false },
  "Tracing": { "Enabled": false },
  "Alerting": { "Enabled": false },
  "Audit": { "Enabled": false },
  "Reporting": { "Enabled": true, "EnablePerformanceReports": true }
}
```

## Implementation Details

### Graceful Degradation

When observability features are disabled:
- The application continues to function normally
- No errors are thrown when observability calls are made
- Null implementations are used to avoid overhead
- Performance impact is minimized

### Service Registration

The dependency injection container automatically registers:
- Full implementations when features are enabled
- Null implementations when features are disabled
- Configuration-based conditional registration

### Error Handling

Observability failures never impact the main application flow:
- All observability operations are wrapped in try-catch blocks
- Failed observability operations are logged to debug output
- The migration process continues even if observability fails

## Best Practices

1. **Production**: Enable most features except tracing
2. **Development**: Enable logging and reporting, disable others
3. **Performance Testing**: Enable only metrics and performance reporting
4. **Debugging**: Enable all logging including debug level
5. **Minimal Overhead**: Disable all observability features

## Validation

The configuration is validated at startup:
- Invalid values use defaults
- Missing sections use default configurations
- Warnings are logged for invalid settings

## Migration Impact

With observability disabled, the VehicleAccessMigration:
- Runs faster due to reduced logging overhead
- Uses less memory by skipping metrics collection
- Avoids database writes for audit trails
- Skips report generation when not needed

This allows the same codebase to run efficiently in different environments with appropriate observability levels.
