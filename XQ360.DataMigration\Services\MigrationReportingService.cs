using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Microsoft.Extensions.Logging;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Services
{
    public class MigrationReportingService
    {
        private readonly ILogger<MigrationReportingService> _logger;
        private bool _isOrchestrationMode = false;

        public MigrationReportingService(ILogger<MigrationReportingService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Set orchestration mode to control report generation
        /// </summary>
        public void SetOrchestrationMode(bool isOrchestration)
        {
            _isOrchestrationMode = isOrchestration;
        }

        /// <summary>
        /// Generates both developer and user reports
        /// </summary>
        public void GenerateMigrationReport(MigrationResult result, string migrationName)
        {
            // Generate developer report (to console/log file)
            GenerateDeveloperReport(result, migrationName);
            
            // Always generate individual step reports for UI access
            // Use step-specific naming to avoid conflicts with comprehensive report
            GenerateIndividualStepReport(result, migrationName);
            
            // Generate comprehensive report only if NOT in orchestration mode  
            if (!_isOrchestrationMode)
            {
                // Generate Full Migration Report for individual migration
                GenerateFullMigrationReportForSingleStep(result, migrationName);
            }
            else
            {
                _logger.LogDebug($"Individual step report generated for {migrationName} - comprehensive report will be generated after orchestration");
            }
        }

        /// <summary>
        /// Generates a Full Migration Report format for a single migration step
        /// </summary>
        private void GenerateFullMigrationReportForSingleStep(MigrationResult result, string migrationName)
        {
            var userReportPath = Path.Combine("Reports", $"Full-Migration-Report-{DateTime.Now:yyyyMMdd-HHmmss}.txt");
            
            // Ensure Reports directory exists
            Directory.CreateDirectory("Reports");
            
            var report = new StringBuilder();
            
            // Header
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");
            if (result.Success)
            {
                report.AppendLine($"🎉 {migrationName.ToUpper()} - COMPLETED SUCCESSFULLY");
            }
            else
            {
                report.AppendLine($"❌ {migrationName.ToUpper()} - COMPLETED WITH ISSUES");
            }
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");
            report.AppendLine($"📅 Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"⏱️  Duration: {result.Duration:hh\\:mm\\:ss}");
            report.AppendLine($"📊 Records Processed: {result.RecordsProcessed:N0}");
            report.AppendLine($"✅ Successfully Imported: {result.RecordsInserted:N0} ({GetPercentage(result.RecordsInserted, result.RecordsProcessed):F1}%)");
            
            if (result.RecordsSkipped > 0)
            {
                report.AppendLine($"⏭️  Records Skipped: {result.RecordsSkipped:N0} ({GetPercentage(result.RecordsSkipped, result.RecordsProcessed):F1}%)");
            }
            
            if (result.DetailedErrors.Any())
            {
                report.AppendLine($"❌ Records Failed: {result.DetailedErrors.Count:N0}");
            }
            
            report.AppendLine();

            // DETAILED RECORD INFORMATION
            // Failed Records Section
            if (result.DetailedErrors.Any())
            {
                report.AppendLine("❌ FAILED RECORDS REQUIRING CSV FIXES:");
                report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                report.AppendLine($"{"Row",-5} | {"Record",-30} | {"Reason",-40} | {"Action Needed"}");
                report.AppendLine(new string('-', 100));
                
                foreach (var error in result.DetailedErrors.Take(20)) // Limit to first 20 to keep report manageable
                {
                    var recordName = error.FieldValue ?? "Unknown";
                    var reason = error.ErrorMessage ?? "Unknown error";
                    var action = error.Suggestion ?? "Check data format";
                    
                    report.AppendLine($"{error.RowNumber,-5} | {recordName,-30} | {reason,-40} | {action}");
                }
                
                if (result.DetailedErrors.Count > 20)
                {
                    report.AppendLine($"{"...",-5} | {"+ " + (result.DetailedErrors.Count - 20) + " more errors",-30} | {"See developer logs for full details",-40} | {"Fix remaining issues"}");
                }
                report.AppendLine();
            }

            // Skipped Records Section
            if (result.DetailedWarnings.Any())
            {
                var skippedWarnings = result.DetailedWarnings.Where(w => 
                    w.WarningType == WarningTypes.EXISTING_RECORD_SKIPPED || 
                    w.WarningType == WarningTypes.MISSING_DEPENDENCY ||
                    w.WarningType == ErrorTypes.FOREIGN_KEY_CONSTRAINT).ToList();
                if (skippedWarnings.Any())
                {
                    report.AppendLine("⏭️  SKIPPED RECORDS:");
                    report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                    report.AppendLine($"{"Row",-5} | {"Record",-30} | {"Reason",-40} | {"Action Needed"}");
                    report.AppendLine(new string('-', 100));
                    
                    foreach (var warning in skippedWarnings) // removed limit
                    {
                        var recordName = warning.FieldValue ?? "Unknown";
                        var reason = warning.WarningMessage ?? "Already exists";
                        var action = warning.Recommendation ?? "No action needed";
                        
                        report.AppendLine($"{warning.RowNumber,-5} | {recordName,-30} | {reason,-40} | {action}");
                    }
                    report.AppendLine();
                }
            }

            // Next steps
            if (!result.Success || result.DetailedErrors.Any())
            {
                report.AppendLine("🚀 NEXT STEPS TO COMPLETE MIGRATION:");
                report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                if (result.DetailedErrors.Any())
                {
                    report.AppendLine("1. Fix the failed records shown above in your CSV file");
                    report.AppendLine("2. Remove or correct the problematic rows");
                    report.AppendLine($"3. Re-run the migration: dotnet run --migrate-{migrationName.ToLower().Replace(" ", "-")}");
                }
                else
                {
                    report.AppendLine("1. Review any errors in the developer logs");
                    report.AppendLine("2. Fix issues and re-run the migration");
                }
                report.AppendLine();
            }
            else if (result.Success)
            {
                if (result.RecordsSkipped > 0)
                {
                    report.AppendLine("🚀 MIGRATION COMPLETE WITH SKIPPED RECORDS:");
                    report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                    report.AppendLine("1. Review skipped records above - most are likely already in the system");
                    report.AppendLine("2. Remove duplicate entries from CSV if you want to avoid skipped records");
                    report.AppendLine("3. Re-run if needed to import remaining data");
                    report.AppendLine();
                }
                else
                {
                    report.AppendLine("🎉 MIGRATION COMPLETE - ALL DATA SUCCESSFULLY IMPORTED!");
                    report.AppendLine();
                }
            }

            report.AppendLine("📝 IMPORTANT NOTES:");
            report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
            report.AppendLine("• This report contains detailed record information for the migration");
            report.AppendLine("• Failed records require CSV file corrections before re-running");
            report.AppendLine("• Skipped records usually mean data already exists in the system");
            report.AppendLine("• Migration is idempotent - safe to run multiple times");
            report.AppendLine("• Use row numbers to locate specific records in your CSV files");
            report.AppendLine();
            report.AppendLine("For technical support, share this report with your system administrator.");
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");

            // Write to file
            try
            {
                File.WriteAllText(userReportPath, report.ToString());
                _logger.LogInformation($"📋 Full Migration Report Generated: {Path.GetFullPath(userReportPath)}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to write user migration report to file");
            }
        }

        /// <summary>
        /// Generates an individual step report for UI access, always generated regardless of orchestration mode
        /// </summary>
        private void GenerateIndividualStepReport(MigrationResult result, string migrationName)
        {
            // Create a unique filename for this specific migration step
            var stepId = GetStepIdFromName(migrationName);
            var timestamp = DateTime.Now.ToString("yyyyMMdd-HHmmss");
            var userReportPath = Path.Combine("Reports", $"Step-{stepId}-Report-{timestamp}.txt");
            
            // Ensure Reports directory exists
            Directory.CreateDirectory("Reports");
            
            var report = new StringBuilder();
            
            // Header
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");
            if (result.Success)
            {
                report.AppendLine($"🎉 {migrationName.ToUpper()} - COMPLETED SUCCESSFULLY");
            }
            else
            {
                report.AppendLine($"❌ {migrationName.ToUpper()} - COMPLETED WITH ISSUES");
            }
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");
            report.AppendLine($"📅 Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"⏱️  Duration: {result.Duration:hh\\:mm\\:ss}");
            report.AppendLine($"📊 Records Processed: {result.RecordsProcessed:N0}");
            report.AppendLine($"✅ Successfully Imported: {result.RecordsInserted:N0} ({GetPercentage(result.RecordsInserted, result.RecordsProcessed):F1}%)");
            
            if (result.RecordsSkipped > 0)
            {
                report.AppendLine($"⏭️  Records Skipped: {result.RecordsSkipped:N0} ({GetPercentage(result.RecordsSkipped, result.RecordsProcessed):F1}%)");
            }
            
            if (result.DetailedErrors.Any())
            {
                report.AppendLine($"❌ Records Failed: {result.DetailedErrors.Count:N0}");
            }
            
            report.AppendLine();

            // DETAILED RECORD INFORMATION
            // Failed Records Section
            if (result.DetailedErrors.Any())
            {
                report.AppendLine("❌ FAILED RECORDS REQUIRING CSV FIXES:");
                report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                report.AppendLine($"{"Row",-5} | {"Record",-30} | {"Reason",-40} | {"Action Needed"}");
                report.AppendLine(new string('-', 100));
                
                foreach (var error in result.DetailedErrors.Take(20)) // Limit to first 20 to keep report manageable
                {
                    var recordName = error.FieldValue ?? "Unknown";
                    var reason = error.ErrorMessage ?? "Unknown error";
                    var action = error.Suggestion ?? "Check data format";
                    
                    report.AppendLine($"{error.RowNumber,-5} | {recordName,-30} | {reason,-40} | {action}");
                }
                
                if (result.DetailedErrors.Count > 20)
                {
                    report.AppendLine($"{"...",-5} | {"+ " + (result.DetailedErrors.Count - 20) + " more errors",-30} | {"See developer logs for full details",-40} | {"Fix remaining issues"}");
                }
                report.AppendLine();
            }

            // Skipped Records Section
            if (result.DetailedWarnings.Any())
            {
                var skippedWarnings = result.DetailedWarnings.Where(w => 
                    w.WarningType == WarningTypes.EXISTING_RECORD_SKIPPED || 
                    w.WarningType == WarningTypes.MISSING_DEPENDENCY ||
                    w.WarningType == ErrorTypes.FOREIGN_KEY_CONSTRAINT).ToList();
                if (skippedWarnings.Any())
                {
                    report.AppendLine("⏭️  SKIPPED RECORDS:");
                    report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                    report.AppendLine($"{"Row",-5} | {"Record",-30} | {"Reason",-40} | {"Action Needed"}");
                    report.AppendLine(new string('-', 100));
                    
                    foreach (var warning in skippedWarnings) // removed limit
                    {
                        var recordName = warning.FieldValue ?? "Unknown";
                        var reason = warning.WarningMessage ?? "Already exists";
                        var action = warning.Recommendation ?? "No action needed";
                        
                        report.AppendLine($"{warning.RowNumber,-5} | {recordName,-30} | {reason,-40} | {action}");
                    }
                    report.AppendLine();
                }
            }

            // Next steps
            if (!result.Success || result.DetailedErrors.Any())
            {
                report.AppendLine("🚀 NEXT STEPS TO COMPLETE MIGRATION:");
                report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                if (result.DetailedErrors.Any())
                {
                    report.AppendLine("1. Fix the failed records shown above in your CSV file");
                    report.AppendLine("2. Remove or correct the problematic rows");
                    report.AppendLine($"3. Re-run the migration: dotnet run --migrate-{migrationName.ToLower().Replace(" ", "-")}");
                }
                else
                {
                    report.AppendLine("1. Review any errors in the developer logs");
                    report.AppendLine("2. Fix issues and re-run the migration");
                }
                report.AppendLine();
            }
            else if (result.Success)
            {
                if (result.RecordsSkipped > 0)
                {
                    report.AppendLine("🚀 MIGRATION COMPLETE WITH SKIPPED RECORDS:");
                    report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                    report.AppendLine("1. Review skipped records above - most are likely already in the system");
                    report.AppendLine("2. Remove duplicate entries from CSV if you want to avoid skipped records");
                    report.AppendLine("3. Re-run if needed to import remaining data");
                    report.AppendLine();
                }
                else
                {
                    report.AppendLine("🎉 MIGRATION COMPLETE - ALL DATA SUCCESSFULLY IMPORTED!");
                    report.AppendLine();
                }
            }

            report.AppendLine("📝 IMPORTANT NOTES:");
            report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
            report.AppendLine("• This is an individual step report for this specific migration");
            report.AppendLine("• Failed records require CSV file corrections before re-running");
            report.AppendLine("• Skipped records usually mean data already exists in the system");
            report.AppendLine("• Migration is idempotent - safe to run multiple times");
            report.AppendLine("• Use row numbers to locate specific records in your CSV files");
            report.AppendLine();
            report.AppendLine("For technical support, share this report with your system administrator.");
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");

            // Write to file
            try
            {
                File.WriteAllText(userReportPath, report.ToString());
                _logger.LogInformation($"📋 Individual Step Report Generated: {Path.GetFullPath(userReportPath)}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to write individual step migration report to file");
            }
        }

        /// <summary>
        /// Helper method to convert migration name to step ID for consistent file naming
        /// </summary>
        private string GetStepIdFromName(string migrationName)
        {
            // Map exact migration names to step IDs used in the UI
            return migrationName switch
            {
                "PreOp Checklist Migration" => "preop-checklist",
                "Vehicle Sync Settings Migration" => "vehicle-sync-settings", 
                "Person Migration" => "persons",
                "Vehicle Migration" => "vehicles",
                "Card Migration" => "cards-and-vehicle-access",
                "Driver Blacklist Migration" => "driver-blacklist",
                "Supervisor Access Migration" => "supervisor-access",
                "Website User Migration" => "website-users",
                "Spare Module Migration" => "spare-modules",
                _ => ConvertToStepId(migrationName)
            };
        }

        /// <summary>
        /// Fallback method to convert migration name to step ID by cleaning the string
        /// </summary>
        private string ConvertToStepId(string migrationName)
        {
            var stepId = migrationName
                .Replace(" ", "-")
                .Replace("Migration", "")
                .Replace("migration", "")
                .Trim('-')
                .ToLower();
                
            return stepId;
        }



        /// <summary>
        /// Generates a detailed and beautiful developer report for console/log
        /// </summary>
        private void GenerateDeveloperReport(MigrationResult result, string migrationName)
        {
            var report = new StringBuilder();
            
            // Header with visual separator
            report.AppendLine("╔═══════════════════════════════════════════════════════════════════════════════════════════════════════════╗");
            if (result.Success)
            {
                report.AppendLine($"║  🎉 {migrationName.ToUpper()} MIGRATION - SUCCESS REPORT");
            }
            else
            {
                report.AppendLine($"║  ❌ {migrationName.ToUpper()} MIGRATION - FAILURE REPORT");
            }
            report.AppendLine("╠═══════════════════════════════════════════════════════════════════════════════════════════════════════════╣");
            
            // Summary statistics with better formatting
            report.AppendLine($"║  ⏱️  Duration: {result.Duration:hh\\:mm\\:ss\\.fff}");
            report.AppendLine($"║  📊 Total Records Processed: {result.RecordsProcessed:N0}");
            report.AppendLine($"║  ✅ Successfully Inserted: {result.RecordsInserted:N0} ({GetPercentage(result.RecordsInserted, result.RecordsProcessed):F1}%)");
            
            if (result.RecordsUpdated > 0)
                report.AppendLine($"║  🔄 Updated: {result.RecordsUpdated:N0} ({GetPercentage(result.RecordsUpdated, result.RecordsProcessed):F1}%)");
            
            if (result.RecordsSkipped > 0)
                report.AppendLine($"║  ⏭️  Skipped: {result.RecordsSkipped:N0} ({GetPercentage(result.RecordsSkipped, result.RecordsProcessed):F1}%)");

            report.AppendLine("╠═══════════════════════════════════════════════════════════════════════════════════════════════════════════╣");

            // Success breakdown section
            if (result.Summary.SuccessBreakdown.Any())
            {
                report.AppendLine("║  📈 SUCCESS BREAKDOWN:");
                foreach (var breakdown in result.Summary.SuccessBreakdown.OrderByDescending(x => x.Value))
                {
                    report.AppendLine($"║     • {breakdown.Key}: {breakdown.Value:N0}");
                }
                report.AppendLine("║");
            }

            // Error summary (brief for developer log)
            if (result.DetailedErrors.Any())
            {
                report.AppendLine("║  ❌ ERROR SUMMARY:");
                var errorGroups = result.DetailedErrors.GroupBy(e => e.ErrorType);
                foreach (var group in errorGroups.OrderByDescending(g => g.Count()))
                {
                    report.AppendLine($"║     • {group.Key}: {group.Count():N0} occurrences");
                }
                report.AppendLine("║");
            }

            // Warning summary
            if (result.DetailedWarnings.Any())
            {
                report.AppendLine("║  ⚠️  WARNING SUMMARY:");
                var warningGroups = result.DetailedWarnings.GroupBy(w => w.WarningType);
                foreach (var group in warningGroups.OrderByDescending(g => g.Count()))
                {
                    report.AppendLine($"║     • {group.Key}: {group.Count():N0} occurrences");
                }
                report.AppendLine("║");
            }

            report.AppendLine("╚═══════════════════════════════════════════════════════════════════════════════════════════════════════════╝");

            if (result.Success)
            {
                _logger.LogInformation(report.ToString());
            }
            else
            {
                _logger.LogError(report.ToString());
            }
        }

        /// <summary>
        /// Generates an enhanced orchestrator summary report for both developer and user
        /// </summary>
        public void GenerateOrchestrationReport(MigrationOrchestrationResult result)
        {
            // Generate developer report (console/log)
            GenerateDeveloperOrchestrationReport(result);
            
            // Generate user report (file)
            GenerateUserOrchestrationReport(result);
        }

        private void GenerateUserOrchestrationReport(MigrationOrchestrationResult result)
        {
            var userReportPath = Path.Combine("Reports", $"Full-Migration-Report-{DateTime.Now:yyyyMMdd-HHmmss}.txt");
            
            // Ensure Reports directory exists
            Directory.CreateDirectory("Reports");
            
            var report = new StringBuilder();
            
            // Header
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");
            if (result.Success)
            {
                report.AppendLine("🎉 FULL MIGRATION ORCHESTRATION - COMPLETED SUCCESSFULLY");
            }
            else
            {
                report.AppendLine("❌ FULL MIGRATION ORCHESTRATION - COMPLETED WITH ISSUES");
            }
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");
            report.AppendLine($"📅 Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"⏱️  Total Duration: {result.Duration:hh\\:mm\\:ss}");
            report.AppendLine($"📊 Migration Steps: {result.StepResults.Count:N0} total");
            report.AppendLine($"✅ Completed Steps: {result.CompletedSteps.Count:N0}");
            
            if (result.FailedStep != null)
            {
                report.AppendLine($"❌ Failed At Step: {result.FailedStep.Order} - {result.FailedStep.Name}");
            }
            
            report.AppendLine();

            // Overall statistics
            var totalProcessed = result.StepResults.Sum(s => s.RecordsProcessed);
            var totalInserted = result.StepResults.Sum(s => s.RecordsInserted);
            var totalSkipped = result.StepResults.Sum(s => s.RecordsSkipped);
            
            if (totalProcessed > 0)
            {
                report.AppendLine("📈 OVERALL STATISTICS:");
                report.AppendLine($"   Total Records Processed: {totalProcessed:N0}");
                report.AppendLine($"   Total Records Imported: {totalInserted:N0} ({GetPercentage(totalInserted, totalProcessed):F1}%)");
                report.AppendLine($"   Total Records Skipped: {totalSkipped:N0} ({GetPercentage(totalSkipped, totalProcessed):F1}%)");
                report.AppendLine();
            }

            // Step-by-step results for user
            report.AppendLine("📋 MIGRATION STEP RESULTS:");
            report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
            report.AppendLine($"{"Step",-4} | {"Status",-6} | {"Migration",-35} | {"Records",-15} | {"Result"}");
            report.AppendLine(new string('-', 90));
            
            var stepNumber = 1;
            foreach (var step in result.StepResults.OrderBy(s => s.StartTime))
            {
                var status = step.Success ? "✅" : "❌";
                var stepName = step.StepName.Length > 33 ? step.StepName.Substring(0, 30) + "..." : step.StepName;
                var recordInfo = step.RecordsProcessed > 0 ? $"{step.RecordsProcessed:N0} processed" : "No records";
                var resultInfo = step.Success ? 
                    (step.RecordsInserted > 0 ? $"{step.RecordsInserted:N0} imported" : "All skipped") :
                    $"{step.Errors.Count:N0} errors";
                
                report.AppendLine($"{stepNumber,-4} | {status,-6} | {stepName,-35} | {recordInfo,-15} | {resultInfo}");
                
                // Show first error for failed steps
                if (!step.Success && step.Errors.Any())
                {
                    var firstError = step.Errors.First();
                    var shortError = firstError.Length > 70 ? firstError.Substring(0, 67) + "..." : firstError;
                    report.AppendLine($"     └─ Error: {shortError}");
                }
                
                stepNumber++;
            }
            
            report.AppendLine();

            // COMPREHENSIVE DETAILED RECORD INFORMATION FOR ALL MIGRATION STEPS
            var allDetailedErrors = result.StepResults.SelectMany(s => s.DetailedErrors.Select(e => new { Step = s.StepName, Error = e })).ToList();
            var allDetailedWarnings = result.StepResults.SelectMany(s => s.DetailedWarnings.Select(w => new { Step = s.StepName, Warning = w })).ToList();

            // Failed Records Section (across all migrations)
            if (allDetailedErrors.Any())
            {
                report.AppendLine("❌ FAILED RECORDS REQUIRING CSV FIXES:");
                report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                report.AppendLine($"{"Row",-5} | {"Migration",-25} | {"Record",-25} | {"Reason",-40} | {"Action Needed"}");
                report.AppendLine(new string('-', 120));
                
                foreach (var errorGroup in allDetailedErrors.GroupBy(e => e.Step))
                {
                                            foreach (var errorItem in errorGroup.Take(50)) // Limit to first 50 per migration to keep report manageable
                    {
                        var error = errorItem.Error;
                        var migrationName = errorGroup.Key.Length > 23 ? errorGroup.Key.Substring(0, 20) + "..." : errorGroup.Key;
                        var recordName = error.FieldValue ?? "Unknown";
                        var reason = error.ErrorMessage ?? "Unknown error";
                        var action = error.Suggestion ?? "Check data format";
                        
                        report.AppendLine($"{error.RowNumber,-5} | {migrationName,-25} | {recordName,-25} | {reason,-40} | {action}");
                    }
                    
                    if (errorGroup.Count() > 50)
                    {
                        report.AppendLine($"{"...",-5} | {errorGroup.Key,-25} | {"+ " + (errorGroup.Count() - 50) + " more errors",-25} | {"See developer logs for full details",-40} | {"Fix remaining issues"}");
                    }
                }
                report.AppendLine();
            }

            // Skipped Records Section (across all migrations)
            if (allDetailedWarnings.Any())
            {
                var skippedWarnings = allDetailedWarnings.Where(w => 
                    w.Warning.WarningType == WarningTypes.EXISTING_RECORD_SKIPPED ||
                    w.Warning.WarningType == WarningTypes.VALIDATION_WARNING ||
                    w.Warning.WarningType == WarningTypes.MISSING_DEPENDENCY ||
                    w.Warning.WarningType == WarningTypes.PARTIAL_MATCH ||
                    w.Warning.WarningType == WarningTypes.DEPRECATED_FIELD).ToList();
                if (skippedWarnings.Any())
                {
                    report.AppendLine("⏭️  SKIPPED RECORDS (ALREADY EXIST):");
                    report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                    report.AppendLine($"{"Row",-5} | {"Migration",-25} | {"Record",-25} | {"Reason",-40} | {"Action Needed"}");
                    report.AppendLine(new string('-', 120));
                    
                    foreach (var warningGroup in skippedWarnings.GroupBy(w => w.Step))
                    {
                        foreach (var warningItem in warningGroup.Take(50)) // Limit to first 50 per migration
                        {
                            var warning = warningItem.Warning;
                            var migrationName = warningGroup.Key.Length > 23 ? warningGroup.Key.Substring(0, 20) + "..." : warningGroup.Key;
                            var recordName = warning.FieldValue ?? "Unknown";
                            var reason = warning.WarningMessage ?? "Already exists";
                            var action = warning.Recommendation ?? "No action needed";
                            
                            report.AppendLine($"{warning.RowNumber,-5} | {migrationName,-25} | {recordName,-25} | {reason,-40} | {action}");
                        }
                        
                        if (warningGroup.Count() > 50)
                        {
                            report.AppendLine($"{"...",-5} | {warningGroup.Key,-25} | {"+ " + (warningGroup.Count() - 50) + " more skipped",-25} | {"Records already in system",-40} | {"No action needed"}");
                        }
                    }
                    report.AppendLine();
                }
            }

            // Next steps
            if (!result.Success && result.FailedStep != null)
            {
                report.AppendLine("🚀 NEXT STEPS TO COMPLETE MIGRATION:");
                report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                report.AppendLine($"1. Fix issues in: {result.FailedStep.Name}");
                report.AppendLine("2. Use the detailed record information above to fix specific CSV issues");
                report.AppendLine("3. Remove or correct the problematic rows in your CSV files");
                report.AppendLine($"4. Re-run from failed step: dotnet run --migrate-{result.FailedStep.Id}");
                report.AppendLine("5. Or run full migration again: dotnet run --migrate-all");
                report.AppendLine();
            }
            else if (result.Success)
            {
                if (allDetailedErrors.Any() || allDetailedWarnings.Any())
                {
                    report.AppendLine("🚀 NEXT STEPS TO COMPLETE REMAINING DATA:");
                    report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
                    if (allDetailedErrors.Any())
                    {
                        report.AppendLine("1. Fix the failed records shown above in your CSV files");
                        report.AppendLine("2. Remove or correct the problematic rows");
                        report.AppendLine("3. Re-run the affected migrations: dotnet run --migrate-all");
                    }
                    else
                    {
                        report.AppendLine("1. Review skipped records above - most are likely already in the system");
                        report.AppendLine("2. Remove duplicate entries from CSV if you want to avoid skipped records");
                    }
                    report.AppendLine();
                }
                else
                {
                    report.AppendLine("🎉 MIGRATION COMPLETE - ALL DATA SUCCESSFULLY IMPORTED!");
                    report.AppendLine();
                }
            }

            report.AppendLine("📝 IMPORTANT NOTES:");
            report.AppendLine("─────────────────────────────────────────────────────────────────────────────────────────────────────────");
            report.AppendLine("• This report contains ALL detailed record information across all migration steps");
            report.AppendLine("• Failed records require CSV file corrections before re-running");
            report.AppendLine("• Skipped records usually mean data already exists in the system");
            report.AppendLine("• Migration is idempotent - safe to run multiple times");
            report.AppendLine("• Use row numbers to locate specific records in your CSV files");
            report.AppendLine();
            report.AppendLine("For technical support, share this report with your system administrator.");
            report.AppendLine("═══════════════════════════════════════════════════════════════════════════════════════════════════════════");

            // Write to file
            try
            {
                File.WriteAllText(userReportPath, report.ToString());
                _logger.LogInformation($"📋 Full Migration Report Generated: {Path.GetFullPath(userReportPath)}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to write user orchestration report to file");
            }
        }

        private void GenerateDeveloperOrchestrationReport(MigrationOrchestrationResult result)
        {
            var report = new StringBuilder();
            
            report.AppendLine("╔═══════════════════════════════════════════════════════════════════════════════════════════════════════════╗");
            if (result.Success)
            {
                report.AppendLine("║  🎉 MIGRATION ORCHESTRATION - COMPLETE SUCCESS");
            }
            else
            {
                report.AppendLine("║  ❌ MIGRATION ORCHESTRATION - FAILED");
            }
            report.AppendLine("╠═══════════════════════════════════════════════════════════════════════════════════════════════════════════╣");
            
            report.AppendLine($"║  ⏱️  Total Duration: {result.Duration:hh\\:mm\\:ss\\.fff}");
            report.AppendLine($"║  📊 Migration Steps: {result.StepResults.Count:N0} total");
            report.AppendLine($"║  ✅ Completed Steps: {result.CompletedSteps.Count:N0}");
            
            if (result.FailedStep != null)
            {
                report.AppendLine($"║  ❌ Failed At Step: {result.FailedStep.Order} - {result.FailedStep.Name}");
            }
            
            report.AppendLine("╠═══════════════════════════════════════════════════════════════════════════════════════════════════════════╣");
            
            // Summary statistics
            var totalProcessed = result.StepResults.Sum(s => s.RecordsProcessed);
            var totalInserted = result.StepResults.Sum(s => s.RecordsInserted);
            var totalSkipped = result.StepResults.Sum(s => s.RecordsSkipped);
            
            if (totalProcessed > 0)
            {
                report.AppendLine("║  📈 OVERALL STATISTICS:");
                report.AppendLine($"║     Total Records Processed: {totalProcessed:N0}");
                report.AppendLine($"║     Total Records Inserted: {totalInserted:N0} ({GetPercentage(totalInserted, totalProcessed):F1}%)");
                report.AppendLine($"║     Total Records Skipped: {totalSkipped:N0} ({GetPercentage(totalSkipped, totalProcessed):F1}%)");
                report.AppendLine("║");
            }
            
            report.AppendLine("╚═══════════════════════════════════════════════════════════════════════════════════════════════════════════╝");
            
            if (result.Success)
            {
                _logger.LogInformation(report.ToString());
            }
            else
            {
                _logger.LogError(report.ToString());
            }
        }

        /// <summary>
        /// Gets a user-friendly record identifier from error/warning context
        /// </summary>
        private string GetRecordIdentifier(DetailedError error)
        {
            // Try to extract meaningful identifier from context or field value
            if (error.Context.ContainsKey("PersonName"))
                return error.Context["PersonName"];
            if (error.Context.ContainsKey("CardNumber"))
                return $"Card {error.Context["CardNumber"]}";
            if (error.Context.ContainsKey("VehicleSerial"))
                return $"Vehicle {error.Context["VehicleSerial"]}";
            if (error.Context.ContainsKey("Username"))
                return $"User {error.Context["Username"]}";
            if (error.Context.ContainsKey("FirstName") && error.Context.ContainsKey("LastName"))
                return $"{error.Context["FirstName"]} {error.Context["LastName"]}";
            if (!string.IsNullOrEmpty(error.FieldValue))
                return error.FieldValue.Length > 23 ? error.FieldValue.Substring(0, 20) + "..." : error.FieldValue;
            
            return "Record";
        }

        /// <summary>
        /// Gets a user-friendly record identifier from warning context
        /// </summary>
        private string GetRecordIdentifier(DetailedWarning warning)
        {
            // Try to extract meaningful identifier from context or field value
            if (warning.Context.ContainsKey("PersonName"))
                return warning.Context["PersonName"];
            if (warning.Context.ContainsKey("CardNumber"))
                return $"Card {warning.Context["CardNumber"]}";
            if (warning.Context.ContainsKey("VehicleSerial"))
                return $"Vehicle {warning.Context["VehicleSerial"]}";
            if (warning.Context.ContainsKey("Username"))
                return $"User {warning.Context["Username"]}";
            if (warning.Context.ContainsKey("FirstName") && warning.Context.ContainsKey("LastName"))
                return $"{warning.Context["FirstName"]} {warning.Context["LastName"]}";
            if (!string.IsNullOrEmpty(warning.FieldValue))
                return warning.FieldValue.Length > 23 ? warning.FieldValue.Substring(0, 20) + "..." : warning.FieldValue;
            
            return "Record";
        }

        /// <summary>
        /// Calculates percentage safely
        /// </summary>
        private double GetPercentage(int value, int total)
        {
            return total == 0 ? 0 : (double)value / total * 100;
        }

        /// <summary>
        /// Adds a detailed error to the migration result with enhanced context
        /// </summary>
        public void AddDetailedError(MigrationResult result, int rowNumber, string errorType, string errorMessage, 
            string? fieldName = null, string? fieldValue = null, string? suggestion = null, 
            Dictionary<string, string>? context = null)
        {
            var detailedError = new DetailedError
            {
                RowNumber = rowNumber,
                ErrorType = errorType,
                ErrorMessage = errorMessage,
                FieldName = fieldName ?? "Unknown",
                FieldValue = fieldValue ?? "Unknown",
                Suggestion = suggestion ?? "No suggestion provided",
                Context = context ?? new Dictionary<string, string>()
            };

            result.DetailedErrors.Add(detailedError);
            
            // Also add to legacy errors for backward compatibility
            var recordId = context != null ? GetRecordIdentifier(detailedError) : "Unknown Record";
            result.Errors.Add($"Row {rowNumber}: {recordId} - {errorMessage}");
            
            // Update error breakdown
            if (result.Summary.ErrorBreakdown.ContainsKey(errorType))
                result.Summary.ErrorBreakdown[errorType]++;
            else
                result.Summary.ErrorBreakdown[errorType] = 1;
        }

        /// <summary>
        /// Adds a detailed warning to the migration result with enhanced context
        /// </summary>
        public void AddDetailedWarning(MigrationResult result, int rowNumber, string warningType, string warningMessage,
            string? fieldName = null, string? fieldValue = null, string? recommendation = null,
            Dictionary<string, string>? context = null)
        {
            var detailedWarning = new DetailedWarning
            {
                RowNumber = rowNumber,
                WarningType = warningType,
                WarningMessage = warningMessage,
                FieldName = fieldName ?? "Unknown",
                FieldValue = fieldValue ?? "Unknown",
                Recommendation = recommendation ?? "No recommendation provided",
                Context = context ?? new Dictionary<string, string>()
            };

            result.DetailedWarnings.Add(detailedWarning);
            
            // Also add to legacy warnings for backward compatibility
            var recordId = context != null ? GetRecordIdentifier(detailedWarning) : "Unknown Record";
            result.Warnings.Add($"Row {rowNumber}: {recordId} - {warningMessage}");
            
            // Update warning breakdown
            if (result.Summary.WarningBreakdown.ContainsKey(warningType))
                result.Summary.WarningBreakdown[warningType]++;
            else
                result.Summary.WarningBreakdown[warningType] = 1;
        }

        /// <summary>
        /// Adds success information to the migration summary
        /// </summary>
        public void AddSuccessInfo(MigrationResult result, string category, int count)
        {
            result.Summary.SuccessBreakdown[category] = count;
        }

        /// <summary>
        /// Adds created relationship information
        /// </summary>
        public void AddCreatedRelationship(MigrationResult result, string relationshipDescription)
        {
            result.Summary.CreatedRelationships.Add(relationshipDescription);
        }

        /// <summary>
        /// Adds skip reason information
        /// </summary>
        public void AddSkipReason(MigrationResult result, string reason)
        {
            if (!result.Summary.SkippedReasons.Contains(reason))
            {
                result.Summary.SkippedReasons.Add(reason);
            }
        }
    }
} 