# FleetXQ BulkImporter - Temporary Table Implementation

## Overview

This implementation provides a **complete replacement for permanent staging tables** using SQL Server temporary tables. It maintains all the functionality of the original staging table approach while working within constraints that prevent creating new permanent tables.

## Key Features

### ✅ **Full Staging Functionality Preserved**
- **Multi-stage validation**: Complete data validation pipeline
- **Foreign key resolution**: Batch lookup of Customer/Site/Department relationships
- **Duplicate detection**: Identification of existing Person/Driver/Vehicle records
- **Error isolation**: Invalid records don't affect successful ones
- **Batch processing**: Configurable chunk sizes for optimal performance
- **Transaction safety**: Full rollback capabilities

### ✅ **No Permanent Tables Required**
- **Session temp tables** (`#TableName`): Automatic cleanup when session ends
- **No schema changes**: Doesn't violate table creation restrictions
- **Concurrent operations**: Multiple import sessions can run simultaneously
- **Session isolation**: No cross-session data contamination

### ✅ **Enterprise-Grade Features**
- **Comprehensive validation**: Field validation, FK resolution, business rules
- **Performance optimization**: Bulk operations, indexes, batching
- **Audit trail**: Session tracking and detailed logging
- **Error handling**: Graceful failure handling with detailed reporting
- **Dry run support**: Validation without production changes

## Architecture

### Service Layer
```
TempBulkImportService
├── ITempStagingService (TempStagingService)
├── ISqlDataGenerationService
└── Configuration Options
```

### Temporary Table Structure
```sql
-- Session tracking
#ImportSession
├── Id (Session GUID)
├── SessionName
├── StartTime/EndTime
├── Status (Running/Completed/Failed)
└── ProcessingMetrics

-- Driver staging
#DriverImport
├── Raw CSV Data Fields
├── Resolved Foreign Keys
├── Validation Status/Errors
└── Processing Metadata

-- Vehicle staging  
#VehicleImport
├── Raw CSV Data Fields
├── Resolved Foreign Keys
├── Validation Status/Errors
└── Processing Metadata
```

### Stored Procedures
```sql
-- Validation procedures
usp_ValidateDriverImportTemp
usp_ValidateVehicleImportTemp
usp_ValidateAllTempData

-- Merge procedures
usp_MergeDriversToProductionTemp
usp_MergeVehiclesToProductionTemp
usp_MergeAllTempToProduction
```

## Configuration

### appsettings.json
```json
{
  "BulkImporter": {
    "UseTempTables": true,
    "TempTableMode": "SessionScoped",
    "TempTableBatchSize": 5000,
    "TempTableIndexes": true,
    "LogTempTableOperations": true,
    "ValidationEnabled": true,
    "StopOnFirstError": false
  }
}
```

### Configuration Options
- **`UseTempTables`**: Enable temporary table mode (default: true)
- **`TempTableMode`**: "SessionScoped" for `#` tables (recommended)
- **`TempTableBatchSize`**: Batch size for temp table operations
- **`TempTableIndexes`**: Create performance indexes on temp tables
- **`LogTempTableOperations`**: Detailed logging of temp table operations

## Usage Examples

### Basic Import
```csharp
var options = new ImportOptions
{
    DriversCount = 1000,
    VehiclesCount = 500,
    GenerateData = true,
    DryRun = false,
    BatchSize = 100
};

var result = await tempBulkImportService.ExecuteImportAsync(options);
```

### Manual Temp Table Operations
```csharp
using var connection = new SqlConnection(connectionString);
await connection.OpenAsync();

var sessionId = Guid.NewGuid();

// Create temp tables
await tempStagingService.CreateTempStagingTablesAsync(connection, sessionId);

// Populate with data
await tempStagingService.PopulateTempTablesAsync(connection, sessionId, drivers, vehicles);

// Validate
var validation = await tempStagingService.ValidateTempDataAsync(connection, sessionId);

// Process (or dry run)
var processing = await tempStagingService.MergeTempToProductionAsync(connection, sessionId, dryRun: true);

// Temp tables auto-cleanup when connection closes
```

### Dry Run Validation
```csharp
var options = new ImportOptions
{
    DriversCount = 10000,
    VehiclesCount = 5000,
    GenerateData = true,
    DryRun = true  // Validates without production changes
};

var result = await tempBulkImportService.ExecuteImportAsync(options);
// Result shows what would be processed without making changes
```

## Data Flow

```mermaid
graph TD
    A[Input Data] --> B[Create Temp Tables]
    B --> C[Populate #DriverImport & #VehicleImport]
    C --> D[Validate Data]
    D --> E{Validation Passed?}
    E -->|Yes| F[Merge to Production]
    E -->|No| G[Generate Error Report]
    F --> H[Update Processing Status]
    G --> H
    H --> I[Connection Close = Auto Cleanup]
```

## Performance Characteristics

### Memory Usage
- **Constant Memory**: Temp tables stored in tempdb, not application memory
- **Streaming Processing**: Process data in configurable batches
- **No Memory Limits**: Can handle datasets larger than available RAM

### Throughput
- **Bulk Operations**: SqlBulkCopy for maximum insert performance
- **Indexed Lookups**: Temp table indexes for fast FK resolution
- **Batch Processing**: Configurable batch sizes for optimal performance
- **Parallel Sessions**: Multiple concurrent import operations

### Scalability
- **Large Datasets**: Tested with 10K+ drivers, 5K+ vehicles
- **Concurrent Operations**: Multiple users can import simultaneously
- **Resource Efficient**: Uses tempdb instead of main database storage

## Error Handling

### Validation Errors
```csharp
var validation = await tempStagingService.ValidateTempDataAsync(connection, sessionId);

if (!validation.Success) {
    Console.WriteLine($"Invalid Drivers: {validation.InvalidDriverRows}");
    Console.WriteLine($"Invalid Vehicles: {validation.InvalidVehicleRows}");
    
    foreach (var error in validation.ValidationErrors) {
        Console.WriteLine($"Error: {error}");
    }
}
```

### Processing Errors
- **Transaction Rollback**: Failed batches don't affect successful ones
- **Error Isolation**: Invalid records marked and reported separately
- **Partial Success**: Valid records processed even if some fail
- **Detailed Logging**: Comprehensive error reporting and correlation

### Recovery Options
- **Reprocess Session**: Fix errors and rerun validation/processing
- **Manual Correction**: Update temp table data and revalidate
- **Incremental Processing**: Process valid records, fix and reprocess invalid ones

## Database Setup

### Required Stored Procedures
```sql
-- Execute these scripts to setup temp table procedures:
TempTable-001-CreateValidationProcedures.sql
TempTable-002-CreateMergeProcedures.sql
```

### Permissions Required
- **tempdb Usage**: Create temporary tables and indexes
- **Production Table Access**: INSERT/UPDATE permissions on Person, Driver, Vehicle
- **Lookup Table Access**: SELECT permissions on Customer, Site, Department, Model, Module

### No Permanent Changes
- **No new tables**: Only creates temporary tables per session
- **No schema changes**: Doesn't modify existing database structure
- **No cleanup required**: Temporary tables auto-cleanup

## Comparison with Permanent Staging

| Feature | Permanent Staging | Temp Table Staging |
|---------|------------------|-------------------|
| **Table Creation** | ❌ Requires schema changes | ✅ No permanent changes |
| **Validation** | ✅ Full validation | ✅ Full validation |
| **Performance** | ✅ Excellent | ✅ Excellent |
| **Error Handling** | ✅ Complete | ✅ Complete |
| **Audit Trail** | ✅ Persistent | ⚠️ Session-based |
| **Concurrent Operations** | ✅ Supported | ✅ Supported |
| **Memory Usage** | ✅ Database storage | ✅ tempdb storage |
| **Cleanup** | ❌ Manual required | ✅ Automatic |
| **Session Isolation** | ⚠️ Requires session IDs | ✅ Built-in |

## Migration from Permanent Staging

### Service Replacement
```csharp
// Old approach
services.AddSingleton<IBulkImportService, BulkImportService>();

// New approach
services.AddSingleton<IBulkImportService, TempBulkImportService>();
services.AddSingleton<ITempStagingService, TempStagingService>();
```

### Configuration Update
```json
{
  "BulkImporter": {
    "UseTempTables": true,  // Enable temp table mode
    // ... rest of configuration remains the same
  }
}
```

### Procedure Deployment
```sql
-- Deploy new stored procedures that work with temp tables
EXEC [dbo].[usp_ValidateAllTempData] @ImportSessionId = @SessionId;
EXEC [dbo].[usp_MergeAllTempToProduction] @ImportSessionId = @SessionId, @DryRun = 0;
```

## Best Practices

### Connection Management
```csharp
// Keep connection open for entire session to maintain temp tables
using var connection = new SqlConnection(connectionString);
await connection.OpenAsync();

// All temp table operations must use the same connection
await CreateTempTables(connection, sessionId);
await PopulateTempTables(connection, sessionId, data);
await ValidateTempTables(connection, sessionId);
await MergeTempTables(connection, sessionId);

// Temp tables auto-cleanup when connection closes
```

### Batch Size Management
```csharp
// Adjust batch size based on available tempdb space
var batchSize = Math.Min(options.BatchSize ?? 1000, 5000);

// For very large datasets, use smaller batches
if (totalRows > 100000) {
    batchSize = Math.Min(batchSize, 1000);
}
```

### Error Handling
```csharp
try {
    var result = await tempBulkImportService.ExecuteImportAsync(options);
    
    if (!result.Success && options.StopOnFirstError) {
        // Handle validation failures
        LogValidationErrors(result.Errors);
    } else {
        // Process partial success
        LogPartialSuccess(result);
    }
} catch (Exception ex) {
    // Connection errors, SQL errors, etc.
    LogProcessingError(ex);
}
```

## Troubleshooting

### Common Issues

#### "Temp table does not exist"
- **Cause**: Connection closed or temp table not created
- **Solution**: Ensure single connection used throughout session

#### "Out of tempdb space"
- **Cause**: Large dataset exceeding tempdb capacity
- **Solution**: Reduce batch size or increase tempdb size

#### "Cannot create index on temp table"
- **Cause**: Insufficient tempdb permissions
- **Solution**: Grant CREATE TABLE permissions in tempdb

### Performance Tuning

#### Slow Validation
```sql
-- Ensure indexes are created
SET TempTableIndexes = true

-- Reduce batch size for memory-constrained environments
SET TempTableBatchSize = 1000
```

#### High Memory Usage
```csharp
// Process in smaller chunks
var options = new ImportOptions {
    BatchSize = 500,  // Smaller batches
    TempTableBatchSize = 1000
};
```

### Monitoring
```csharp
// Enable detailed logging
"LogTempTableOperations": true

// Monitor session progress
var summary = await tempStagingService.GetTempStagingSummaryAsync(connection, sessionId);
Console.WriteLine($"Progress: {summary.ProcessedDriverRows}/{summary.TotalDriverRows} drivers");
```

## Conclusion

The temporary table implementation provides a **complete replacement** for permanent staging tables while working within table creation constraints. It maintains all enterprise-grade features including validation, error handling, performance optimization, and audit capabilities while providing automatic cleanup and session isolation.

This approach is **production-ready** and can handle the same scale and complexity as the original permanent staging table implementation.

