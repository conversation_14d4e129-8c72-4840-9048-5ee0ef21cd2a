import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr'
import { ref, reactive } from 'vue'

export interface ProgressUpdate {
    sessionId: string
    stage: string
    currentStep: number
    totalSteps: number
    percentage: number
    message: string
    estimatedTimeRemaining?: string
    timestamp: Date
}

export interface StatusUpdate {
    sessionId: string
    status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled'
    message: string
    timestamp: Date
}

export interface ErrorNotification {
    sessionId: string
    error: string
    details?: string
    timestamp: Date
}

export class SignalRService {
    private connection: HubConnection | null = null
    private isConnecting = ref(false)
    private isConnected = ref(false)
    private connectionError = ref<string | null>(null)
    private reconnectAttempts = 0
    private maxReconnectAttempts = 5
    private reconnectDelay = 1000 // Start with 1 second

    // Event handlers
    private progressHandlers = new Set<(update: ProgressUpdate) => void>()
    private statusHandlers = new Set<(update: StatusUpdate) => void>()
    private errorHandlers = new Set<(error: ErrorNotification) => void>()

    constructor(private baseUrl: string = '/hub/bulkimport') {
        this.setupConnection()
    }

    private setupConnection() {
        this.connection = new HubConnectionBuilder()
            .withUrl(this.baseUrl)
            .withAutomaticReconnect({
                nextRetryDelayInMilliseconds: (retryContext) => {
                    // Exponential backoff with jitter
                    const delay = Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000)
                    const jitter = Math.random() * 1000
                    return delay + jitter
                }
            })
            .configureLogging(LogLevel.Information)
            .build()

        this.setupEventHandlers()
    }

    private setupEventHandlers() {
        if (!this.connection) return

        // Connection state handlers
        this.connection.onclose((error) => {
            this.isConnected.value = false
            if (error) {
                console.error('SignalR connection closed with error:', error)
                this.connectionError.value = error.message
            }
        })

        this.connection.onreconnecting((error) => {
            this.isConnected.value = false
            console.log('SignalR reconnecting...', error)
        })

        this.connection.onreconnected(() => {
            this.isConnected.value = true
            this.connectionError.value = null
            this.reconnectAttempts = 0
            console.log('SignalR reconnected successfully')
        })

        // Progress updates
        this.connection.on('ProgressUpdate', (update: ProgressUpdate) => {
            update.timestamp = new Date(update.timestamp)
            this.progressHandlers.forEach(handler => handler(update))
        })

        // Status updates
        this.connection.on('StatusUpdate', (update: StatusUpdate) => {
            update.timestamp = new Date(update.timestamp)
            this.statusHandlers.forEach(handler => handler(update))
        })

        // Error notifications
        this.connection.on('ErrorNotification', (error: ErrorNotification) => {
            error.timestamp = new Date(error.timestamp)
            this.errorHandlers.forEach(handler => handler(error))
        })

        // Import completion
        this.connection.on('ImportCompleted', (data: { sessionId: string; summary: any }) => {
            const statusUpdate: StatusUpdate = {
                sessionId: data.sessionId,
                status: 'completed',
                message: 'Import operation completed successfully',
                timestamp: new Date()
            }
            this.statusHandlers.forEach(handler => handler(statusUpdate))
        })

        // Import failed
        this.connection.on('ImportFailed', (data: { sessionId: string; error: string; details?: string }) => {
            const statusUpdate: StatusUpdate = {
                sessionId: data.sessionId,
                status: 'failed',
                message: data.error,
                timestamp: new Date()
            }
            this.statusHandlers.forEach(handler => handler(statusUpdate))

            const errorNotification: ErrorNotification = {
                sessionId: data.sessionId,
                error: data.error,
                details: data.details,
                timestamp: new Date()
            }
            this.errorHandlers.forEach(handler => handler(errorNotification))
        })
    }

    async connect(): Promise<void> {
        if (this.isConnected.value || this.isConnecting.value || !this.connection) {
            return
        }

        this.isConnecting.value = true
        this.connectionError.value = null

        try {
            await this.connection.start()
            this.isConnected.value = true
            this.reconnectAttempts = 0
            console.log('SignalR connected successfully')
        } catch (error) {
            console.error('Failed to connect to SignalR:', error)
            this.connectionError.value = error instanceof Error ? error.message : 'Connection failed'

            // Retry connection with exponential backoff
            this.scheduleReconnect()
        } finally {
            this.isConnecting.value = false
        }
    }

    private scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached')
            return
        }

        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts), 30000)
        this.reconnectAttempts++

        setTimeout(() => {
            if (!this.isConnected.value) {
                console.log(`Attempting to reconnect (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
                this.connect()
            }
        }, delay)
    }

    async disconnect(): Promise<void> {
        if (this.connection && this.isConnected.value) {
            await this.connection.stop()
            this.isConnected.value = false
        }
    }

    async joinSession(sessionId: string): Promise<void> {
        if (!this.connection || !this.isConnected.value) {
            throw new Error('SignalR connection not established')
        }

        try {
            await this.connection.invoke('JoinSession', sessionId)
            console.log(`Joined session: ${sessionId}`)
        } catch (error) {
            console.error(`Failed to join session ${sessionId}:`, error)
            throw error
        }
    }

    async leaveSession(sessionId: string): Promise<void> {
        if (!this.connection || !this.isConnected.value) {
            return // Connection already closed
        }

        try {
            await this.connection.invoke('LeaveSession', sessionId)
            console.log(`Left session: ${sessionId}`)
        } catch (error) {
            console.error(`Failed to leave session ${sessionId}:`, error)
            // Don't throw, as this is not critical
        }
    }

    // Event subscription methods
    onProgress(handler: (update: ProgressUpdate) => void): () => void {
        this.progressHandlers.add(handler)
        return () => this.progressHandlers.delete(handler)
    }

    onStatus(handler: (update: StatusUpdate) => void): () => void {
        this.statusHandlers.add(handler)
        return () => this.statusHandlers.delete(handler)
    }

    onError(handler: (error: ErrorNotification) => void): () => void {
        this.errorHandlers.add(handler)
        return () => this.errorHandlers.delete(handler)
    }

    // Getters for reactive state
    get connected() {
        return this.isConnected.value
    }

    get connecting() {
        return this.isConnecting.value
    }

    get error() {
        return this.connectionError.value
    }

    get state() {
        return this.connection?.state || 'Disconnected'
    }
}

// Export singleton instance
export const signalRService = new SignalRService()

// Vue composable for using SignalR
export function useSignalR() {
    const progressUpdates = reactive(new Map<string, ProgressUpdate>())
    const statusUpdates = reactive(new Map<string, StatusUpdate>())
    const errorNotifications = reactive(new Map<string, ErrorNotification[]>())

    const unsubscribeHandlers: (() => void)[] = []

    const subscribe = () => {
        // Subscribe to progress updates
        const unsubProgress = signalRService.onProgress((update) => {
            progressUpdates.set(update.sessionId, update)
        })

        // Subscribe to status updates
        const unsubStatus = signalRService.onStatus((update) => {
            statusUpdates.set(update.sessionId, update)
        })

        // Subscribe to error notifications
        const unsubError = signalRService.onError((error) => {
            if (!errorNotifications.has(error.sessionId)) {
                errorNotifications.set(error.sessionId, [])
            }
            errorNotifications.get(error.sessionId)!.push(error)
        })

        unsubscribeHandlers.push(unsubProgress, unsubStatus, unsubError)
    }

    const unsubscribe = () => {
        unsubscribeHandlers.forEach(unsub => unsub())
        unsubscribeHandlers.length = 0
    }

    const clearSession = (sessionId: string) => {
        progressUpdates.delete(sessionId)
        statusUpdates.delete(sessionId)
        errorNotifications.delete(sessionId)
    }

    const getSessionProgress = (sessionId: string) => {
        return progressUpdates.get(sessionId)
    }

    const getSessionStatus = (sessionId: string) => {
        return statusUpdates.get(sessionId)
    }

    const getSessionErrors = (sessionId: string) => {
        return errorNotifications.get(sessionId) || []
    }

    return {
        // Service instance
        service: signalRService,

        // Reactive data
        progressUpdates,
        statusUpdates,
        errorNotifications,

        // Methods
        subscribe,
        unsubscribe,
        clearSession,
        getSessionProgress,
        getSessionStatus,
        getSessionErrors,

        // Service state
        connected: ref(() => signalRService.connected),
        connecting: ref(() => signalRService.connecting),
        error: ref(() => signalRService.error)
    }
}
