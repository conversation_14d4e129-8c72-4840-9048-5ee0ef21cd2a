using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Web.Services.Monitoring
{
    public class AuditTrailService : IAuditTrailService
    {
        private readonly ILogger<AuditTrailService> _logger;
        private readonly MigrationConfiguration _config;
        private readonly AuditConfiguration _auditConfig;
        private readonly string _connectionString;

        public AuditTrailService(
            ILogger<AuditTrailService> logger,
            IOptions<MigrationConfiguration> config,
            IOptions<AuditConfiguration> auditConfig)
        {
            _logger = logger;
            _config = config.Value;
            _auditConfig = auditConfig.Value;
            _connectionString = _config.DatabaseConnection;

            InitializeAuditTableAsync().ConfigureAwait(false);
        }

        public async Task LogOperationAsync(AuditEntry entry, CancellationToken cancellationToken = default)
        {
            try
            {
                // Mask sensitive data if enabled
                if (_auditConfig.MaskSensitiveData)
                {
                    MaskSensitiveData(entry);
                }

                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync(cancellationToken);

                var sql = @"
                    INSERT INTO AuditTrail (
                        AuditId, SessionId, Timestamp, UserId, UserName, EventType, OperationType,
                        EntityType, EntityId, Action, Description, BeforeData, AfterData, Metadata,
                        Success, ErrorMessage, Duration, IpAddress, UserAgent, Severity, CorrelationId
                    ) VALUES (
                        @AuditId, @SessionId, @Timestamp, @UserId, @UserName, @EventType, @OperationType,
                        @EntityType, @EntityId, @Action, @Description, @BeforeData, @AfterData, @Metadata,
                        @Success, @ErrorMessage, @Duration, @IpAddress, @UserAgent, @Severity, @CorrelationId
                    )";

                using var cmd = new SqlCommand(sql, connection);
                AddAuditParameters(cmd, entry);

                await cmd.ExecuteNonQueryAsync(cancellationToken);

                _logger.LogDebug("Audit entry logged: {EventType} - {Action} by {UserId}", 
                    entry.EventType, entry.Action, entry.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log audit entry for {EventType} - {Action}", 
                    entry.EventType, entry.Action);
                // Don't throw to avoid breaking the main operation
            }
        }

        public async Task<List<AuditEntry>> GetAuditTrailAsync(AuditFilter filter, CancellationToken cancellationToken = default)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            var sql = BuildAuditQuery(filter);
            using var cmd = new SqlCommand(sql, connection);
            AddFilterParameters(cmd, filter);

            var entries = new List<AuditEntry>();
            using var reader = await cmd.ExecuteReaderAsync(cancellationToken);

            while (await reader.ReadAsync(cancellationToken))
            {
                entries.Add(ReadAuditEntry(reader));
            }

            _logger.LogDebug("Retrieved {Count} audit entries for filter", entries.Count);
            return entries;
        }

        public async Task<AuditSummary> GetAuditSummaryAsync(Guid sessionId, CancellationToken cancellationToken = default)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            var entries = await GetAuditTrailAsync(new AuditFilter { SessionId = sessionId }, cancellationToken);

            if (!entries.Any())
            {
                return new AuditSummary { SessionId = sessionId };
            }

            var summary = new AuditSummary
            {
                SessionId = sessionId,
                SessionStart = entries.Min(e => e.Timestamp),
                SessionEnd = entries.Max(e => e.Timestamp),
                TotalEntries = entries.Count,
                SuccessfulOperations = entries.Count(e => e.Success),
                FailedOperations = entries.Count(e => !e.Success),
                EventTypeCounts = entries.GroupBy(e => e.EventType).ToDictionary(g => g.Key, g => g.Count()),
                ActionCounts = entries.GroupBy(e => e.Action).ToDictionary(g => g.Key, g => g.Count()),
                EntityTypeCounts = entries.GroupBy(e => e.EntityType).ToDictionary(g => g.Key, g => g.Count()),
                UniqueUsers = entries.Select(e => e.UserId).Distinct().ToList(),
                CriticalEvents = entries.Where(e => e.Severity == AuditSeverity.Critical).ToList(),
                Errors = entries.Where(e => e.Severity == AuditSeverity.Error).ToList()
            };

            summary.SessionDuration = summary.SessionEnd?.Subtract(summary.SessionStart);

            _logger.LogDebug("Generated audit summary for session {SessionId}: {TotalEntries} entries, {SuccessRate:F1}% success rate",
                sessionId, summary.TotalEntries, summary.SuccessRate);

            return summary;
        }

        public async Task CleanupOldAuditEntriesAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default)
        {
            if (!_auditConfig.EnableAutomaticCleanup)
            {
                _logger.LogDebug("Automatic audit cleanup is disabled");
                return;
            }

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync(cancellationToken);

                var cutoffDate = DateTime.UtcNow.Subtract(retentionPeriod);
                var criticalCutoffDate = DateTime.UtcNow.Subtract(_auditConfig.CriticalEventRetention);

                // Delete non-critical entries older than retention period
                var deleteSql = @"
                    DELETE FROM AuditTrail 
                    WHERE Timestamp < @CutoffDate 
                    AND Severity != @CriticalSeverity";

                using var cmd = new SqlCommand(deleteSql, connection);
                cmd.Parameters.AddWithValue("@CutoffDate", cutoffDate);
                cmd.Parameters.AddWithValue("@CriticalSeverity", (int)AuditSeverity.Critical);

                var deletedCount = await cmd.ExecuteNonQueryAsync(cancellationToken);

                // Delete even critical entries if they're older than critical retention period
                var deleteCriticalSql = @"
                    DELETE FROM AuditTrail 
                    WHERE Timestamp < @CriticalCutoffDate";

                using var criticalCmd = new SqlCommand(deleteCriticalSql, connection);
                criticalCmd.Parameters.AddWithValue("@CriticalCutoffDate", criticalCutoffDate);

                var criticalDeletedCount = await criticalCmd.ExecuteNonQueryAsync(cancellationToken);

                _logger.LogInformation("Audit cleanup completed: {DeletedCount} standard entries, {CriticalDeletedCount} critical entries removed",
                    deletedCount, criticalDeletedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup old audit entries");
                throw;
            }
        }

        public async Task<List<AuditEntry>> SearchAuditEntriesAsync(AuditSearchCriteria criteria, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(criteria.SearchTerm))
            {
                return await GetAuditTrailAsync(criteria.Filter ?? new AuditFilter(), cancellationToken);
            }

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            var searchConditions = new List<string>();
            var searchTerm = criteria.CaseSensitive ? criteria.SearchTerm : criteria.SearchTerm.ToLower();
            
            if (criteria.UseWildcards && !searchTerm.Contains('%'))
            {
                searchTerm = $"%{searchTerm}%";
            }

            foreach (var field in criteria.SearchFields)
            {
                var fieldCondition = criteria.CaseSensitive 
                    ? $"[{field}] LIKE @SearchTerm"
                    : $"LOWER([{field}]) LIKE @SearchTerm";
                searchConditions.Add(fieldCondition);
            }

            var baseQuery = BuildAuditQuery(criteria.Filter ?? new AuditFilter());
            var searchQuery = baseQuery.Replace("WHERE", $"WHERE ({string.Join(" OR ", searchConditions)}) AND");

            using var cmd = new SqlCommand(searchQuery, connection);
            cmd.Parameters.AddWithValue("@SearchTerm", searchTerm);
            AddFilterParameters(cmd, criteria.Filter ?? new AuditFilter());

            var entries = new List<AuditEntry>();
            using var reader = await cmd.ExecuteReaderAsync(cancellationToken);

            while (await reader.ReadAsync(cancellationToken))
            {
                entries.Add(ReadAuditEntry(reader));
            }

            _logger.LogDebug("Search found {Count} audit entries for term '{SearchTerm}'", entries.Count, criteria.SearchTerm);
            return entries;
        }

        public async Task ExportAuditTrailAsync(AuditFilter filter, string exportPath, AuditExportFormat format, CancellationToken cancellationToken = default)
        {
            var entries = await GetAuditTrailAsync(filter, cancellationToken);

            switch (format)
            {
                case AuditExportFormat.Json:
                    await ExportToJsonAsync(entries, exportPath, cancellationToken);
                    break;
                case AuditExportFormat.Csv:
                    await ExportToCsvAsync(entries, exportPath, cancellationToken);
                    break;
                default:
                    throw new NotSupportedException($"Export format {format} is not yet implemented");
            }

            _logger.LogInformation("Exported {Count} audit entries to {Path} in {Format} format",
                entries.Count, exportPath, format);
        }

        private async Task InitializeAuditTableAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var createTableSql = @"
                    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'AuditTrail')
                    BEGIN
                        CREATE TABLE AuditTrail (
                            AuditId UNIQUEIDENTIFIER PRIMARY KEY,
                            SessionId UNIQUEIDENTIFIER NOT NULL,
                            Timestamp DATETIME2 NOT NULL,
                            UserId NVARCHAR(255) NOT NULL,
                            UserName NVARCHAR(255) NOT NULL,
                            EventType INT NOT NULL,
                            OperationType NVARCHAR(255) NOT NULL,
                            EntityType NVARCHAR(255) NOT NULL,
                            EntityId NVARCHAR(255) NOT NULL,
                            Action INT NOT NULL,
                            Description NVARCHAR(MAX) NOT NULL,
                            BeforeData NVARCHAR(MAX),
                            AfterData NVARCHAR(MAX),
                            Metadata NVARCHAR(MAX),
                            Success BIT NOT NULL,
                            ErrorMessage NVARCHAR(MAX),
                            Duration BIGINT,
                            IpAddress NVARCHAR(45),
                            UserAgent NVARCHAR(MAX),
                            Severity INT NOT NULL,
                            CorrelationId NVARCHAR(255)
                        );

                        CREATE INDEX IX_AuditTrail_SessionId ON AuditTrail(SessionId);
                        CREATE INDEX IX_AuditTrail_Timestamp ON AuditTrail(Timestamp);
                        CREATE INDEX IX_AuditTrail_UserId ON AuditTrail(UserId);
                        CREATE INDEX IX_AuditTrail_EventType ON AuditTrail(EventType);
                        CREATE INDEX IX_AuditTrail_Severity ON AuditTrail(Severity);
                    END";

                using var cmd = new SqlCommand(createTableSql, connection);
                await cmd.ExecuteNonQueryAsync();

                _logger.LogDebug("Audit trail table initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize audit trail table");
                throw;
            }
        }

        private void MaskSensitiveData(AuditEntry entry)
        {
            foreach (var sensitiveField in _auditConfig.SensitiveFields)
            {
                MaskDataInDictionary(entry.BeforeData, sensitiveField);
                MaskDataInDictionary(entry.AfterData, sensitiveField);
                MaskDataInDictionary(entry.Metadata, sensitiveField);
            }
        }

        private void MaskDataInDictionary(Dictionary<string, object> data, string sensitiveField)
        {
            if (data.ContainsKey(sensitiveField))
            {
                data[sensitiveField] = "***MASKED***";
            }

            // Check for case-insensitive matches
            var keys = data.Keys.ToList();
            foreach (var key in keys)
            {
                if (string.Equals(key, sensitiveField, StringComparison.OrdinalIgnoreCase) && key != sensitiveField)
                {
                    data[key] = "***MASKED***";
                }
            }
        }

        private string BuildAuditQuery(AuditFilter filter)
        {
            var sql = @"
                SELECT AuditId, SessionId, Timestamp, UserId, UserName, EventType, OperationType,
                       EntityType, EntityId, Action, Description, BeforeData, AfterData, Metadata,
                       Success, ErrorMessage, Duration, IpAddress, UserAgent, Severity, CorrelationId
                FROM AuditTrail
                WHERE 1=1";

            if (filter.StartDate.HasValue)
                sql += " AND Timestamp >= @StartDate";
            if (filter.EndDate.HasValue)
                sql += " AND Timestamp <= @EndDate";
            if (filter.SessionId.HasValue)
                sql += " AND SessionId = @SessionId";
            if (!string.IsNullOrEmpty(filter.UserId))
                sql += " AND UserId = @UserId";
            if (filter.EventType.HasValue)
                sql += " AND EventType = @EventType";
            if (filter.Action.HasValue)
                sql += " AND Action = @Action";
            if (!string.IsNullOrEmpty(filter.EntityType))
                sql += " AND EntityType = @EntityType";
            if (filter.Severity.HasValue)
                sql += " AND Severity = @Severity";
            if (filter.Success.HasValue)
                sql += " AND Success = @Success";

            sql += $" ORDER BY {filter.OrderBy} {(filter.OrderDescending ? "DESC" : "ASC")}";
            sql += " OFFSET @Skip ROWS FETCH NEXT @Take ROWS ONLY";

            return sql;
        }

        private void AddFilterParameters(SqlCommand cmd, AuditFilter filter)
        {
            if (filter.StartDate.HasValue)
                cmd.Parameters.AddWithValue("@StartDate", filter.StartDate.Value);
            if (filter.EndDate.HasValue)
                cmd.Parameters.AddWithValue("@EndDate", filter.EndDate.Value);
            if (filter.SessionId.HasValue)
                cmd.Parameters.AddWithValue("@SessionId", filter.SessionId.Value);
            if (!string.IsNullOrEmpty(filter.UserId))
                cmd.Parameters.AddWithValue("@UserId", filter.UserId);
            if (filter.EventType.HasValue)
                cmd.Parameters.AddWithValue("@EventType", (int)filter.EventType.Value);
            if (filter.Action.HasValue)
                cmd.Parameters.AddWithValue("@Action", (int)filter.Action.Value);
            if (!string.IsNullOrEmpty(filter.EntityType))
                cmd.Parameters.AddWithValue("@EntityType", filter.EntityType);
            if (filter.Severity.HasValue)
                cmd.Parameters.AddWithValue("@Severity", (int)filter.Severity.Value);
            if (filter.Success.HasValue)
                cmd.Parameters.AddWithValue("@Success", filter.Success.Value);

            cmd.Parameters.AddWithValue("@Skip", filter.Skip);
            cmd.Parameters.AddWithValue("@Take", filter.Take);
        }

        private void AddAuditParameters(SqlCommand cmd, AuditEntry entry)
        {
            cmd.Parameters.AddWithValue("@AuditId", entry.AuditId);
            cmd.Parameters.AddWithValue("@SessionId", entry.SessionId);
            cmd.Parameters.AddWithValue("@Timestamp", entry.Timestamp);
            cmd.Parameters.AddWithValue("@UserId", entry.UserId);
            cmd.Parameters.AddWithValue("@UserName", entry.UserName);
            cmd.Parameters.AddWithValue("@EventType", (int)entry.EventType);
            cmd.Parameters.AddWithValue("@OperationType", entry.OperationType);
            cmd.Parameters.AddWithValue("@EntityType", entry.EntityType);
            cmd.Parameters.AddWithValue("@EntityId", entry.EntityId);
            cmd.Parameters.AddWithValue("@Action", (int)entry.Action);
            cmd.Parameters.AddWithValue("@Description", entry.Description);
            cmd.Parameters.AddWithValue("@BeforeData", JsonSerializer.Serialize(entry.BeforeData));
            cmd.Parameters.AddWithValue("@AfterData", JsonSerializer.Serialize(entry.AfterData));
            cmd.Parameters.AddWithValue("@Metadata", JsonSerializer.Serialize(entry.Metadata));
            cmd.Parameters.AddWithValue("@Success", entry.Success);
            cmd.Parameters.AddWithValue("@ErrorMessage", (object?)entry.ErrorMessage ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Duration", entry.Duration?.Ticks ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@IpAddress", entry.IpAddress);
            cmd.Parameters.AddWithValue("@UserAgent", entry.UserAgent);
            cmd.Parameters.AddWithValue("@Severity", (int)entry.Severity);
            cmd.Parameters.AddWithValue("@CorrelationId", entry.CorrelationId);
        }

        private AuditEntry ReadAuditEntry(SqlDataReader reader)
        {
            return new AuditEntry
            {
                AuditId = reader.GetGuid("AuditId"),
                SessionId = reader.GetGuid("SessionId"),
                Timestamp = reader.GetDateTime("Timestamp"),
                UserId = reader.GetString("UserId"),
                UserName = reader.GetString("UserName"),
                EventType = (AuditEventType)reader.GetInt32("EventType"),
                OperationType = reader.GetString("OperationType"),
                EntityType = reader.GetString("EntityType"),
                EntityId = reader.GetString("EntityId"),
                Action = (AuditAction)reader.GetInt32("Action"),
                Description = reader.GetString("Description"),
                BeforeData = JsonSerializer.Deserialize<Dictionary<string, object>>(reader.GetString("BeforeData")) ?? new(),
                AfterData = JsonSerializer.Deserialize<Dictionary<string, object>>(reader.GetString("AfterData")) ?? new(),
                Metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(reader.GetString("Metadata")) ?? new(),
                Success = reader.GetBoolean("Success"),
                ErrorMessage = reader.IsDBNull("ErrorMessage") ? null : reader.GetString("ErrorMessage"),
                Duration = reader.IsDBNull("Duration") ? null : TimeSpan.FromTicks(reader.GetInt64("Duration")),
                IpAddress = reader.GetString("IpAddress"),
                UserAgent = reader.GetString("UserAgent"),
                Severity = (AuditSeverity)reader.GetInt32("Severity"),
                CorrelationId = reader.GetString("CorrelationId")
            };
        }

        private async Task ExportToJsonAsync(List<AuditEntry> entries, string exportPath, CancellationToken cancellationToken)
        {
            var json = JsonSerializer.Serialize(entries, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await File.WriteAllTextAsync(exportPath, json, cancellationToken);
        }

        private async Task ExportToCsvAsync(List<AuditEntry> entries, string exportPath, CancellationToken cancellationToken)
        {
            using var writer = new StreamWriter(exportPath);
            
            // Write header
            await writer.WriteLineAsync("AuditId,SessionId,Timestamp,UserId,UserName,EventType,OperationType,EntityType,EntityId,Action,Description,Success,ErrorMessage,Duration,IpAddress,Severity,CorrelationId");

            // Write data
            foreach (var entry in entries)
            {
                var line = $"{entry.AuditId},{entry.SessionId},{entry.Timestamp:yyyy-MM-dd HH:mm:ss},{entry.UserId},{entry.UserName},{entry.EventType},{entry.OperationType},{entry.EntityType},{entry.EntityId},{entry.Action},\"{entry.Description.Replace("\"", "\"\"")}\",{entry.Success},\"{entry.ErrorMessage?.Replace("\"", "\"\"")}\",{entry.Duration?.TotalMilliseconds},{entry.IpAddress},{entry.Severity},{entry.CorrelationId}";
                await writer.WriteLineAsync(line);
            }
        }
    }
}
