using Microsoft.AspNetCore.Mvc;
using System.Collections.Concurrent;
using FleetXQ.Tools.BulkImporter.Core.Services;

namespace FleetXQ.Tools.BulkImporter.WebApi.Controllers;

/// <summary>
/// Controller for managing bulk import operations and sessions
/// </summary>
[ApiController]
[Route("api/bulk-import")]
[Produces("application/json")]
public class BulkImportController : ControllerBase
{
    private readonly ILogger<BulkImportController> _logger;
    private readonly IBulkImportService _bulkImportService;
    private readonly IEnvironmentService _environmentService;

    // In-memory session tracking (in production, this would be in a database or cache)
    private static readonly ConcurrentDictionary<Guid, ImportSessionInfo> _activeSessions = new();

    public BulkImportController(
        ILogger<BulkImportController> logger,
        IBulkImportService bulkImportService,
        IEnvironmentService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _bulkImportService = bulkImportService ?? throw new ArgumentNullException(nameof(bulkImportService));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    /// <summary>
    /// Creates a new import session
    /// </summary>
    /// <param name="request">Import session creation request</param>
    /// <returns>Created import session information</returns>
    /// <response code="201">Returns the created import session</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost("sessions")]
    [ProducesResponseType(typeof(ImportSessionInfo), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ImportSessionInfo>> CreateSession([FromBody] CreateImportSessionRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Validate request
            var validationErrors = new List<string>();

            if (request.DealerId == Guid.Empty)
                validationErrors.Add("DealerId is required");

            if (request.CustomerId == Guid.Empty)
                validationErrors.Add("CustomerId is required");

            if (request.DriversCount <= 0)
                validationErrors.Add("DriversCount must be greater than 0");

            if (request.VehiclesCount <= 0)
                validationErrors.Add("VehiclesCount must be greater than 0");

            if (validationErrors.Any())
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Validation failed",
                    Detail = string.Join("; ", validationErrors),
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Basic validation for demo purposes
            var totalOperationSize = request.DriversCount + request.VehiclesCount;
            if (totalOperationSize > 10000)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Operation too large",
                    Detail = "Total operation size cannot exceed 10,000 records",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Creating import session for dealer: {DealerId}, customer: {CustomerId}, drivers: {DriversCount}, vehicles: {VehiclesCount}",
                request.DealerId, request.CustomerId, request.DriversCount, request.VehiclesCount);

            // Validate environment
            var environmentValidation = _environmentService.ValidateOperation(totalOperationSize);
            if (!environmentValidation.IsValid)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Environment validation failed",
                    Detail = environmentValidation.ErrorMessage,
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Create session
            var sessionId = Guid.NewGuid();
            var sessionInfo = new ImportSessionInfo
            {
                Id = sessionId,
                DealerId = request.DealerId,
                CustomerId = request.CustomerId,
                DriversCount = request.DriversCount,
                VehiclesCount = request.VehiclesCount,
                BatchSize = request.BatchSize ?? 1000, // Default batch size
                DryRun = request.DryRun,
                Status = ImportSessionStatus.Created,
                CreatedAt = DateTime.UtcNow,
                EnvironmentName = _environmentService.Environment.Name,
                RequiresApproval = environmentValidation.RequiresApproval,
                Warnings = environmentValidation.Warnings
            };

            // Store session
            _activeSessions[sessionId] = sessionInfo;

            _logger.LogDebug("Created import session: {SessionId}", sessionId);

            return CreatedAtAction(nameof(GetSession), new { id = sessionId }, sessionInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating import session");
            return Problem(
                title: "Error creating import session",
                detail: "An error occurred while creating the import session",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets information about a specific import session
    /// </summary>
    /// <param name="id">Import session ID</param>
    /// <returns>Import session information</returns>
    /// <response code="200">Returns the import session information</response>
    /// <response code="404">If the session is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("sessions/{id:guid}")]
    [ProducesResponseType(typeof(ImportSessionInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public ActionResult<ImportSessionInfo> GetSession(Guid id)
    {
        try
        {
            _logger.LogDebug("Getting import session: {SessionId}", id);

            if (!_activeSessions.TryGetValue(id, out var sessionInfo))
            {
                _logger.LogWarning("Import session not found: {SessionId}", id);
                return NotFound(new ProblemDetails
                {
                    Title = "Session not found",
                    Detail = $"No import session found with ID: {id}",
                    Status = StatusCodes.Status404NotFound
                });
            }

            _logger.LogDebug("Retrieved import session: {SessionId}, status: {Status}", id, sessionInfo.Status);
            return Ok(sessionInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving import session: {SessionId}", id);
            return Problem(
                title: "Error retrieving import session",
                detail: "An error occurred while retrieving the import session",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets a list of all import sessions
    /// </summary>
    /// <param name="status">Optional status filter</param>
    /// <param name="dealerId">Optional dealer ID filter</param>
    /// <param name="limit">Maximum number of sessions to return (default: 50, max: 100)</param>
    /// <returns>List of import sessions</returns>
    /// <response code="200">Returns the list of import sessions</response>
    /// <response code="400">If the request parameters are invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("sessions")]
    [ProducesResponseType(typeof(IEnumerable<ImportSessionInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public ActionResult<IEnumerable<ImportSessionInfo>> GetSessions(
        [FromQuery] ImportSessionStatus? status = null,
        [FromQuery] Guid? dealerId = null,
        [FromQuery] int limit = 50)
    {
        try
        {
            if (limit < 1 || limit > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid limit",
                    Detail = "Limit must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Getting import sessions with status: {Status}, dealerId: {DealerId}, limit: {Limit}",
                status, dealerId, limit);

            var sessions = _activeSessions.Values.AsEnumerable();

            // Apply filters
            if (status.HasValue)
            {
                sessions = sessions.Where(s => s.Status == status.Value);
            }

            if (dealerId.HasValue)
            {
                sessions = sessions.Where(s => s.DealerId == dealerId.Value);
            }

            // Order by creation time (newest first) and apply limit
            var result = sessions
                .OrderByDescending(s => s.CreatedAt)
                .Take(limit)
                .ToList();

            _logger.LogDebug("Retrieved {Count} import sessions", result.Count);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving import sessions");
            return Problem(
                title: "Error retrieving import sessions",
                detail: "An error occurred while retrieving the import sessions",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Executes an import session
    /// </summary>
    /// <param name="id">Import session ID</param>
    /// <returns>Import execution result</returns>
    /// <response code="200">Returns the import execution result</response>
    /// <response code="404">If the session is not found</response>
    /// <response code="400">If the session cannot be executed</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost("sessions/{id:guid}/execute")]
    [ProducesResponseType(typeof(ImportExecutionResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ImportExecutionResult>> ExecuteSession(Guid id)
    {
        try
        {
            _logger.LogDebug("Executing import session: {SessionId}", id);

            if (!_activeSessions.TryGetValue(id, out var sessionInfo))
            {
                _logger.LogWarning("Import session not found for execution: {SessionId}", id);
                return NotFound(new ProblemDetails
                {
                    Title = "Session not found",
                    Detail = $"No import session found with ID: {id}",
                    Status = StatusCodes.Status404NotFound
                });
            }

            // Check if session can be executed
            if (sessionInfo.Status != ImportSessionStatus.Created)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Session cannot be executed",
                    Detail = $"Session is in {sessionInfo.Status} status and cannot be executed",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Update session status
            sessionInfo.Status = ImportSessionStatus.Running;
            sessionInfo.StartedAt = DateTime.UtcNow;

            try
            {
                // Create import options from session
                var importOptions = new ImportOptions
                {
                    DriversCount = sessionInfo.DriversCount,
                    VehiclesCount = sessionInfo.VehiclesCount,
                    BatchSize = sessionInfo.BatchSize,
                    DryRun = sessionInfo.DryRun,
                    GenerateData = true,
                    Interactive = false
                };

                // Execute the import
                var importResult = await _bulkImportService.ExecuteImportAsync(importOptions);

                // Update session with results
                sessionInfo.Status = importResult.Success ? ImportSessionStatus.Completed : ImportSessionStatus.Failed;
                sessionInfo.CompletedAt = DateTime.UtcNow;
                sessionInfo.TotalRows = importResult.TotalRows;
                sessionInfo.ProcessedRows = importResult.ProcessedRows;
                sessionInfo.SuccessfulRows = importResult.SuccessfulRows;
                sessionInfo.FailedRows = importResult.FailedRows;
                sessionInfo.Duration = importResult.Duration;
                sessionInfo.Errors = importResult.Errors;
                sessionInfo.Warnings.AddRange(importResult.Warnings);
                sessionInfo.Summary = importResult.Summary;

                var executionResult = new ImportExecutionResult
                {
                    SessionId = id,
                    Success = importResult.Success,
                    TotalRows = importResult.TotalRows,
                    ProcessedRows = importResult.ProcessedRows,
                    SuccessfulRows = importResult.SuccessfulRows,
                    FailedRows = importResult.FailedRows,
                    Duration = importResult.Duration,
                    Errors = importResult.Errors,
                    Warnings = sessionInfo.Warnings,
                    Summary = importResult.Summary,
                    Status = sessionInfo.Status
                };

                _logger.LogDebug("Import session executed: {SessionId}, success: {Success}", id, importResult.Success);
                return Ok(executionResult);
            }
            catch (Exception ex)
            {
                // Update session status on error
                sessionInfo.Status = ImportSessionStatus.Failed;
                sessionInfo.CompletedAt = DateTime.UtcNow;
                sessionInfo.Errors = new List<string> { ex.Message };

                _logger.LogError(ex, "Error executing import session: {SessionId}", id);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing import session: {SessionId}", id);
            return Problem(
                title: "Error executing import session",
                detail: "An error occurred while executing the import session",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Cancels or cleans up an import session
    /// </summary>
    /// <param name="id">Import session ID</param>
    /// <returns>Cancellation result</returns>
    /// <response code="200">Returns the cancellation result</response>
    /// <response code="404">If the session is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpDelete("sessions/{id:guid}")]
    [ProducesResponseType(typeof(SessionCancellationResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public ActionResult<SessionCancellationResult> CancelSession(Guid id)
    {
        try
        {
            _logger.LogDebug("Cancelling import session: {SessionId}", id);

            if (!_activeSessions.TryGetValue(id, out var sessionInfo))
            {
                _logger.LogWarning("Import session not found for cancellation: {SessionId}", id);
                return NotFound(new ProblemDetails
                {
                    Title = "Session not found",
                    Detail = $"No import session found with ID: {id}",
                    Status = StatusCodes.Status404NotFound
                });
            }

            var originalStatus = sessionInfo.Status;

            // Update session status based on current state
            switch (sessionInfo.Status)
            {
                case ImportSessionStatus.Created:
                    sessionInfo.Status = ImportSessionStatus.Cancelled;
                    break;
                case ImportSessionStatus.Running:
                    // In a real implementation, this would signal the running operation to stop
                    sessionInfo.Status = ImportSessionStatus.Cancelled;
                    break;
                case ImportSessionStatus.Completed:
                case ImportSessionStatus.Failed:
                case ImportSessionStatus.Cancelled:
                    // Already in a final state, just remove from tracking
                    break;
            }

            sessionInfo.CompletedAt = DateTime.UtcNow;

            // Remove from active sessions
            _activeSessions.TryRemove(id, out _);

            var result = new SessionCancellationResult
            {
                SessionId = id,
                PreviousStatus = originalStatus,
                CurrentStatus = sessionInfo.Status,
                WasCancelled = originalStatus != ImportSessionStatus.Completed && originalStatus != ImportSessionStatus.Failed,
                Message = GetCancellationMessage(originalStatus, sessionInfo.Status)
            };

            _logger.LogDebug("Import session cancelled: {SessionId}, previous status: {PreviousStatus}, current status: {CurrentStatus}",
                id, originalStatus, sessionInfo.Status);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling import session: {SessionId}", id);
            return Problem(
                title: "Error cancelling import session",
                detail: "An error occurred while cancelling the import session",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static string GetCancellationMessage(ImportSessionStatus previousStatus, ImportSessionStatus currentStatus)
    {
        return previousStatus switch
        {
            ImportSessionStatus.Created => "Session was cancelled before execution",
            ImportSessionStatus.Running => "Session was cancelled during execution",
            ImportSessionStatus.Completed => "Session was already completed",
            ImportSessionStatus.Failed => "Session had already failed",
            ImportSessionStatus.Cancelled => "Session was already cancelled",
            _ => "Session status updated"
        };
    }
}

/// <summary>
/// Request for creating a new import session
/// </summary>
public class CreateImportSessionRequest
{
    /// <summary>
    /// Dealer ID for the import operation (required)
    /// </summary>
    public Guid DealerId { get; set; }

    /// <summary>
    /// Customer ID for the import operation (required)
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// Number of drivers to import (required, must be > 0)
    /// </summary>
    public int DriversCount { get; set; }

    /// <summary>
    /// Number of vehicles to import (required, must be > 0)
    /// </summary>
    public int VehiclesCount { get; set; }

    /// <summary>
    /// Batch size for processing (optional, uses default if not specified)
    /// </summary>
    public int? BatchSize { get; set; }

    /// <summary>
    /// Whether this is a dry run (no actual data changes)
    /// </summary>
    public bool DryRun { get; set; } = false;
}

/// <summary>
/// Information about an import session
/// </summary>
public class ImportSessionInfo
{
    /// <summary>
    /// Session unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Dealer ID for the import operation
    /// </summary>
    public Guid DealerId { get; set; }

    /// <summary>
    /// Customer ID for the import operation
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// Number of drivers to import
    /// </summary>
    public int DriversCount { get; set; }

    /// <summary>
    /// Number of vehicles to import
    /// </summary>
    public int VehiclesCount { get; set; }

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; }

    /// <summary>
    /// Whether this is a dry run
    /// </summary>
    public bool DryRun { get; set; }

    /// <summary>
    /// Current status of the session
    /// </summary>
    public ImportSessionStatus Status { get; set; }

    /// <summary>
    /// When the session was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the session execution started
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// When the session execution completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Environment where the session is running
    /// </summary>
    public string EnvironmentName { get; set; } = string.Empty;

    /// <summary>
    /// Whether the operation requires approval
    /// </summary>
    public bool RequiresApproval { get; set; }

    /// <summary>
    /// Total number of rows to process
    /// </summary>
    public int TotalRows { get; set; }

    /// <summary>
    /// Number of rows processed
    /// </summary>
    public int ProcessedRows { get; set; }

    /// <summary>
    /// Number of rows successfully processed
    /// </summary>
    public int SuccessfulRows { get; set; }

    /// <summary>
    /// Number of rows that failed processing
    /// </summary>
    public int FailedRows { get; set; }

    /// <summary>
    /// Duration of the import operation
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// List of errors encountered
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// List of warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Summary of the import operation
    /// </summary>
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of executing an import session
/// </summary>
public class ImportExecutionResult
{
    /// <summary>
    /// Session ID that was executed
    /// </summary>
    public Guid SessionId { get; set; }

    /// <summary>
    /// Whether the import was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Total number of rows processed
    /// </summary>
    public int TotalRows { get; set; }

    /// <summary>
    /// Number of rows processed
    /// </summary>
    public int ProcessedRows { get; set; }

    /// <summary>
    /// Number of rows successfully processed
    /// </summary>
    public int SuccessfulRows { get; set; }

    /// <summary>
    /// Number of rows that failed processing
    /// </summary>
    public int FailedRows { get; set; }

    /// <summary>
    /// Duration of the import operation
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// List of errors encountered
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// List of warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Summary of the import operation
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// Final status of the session
    /// </summary>
    public ImportSessionStatus Status { get; set; }
}

/// <summary>
/// Result of cancelling an import session
/// </summary>
public class SessionCancellationResult
{
    /// <summary>
    /// Session ID that was cancelled
    /// </summary>
    public Guid SessionId { get; set; }

    /// <summary>
    /// Previous status of the session
    /// </summary>
    public ImportSessionStatus PreviousStatus { get; set; }

    /// <summary>
    /// Current status of the session
    /// </summary>
    public ImportSessionStatus CurrentStatus { get; set; }

    /// <summary>
    /// Whether the session was actually cancelled
    /// </summary>
    public bool WasCancelled { get; set; }

    /// <summary>
    /// Message describing the cancellation result
    /// </summary>
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Status of an import session
/// </summary>
public enum ImportSessionStatus
{
    /// <summary>
    /// Session has been created but not yet executed
    /// </summary>
    Created,

    /// <summary>
    /// Session is currently running
    /// </summary>
    Running,

    /// <summary>
    /// Session completed successfully
    /// </summary>
    Completed,

    /// <summary>
    /// Session failed during execution
    /// </summary>
    Failed,

    /// <summary>
    /// Session was cancelled
    /// </summary>
    Cancelled
}
