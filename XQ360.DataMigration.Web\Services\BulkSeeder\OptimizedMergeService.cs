using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data;
using System.Diagnostics;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Optimized MERGE service for Phase 3.2.2: Optimized MERGE Operations
/// Implements bulk MERGE operations with parallel execution plans and OUTPUT clause tracking
/// </summary>
public class OptimizedMergeService : IOptimizedMergeService
{
    private readonly ILogger<OptimizedMergeService> _logger;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly BulkSeederConfiguration _config;

    public OptimizedMergeService(
        ILogger<OptimizedMergeService> logger,
        IEnvironmentConfigurationService environmentService,
        IOptions<BulkSeederConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
    }

    public async Task<MergeOperationResult> ExecuteBulkMergeVehiclesAsync(
        Guid sessionId,
        MergeOperationOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new MergeOperationResult
        {
            SessionId = sessionId,
            EntityType = "Vehicle",
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting bulk MERGE operation for vehicles in session {SessionId}", sessionId);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            // Execute the optimized MERGE stored procedure
            using var command = new SqlCommand("dbo.sp_BulkMergeVehicles", connection)
            {
                CommandType = CommandType.StoredProcedure,
                CommandTimeout = options.CommandTimeoutSeconds
            };

            command.Parameters.AddWithValue("@SessionId", sessionId);
            command.Parameters.AddWithValue("@BatchSize", options.BatchSize);
            command.Parameters.AddWithValue("@EnableParallelism", options.EnableParallelExecution);

            // Execute with result tracking
            using var reader = await command.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);

            if (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
            {
                result.ProcessedRecords = reader.GetInt32("ProcessedRecords");
                result.DurationMs = reader.GetInt32("DurationMs");
                result.RecordsPerSecond = reader.GetDecimal("RecordsPerSecond");
            }

            result.Success = true;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Bulk Vehicle MERGE completed successfully: {ProcessedRecords} records in {Duration}ms ({RecordsPerSecond:F2} records/sec)",
                result.ProcessedRecords, result.DurationMs, result.RecordsPerSecond);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Bulk Vehicle MERGE failed for session {SessionId}", sessionId);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<MergeOperationResult> ExecuteBulkMergePersonsAsync(
        Guid sessionId,
        MergeOperationOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new MergeOperationResult
        {
            SessionId = sessionId,
            EntityType = "Person/Driver",
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting bulk MERGE operation for persons/drivers in session {SessionId}", sessionId);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            // Execute the optimized MERGE stored procedure
            using var command = new SqlCommand("dbo.sp_BulkMergePersons", connection)
            {
                CommandType = CommandType.StoredProcedure,
                CommandTimeout = options.CommandTimeoutSeconds
            };

            command.Parameters.AddWithValue("@SessionId", sessionId);
            command.Parameters.AddWithValue("@BatchSize", options.BatchSize);
            command.Parameters.AddWithValue("@EnableParallelism", options.EnableParallelExecution);

            // Execute with result tracking
            using var reader = await command.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);

            if (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
            {
                result.ProcessedRecords = reader.GetInt32("ProcessedRecords");
                result.DurationMs = reader.GetInt32("DurationMs");
                result.RecordsPerSecond = reader.GetDecimal("RecordsPerSecond");
            }

            result.Success = true;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Bulk Person/Driver MERGE completed successfully: {ProcessedRecords} records in {Duration}ms ({RecordsPerSecond:F2} records/sec)",
                result.ProcessedRecords, result.DurationMs, result.RecordsPerSecond);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Bulk Person/Driver MERGE failed for session {SessionId}", sessionId);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<MergeOperationResult> ExecuteBulkMergeCardAccessAsync(
        Guid sessionId,
        MergeOperationOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new MergeOperationResult
        {
            SessionId = sessionId,
            EntityType = "Card/Access",
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting bulk MERGE operation for cards/access in session {SessionId}", sessionId);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            // Execute the optimized MERGE stored procedure
            using var command = new SqlCommand("dbo.sp_BulkMergeCardAccess", connection)
            {
                CommandType = CommandType.StoredProcedure,
                CommandTimeout = options.CommandTimeoutSeconds
            };

            command.Parameters.AddWithValue("@SessionId", sessionId);
            command.Parameters.AddWithValue("@BatchSize", options.BatchSize);
            command.Parameters.AddWithValue("@EnableParallelism", options.EnableParallelExecution);

            // Execute with result tracking
            using var reader = await command.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);

            if (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
            {
                result.ProcessedRecords = reader.GetInt32("ProcessedRecords");
                result.DurationMs = reader.GetInt32("DurationMs");
                result.RecordsPerSecond = reader.GetDecimal("RecordsPerSecond");
            }

            result.Success = true;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Bulk Card/Access MERGE completed successfully: {ProcessedRecords} records in {Duration}ms ({RecordsPerSecond:F2} records/sec)",
                result.ProcessedRecords, result.DurationMs, result.RecordsPerSecond);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Bulk Card/Access MERGE failed for session {SessionId}", sessionId);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<ComprehensiveMergeResult> ExecuteComprehensiveMergeAsync(
        Guid sessionId,
        ComprehensiveMergeOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ComprehensiveMergeResult
        {
            SessionId = sessionId,
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting comprehensive MERGE operation for session {SessionId}", sessionId);

        try
        {
            var mergeOptions = new MergeOperationOptions
            {
                BatchSize = options.BatchSize,
                CommandTimeoutSeconds = options.CommandTimeoutSeconds,
                EnableParallelExecution = options.EnableParallelExecution
            };

            // Execute MERGE operations in sequence based on dependencies
            if (options.MergePersonsDrivers)
            {
                result.PersonDriverResult = await ExecuteBulkMergePersonsAsync(
                    sessionId, mergeOptions, cancellationToken).ConfigureAwait(false);
                
                if (!result.PersonDriverResult.Success && options.StopOnFirstFailure)
                {
                    result.Success = false;
                    result.ErrorMessage = "Person/Driver MERGE failed";
                    return result;
                }
            }

            if (options.MergeVehicles)
            {
                result.VehicleResult = await ExecuteBulkMergeVehiclesAsync(
                    sessionId, mergeOptions, cancellationToken).ConfigureAwait(false);
                
                if (!result.VehicleResult.Success && options.StopOnFirstFailure)
                {
                    result.Success = false;
                    result.ErrorMessage = "Vehicle MERGE failed";
                    return result;
                }
            }

            if (options.MergeCardAccess)
            {
                result.CardAccessResult = await ExecuteBulkMergeCardAccessAsync(
                    sessionId, mergeOptions, cancellationToken).ConfigureAwait(false);
                
                if (!result.CardAccessResult.Success && options.StopOnFirstFailure)
                {
                    result.Success = false;
                    result.ErrorMessage = "Card/Access MERGE failed";
                    return result;
                }
            }

            // Calculate comprehensive statistics
            result.TotalProcessedRecords = (result.PersonDriverResult?.ProcessedRecords ?? 0) +
                                         (result.VehicleResult?.ProcessedRecords ?? 0) +
                                         (result.CardAccessResult?.ProcessedRecords ?? 0);

            result.Success = (result.PersonDriverResult?.Success ?? true) &&
                           (result.VehicleResult?.Success ?? true) &&
                           (result.CardAccessResult?.Success ?? true);

            result.Duration = stopwatch.Elapsed;

            // Get final session statistics
            result.SessionStatistics = await GetSessionStatisticsAsync(sessionId, cancellationToken)
                .ConfigureAwait(false);

            _logger.LogInformation("Comprehensive MERGE operation completed for session {SessionId}: {TotalRecords} total records in {Duration}ms",
                sessionId, result.TotalProcessedRecords, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Comprehensive MERGE operation failed for session {SessionId}", sessionId);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<List<MergePerformanceMetric>> GetMergePerformanceMetricsAsync(
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Retrieving MERGE operation performance metrics");

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            const string query = @"
                SELECT 
                    session_id AS SessionId,
                    start_time AS StartTime,
                    total_elapsed_time AS TotalElapsedTimeMs,
                    cpu_time AS CpuTimeMs,
                    logical_reads AS LogicalReads,
                    writes AS Writes,
                    row_count AS RowCount,
                    LEFT(query_text, 100) AS QueryTextPreview
                FROM dbo.vw_MergeOperationPerformance
                ORDER BY start_time DESC";

            using var command = new SqlCommand(query, connection);
            using var reader = await command.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);

            var metrics = new List<MergePerformanceMetric>();

            while (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
            {
                metrics.Add(new MergePerformanceMetric
                {
                    SessionId = reader.GetInt32("SessionId"),
                    StartTime = reader.GetDateTime("StartTime"),
                    TotalElapsedTimeMs = reader.GetInt64("TotalElapsedTimeMs"),
                    CpuTimeMs = reader.GetInt64("CpuTimeMs"),
                    LogicalReads = reader.GetInt64("LogicalReads"),
                    Writes = reader.GetInt64("Writes"),
                    RowCount = reader.GetInt64("RowCount"),
                    QueryTextPreview = reader.GetString("QueryTextPreview")
                });
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve MERGE performance metrics");
            return new List<MergePerformanceMetric>();
        }
    }

    public async Task<List<SessionStatistic>> GetSessionStatisticsAsync(
        Guid sessionId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Retrieving session statistics for {SessionId}", sessionId);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            const string query = @"
                SELECT 
                    EntityType,
                    TotalRecords,
                    ProcessedRecords,
                    PendingRecords,
                    FailedRecords,
                    AvgProcessingTimeMs
                FROM dbo.fn_GetMergeOperationStats(@SessionId)";

            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);

            var statistics = new List<SessionStatistic>();

            while (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
            {
                statistics.Add(new SessionStatistic
                {
                    EntityType = reader.GetString("EntityType"),
                    TotalRecords = reader.GetInt32("TotalRecords"),
                    ProcessedRecords = reader.GetInt32("ProcessedRecords"),
                    PendingRecords = reader.GetInt32("PendingRecords"),
                    FailedRecords = reader.GetInt32("FailedRecords"),
                    AvgProcessingTimeMs = reader.IsDBNull("AvgProcessingTimeMs") 
                        ? 0 
                        : reader.GetInt32("AvgProcessingTimeMs")
                });
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve session statistics for {SessionId}", sessionId);
            return new List<SessionStatistic>();
        }
    }

    public async Task<MergeOperationResult> OptimizeMergePerformanceAsync(
        OptimizationOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new MergeOperationResult
        {
            EntityType = "Performance Optimization",
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting MERGE performance optimization");

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            var optimizationTasks = new List<Task>();

            // Update statistics if requested
            if (options.UpdateStatistics)
            {
                optimizationTasks.Add(UpdateMergeStatisticsAsync(connection, cancellationToken));
            }

            // Rebuild fragmented indexes if requested
            if (options.RebuildFragmentedIndexes)
            {
                optimizationTasks.Add(RebuildFragmentedIndexesAsync(connection, options.FragmentationThreshold, cancellationToken));
            }

            // Optimize query plans if requested
            if (options.OptimizeQueryPlans)
            {
                optimizationTasks.Add(OptimizeQueryPlansAsync(connection, cancellationToken));
            }

            // Execute all optimization tasks
            await Task.WhenAll(optimizationTasks).ConfigureAwait(false);

            result.Success = true;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("MERGE performance optimization completed in {Duration}ms", 
                result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MERGE performance optimization failed");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    private async Task UpdateMergeStatisticsAsync(SqlConnection connection, CancellationToken cancellationToken)
    {
        var tables = new[] { "Vehicle", "Person", "Driver", "Module", "Card", "StagingVehicle", "StagingDriver", "StagingCard" };

        foreach (var table in tables)
        {
            try
            {
                var updateStatsQuery = $"UPDATE STATISTICS [dbo].[{table}] WITH FULLSCAN";
                using var command = new SqlCommand(updateStatsQuery, connection) { CommandTimeout = 120 };
                await command.ExecuteNonQueryAsync(cancellationToken).ConfigureAwait(false);
                
                _logger.LogDebug("Updated statistics for table: {TableName}", table);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to update statistics for table: {TableName}", table);
            }
        }
    }

    private async Task RebuildFragmentedIndexesAsync(SqlConnection connection, double fragmentationThreshold, CancellationToken cancellationToken)
    {
        const string fragmentationQuery = @"
            SELECT 
                t.name AS TableName,
                i.name AS IndexName,
                ips.avg_fragmentation_in_percent
            FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'SAMPLED') ips
                INNER JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
                INNER JOIN sys.tables t ON ips.object_id = t.object_id
            WHERE ips.avg_fragmentation_in_percent > @FragmentationThreshold
                AND ips.page_count > 1000
                AND i.type > 0";

        using var fragmentationCommand = new SqlCommand(fragmentationQuery, connection);
        fragmentationCommand.Parameters.AddWithValue("@FragmentationThreshold", fragmentationThreshold);

        using var reader = await fragmentationCommand.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);
        var fragmentedIndexes = new List<(string TableName, string IndexName, double Fragmentation)>();

        while (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
        {
            fragmentedIndexes.Add((
                reader.GetString("TableName"),
                reader.GetString("IndexName"),
                reader.GetDouble("avg_fragmentation_in_percent")
            ));
        }

        reader.Close();

        foreach (var (tableName, indexName, fragmentation) in fragmentedIndexes)
        {
            try
            {
                var rebuildQuery = fragmentation > 30 
                    ? $"ALTER INDEX [{indexName}] ON [dbo].[{tableName}] REBUILD WITH (ONLINE = OFF)"
                    : $"ALTER INDEX [{indexName}] ON [dbo].[{tableName}] REORGANIZE";

                using var rebuildCommand = new SqlCommand(rebuildQuery, connection) { CommandTimeout = 600 };
                await rebuildCommand.ExecuteNonQueryAsync(cancellationToken).ConfigureAwait(false);

                _logger.LogDebug("Rebuilt/reorganized index {IndexName} on table {TableName} (fragmentation: {Fragmentation:F1}%)",
                    indexName, tableName, fragmentation);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to rebuild index {IndexName} on table {TableName}", indexName, tableName);
            }
        }
    }

    private async Task OptimizeQueryPlansAsync(SqlConnection connection, CancellationToken cancellationToken)
    {
        try
        {
            // Clear plan cache for MERGE operations to force recompilation with current statistics
            const string clearCacheQuery = "DBCC FREEPROCCACHE";
            using var clearCommand = new SqlCommand(clearCacheQuery, connection) { CommandTimeout = 60 };
            await clearCommand.ExecuteNonQueryAsync(cancellationToken).ConfigureAwait(false);

            _logger.LogDebug("Cleared procedure cache to optimize query plans");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to clear procedure cache");
        }
    }
}
