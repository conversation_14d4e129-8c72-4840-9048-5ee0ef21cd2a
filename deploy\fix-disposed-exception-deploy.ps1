# Deployment script for ObjectDisposedException fix
# This script will build and deploy the application with the IServiceScopeFactory fix

Write-Host "=== Deploying ObjectDisposedException Fix ===" -ForegroundColor Cyan
Write-Host ""

# Define paths
$projectRoot = "C:\files\XQ360DataMigration"
$webProjectPath = "$projectRoot\XQ360.DataMigration.Web"
$publishPath = "$projectRoot\publish"
$remotePath = "C:\inetpub\wwwroot\XQ360Migration"

Write-Host "1. Building the application..." -ForegroundColor Yellow
Set-Location $projectRoot

# Clean previous builds
if (Test-Path $publishPath) {
    Remove-Item $publishPath -Recurse -Force
    Write-Host "   ✓ Cleaned previous publish directory" -ForegroundColor Green
}

# Build the application
Write-Host "   Building with IServiceScopeFactory fix..." -ForegroundColor Gray
dotnet build --configuration Release --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Host "   ❌ Build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "   ✓ Build completed successfully" -ForegroundColor Green

Write-Host ""

Write-Host "2. Publishing the application..." -ForegroundColor Yellow
dotnet publish XQ360.DataMigration.Web --configuration Release --output $publishPath --self-contained false

if ($LASTEXITCODE -ne 0) {
    Write-Host "   ❌ Publish failed!" -ForegroundColor Red
    exit 1
}

Write-Host "   ✓ Application published successfully" -ForegroundColor Green

Write-Host ""

Write-Host "3. Stopping IIS application pool..." -ForegroundColor Yellow
Import-Module WebAdministration
Stop-WebAppPool -Name "XQ360Migration"

Write-Host "   ✓ Application pool stopped" -ForegroundColor Green

Write-Host ""

Write-Host "4. Backing up current deployment..." -ForegroundColor Yellow
$backupPath = "$remotePath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
if (Test-Path $remotePath) {
    Copy-Item $remotePath $backupPath -Recurse
    Write-Host "   ✓ Backup created: $backupPath" -ForegroundColor Green
} else {
    Write-Host "   ⚠️ No existing deployment found to backup" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "5. Deploying updated application..." -ForegroundColor Yellow
if (Test-Path $remotePath) {
    Remove-Item $remotePath -Recurse -Force
}

Copy-Item $publishPath $remotePath -Recurse -Force
Write-Host "   ✓ Application deployed to: $remotePath" -ForegroundColor Green

Write-Host ""

Write-Host "6. Starting IIS application pool..." -ForegroundColor Yellow
Start-WebAppPool -Name "XQ360Migration"
Write-Host "   ✓ Application pool started" -ForegroundColor Green

Write-Host ""

Write-Host "7. Verifying deployment..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

try {
    $response = Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✓ Application is responding successfully" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️ Application responded with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠️ Could not verify application response: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "=== DEPLOYMENT COMPLETED ===" -ForegroundColor Green
Write-Host "The ObjectDisposedException fix has been deployed successfully." -ForegroundColor Cyan
Write-Host ""
Write-Host "Changes applied:" -ForegroundColor Yellow
Write-Host "- IServiceProvider → IServiceScopeFactory" -ForegroundColor Gray
Write-Host "- _serviceProvider.CreateScope() → _serviceScopeFactory.CreateScope()" -ForegroundColor Gray
Write-Host "- Added proper scope disposal in ExecuteVehicleSyncIfNeededAsync" -ForegroundColor Gray
Write-Host ""
Write-Host "The application should now handle service provider disposal correctly." -ForegroundColor Cyan
Write-Host "Test the migration functionality to verify the fix works." -ForegroundColor Yellow 