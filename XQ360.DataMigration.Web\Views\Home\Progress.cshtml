@{
    ViewData["Title"] = "Migration Progress";
    var migrationId = ViewBag.MigrationId;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="display-4">🔄 Migration in Progress</h1>
                <p class="lead">Migration ID: @migrationId</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Progress Overview</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Overall Progress:</label>
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" 
                                 role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                                100% - Completed Successfully!
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>Migration Completed!</strong> Your data has been successfully migrated.
                    </div>
                    
                    <div class="mt-3">
                        <a href="/Home/Index" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Start New Migration
                        </a>
                        <a href="/" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-file-text"></i> Migration Reports</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">View the detailed migration report for this session with statistics, errors, and recommendations.</p>
                    
                    <div id="reportsLoading" class="text-center" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> Loading reports...
                    </div>
                    
                    <div id="reportsContainer">
                        <!-- Reports will be loaded here -->
                    </div>
                    
                    <div class="mt-3">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="loadReports()">
                            <i class="fas fa-refresh"></i> Refresh Report
                        </button>
                                                    <a href="/Home/Index" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-history"></i> View All Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Viewer Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">
                    <i class="fas fa-file-text"></i> Migration Report
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="reportContent" style="white-space: pre-wrap; font-family: monospace; max-height: 70vh; overflow-y: auto;">
                    <!-- Report content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="downloadCurrentReport">
                    <i class="fas fa-download"></i> Download Report
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentReportFileName = '';

        // Load reports on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadReports();
        });

        async function loadReports() {
            const container = document.getElementById('reportsContainer');
            const loading = document.getElementById('reportsLoading');
            
            loading.style.display = 'block';
            container.innerHTML = '';
            
            try {
                console.log('Fetching current migration report...');
                const response = await fetch('/Home/GetReports?currentOnly=true');
                const result = await response.json();
                
                console.log('Reports response:', result);
                
                if (result.success) {
                    if (result.reports && result.reports.length > 0) {
                        console.log(`Found ${result.reports.length} reports`);
                        displayReports(result.reports);
                    } else {
                        console.log('No current report found');
                        let message = 'No migration report available for this session yet.';
                        if (result.message) {
                            message += `<br><small class="text-muted">${result.message}</small>`;
                        }
                        container.innerHTML = `<p class="text-muted">${message}</p>`;
                    }
                } else {
                    console.error('Reports error:', result.error);
                    container.innerHTML = `<div class="alert alert-warning">Error loading reports: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('Reports fetch error:', error);
                container.innerHTML = `<div class="alert alert-danger">Failed to load reports: ${error.message}</div>`;
            } finally {
                loading.style.display = 'none';
            }
        }

        function displayReports(reports) {
            const container = document.getElementById('reportsContainer');
            
            let html = '<div class="list-group list-group-flush">';
            
            reports.forEach(report => {
                const date = new Date(report.createdDate).toLocaleDateString();
                const time = new Date(report.createdDate).toLocaleTimeString();
                const sizeKB = Math.round(report.size / 1024);
                
                html += `
                    <div class="list-group-item list-group-item-action p-2">
                        <div class="d-flex w-100 justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">${report.displayName}</h6>
                                <small class="text-muted">${date} ${time} • ${sizeKB}KB</small>
                            </div>
                            <div class="btn-group-vertical btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                        onclick="viewReport('${report.fileName}')" title="View Report">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" 
                                        onclick="downloadReport('${report.fileName}')" title="Download Report">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        async function viewReport(fileName) {
            try {
                const response = await fetch(`/Home/ViewReport?fileName=${encodeURIComponent(fileName)}`);
                const result = await response.json();
                
                if (result.success) {
                    currentReportFileName = fileName;
                    document.getElementById('reportModalLabel').innerHTML = 
                        `<i class="fas fa-file-text"></i> ${result.report.displayName}`;
                    document.getElementById('reportContent').textContent = result.report.content;
                    
                    const modal = new bootstrap.Modal(document.getElementById('reportModal'));
                    modal.show();
                } else {
                    alert('Error viewing report: ' + result.error);
                }
            } catch (error) {
                alert('Failed to load report: ' + error.message);
            }
        }

        function downloadReport(fileName) {
            window.open(`/Home/DownloadReport?fileName=${encodeURIComponent(fileName)}`, '_blank');
        }

        // Download current report from modal
        document.getElementById('downloadCurrentReport').addEventListener('click', function() {
            if (currentReportFileName) {
                downloadReport(currentReportFileName);
            }
        });
    </script>
} 