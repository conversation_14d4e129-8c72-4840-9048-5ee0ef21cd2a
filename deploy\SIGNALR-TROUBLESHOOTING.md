# SignalR WebSocket Troubleshooting Guide

## Common SignalR Errors

### 1. "Failed to start the transport 'WebSockets': Error: WebSocket failed to connect"

**Causes:**
- IIS WebSockets feature not enabled
- Proxy blocking WebSocket connections
- Firewall blocking WebSocket traffic
- Application pool not configured for WebSockets

**Solutions:**

#### A. Enable IIS WebSockets Feature
```powershell
# Run as Administrator
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -All
```

#### B. Configure Application Pool for WebSockets
```powershell
# Set idle timeout to 0 (never)
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.idleTimeout" -Value "00:00:00"

# Set ping interval
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.pingInterval" -Value "00:00:30"

# Set ping response time
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.pingResponseTime" -Value "00:01:30"
```

#### C. Check web.config Configuration
Ensure your web.config includes:
```xml
<webSocket enabled="true" />
```

### 2. "The connection could not be found on the server"

**Causes:**
- Server restart without sticky sessions
- Multiple servers without load balancer configuration
- Application pool recycling

**Solutions:**

#### A. Enable Sticky Sessions (if using load balancer)
Configure your load balancer to use sticky sessions based on:
- Source IP
- Session cookie
- Connection ID

#### B. Configure Application Pool Recycling
```powershell
# Disable regular recycling
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "recycling.periodicRestart.time" -Value "00:00:00"
```

### 3. "Transport fallback to Server-Sent Events"

**Causes:**
- WebSockets blocked by firewall/proxy
- Network infrastructure blocking WebSocket upgrade

**Solutions:**

#### A. Check Firewall Rules
```powershell
# Allow WebSocket traffic
New-NetFirewallRule -DisplayName "WebSocket Traffic" -Direction Inbound -Protocol TCP -LocalPort 80,443 -Action Allow
```

#### B. Configure Proxy Settings
If using a reverse proxy (nginx, Apache), ensure it supports WebSocket upgrades:

**Nginx Example:**
```nginx
location /migrationHub {
    proxy_pass http://localhost:5000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
}
```

## Testing SignalR Connection

### 1. Use the SignalR Test Page
Navigate to: `http://your-server/Home/SignalRTest`

This page will:
- Test WebSocket connection
- Show connection details
- Display transport method used
- Provide troubleshooting information

### 2. Manual Testing with PowerShell
```powershell
# Test SignalR hub endpoint
$response = Invoke-WebRequest -Uri "http://localhost/migrationHub" -UseBasicParsing
Write-Host "Status: $($response.StatusCode)"
```

### 3. Browser Developer Tools
1. Open browser developer tools (F12)
2. Go to Network tab
3. Navigate to your application
4. Look for WebSocket connections or failed requests

## Server Requirements Checklist

### IIS Configuration
- [ ] ASP.NET Core Hosting Bundle installed
- [ ] IIS WebSockets feature enabled
- [ ] Application pool configured for WebSockets
- [ ] web.config includes `<webSocket enabled="true" />`

### Network Configuration
- [ ] Firewall allows WebSocket traffic (ports 80/443)
- [ ] No proxy blocking WebSocket upgrade
- [ ] Load balancer configured for sticky sessions (if applicable)

### Application Configuration
- [ ] SignalR properly configured in Program.cs
- [ ] Hub endpoints mapped correctly
- [ ] CORS configured (if needed)

## Debugging Steps

### 1. Check IIS Logs
```powershell
# View IIS logs
Get-Content "C:\inetpub\logs\LogFiles\W3SVC1\*.log" | Select-String "WebSocket"
```

### 2. Check Application Logs
```powershell
# View application logs
Get-Content "C:\inetpub\wwwroot\XQ360Migration\Logs\*.log" | Select-String "SignalR"
```

### 3. Test with Different Transports
SignalR will automatically fallback to:
1. WebSockets (preferred)
2. Server-Sent Events
3. Long Polling

You can force a specific transport for testing:
```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl('/migrationHub')
    .withTransport(signalR.HttpTransportType.ServerSentEvents) // Force SSE
    .build();
```

## Quick Fix Commands

Run these commands as Administrator on your server:

```powershell
# 1. Enable WebSockets
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebSockets -All

# 2. Configure application pool
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.idleTimeout" -Value "00:00:00"
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.pingInterval" -Value "00:00:30"
Set-ItemProperty -Path "IIS:\AppPools\XQ360MigrationPool" -Name "processModel.pingResponseTime" -Value "00:01:30"

# 3. Restart application pool
Restart-WebAppPool -Name "XQ360MigrationPool"

# 4. Test connection
Invoke-WebRequest -Uri "http://localhost/migrationHub" -UseBasicParsing
```

## Additional Resources

- [SignalR Documentation](https://docs.microsoft.com/en-us/aspnet/core/signalr/)
- [IIS WebSockets Configuration](https://docs.microsoft.com/en-us/iis/get-started/whats-new-in-iis-8/iis-80-websocket-protocol-support)
- [ASP.NET Core Hosting Bundle](https://docs.microsoft.com/en-us/aspnet/core/host-and-deploy/iis/) 