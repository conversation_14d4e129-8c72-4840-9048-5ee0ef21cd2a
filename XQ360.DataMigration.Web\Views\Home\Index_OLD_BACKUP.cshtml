﻿@{
    ViewData["Title"] = "XQ360 Data Migration Tool";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5 mt-5">
                <h1 class="display-3">🚀 XQ360 Data Migration Tool</h1>
                <p class="lead">Professional data migration solution for XQ360 systems</p>
                
                <div class="mt-4">
                    <a href="/Home/Index" class="btn btn-primary btn-lg">
                        <i class="fas fa-database"></i> Start Migration
                    </a>
                    <a href="#" class="btn btn-info btn-lg ml-3">
                        <i class="fas fa-book"></i> User Manual
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-5">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-globe fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Multi-Environment</h5>
                    <p class="card-text">Deploy to US, UK, AU, Pilot, or Development environments with environment-specific configurations.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-list-check fa-3x text-success mb-3"></i>
                    <h5 class="card-title">Multiple Data Types</h5>
                    <p class="card-text">Migrate 9 different data types including vehicles, persons, cards, and more.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">Real-time Progress</h5>
                    <p class="card-text">Monitor migration progress with live updates and detailed logging.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Features:</strong>
                ✅ Environment Selection | ✅ CSV File Upload | ✅ Real-time Monitoring | ✅ Cross-platform Support
            </div>
        </div>
    </div>
</div>
