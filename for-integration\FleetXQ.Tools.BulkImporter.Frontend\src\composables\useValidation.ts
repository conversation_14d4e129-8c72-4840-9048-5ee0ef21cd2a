import { ref, computed, reactive, watch } from 'vue'
import type { Ref } from 'vue'

export interface ValidationRule {
  validator: (value: any) => boolean | Promise<boolean>
  message: string
  trigger?: 'blur' | 'change' | 'submit'
}

export interface FieldValidation {
  rules: ValidationRule[]
  isValid: boolean
  errors: string[]
  isValidating: boolean
  touched: boolean
}

export interface FormValidation {
  [fieldName: string]: FieldValidation
}

export interface ValidationOptions {
  immediate?: boolean
  debounceMs?: number
}

export function useValidation(options: ValidationOptions = {}) {
  const { immediate = false, debounceMs = 300 } = options
  
  const formValidation = reactive<FormValidation>({})
  const isSubmitting = ref(false)
  const submitAttempted = ref(false)

  // Computed properties
  const isFormValid = computed(() => {
    return Object.values(formValidation).every(field => field.isValid)
  })

  const hasErrors = computed(() => {
    return Object.values(formValidation).some(field => field.errors.length > 0)
  })

  const touchedFields = computed(() => {
    return Object.keys(formValidation).filter(key => formValidation[key].touched)
  })

  const errorCount = computed(() => {
    return Object.values(formValidation).reduce((count, field) => count + field.errors.length, 0)
  })

  const firstError = computed(() => {
    for (const field of Object.values(formValidation)) {
      if (field.errors.length > 0) {
        return field.errors[0]
      }
    }
    return null
  })

  // Validation rules
  const createRule = (validator: (value: any) => boolean | Promise<boolean>, message: string, trigger: 'blur' | 'change' | 'submit' = 'blur'): ValidationRule => {
    return { validator, message, trigger }
  }

  const required = (message = 'This field is required'): ValidationRule => {
    return createRule((value) => {
      if (Array.isArray(value)) return value.length > 0
      if (typeof value === 'string') return value.trim().length > 0
      return value !== null && value !== undefined && value !== ''
    }, message)
  }

  const minLength = (min: number, message?: string): ValidationRule => {
    return createRule((value) => {
      if (!value) return true // Let required rule handle empty values
      return String(value).length >= min
    }, message || `Must be at least ${min} characters`)
  }

  const maxLength = (max: number, message?: string): ValidationRule => {
    return createRule((value) => {
      if (!value) return true
      return String(value).length <= max
    }, message || `Must be no more than ${max} characters`)
  }

  const email = (message = 'Must be a valid email address'): ValidationRule => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return createRule((value) => {
      if (!value) return true
      return emailRegex.test(String(value))
    }, message)
  }

  const numeric = (message = 'Must be a number'): ValidationRule => {
    return createRule((value) => {
      if (!value) return true
      return !isNaN(Number(value))
    }, message)
  }

  const min = (minValue: number, message?: string): ValidationRule => {
    return createRule((value) => {
      if (!value) return true
      return Number(value) >= minValue
    }, message || `Must be at least ${minValue}`)
  }

  const max = (maxValue: number, message?: string): ValidationRule => {
    return createRule((value) => {
      if (!value) return true
      return Number(value) <= maxValue
    }, message || `Must be no more than ${maxValue}`)
  }

  const pattern = (regex: RegExp, message: string): ValidationRule => {
    return createRule((value) => {
      if (!value) return true
      return regex.test(String(value))
    }, message)
  }

  const custom = (validator: (value: any) => boolean | Promise<boolean>, message: string): ValidationRule => {
    return createRule(validator, message)
  }

  // Field management
  const addField = (fieldName: string, rules: ValidationRule[] = []) => {
    formValidation[fieldName] = {
      rules,
      isValid: true,
      errors: [],
      isValidating: false,
      touched: false
    }

    if (immediate) {
      validateField(fieldName, undefined)
    }
  }

  const removeField = (fieldName: string) => {
    delete formValidation[fieldName]
  }

  const setFieldRules = (fieldName: string, rules: ValidationRule[]) => {
    if (formValidation[fieldName]) {
      formValidation[fieldName].rules = rules
    } else {
      addField(fieldName, rules)
    }
  }

  // Validation functions
  const validateField = async (fieldName: string, value: any, trigger: 'blur' | 'change' | 'submit' = 'blur') => {
    const field = formValidation[fieldName]
    if (!field) return true

    field.isValidating = true
    field.errors = []

    const applicableRules = field.rules.filter(rule => 
      !rule.trigger || rule.trigger === trigger || trigger === 'submit'
    )

    for (const rule of applicableRules) {
      try {
        const isValid = await rule.validator(value)
        if (!isValid) {
          field.errors.push(rule.message)
        }
      } catch (error) {
        field.errors.push('Validation error occurred')
        console.error('Validation error:', error)
      }
    }

    field.isValid = field.errors.length === 0
    field.isValidating = false

    return field.isValid
  }

  const validateForm = async (trigger: 'submit' = 'submit') => {
    const validationPromises = Object.keys(formValidation).map(async (fieldName) => {
      // Get current value from the field (this would need to be passed in real usage)
      return validateField(fieldName, undefined, trigger)
    })

    const results = await Promise.all(validationPromises)
    return results.every(result => result)
  }

  const markFieldTouched = (fieldName: string) => {
    if (formValidation[fieldName]) {
      formValidation[fieldName].touched = true
    }
  }

  const markAllTouched = () => {
    Object.keys(formValidation).forEach(markFieldTouched)
  }

  const clearFieldErrors = (fieldName: string) => {
    if (formValidation[fieldName]) {
      formValidation[fieldName].errors = []
      formValidation[fieldName].isValid = true
    }
  }

  const clearAllErrors = () => {
    Object.keys(formValidation).forEach(clearFieldErrors)
  }

  const resetForm = () => {
    Object.keys(formValidation).forEach(fieldName => {
      const field = formValidation[fieldName]
      field.errors = []
      field.isValid = true
      field.touched = false
      field.isValidating = false
    })
    submitAttempted.value = false
  }

  const setFieldError = (fieldName: string, error: string) => {
    if (formValidation[fieldName]) {
      formValidation[fieldName].errors = [error]
      formValidation[fieldName].isValid = false
    }
  }

  const setFieldErrors = (fieldName: string, errors: string[]) => {
    if (formValidation[fieldName]) {
      formValidation[fieldName].errors = errors
      formValidation[fieldName].isValid = errors.length === 0
    }
  }

  // Server validation integration
  const setServerErrors = (serverErrors: Record<string, string[]>) => {
    Object.entries(serverErrors).forEach(([fieldName, errors]) => {
      setFieldErrors(fieldName, errors)
    })
  }

  return {
    // State
    formValidation,
    isSubmitting,
    submitAttempted,

    // Computed
    isFormValid,
    hasErrors,
    touchedFields,
    errorCount,
    firstError,

    // Rule creators
    createRule,
    required,
    minLength,
    maxLength,
    email,
    numeric,
    min,
    max,
    pattern,
    custom,

    // Field management
    addField,
    removeField,
    setFieldRules,

    // Validation
    validateField,
    validateForm,
    markFieldTouched,
    markAllTouched,
    clearFieldErrors,
    clearAllErrors,
    resetForm,
    setFieldError,
    setFieldErrors,
    setServerErrors
  }
}
