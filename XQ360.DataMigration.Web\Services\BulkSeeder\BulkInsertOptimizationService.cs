using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data;
using System.Diagnostics;
using System.Text;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// High-performance bulk insert optimization service
/// Implements Phase 1.2.1: SqlBulkCopy for staging, parameterized MERGE for production
/// Performance targets: 10,000 records/batch for staging, 1,000 records/batch for production
/// </summary>
public class BulkInsertOptimizationService : IBulkInsertOptimizationService
{
    private readonly ILogger<BulkInsertOptimizationService> _logger;
    private readonly BulkSeederConfiguration _options;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly IForeignKeyLookupCacheService _fkCacheService;
    private readonly IStagingSchemaService _stagingSchemaService;

    public BulkInsertOptimizationService(
        ILogger<BulkInsertOptimizationService> logger,
        IOptions<BulkSeederConfiguration> options,
        IEnvironmentConfigurationService environmentService,
        IForeignKeyLookupCacheService fkCacheService,
        IStagingSchemaService stagingSchemaService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _fkCacheService = fkCacheService ?? throw new ArgumentNullException(nameof(fkCacheService));
        _stagingSchemaService = stagingSchemaService ?? throw new ArgumentNullException(nameof(stagingSchemaService));
    }

    public async Task<BulkInsertResult> BulkInsertToStagingAsync<T>(
        Guid sessionId,
        IEnumerable<T> data,
        string stagingTableName,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default) where T : class
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new BulkInsertResult();
        var dataList = data.ToList();
        result.TotalRecords = dataList.Count;

        _logger.LogInformation("Starting bulk insert of {RecordCount} records to {TableName} for session {SessionId}",
            dataList.Count, stagingTableName, sessionId);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Create DataTable from data
            var dataTable = CreateDataTableFromObjects(dataList, stagingTableName);
            
            var batches = dataList.Chunk(batchSize).ToList();
            var processedRecords = 0;

            foreach (var (batch, batchIndex) in batches.Select((batch, index) => (batch, index)))
            {
                var batchStopwatch = Stopwatch.StartNew();
                
                try
                {
                    var batchDataTable = CreateDataTableFromObjects(batch, stagingTableName);
                    
                    // Configure SqlBulkCopy for optimal performance
                    using var bulkCopy = new SqlBulkCopy(connection)
                    {
                        DestinationTableName = $"[Staging].[{stagingTableName}]",
                        BatchSize = batchSize,
                        BulkCopyTimeout = _options.BulkCopyTimeout,
                        NotifyAfter = _options.NotifyAfter,
                        EnableStreaming = true
                    };

                    // Map columns
                    MapDataTableColumns(bulkCopy, batchDataTable, stagingTableName);

                    // Execute bulk insert with progress tracking
                    bulkCopy.SqlRowsCopied += (sender, e) =>
                    {
                        var currentProgress = new BulkInsertProgress
                        {
                            TotalRecords = result.TotalRecords,
                            ProcessedRecords = processedRecords + (int)e.RowsCopied,
                            ElapsedTime = stopwatch.Elapsed,
                            CurrentOperation = $"Inserting batch {batchIndex + 1}/{batches.Count}",
                            CurrentRecordsPerSecond = (decimal)e.RowsCopied / (decimal)batchStopwatch.Elapsed.TotalSeconds
                        };
                        
                        progress?.Report(currentProgress);
                    };

                    await bulkCopy.WriteToServerAsync(batchDataTable, cancellationToken);
                    
                    processedRecords += batch.Length;
                    result.InsertedRecords += batch.Length;

                    batchStopwatch.Stop();
                    _logger.LogDebug("Batch {BatchIndex} completed: {RecordCount} records in {Duration}ms",
                        batchIndex + 1, batch.Length, batchStopwatch.ElapsedMilliseconds);
                }
                catch (Exception ex)
                {
                    result.ErrorRecords += batch.Length;
                    result.Errors.Add($"Batch {batchIndex + 1} failed: {ex.Message}");
                    _logger.LogError(ex, "Batch {BatchIndex} failed for session {SessionId}", batchIndex + 1, sessionId);
                }
            }

            result.Success = result.ErrorRecords == 0;
            result.Duration = stopwatch.Elapsed;
            result.Metrics = new BulkInsertMetrics
            {
                SqlBulkCopyTime = stopwatch.Elapsed,
                BatchCount = batches.Count,
                AverageBatchSize = dataList.Count / (decimal)batches.Count,
                ThroughputRecordsPerSecond = result.RecordsPerSecond
            };

            _logger.LogInformation("Bulk insert completed for session {SessionId}: {InsertedRecords}/{TotalRecords} records in {Duration}",
                sessionId, result.InsertedRecords, result.TotalRecords, result.Duration);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Errors.Add(ex.Message);
            _logger.LogError(ex, "Bulk insert failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task<MergeResult> MergeFromStagingToProductionAsync(
        Guid sessionId,
        string stagingTableName,
        string productionTableName,
        MergeConfiguration mergeConfig,
        int batchSize = 1000,
        IProgress<MergeProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new MergeResult();

        _logger.LogInformation("Starting MERGE operation from {StagingTable} to {ProductionTable} for session {SessionId}",
            stagingTableName, productionTableName, sessionId);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            // Get total record count for progress tracking
            var totalRecords = await GetStagingRecordCountAsync(connection, stagingTableName, sessionId, cancellationToken);
            var totalBatches = (int)Math.Ceiling((double)totalRecords / batchSize);

            var mergeSQL = BuildMergeSQL(stagingTableName, productionTableName, mergeConfig);
            
            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
            {
                var batchStopwatch = Stopwatch.StartNew();
                
                try
                {
                    var batchSql = $@"
                        WITH BatchedData AS (
                            SELECT *, ROW_NUMBER() OVER (ORDER BY Id) as RowNum
                            FROM [Staging].[{stagingTableName}]
                            WHERE SessionId = @SessionId
                        )
                        {mergeSQL}
                        AND source.RowNum BETWEEN @StartRow AND @EndRow";

                    using var command = new SqlCommand(batchSql, connection);
                    command.Parameters.AddWithValue("@SessionId", sessionId);
                    command.Parameters.AddWithValue("@StartRow", batchIndex * batchSize + 1);
                    command.Parameters.AddWithValue("@EndRow", (batchIndex + 1) * batchSize);
                    command.CommandTimeout = _options.CommandTimeout;

                    // Add merge configuration parameters
                    foreach (var param in mergeConfig.Parameters)
                    {
                        command.Parameters.AddWithValue($"@{param.Key}", param.Value);
                    }

                    var affectedRows = await command.ExecuteNonQueryAsync(cancellationToken);
                    result.ProcessedRecords += affectedRows;

                    // Report progress
                    var currentProgress = new MergeProgress
                    {
                        TotalBatches = totalBatches,
                        ProcessedBatches = batchIndex + 1,
                        CurrentBatchSize = affectedRows,
                        TotalRecords = totalRecords,
                        ProcessedRecords = result.ProcessedRecords,
                        ElapsedTime = stopwatch.Elapsed,
                        CurrentOperation = $"MERGE batch {batchIndex + 1}/{totalBatches}"
                    };
                    
                    progress?.Report(currentProgress);

                    batchStopwatch.Stop();
                    _logger.LogDebug("MERGE batch {BatchIndex} completed: {RecordCount} records in {Duration}ms",
                        batchIndex + 1, affectedRows, batchStopwatch.ElapsedMilliseconds);
                }
                catch (Exception ex)
                {
                    result.Errors.Add($"MERGE batch {batchIndex + 1} failed: {ex.Message}");
                    _logger.LogError(ex, "MERGE batch {BatchIndex} failed for session {SessionId}", batchIndex + 1, sessionId);
                }
            }

            result.Success = result.Errors.Count == 0;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("MERGE operation completed for session {SessionId}: {ProcessedRecords} records in {Duration}",
                sessionId, result.ProcessedRecords, result.Duration);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Errors.Add(ex.Message);
            _logger.LogError(ex, "MERGE operation failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task<BulkInsertResult> BulkInsertDriversToStagingAsync(
        Guid sessionId,
        IEnumerable<DriverStagingRecord> drivers,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new BulkInsertResult();
        var driverList = drivers.ToList();
        result.TotalRecords = driverList.Count;

        _logger.LogInformation("Starting bulk insert of {DriverCount} drivers for session {SessionId}",
            driverList.Count, sessionId);

        try
        {
            // Pre-populate FK cache for better performance
            var customerNames = driverList.Select(d => d.CustomerName).Distinct().ToList();
            var siteNames = driverList.Select(d => d.SiteName).Distinct().ToList();
            var departmentNames = driverList.Select(d => d.DepartmentName).Distinct().ToList();
            
            await _fkCacheService.WarmupCacheAsync(customerNames, siteNames, departmentNames, 
                new List<string>(), new List<string>(), cancellationToken);

            // Resolve foreign keys and enrich data
            var enrichedDrivers = new List<DriverStagingRecord>();
            var processedCount = 0;

            foreach (var driver in driverList)
            {
                try
                {
                    // Resolve FKs using cache
                    var customerResult = await _fkCacheService.GetCustomerIdAsync(driver.CustomerName, null, cancellationToken);
                    var siteResult = await _fkCacheService.GetSiteIdAsync(driver.CustomerName, driver.SiteName, cancellationToken);
                    var departmentResult = await _fkCacheService.GetDepartmentIdAsync(driver.CustomerName, driver.SiteName, driver.DepartmentName, cancellationToken);

                    if (customerResult.Found && siteResult.Found && departmentResult.Found)
                    {
                        enrichedDrivers.Add(driver);
                        result.InsertedRecords++;
                    }
                    else
                    {
                        result.SkippedRecords++;
                        result.Warnings.Add($"Driver {driver.FirstName} {driver.LastName}: Missing FK references");
                    }
                }
                catch (Exception ex)
                {
                    result.ErrorRecords++;
                    result.Errors.Add($"Driver {driver.FirstName} {driver.LastName}: {ex.Message}");
                }

                processedCount++;
                
                // Report progress periodically
                if (processedCount % 1000 == 0 || processedCount == driverList.Count)
                {
                    progress?.Report(new BulkInsertProgress
                    {
                        TotalRecords = result.TotalRecords,
                        ProcessedRecords = processedCount,
                        ElapsedTime = stopwatch.Elapsed,
                        CurrentOperation = "Resolving foreign keys"
                    });
                }
            }

            // Perform bulk insert of enriched data
            if (enrichedDrivers.Any())
            {
                var bulkResult = await BulkInsertToStagingAsync(sessionId, enrichedDrivers, 
                    "DriverStaging", batchSize, progress, cancellationToken);
                
                result.Success = bulkResult.Success;
                result.Errors.AddRange(bulkResult.Errors);
                result.Warnings.AddRange(bulkResult.Warnings);
                result.Metrics = bulkResult.Metrics;
            }

            result.Duration = stopwatch.Elapsed;
            
            _logger.LogInformation("Driver bulk insert completed for session {SessionId}: {InsertedRecords}/{TotalRecords} records in {Duration}",
                sessionId, result.InsertedRecords, result.TotalRecords, result.Duration);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Errors.Add(ex.Message);
            _logger.LogError(ex, "Driver bulk insert failed for session {SessionId}", sessionId);
        }

        return result;
    }

    public async Task<BulkInsertResult> BulkInsertVehiclesToStagingAsync(
        Guid sessionId,
        IEnumerable<VehicleStagingRecord> vehicles,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        var result = await BulkInsertToStagingAsync(sessionId, vehicles, "VehicleStaging", batchSize, progress, cancellationToken);
        
        _logger.LogInformation("Vehicle bulk insert completed for session {SessionId}: {InsertedRecords} records",
            sessionId, result.InsertedRecords);
        
        return result;
    }

    public async Task<BulkInsertResult> BulkInsertCardsToStagingAsync(
        Guid sessionId,
        IEnumerable<CardStagingRecord> cards,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        var result = await BulkInsertToStagingAsync(sessionId, cards, "CardStaging", batchSize, progress, cancellationToken);
        
        _logger.LogInformation("Card bulk insert completed for session {SessionId}: {InsertedRecords} records",
            sessionId, result.InsertedRecords);
        
        return result;
    }

    public async Task<BulkInsertResult> BulkInsertAccessPermissionsToStagingAsync(
        Guid sessionId,
        IEnumerable<AccessPermissionStagingRecord> accessRecords,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default)
    {
        var result = await BulkInsertToStagingAsync(sessionId, accessRecords, "AccessPermissionStaging", batchSize, progress, cancellationToken);
        
        _logger.LogInformation("Access permission bulk insert completed for session {SessionId}: {InsertedRecords} records",
            sessionId, result.InsertedRecords);
        
        return result;
    }

    public async Task<TransactionalMergeResult> ExecuteTransactionalMergeAsync(
        Guid sessionId,
        IEnumerable<MergeOperation> mergeOperations,
        TransactionStrategy strategy = TransactionStrategy.NestedWithSavepoints,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new TransactionalMergeResult { StrategyUsed = strategy };
        var operations = mergeOperations.OrderBy(op => op.Priority).ToList();

        _logger.LogInformation("Starting transactional MERGE with {OperationCount} operations using {Strategy} strategy",
            operations.Count, strategy);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            using var transaction = connection.BeginTransaction();
            
            try
            {
                foreach (var operation in operations)
                {
                    // Create savepoint for granular rollback
                    string? savepointName = null;
                    if (strategy == TransactionStrategy.NestedWithSavepoints)
                    {
                        savepointName = $"SP_{Guid.NewGuid():N}";
                        operation.SavepointName = savepointName;
                        
                        var savepointCommand = new SqlCommand($"SAVE TRANSACTION {savepointName}", connection, transaction);
                        await savepointCommand.ExecuteNonQueryAsync(cancellationToken);
                        result.SavepointsCreated.Add(savepointName);
                    }

                    try
                    {
                        var mergeResult = await MergeFromStagingToProductionAsync(
                            sessionId, 
                            operation.StagingTableName,
                            operation.ProductionTableName,
                            operation.Configuration,
                            1000, // Use smaller batch size for transactional operations
                            null,
                            cancellationToken);

                        result.OperationResults.Add(mergeResult);
                        result.TotalProcessedRecords += mergeResult.ProcessedRecords;

                        if (!mergeResult.Success)
                        {
                            result.Errors.AddRange(mergeResult.Errors);
                            
                            if (strategy == TransactionStrategy.NestedWithSavepoints && savepointName != null)
                            {
                                // Rollback to savepoint
                                var rollbackCommand = new SqlCommand($"ROLLBACK TRANSACTION {savepointName}", connection, transaction);
                                await rollbackCommand.ExecuteNonQueryAsync(cancellationToken);
                                result.SavepointsRolledBack.Add(savepointName);
                                
                                _logger.LogWarning("Rolled back to savepoint {SavepointName} due to MERGE failure", savepointName);
                            }
                            else
                            {
                                throw new InvalidOperationException($"MERGE operation failed: {string.Join("; ", mergeResult.Errors)}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (strategy == TransactionStrategy.NestedWithSavepoints && savepointName != null)
                        {
                            try
                            {
                                var rollbackCommand = new SqlCommand($"ROLLBACK TRANSACTION {savepointName}", connection, transaction);
                                await rollbackCommand.ExecuteNonQueryAsync(cancellationToken);
                                result.SavepointsRolledBack.Add(savepointName);
                            }
                            catch (Exception rollbackEx)
                            {
                                _logger.LogError(rollbackEx, "Failed to rollback savepoint {SavepointName}", savepointName);
                            }
                        }
                        
                        result.Errors.Add($"Operation {operation.StagingTableName}→{operation.ProductionTableName}: {ex.Message}");
                        throw;
                    }
                }

                await transaction.CommitAsync(cancellationToken);
                result.Success = true;
                
                _logger.LogInformation("Transactional MERGE completed successfully for session {SessionId}", sessionId);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync(cancellationToken);
                result.Success = false;
                result.Errors.Add($"Transaction failed: {ex.Message}");
                
                _logger.LogError(ex, "Transactional MERGE failed for session {SessionId}", sessionId);
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Errors.Add(ex.Message);
            _logger.LogError(ex, "Transactional MERGE setup failed for session {SessionId}", sessionId);
        }

        result.TotalDuration = stopwatch.Elapsed;
        return result;
    }

    public async Task OptimizeBulkOperationPerformanceAsync(
        string tableName,
        bool updateStatistics = true,
        bool recompileQueries = true,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            var optimizationCommands = new List<string>();

            if (updateStatistics)
            {
                optimizationCommands.Add($"UPDATE STATISTICS [{tableName}] WITH FULLSCAN");
            }

            if (recompileQueries)
            {
                optimizationCommands.Add("DBCC FREEPROCCACHE");
            }

            foreach (var commandText in optimizationCommands)
            {
                using var command = new SqlCommand(commandText, connection);
                command.CommandTimeout = _options.CommandTimeout * 2;
                await command.ExecuteNonQueryAsync(cancellationToken);
            }

            _logger.LogInformation("Performance optimization completed for table {TableName}", tableName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Performance optimization failed for table {TableName}", tableName);
        }
    }

    public async Task<BulkOperationMetrics> GetBulkOperationMetricsAsync(Guid sessionId)
    {
        // This would typically query the database for session metrics
        // For now, return a basic metrics object
        return new BulkOperationMetrics
        {
            SessionId = sessionId,
            MetricsTimestamp = DateTime.UtcNow
        };
    }

    public async Task<StagingValidationResult> ValidateStagingDataIntegrityAsync(
        Guid sessionId,
        IEnumerable<string> stagingTableNames,
        CancellationToken cancellationToken = default)
    {
        var result = new StagingValidationResult();
        var tableNames = stagingTableNames.ToList();

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken);

            foreach (var tableName in tableNames)
            {
                var tableResult = await ValidateIndividualStagingTableAsync(connection, tableName, sessionId, cancellationToken);
                result.TableResults[tableName] = tableResult;
                
                result.TotalRecordsValidated += tableResult.TotalRecords;
                result.ValidRecords += tableResult.ValidRecords;
                result.InvalidRecords += tableResult.InvalidRecords;
                
                if (!tableResult.IsValid)
                {
                    result.IsValid = false;
                    result.ReferentialIntegrityErrors.AddRange(tableResult.ValidationErrors);
                }
            }

            _logger.LogInformation("Staging validation completed for session {SessionId}: {ValidRecords}/{TotalRecords} valid",
                sessionId, result.ValidRecords, result.TotalRecordsValidated);
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.ReferentialIntegrityErrors.Add($"Validation failed: {ex.Message}");
            _logger.LogError(ex, "Staging validation failed for session {SessionId}", sessionId);
        }

        return result;
    }

    // Helper methods

    private DataTable CreateDataTableFromObjects<T>(IEnumerable<T> objects, string tableName) where T : class
    {
        var dataTable = new DataTable(tableName);
        var properties = typeof(T).GetProperties();

        // Add columns
        foreach (var property in properties)
        {
            var columnType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;
            dataTable.Columns.Add(property.Name, columnType);
        }

        // Add rows
        foreach (var obj in objects)
        {
            var row = dataTable.NewRow();
            foreach (var property in properties)
            {
                var value = property.GetValue(obj);
                row[property.Name] = value ?? DBNull.Value;
            }
            dataTable.Rows.Add(row);
        }

        return dataTable;
    }

    private void MapDataTableColumns(SqlBulkCopy bulkCopy, DataTable dataTable, string stagingTableName)
    {
        // Map columns based on staging table schema
        foreach (DataColumn column in dataTable.Columns)
        {
            bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
        }
    }

    private async Task<int> GetStagingRecordCountAsync(
        SqlConnection connection,
        string tableName,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        const string sql = "SELECT COUNT(*) FROM [Staging].[{0}] WHERE SessionId = @SessionId";
        using var command = new SqlCommand(string.Format(sql, tableName), connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        
        var result = await command.ExecuteScalarAsync(cancellationToken);
        return Convert.ToInt32(result ?? 0);
    }

    private string BuildMergeSQL(string stagingTableName, string productionTableName, MergeConfiguration config)
    {
        var sb = new StringBuilder();
        
        sb.AppendLine($"MERGE [dbo].[{productionTableName}] AS target");
        sb.AppendLine($"USING (SELECT * FROM BatchedData) AS source");
        sb.AppendLine($"ON ({string.Join(" AND ", config.KeyColumns.Select(col => $"target.[{col}] = source.[{col}]"))})");
        
        if (config.UpdateColumns.Any())
        {
            sb.AppendLine("WHEN MATCHED THEN");
            sb.AppendLine($"    UPDATE SET {string.Join(", ", config.UpdateColumns.Select(col => $"[{col}] = source.[{col}]"))}");
        }
        
        if (config.InsertColumns.Any())
        {
            sb.AppendLine("WHEN NOT MATCHED BY TARGET THEN");
            sb.AppendLine($"    INSERT ([{string.Join("], [", config.InsertColumns)}])");
            sb.AppendLine($"    VALUES (source.[{string.Join("], source.[", config.InsertColumns)}])");
        }
        
        if (config.AllowDelete)
        {
            sb.AppendLine("WHEN NOT MATCHED BY SOURCE THEN DELETE");
        }

        if (config.UseOutputClause)
        {
            sb.AppendLine("OUTPUT $action, inserted.*, deleted.*;");
        }
        else
        {
            sb.AppendLine(";");
        }

        return sb.ToString();
    }

    private async Task<TableValidationResult> ValidateIndividualStagingTableAsync(
        SqlConnection connection,
        string tableName,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        var result = new TableValidationResult { TableName = tableName };

        try
        {
            // Basic validation - count records and check for null required fields
            const string sql = @"
                SELECT 
                    COUNT(*) as TotalRecords,
                    COUNT(CASE WHEN ProcessingStatus = 'Failed' THEN 1 END) as ErrorRecords
                FROM [Staging].[{0}] 
                WHERE SessionId = @SessionId";

            using var command = new SqlCommand(string.Format(sql, tableName), connection);
            command.Parameters.AddWithValue("@SessionId", sessionId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync())
            {
                result.TotalRecords = reader.GetInt32("TotalRecords");
                result.InvalidRecords = reader.GetInt32("ErrorRecords");
                result.ValidRecords = result.TotalRecords - result.InvalidRecords;
                result.IsValid = result.InvalidRecords == 0;
            }
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.ValidationErrors.Add($"Validation query failed: {ex.Message}");
        }

        return result;
    }
}
