using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Helper class to provide test configuration using the new environment-based structure
    /// </summary>
    public static class TestConfigurationHelper
    {
        private static IEnvironmentConfigurationService? _environmentService;
        private static readonly object _lock = new object();

        public static IEnvironmentConfigurationService GetEnvironmentService()
        {
            if (_environmentService == null)
            {
                lock (_lock)
                {
                    if (_environmentService == null)
                    {
                        var configuration = new ConfigurationBuilder()
                            .AddJsonFile("appsettings.json", optional: false)
                            .Build();

                        var environmentConfig = new EnvironmentConfiguration();
                        configuration.GetSection("Migration").Bind(environmentConfig);

                        var options = Options.Create(environmentConfig);
                        _environmentService = new EnvironmentConfigurationService(options);
                        
                        // Set default environment to Development for tests
                        _environmentService.SetCurrentEnvironment("Development");
                    }
                }
            }
            return _environmentService;
        }

        public static MigrationConfiguration GetTestConfiguration()
        {
            var envService = GetEnvironmentService();
            return envService.CurrentMigrationConfiguration;
        }

        public static string GetTestConnectionString()
        {
            var envService = GetEnvironmentService();
            return envService.CurrentEnvironment.DatabaseConnection;
        }

        public static EnvironmentSettings GetTestEnvironment()
        {
            var envService = GetEnvironmentService();
            return envService.CurrentEnvironment;
        }
    }
} 