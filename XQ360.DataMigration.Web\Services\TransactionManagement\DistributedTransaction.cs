using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace XQ360.DataMigration.Web.Services.TransactionManagement
{
    /// <summary>
    /// Implementation of distributed transaction
    /// </summary>
    public class DistributedTransaction : IDistributedTransaction
    {
        private readonly ILogger _logger;
        private readonly DistributedTransactionOptions _options;
        private readonly List<ITransactionOperation> _operations;
        private readonly List<ICompensatingAction> _compensatingActions;
        private readonly List<ISavepoint> _savepoints;
        private bool _disposed;

        public Guid TransactionId { get; }
        public TransactionStatus Status { get; private set; }
        public SqlConnection SqlConnection { get; }
        public SqlTransaction SqlTransaction { get; }

        public DistributedTransaction(
            Guid transactionId,
            SqlConnection sqlConnection,
            SqlTransaction sqlTransaction,
            DistributedTransactionOptions options,
            ILogger logger)
        {
            TransactionId = transactionId;
            SqlConnection = sqlConnection;
            SqlTransaction = sqlTransaction;
            _options = options;
            _logger = logger;
            _operations = new List<ITransactionOperation>();
            _compensatingActions = new List<ICompensatingAction>();
            _savepoints = new List<ISavepoint>();
            Status = TransactionStatus.Active;
        }

        public async Task AddOperationAsync(ITransactionOperation operation, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (Status != TransactionStatus.Active)
            {
                throw new InvalidOperationException($"Cannot add operation to transaction in status {Status}");
            }

            _operations.Add(operation);
            _logger.LogDebug("Added operation {OperationId} to transaction {TransactionId}", 
                operation.OperationId, TransactionId);
        }

        public async Task AddCompensatingActionAsync(ICompensatingAction action, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (Status != TransactionStatus.Active)
            {
                throw new InvalidOperationException($"Cannot add compensating action to transaction in status {Status}");
            }

            _compensatingActions.Add(action);
            _logger.LogDebug("Added compensating action {ActionId} to transaction {TransactionId}", 
                action.ActionId, TransactionId);
        }

        public async Task<ISavepoint> CreateSavepointAsync(string name, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (Status != TransactionStatus.Active)
            {
                throw new InvalidOperationException($"Cannot create savepoint in transaction with status {Status}");
            }

            if (!_options.EnableSavepoints)
            {
                throw new InvalidOperationException("Savepoints are not enabled for this transaction");
            }

            try
            {
                // Create SQL savepoint
                var command = SqlConnection.CreateCommand();
                command.Transaction = SqlTransaction;
                command.CommandText = $"SAVE TRANSACTION {name}";
                await command.ExecuteNonQueryAsync(cancellationToken);

                var savepoint = new Savepoint(
                    name,
                    TransactionId,
                    DateTime.UtcNow,
                    _operations.Select(o => o.OperationId).ToList());

                _savepoints.Add(savepoint);

                _logger.LogDebug("Created savepoint {SavepointName} in transaction {TransactionId}", 
                    name, TransactionId);

                return savepoint;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create savepoint {SavepointName} in transaction {TransactionId}", 
                    name, TransactionId);
                throw;
            }
        }

        public async Task CommitAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (Status != TransactionStatus.Active)
            {
                throw new InvalidOperationException($"Cannot commit transaction in status {Status}");
            }

            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                Status = TransactionStatus.Committing;
                _logger.LogInformation("Committing distributed transaction {TransactionId}", TransactionId);

                // Commit SQL transaction
                SqlTransaction.Commit();

                Status = TransactionStatus.Committed;
                
                _logger.LogInformation("Distributed transaction {TransactionId} committed successfully in {Duration}ms", 
                    TransactionId, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                Status = TransactionStatus.Failed;
                _logger.LogError(ex, "Failed to commit distributed transaction {TransactionId}", TransactionId);
                
                // Attempt to execute compensating actions
                if (_options.EnableCompensatingActions && _compensatingActions.Any())
                {
                    await ExecuteCompensatingActionsAsync(cancellationToken);
                }
                
                throw;
            }
        }

        public async Task RollbackAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (Status == TransactionStatus.Committed || Status == TransactionStatus.RolledBack)
            {
                _logger.LogWarning("Attempted to rollback transaction {TransactionId} in status {Status}", 
                    TransactionId, Status);
                return;
            }

            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                Status = TransactionStatus.RollingBack;
                _logger.LogInformation("Rolling back distributed transaction {TransactionId}", TransactionId);

                // Rollback SQL transaction
                SqlTransaction.Rollback();

                // Execute compensating actions for any API operations
                if (_options.EnableCompensatingActions && _compensatingActions.Any())
                {
                    await ExecuteCompensatingActionsAsync(cancellationToken);
                }

                Status = TransactionStatus.RolledBack;
                
                _logger.LogInformation("Distributed transaction {TransactionId} rolled back successfully in {Duration}ms", 
                    TransactionId, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                Status = TransactionStatus.Failed;
                _logger.LogError(ex, "Failed to rollback distributed transaction {TransactionId}", TransactionId);
                throw;
            }
        }

        public async Task RollbackToSavepointAsync(ISavepoint savepoint, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (Status != TransactionStatus.Active)
            {
                throw new InvalidOperationException($"Cannot rollback to savepoint in transaction with status {Status}");
            }

            if (savepoint.TransactionId != TransactionId)
            {
                throw new ArgumentException("Savepoint does not belong to this transaction");
            }

            try
            {
                _logger.LogInformation("Rolling back to savepoint {SavepointName} in transaction {TransactionId}", 
                    savepoint.Name, TransactionId);

                // Rollback to SQL savepoint
                var command = SqlConnection.CreateCommand();
                command.Transaction = SqlTransaction;
                command.CommandText = $"ROLLBACK TRANSACTION {savepoint.Name}";
                await command.ExecuteNonQueryAsync(cancellationToken);

                // Execute compensating actions for operations after this savepoint
                var operationsToCompensate = _operations
                    .Where(o => !savepoint.OperationIds.Contains(o.OperationId))
                    .ToList();

                foreach (var operation in operationsToCompensate)
                {
                    var compensatingAction = operation.CreateCompensatingAction();
                    if (compensatingAction != null)
                    {
                        try
                        {
                            await compensatingAction.ExecuteAsync(cancellationToken);
                            _logger.LogDebug("Executed compensating action for operation {OperationId}", 
                                operation.OperationId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to execute compensating action for operation {OperationId}", 
                                operation.OperationId);
                        }
                    }
                }

                _logger.LogInformation("Successfully rolled back to savepoint {SavepointName} in transaction {TransactionId}", 
                    savepoint.Name, TransactionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to rollback to savepoint {SavepointName} in transaction {TransactionId}", 
                    savepoint.Name, TransactionId);
                throw;
            }
        }

        private async Task ExecuteCompensatingActionsAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Executing {ActionCount} compensating actions for transaction {TransactionId}", 
                _compensatingActions.Count, TransactionId);

            // Execute compensating actions in reverse order of priority
            var orderedActions = _compensatingActions.OrderByDescending(a => a.Priority).ToList();

            foreach (var action in orderedActions)
            {
                try
                {
                    var result = await action.ExecuteAsync(cancellationToken);
                    if (result.Success)
                    {
                        _logger.LogDebug("Compensating action {ActionId} executed successfully", action.ActionId);
                    }
                    else
                    {
                        _logger.LogWarning("Compensating action {ActionId} failed: {ErrorMessage}", 
                            action.ActionId, result.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing compensating action {ActionId}", action.ActionId);
                }
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(DistributedTransaction));
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                if (Status == TransactionStatus.Active)
                {
                    _logger.LogWarning("Disposing active transaction {TransactionId} - performing rollback", TransactionId);
                    SqlTransaction.Rollback();
                    Status = TransactionStatus.RolledBack;
                }

                SqlTransaction?.Dispose();
                SqlConnection?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing distributed transaction {TransactionId}", TransactionId);
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Implementation of savepoint
    /// </summary>
    public class Savepoint : ISavepoint
    {
        public string Name { get; }
        public Guid TransactionId { get; }
        public DateTime CreatedAt { get; }
        public IReadOnlyList<string> OperationIds { get; }

        public Savepoint(string name, Guid transactionId, DateTime createdAt, IReadOnlyList<string> operationIds)
        {
            Name = name;
            TransactionId = transactionId;
            CreatedAt = createdAt;
            OperationIds = operationIds;
        }
    }

    /// <summary>
    /// Implementation of compensating transaction
    /// </summary>
    public class CompensatingTransaction : ICompensatingTransaction
    {
        private readonly List<ICompensatingAction> _actions;
        private readonly ILogger _logger;
        private bool _disposed;

        public Guid CompensatingTransactionId { get; }
        public Guid OriginalTransactionId { get; }

        public CompensatingTransaction(
            Guid compensatingTransactionId,
            Guid originalTransactionId,
            List<ICompensatingAction> actions,
            ILogger logger)
        {
            CompensatingTransactionId = compensatingTransactionId;
            OriginalTransactionId = originalTransactionId;
            _actions = actions;
            _logger = logger;
        }

        public async Task<CompensatingTransactionResult> ExecuteAsync(CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new CompensatingTransactionResult
            {
                CompensatingTransactionId = CompensatingTransactionId,
                OriginalTransactionId = OriginalTransactionId
            };

            try
            {
                _logger.LogInformation("Executing compensating transaction {CompensatingTransactionId} with {ActionCount} actions", 
                    CompensatingTransactionId, _actions.Count);

                // Execute actions in reverse priority order
                var orderedActions = _actions.OrderByDescending(a => a.Priority).ToList();

                foreach (var action in orderedActions)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        var actionResult = await action.ExecuteAsync(cancellationToken);
                        result.ActionResults.Add(actionResult);

                        if (!actionResult.Success)
                        {
                            result.Errors.Add($"Action {action.ActionId} failed: {actionResult.ErrorMessage}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error executing compensating action {ActionId}", action.ActionId);
                        result.ActionResults.Add(new CompensatingActionResult
                        {
                            ActionId = action.ActionId,
                            RelatedOperationId = action.RelatedOperationId,
                            Success = false,
                            ErrorMessage = ex.Message,
                            Exception = ex
                        });
                        result.Errors.Add($"Action {action.ActionId} failed: {ex.Message}");
                    }
                }

                result.Success = !result.Errors.Any();
                result.Duration = stopwatch.Elapsed;

                _logger.LogInformation("Compensating transaction {CompensatingTransactionId} completed: {Success} in {Duration}ms", 
                    CompensatingTransactionId, result.Success, result.Duration.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing compensating transaction {CompensatingTransactionId}", 
                    CompensatingTransactionId);
                
                result.Success = false;
                result.Errors.Add(ex.Message);
                result.Duration = stopwatch.Elapsed;
                
                return result;
            }
        }

        public void Dispose()
        {
            if (_disposed) return;
            
            // Cleanup resources if needed
            _disposed = true;
        }
    }
}
