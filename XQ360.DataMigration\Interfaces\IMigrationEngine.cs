﻿using System.Threading.Tasks;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Interfaces
{
    public interface IMigrationEngine
    {
        Task<MigrationResult> ExecuteMigrationAsync(MigrationPlan plan);
        Task<bool> ValidateDataIntegrityAsync();
        Task SyncAllVehicleSettingsAsync();
    }

    public class MigrationPlan
    {
        public List<MigrationStep> ExecutionOrder { get; set; } = new List<MigrationStep>();
        public string? CsvInputDirectory { get; set; }
        public bool ValidateOnly { get; set; } = false;
        public bool ContinueOnError { get; set; } = false;
        public DateTime StartTime { get; set; } = DateTime.UtcNow;
    }

    public class MigrationStep
    {
        public string? Name { get; set; }
        public MigrationStrategy Strategy { get; set; }
        public string? CsvFile { get; set; }
        public bool IsRequired { get; set; } = true;
    }

    public enum MigrationStrategy
    {
        Api,
        Sql
    }
}