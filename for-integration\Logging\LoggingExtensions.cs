using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Events;

namespace FleetXQ.Tools.BulkImporter.Logging;

/// <summary>
/// Extension methods for configuring logging
/// </summary>
public static class LoggingExtensions
{
    /// <summary>
    /// Configures Serilog for the bulk importer application
    /// </summary>
    public static IHostBuilder ConfigureBulkImporterLogging(this IHostBuilder hostBuilder)
    {
        return hostBuilder.UseSerilog((context, configuration) =>
        {
            configuration
                .ReadFrom.Configuration(context.Configuration)
                .Enrich.With<CorrelationEnricher>()
                .Enrich.FromLogContext()
                .Enrich.WithProperty("Application", "FleetXQ.BulkImporter")
                .Enrich.WithProperty("Version", GetVersion())
                .Enrich.WithProperty("MachineName", Environment.MachineName)
                .Enrich.WithProperty("ProcessId", Environment.ProcessId);

            // Override minimum level for specific scenarios
            var environment = context.HostingEnvironment.EnvironmentName;
            if (environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                configuration.MinimumLevel.Debug();
            }
            else
            {
                configuration.MinimumLevel.Information();
            }

            // Override specific namespaces
            configuration
                .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
                .MinimumLevel.Override("System", LogEventLevel.Warning)
                .MinimumLevel.Override("Microsoft.Data.SqlClient", LogEventLevel.Error);
        });
    }

    /// <summary>
    /// Ensures log directories exist
    /// </summary>
    public static void EnsureLogDirectoriesExist()
    {
        var logDirectory = Path.Combine(Directory.GetCurrentDirectory(), "logs");
        if (!Directory.Exists(logDirectory))
        {
            Directory.CreateDirectory(logDirectory);
        }
    }

    /// <summary>
    /// Gets the application version
    /// </summary>
    private static string GetVersion()
    {
        var assembly = System.Reflection.Assembly.GetExecutingAssembly();
        var version = assembly.GetName().Version;
        return version?.ToString() ?? "Unknown";
    }
}

/// <summary>
/// Extension methods for ILogger to support correlation context
/// </summary>
public static class LoggerExtensions
{
    /// <summary>
    /// Logs with correlation context
    /// </summary>
    public static void LogWithCorrelation(this Microsoft.Extensions.Logging.ILogger logger, LogLevel level, string message, params object[] args)
    {
        using (Microsoft.Extensions.Logging.LoggerExtensions.BeginScope(logger, new Dictionary<string, object>
        {
            ["CorrelationId"] = CorrelationContext.CorrelationId ?? "none",
            ["OperationId"] = CorrelationContext.OperationId ?? "none"
        }))
        {
            logger.Log(level, message, args);
        }
    }

    /// <summary>
    /// Logs information with correlation context
    /// </summary>
    public static void LogInformationWithCorrelation(this Microsoft.Extensions.Logging.ILogger logger, string message, params object[] args)
    {
        LogWithCorrelation(logger, LogLevel.Information, message, args);
    }

    /// <summary>
    /// Logs error with correlation context
    /// </summary>
    public static void LogErrorWithCorrelation(this Microsoft.Extensions.Logging.ILogger logger, Exception exception, string message, params object[] args)
    {
        using (Microsoft.Extensions.Logging.LoggerExtensions.BeginScope(logger, new Dictionary<string, object>
        {
            ["CorrelationId"] = CorrelationContext.CorrelationId ?? "none",
            ["OperationId"] = CorrelationContext.OperationId ?? "none"
        }))
        {
            logger.LogError(exception, message, args);
        }
    }
}