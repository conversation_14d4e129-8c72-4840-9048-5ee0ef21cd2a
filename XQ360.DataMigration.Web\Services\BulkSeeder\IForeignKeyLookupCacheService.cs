using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Foreign Key Lookup Cache Service for sub-millisecond FK reference resolution
/// Implements Phase 1.1.2: Redis-style in-memory cache with <PERSON>R<PERSON> eviction
/// Performance target: Sub-millisecond lookup for repeated FK references
/// Memory target: <100MB cache size with compression
/// </summary>
public interface IForeignKeyLookupCacheService
{
    /// <summary>
    /// Initializes cache with pre-populated lookup data for a session
    /// TTL-based expiration with session lifetime management
    /// </summary>
    Task InitializeCacheAsync(Guid sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets Customer ID by name with automatic cache population
    /// Cache strategy: Hit → return, Miss → DB lookup → cache → return
    /// </summary>
    Task<CacheResult<Guid>> GetCustomerIdAsync(
        string customerName,
        string? dealerName = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets Site ID by customer and site name with cache optimization
    /// Uses composite key caching for optimal lookup performance
    /// </summary>
    Task<CacheResult<Guid>> GetSiteIdAsync(
        string customerName,
        string siteName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets Department ID by organizational hierarchy with cache chaining
    /// Leverages cached customer and site lookups for optimal performance
    /// </summary>
    Task<CacheResult<Guid>> GetDepartmentIdAsync(
        string customerName,
        string siteName,
        string departmentName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets Model ID by model name and dealer with cache optimization
    /// Supports dealer name resolution and caching
    /// </summary>
    Task<CacheResult<Guid>> GetModelIdAsync(
        string modelName,
        string dealerName,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets available Module ID by serial number with allocation tracking
    /// Includes module allocation status and session-based reservations
    /// </summary>
    Task<CacheResult<ModuleLookupResult>> GetAvailableModuleAsync(
        string? moduleSerialNumber = null,
        string? moduleType = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Reserves a module for allocation to prevent double-allocation
    /// Thread-safe module reservation with session correlation
    /// </summary>
    Task<bool> ReserveModuleAsync(
        Guid moduleId,
        Guid sessionId,
        Guid? vehicleId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Releases module reservation (for rollback scenarios)
    /// Supports partial rollback and session cleanup
    /// </summary>
    Task ReleaseModuleReservationAsync(
        Guid moduleId,
        Guid sessionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk cache warmup for improved batch processing performance
    /// Pre-loads commonly used FK references based on staging data
    /// </summary>
    Task WarmupCacheAsync(
        IEnumerable<string> customerNames,
        IEnumerable<string> siteNames,
        IEnumerable<string> departmentNames,
        IEnumerable<string> modelNames,
        IEnumerable<string> dealerNames,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets comprehensive cache performance metrics
    /// Includes hit ratios, memory usage, and performance statistics
    /// </summary>
    Task<CacheMetrics> GetCacheMetricsAsync();

    /// <summary>
    /// Optimizes cache performance with LRU eviction and compression
    /// Maintains target memory usage under 100MB
    /// </summary>
    Task OptimizeCacheAsync();

    /// <summary>
    /// Clears cache entries for a specific session
    /// Supports session-based cleanup and isolation
    /// </summary>
    Task ClearSessionCacheAsync(Guid sessionId);

    /// <summary>
    /// Validates cache consistency with database
    /// Detects stale cache entries and synchronization issues
    /// </summary>
    Task<CacheValidationResult> ValidateCacheConsistencyAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Result wrapper for cached lookups with performance metadata
/// </summary>
public class CacheResult<T>
{
    public bool Found { get; set; }
    public T? Value { get; set; }
    public bool CacheHit { get; set; }
    public TimeSpan LookupDuration { get; set; }
    public string? ErrorMessage { get; set; }
    
    public static CacheResult<T> Success(T value, bool cacheHit, TimeSpan duration)
    {
        return new CacheResult<T>
        {
            Found = true,
            Value = value,
            CacheHit = cacheHit,
            LookupDuration = duration
        };
    }
    
    public static CacheResult<T> NotFound(bool cacheHit, TimeSpan duration)
    {
        return new CacheResult<T>
        {
            Found = false,
            CacheHit = cacheHit,
            LookupDuration = duration
        };
    }
    
    public static CacheResult<T> Error(string errorMessage, TimeSpan duration)
    {
        return new CacheResult<T>
        {
            Found = false,
            ErrorMessage = errorMessage,
            LookupDuration = duration
        };
    }
}

/// <summary>
/// Module lookup result with allocation status
/// </summary>
public class ModuleLookupResult
{
    public Guid ModuleId { get; set; }
    public string SerialNumber { get; set; } = string.Empty;
    public string ModuleType { get; set; } = string.Empty;
    public string AllocationStatus { get; set; } = string.Empty; // Available, Allocated, Reserved
    public Guid? AllocatedToVehicleId { get; set; }
    public Guid? ReservedBySessionId { get; set; }
    public DateTime? AllocationTimestamp { get; set; }
}

/// <summary>
/// Cache performance metrics for monitoring and optimization
/// </summary>
public class CacheMetrics
{
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    
    // Hit/Miss Statistics
    public long TotalLookups { get; set; }
    public long CacheHits { get; set; }
    public long CacheMisses { get; set; }
    public decimal HitRatio => TotalLookups > 0 ? (decimal)CacheHits / TotalLookups * 100 : 0;
    
    // Performance Statistics
    public TimeSpan AverageLookupTime { get; set; }
    public TimeSpan AverageCacheHitTime { get; set; }
    public TimeSpan AverageCacheMissTime { get; set; }
    
    // Memory Usage
    public long TotalMemoryUsageBytes { get; set; }
    public decimal MemoryUsageMB => TotalMemoryUsageBytes / 1024.0m / 1024.0m;
    public int CachedItemCount { get; set; }
    public int EvictedItemCount { get; set; }
    
    // Cache Category Breakdown
    public CacheCategoryMetrics CustomerCache { get; set; } = new();
    public CacheCategoryMetrics SiteCache { get; set; } = new();
    public CacheCategoryMetrics DepartmentCache { get; set; } = new();
    public CacheCategoryMetrics ModelCache { get; set; } = new();
    public CacheCategoryMetrics ModuleCache { get; set; } = new();
    
    // Performance Recommendations
    public List<string> OptimizationRecommendations { get; set; } = new();
}

/// <summary>
/// Metrics for individual cache categories
/// </summary>
public class CacheCategoryMetrics
{
    public string CategoryName { get; set; } = string.Empty;
    public int ItemCount { get; set; }
    public long MemoryUsageBytes { get; set; }
    public long TotalLookups { get; set; }
    public long CacheHits { get; set; }
    public decimal HitRatio => TotalLookups > 0 ? (decimal)CacheHits / TotalLookups * 100 : 0;
    public TimeSpan AverageLookupTime { get; set; }
    public DateTime OldestEntry { get; set; }
    public DateTime NewestEntry { get; set; }
}

/// <summary>
/// Result of cache consistency validation
/// </summary>
public class CacheValidationResult
{
    public bool IsConsistent { get; set; }
    public List<string> InconsistencyDetails { get; set; } = new();
    public List<string> StaleEntries { get; set; } = new();
    public List<string> MissingEntries { get; set; } = new();
    public List<string> SynchronizationRecommendations { get; set; } = new();
    public DateTime ValidationTimestamp { get; set; } = DateTime.UtcNow;
}
