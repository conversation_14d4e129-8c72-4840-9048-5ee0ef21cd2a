namespace XQ360.DataMigration.Web.Services.BusinessRules.Rules
{
    /// <summary>
    /// Base interface for all business rules
    /// </summary>
    public interface IBusinessRule
    {
        /// <summary>
        /// Unique identifier for the rule
        /// </summary>
        string Id { get; }

        /// <summary>
        /// Human-readable name of the rule
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Detailed description of what the rule validates
        /// </summary>
        string Description { get; }

        /// <summary>
        /// Category for grouping related rules
        /// </summary>
        string Category { get; }

        /// <summary>
        /// Default severity level for violations of this rule
        /// </summary>
        ValidationSeverity DefaultSeverity { get; }

        /// <summary>
        /// Whether this rule is currently enabled
        /// </summary>
        bool IsEnabled { get; }

        /// <summary>
        /// Execution priority (lower numbers execute first)
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// Entity types this rule applies to
        /// </summary>
        string[] ApplicableEntityTypes { get; }

        /// <summary>
        /// Rule-specific configuration parameters
        /// </summary>
        Dictionary<string, object> Configuration { get; }

        /// <summary>
        /// Validates an entity against this business rule
        /// </summary>
        /// <param name="entity">The entity to validate</param>
        /// <param name="context">Validation context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result</returns>
        Task<ValidationResult> ValidateAsync(object entity, ValidationContext context, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Base abstract class for business rules providing common functionality
    /// </summary>
    public abstract class BusinessRuleBase : IBusinessRule
    {
        public abstract string Id { get; }
        public abstract string Name { get; }
        public abstract string Description { get; }
        public virtual string Category => "General";
        public virtual ValidationSeverity DefaultSeverity => ValidationSeverity.Error;
        public virtual bool IsEnabled => true;
        public virtual int Priority => 100;
        public abstract string[] ApplicableEntityTypes { get; }
        public virtual Dictionary<string, object> Configuration => new();

        public abstract Task<ValidationResult> ValidateAsync(object entity, ValidationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Helper method to create a validation issue
        /// </summary>
        protected ValidationIssue CreateIssue(ValidationSeverity severity, string message, string fieldName = "", object? fieldValue = null, string suggestion = "")
        {
            return new ValidationIssue
            {
                RuleId = Id,
                RuleName = Name,
                Severity = severity,
                Message = message,
                FieldName = fieldName,
                FieldValue = fieldValue,
                Suggestion = suggestion
            };
        }

        /// <summary>
        /// Helper method to create a successful validation result
        /// </summary>
        protected ValidationResult CreateSuccessResult()
        {
            return new ValidationResult
            {
                IsValid = true,
                Issues = new List<ValidationIssue>()
            };
        }

        /// <summary>
        /// Helper method to create a failed validation result
        /// </summary>
        protected ValidationResult CreateFailureResult(params ValidationIssue[] issues)
        {
            return new ValidationResult
            {
                IsValid = false,
                Issues = issues.ToList()
            };
        }

        /// <summary>
        /// Helper method to get property value from entity using reflection
        /// </summary>
        protected object? GetPropertyValue(object entity, string propertyName)
        {
            if (entity == null) return null;
            
            var property = entity.GetType().GetProperty(propertyName);
            return property?.GetValue(entity);
        }

        /// <summary>
        /// Helper method to check if entity has a property
        /// </summary>
        protected bool HasProperty(object entity, string propertyName)
        {
            if (entity == null) return false;
            return entity.GetType().GetProperty(propertyName) != null;
        }
    }
}
