﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System;
using System.IO;
using System.Threading.Tasks;
using XQ360.DataMigration.Implementations;
using XQ360.DataMigration.Interfaces;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using System.Linq;
using System.Net.Http;

namespace XQ360.DataMigration
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(GetConfiguration())
                .CreateLogger();

            try
            {
                Log.Information("Starting XQ360 Data Migration - Technology Stack: .NET 8.0");
                Log.Information("🔒 BULLETPROOF ORDERING GUARANTEE SYSTEM ACTIVATED");

                var host = CreateHostBuilder(args).Build();

                // Process command line arguments
                if (args.Length > 0)
                {
                    await ProcessCommand(args[0], host);
                }
                else
                {
                    DisplayHelp();
                }

                Log.Information("Migration application completed successfully");
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Migration application crashed");
                Environment.Exit(1);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        static async Task ProcessCommand(string command, IHost host)
        {
            var orchestrator = host.Services.GetRequiredService<MigrationOrchestrator>();
            
            switch (command.ToLower())
            {
                case "--migrate-spare-modules":
                    await ExecuteIndividualMigration(orchestrator, "spare-modules");
                    break;
                
                case "--migrate-preop-checklist":
                    await ExecuteIndividualMigration(orchestrator, "preop-checklist");
                    break;
                
                case "--migrate-vehicles":
                    await ExecuteIndividualMigration(orchestrator, "vehicles");
                    break;
                
                case "--migrate-persons":
                    await ExecuteIndividualMigration(orchestrator, "persons");
                    break;
                
                case "--migrate-cards-and-vehicle-access":
                    await ExecuteIndividualMigration(orchestrator, "cards-and-vehicle-access");
                    break;
                
                case "--migrate-supervisor-access":
                    await ExecuteIndividualMigration(orchestrator, "supervisor-access");
                    break;
                
                case "--migrate-driver-blacklist":
                    await ExecuteIndividualMigration(orchestrator, "driver-blacklist");
                    break;
                
                case "--migrate-website-users":
                    await ExecuteIndividualMigration(orchestrator, "website-users");
                    break;
                
                case "--sync-vehicle-settings":
                    await ExecuteIndividualMigration(orchestrator, "vehicle-sync-settings");
                    break;
                
                case "--migrate-all":
                    await ExecuteAllMigrations(orchestrator);
                    break;
                
                case "--help":
                case "-h":
                    DisplayHelp();
                    break;
                
                default:
                    Log.Error($"Unknown command: {command}");
                    DisplayHelp();
                    break;
            }
        }

        static void DisplayHelp()
        {
            Log.Information("🔒 XQ360 Data Migration - BULLETPROOF ORDERING GUARANTEE SYSTEM");
            Log.Information("Available commands:");
            Log.Information("");
            Log.Information("🚀 GUARANTEED EXECUTION ORDER:");
            Log.Information("  --migrate-spare-modules              : Step 1 - Spare Module (API - IoT hub integration)");
            Log.Information("  --migrate-preop-checklist            : Step 2 - Department Checklist + PreOp Questions (SQL - configuration data)");
            Log.Information("  --migrate-vehicles                    : Step 3 - Vehicles (SQL - complex but manageable)");
            Log.Information("  --migrate-persons                     : Step 4 - Persons (API - creates drivers automatically)");
            Log.Information("  --migrate-cards-and-vehicle-access   : Step 5 - Cards + Vehicle Access (SQL - atomic operation)");
            Log.Information("  --migrate-supervisor-access          : Step 6 - Supervisor Access (SQL - enhanced permissions)");
            Log.Information("  --migrate-driver-blacklist            : Step 7 - Driver Blacklist (SQL - removes vehicle access)");
            Log.Information("  --migrate-website-users               : Step 8 - Website Users (API - creates users with roles)");
            Log.Information("  --sync-vehicle-settings               : Step 9 - Sync Vehicle IoT Settings (API - synchronize device configurations)");
            Log.Information("");
            Log.Information("🔒 BULLETPROOF BATCH OPERATION:");
            Log.Information("  --migrate-all                         : Execute ALL migrations in GUARANTEED dependency order");
            Log.Information("                                          ✅ Validates all dependencies before starting");
            Log.Information("                                          ✅ Stops on first failure to preserve data integrity");
            Log.Information("                                          ✅ Provides detailed progress and error reporting");
            Log.Information("");
            Log.Information("Help:");
            Log.Information("  --help, -h                            : Show this help message");
            Log.Information("");
            Log.Information("Examples:");
            Log.Information("  dotnet run --migrate-all                    # RECOMMENDED: Full migration with bulletproof ordering");
            Log.Information("  dotnet run --migrate-spare-modules          # Individual step (validates dependencies)");
            Log.Information("  dotnet run --migrate-cards-and-vehicle-access   # Atomic Card + Vehicle Access operation");
            Log.Information("  dotnet run --migrate-driver-blacklist       # Remove vehicle access for blacklisted drivers");
            Log.Information("  dotnet run --sync-vehicle-settings          # Sync IoT device settings for all imported vehicles");
        }

        static async Task ExecuteIndividualMigration(MigrationOrchestrator orchestrator, string stepId)
        {
            Log.Information($"🚀 Executing individual migration: {stepId}");
            Log.Warning("⚠️  IMPORTANT: Individual migrations don't guarantee dependency order!");
            Log.Warning("⚠️  Use --migrate-all for bulletproof ordering guarantee.");
            
            var result = await orchestrator.ExecuteStepAsync(stepId);
            LogOrchestrationResult(result);
        }

        static async Task ExecuteAllMigrations(MigrationOrchestrator orchestrator)
        {
            Log.Information("🔒 BULLETPROOF ORDERED MIGRATION PROCESS STARTING");
            Log.Information("🎯 GUARANTEED EXECUTION ORDER - NO EXCEPTIONS!");
            
            var result = await orchestrator.ExecuteAllMigrationsAsync();
            LogOrchestrationResult(result);
            
            if (!result.Success)
            {
                Log.Error("❌ MIGRATION PROCESS FAILED - EXITING WITH ERROR CODE");
                Environment.Exit(1);
            }
        }

        static void LogOrchestrationResult(MigrationOrchestrationResult result)
        {
            if (result.Success)
            {
                Log.Information("🎉 === MIGRATION ORCHESTRATION SUCCESS ===");
                Log.Information($"✅ Total Duration: {result.Duration}");
                Log.Information($"✅ Completed Steps: {result.CompletedSteps.Count}");
                Log.Information($"✅ Steps: {string.Join(" → ", result.CompletedSteps)}");
                
                // Log individual step results
                foreach (var stepResult in result.StepResults)
                {
                    Log.Information($"📊 {stepResult.StepName}:");
                    Log.Information($"     Records: {stepResult.RecordsProcessed} processed, {stepResult.RecordsInserted} inserted, {stepResult.RecordsSkipped} skipped");
                    Log.Information($"     Duration: {stepResult.Duration}");
                    
                    if (stepResult.Warnings.Any())
                    {
                        foreach (var warning in stepResult.Warnings)
                        {
                            Log.Warning($"     Warning: {warning}");
                        }
                    }
                }
            }
            else
            {
                Log.Error("❌ === MIGRATION ORCHESTRATION FAILED ===");
                Log.Error($"❌ Total Duration: {result.Duration}");
                Log.Error($"❌ Completed Steps: {result.CompletedSteps.Count}");
                Log.Error($"❌ Failed At: {result.FailedStep?.Name ?? "Unknown"}");
                
                foreach (var error in result.Errors)
                {
                    Log.Error($"❌ Error: {error}");
                }
                
                // Log individual step results
                foreach (var stepResult in result.StepResults)
                {
                    if (stepResult.Success)
                    {
                        Log.Information($"✅ {stepResult.StepName}: SUCCESS");
                    }
                    else
                    {
                        Log.Error($"❌ {stepResult.StepName}: FAILED");
                        foreach (var error in stepResult.Errors)
                        {
                            Log.Error($"     Error: {error}");
                        }
                    }
                }
            }
        }

        static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseSerilog()
                .ConfigureServices((context, services) =>
                {
                    // Bind configuration
                    services.Configure<MigrationConfiguration>(
                        context.Configuration.GetSection("Migration"));
                    
                    // Bind observability configuration
                    services.Configure<ObservabilityConfiguration>(
                        context.Configuration.GetSection("Observability"));

                    // Register HTTP client and API services
                    services.AddHttpClient<XQ360ApiClient>(client =>
                    {
                        // Configure HttpClient to handle cookies and session state
                        // This matches the behavior of Python's requests.Session()
                        client.Timeout = TimeSpan.FromMinutes(5);
                    })
                    .ConfigurePrimaryHttpMessageHandler(() =>
                    {
                        return new HttpClientHandler()
                        {
                            CookieContainer = new System.Net.CookieContainer(),
                            UseCookies = true
                        };
                    });
                    
                    services.AddScoped<XQ360ApiClient>();

                    // Register the bulletproof migration orchestrator
                    services.AddScoped<MigrationOrchestrator>();

                    // Register reporting service
                    services.AddScoped<MigrationReportingService>();

                    // Register permission service
                    services.AddScoped<IPermissionService, PermissionService>();

                    // Register observability service
                    services.AddScoped<IObservabilityService, ObservabilityService>();

                    // Register migration services
                    services.AddScoped<SpareModuleMigration>();
                    services.AddScoped<PreOpChecklistMigration>();
                    services.AddScoped<VehicleMigration>();
                    services.AddScoped<PersonMigration>();
                    services.AddScoped<CardMigration>();
                    services.AddScoped<VehicleAccessMigration>();
                    services.AddScoped<SupervisorAccessMigration>();
                    services.AddScoped<DriverBlacklistMigration>();
                    services.AddScoped<WebsiteUserMigration>();
                    services.AddScoped<VehicleSyncMigration>();

                    // Add logging
                    services.AddLogging();
                });

        static IConfiguration GetConfiguration()
        {
            return new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.Development.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
        }
    }
}