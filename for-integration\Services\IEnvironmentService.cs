using FleetXQ.Tools.BulkImporter.Configuration;

namespace FleetXQ.Tools.BulkImporter.Services;

/// <summary>
/// Service for managing environment-specific operations and validations
/// </summary>
public interface IEnvironmentService
{
    /// <summary>
    /// Gets the current environment configuration
    /// </summary>
    EnvironmentOptions Environment { get; }

    /// <summary>
    /// Validates if an operation is allowed in the current environment
    /// </summary>
    /// <param name="operationSize">Size of the operation (number of records)</param>
    /// <returns>Validation result</returns>
    EnvironmentValidationResult ValidateOperation(int operationSize);

    /// <summary>
    /// Checks if the current time is within a maintenance window
    /// </summary>
    /// <returns>True if in maintenance window</returns>
    bool IsInMaintenanceWindow();

    /// <summary>
    /// Gets environment-specific file paths with proper resolution
    /// </summary>
    /// <param name="relativePath">Relative path from configuration</param>
    /// <returns>Resolved absolute path for the current environment</returns>
    string ResolveFilePath(string relativePath);

    /// <summary>
    /// Sends notification to environment-specific webhooks
    /// </summary>
    /// <param name="message">Notification message</param>
    /// <param name="level">Notification level</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SendNotificationAsync(string message, NotificationLevel level = NotificationLevel.Info, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets environment display information for logging and UI
    /// </summary>
    string GetEnvironmentDisplayName();
}

/// <summary>
/// Result of environment validation
/// </summary>
public class EnvironmentValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public bool RequiresApproval { get; set; }
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Notification level for environment notifications
/// </summary>
public enum NotificationLevel
{
    Info,
    Warning,
    Error,
    Critical
}
