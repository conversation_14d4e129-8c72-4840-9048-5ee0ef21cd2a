# FleetXQ Bulk Importer Tool

A comprehensive bulk data import tool for FleetXQ that provides both simplified demo functionality and production-ready integration with the full FleetXQ data layer.

## Overview

The Bulk Importer Tool is designed to handle large-scale data imports for FleetXQ, supporting both drivers and vehicles with comprehensive validation, error handling, and progress tracking.

## Architecture

### Projects Structure

- **FleetXQ.Tools.BulkImporter.Core** - Core business logic and services
- **FleetXQ.Tools.BulkImporter.WebApi** - REST API and web interface

### Key Features

- **Dual Implementation Approach**:
  - Simplified controllers for demo/development
  - Production controllers with full FleetXQ integration (available as .bak files)
- **Environment-aware Operations**: Different validation rules for different environments
- **Real-time Progress Tracking**: SignalR integration for live updates
- **Comprehensive Error Handling**: Detailed error reporting and validation
- **Batch Processing**: Configurable batch sizes for optimal performance
- **Data Generation**: Built-in test data generation capabilities

## API Endpoints

### Dealer Management
- `GET /api/dealer` - List dealers with filtering and pagination
- `GET /api/dealer/{id}` - Get specific dealer
- `GET /api/dealer/search` - Search dealers by name/subdomain
- `GET /api/dealer/{id}/validation` - Validate dealer for import operations

### Customer Management
- `GET /api/customer` - List customers for a dealer
- `GET /api/customer/{id}` - Get specific customer
- `POST /api/customer` - Create new customer
- `GET /api/customer/{dealerId}/{customerId}/validation` - Validate customer

### Bulk Import Operations
- `POST /api/bulk-import/session` - Create import session
- `GET /api/bulk-import/session/{id}` - Get session status
- `POST /api/bulk-import/session/{id}/execute` - Execute import
- `DELETE /api/bulk-import/session/{id}` - Cancel session

### Data Generation
- `POST /api/data-generation/drivers` - Generate driver data
- `POST /api/data-generation/vehicles` - Generate vehicle data
- `GET /api/data-generation/preview/drivers` - Preview driver data
- `GET /api/data-generation/preview/vehicles` - Preview vehicle data

### Environment Information
- `GET /api/environment` - Get current environment info
- `GET /api/environment/validation` - Validate environment for operations

## Configuration

### Core Configuration (appsettings.json)

```json
{
  "BulkImporter": {
    "DefaultBatchSize": 1000,
    "MaxBatchSize": 5000,
    "MaxConcurrentOperations": 3,
    "SessionTimeoutMinutes": 60,
    "EnableProgressTracking": true,
    "EnableDataValidation": true
  },
  "DataGeneration": {
    "DefaultDriverCount": 100,
    "DefaultVehicleCount": 50,
    "MaxDriverCount": 10000,
    "MaxVehicleCount": 5000,
    "UseRealisticData": true,
    "SeedValue": null
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=FleetXQ;Trusted_Connection=true;",
    "StagingConnection": "Server=localhost;Database=FleetXQ_Staging;Trusted_Connection=true;"
  },
  "Environment": {
    "Name": "Development",
    "RequireApprovalThreshold": 5000,
    "MaintenanceWindowStart": "02:00",
    "MaintenanceWindowEnd": "04:00",
    "NotificationWebhookUrl": null
  }
}
```

## Usage Examples

### Creating an Import Session

```bash
curl -X POST "https://localhost:7001/api/bulk-import/session" \
  -H "Content-Type: application/json" \
  -d '{
    "dealerId": "12345678-1234-1234-1234-123456789012",
    "customerId": "87654321-4321-4321-4321-210987654321",
    "driversCount": 500,
    "vehiclesCount": 250,
    "batchSize": 100,
    "dryRun": false
  }'
```

### Executing an Import

```bash
curl -X POST "https://localhost:7001/api/bulk-import/session/{sessionId}/execute"
```

### Monitoring Progress (SignalR)

```javascript
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/hubs/import-progress")
    .build();

connection.on("ImportProgress", (update) => {
    console.log(`Progress: ${update.percentageComplete}%`);
    console.log(`Processed: ${update.processedRows}/${update.totalRows}`);
});

connection.start();
```

## Development vs Production

### Simplified Controllers (Development)
- In-memory session storage
- Mock data validation
- Basic error handling
- Suitable for demos and development

### Production Controllers (Available)
- Full FleetXQ data layer integration
- Database-backed session management
- Comprehensive validation
- Production-grade error handling
- Real-time progress tracking

## Building and Running

### Prerequisites
- .NET 7.0 SDK
- SQL Server (for production mode)
- Visual Studio 2022 or VS Code

### Build
```bash
cd Tools/BulkImporter
dotnet build
```

### Run
```bash
cd Tools/BulkImporter/FleetXQ.Tools.BulkImporter.WebApi
dotnet run
```

The API will be available at:
- HTTPS: https://localhost:7001
- HTTP: http://localhost:5001
- Swagger UI: https://localhost:7001/swagger

## Health Checks

The application includes comprehensive health checks:
- `/health` - Overall health status
- `/health/ready` - Readiness probe
- `/health/live` - Liveness probe

## Logging

Structured logging with Serilog:
- Console output for development
- File logging for production
- Correlation IDs for request tracking
- Performance metrics

## Security Features

- JWT Bearer authentication support
- Rate limiting
- CORS configuration
- Request/response logging
- Error sanitization

## Monitoring and Observability

- Real-time progress updates via SignalR
- Comprehensive error tracking
- Performance metrics
- Health check endpoints
- Structured logging with correlation IDs