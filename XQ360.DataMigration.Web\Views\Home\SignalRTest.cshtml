@{
    ViewData["Title"] = "SignalR Test";
}

<div class="container mt-4">
    <h1>SignalR WebSocket Connection Test</h1>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Connection Status</h5>
                </div>
                <div class="card-body">
                    <div id="connection-status" class="alert alert-info">
                        Initializing connection...
                    </div>
                    
                    <div class="mb-3">
                        <button id="connect-btn" class="btn btn-primary">Connect to SignalR</button>
                        <button id="disconnect-btn" class="btn btn-secondary" disabled>Disconnect</button>
                    </div>
                    
                    <div class="mb-3">
                        <button id="test-message-btn" class="btn btn-success" disabled>Send Test Message</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Connection Details</h5>
                </div>
                <div class="card-body">
                    <div id="connection-details">
                        <p><strong>Hub URL:</strong> <span id="hub-url">/migrationHub</span></p>
                        <p><strong>Connection ID:</strong> <span id="connection-id">Not connected</span></p>
                        <p><strong>Transport:</strong> <span id="transport">Unknown</span></p>
                        <p><strong>State:</strong> <span id="connection-state">Disconnected</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Console Output</h5>
                </div>
                <div class="card-body">
                    <div id="console-output" class="bg-dark text-light p-3 rounded" style="font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                        <div>Waiting for connection events...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Troubleshooting Information</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6>Common SignalR Issues:</h6>
                        <ul>
                            <li><strong>WebSocket failed to connect:</strong> IIS WebSockets not enabled or proxy blocking</li>
                            <li><strong>Connection ID not found:</strong> Server restart or sticky sessions disabled</li>
                            <li><strong>Transport fallback:</strong> WebSockets blocked, using Server-Sent Events or Long Polling</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6>Server Requirements:</h6>
                        <ul>
                            <li>IIS WebSockets feature enabled</li>
                            <li>ASP.NET Core Hosting Bundle installed</li>
                            <li>Application pool configured for WebSockets</li>
                            <li>No proxy blocking WebSocket connections</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.0/signalr.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const consoleOutput = document.getElementById('console-output');
    const connectionStatus = document.getElementById('connection-status');
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const testMessageBtn = document.getElementById('test-message-btn');
    const connectionId = document.getElementById('connection-id');
    const transport = document.getElementById('transport');
    const connectionState = document.getElementById('connection-state');
    
    let connection = null;
    
    function log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const color = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
        consoleOutput.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
        console.log(`[${timestamp}] ${message}`);
    }
    
    function updateConnectionStatus(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : 'alert-info';
        connectionStatus.className = `alert ${alertClass}`;
        connectionStatus.textContent = message;
    }
    
    function updateConnectionDetails() {
        if (connection) {
            connectionId.textContent = connection.connectionId || 'Not available';
            transport.textContent = connection.transport?.name || 'Unknown';
            connectionState.textContent = connection.state;
        } else {
            connectionId.textContent = 'Not connected';
            transport.textContent = 'Unknown';
            connectionState.textContent = 'Disconnected';
        }
    }
    
    connectBtn.addEventListener('click', async function() {
        try {
            log('Attempting to connect to SignalR hub...');
            updateConnectionStatus('Connecting to SignalR...', 'info');
            
            // Create connection
            connection = new signalR.HubConnectionBuilder()
                .withUrl('/migrationHub')
                .withAutomaticReconnect()
                .configureLogging(signalR.LogLevel.Debug)
                .build();
            
            // Connection event handlers
            connection.onreconnecting(error => {
                log(`Reconnecting... Error: ${error}`, 'warning');
                updateConnectionStatus('Reconnecting...', 'warning');
                updateConnectionDetails();
            });
            
            connection.onreconnected(connectionId => {
                log(`Reconnected. Connection ID: ${connectionId}`, 'success');
                updateConnectionStatus('Reconnected!', 'success');
                updateConnectionDetails();
            });
            
            connection.onclose(error => {
                log(`Connection closed. Error: ${error}`, 'error');
                updateConnectionStatus('Connection closed', 'error');
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                testMessageBtn.disabled = true;
                updateConnectionDetails();
            });
            
            // Start connection
            await connection.start();
            
            log('Successfully connected to SignalR hub!', 'success');
            updateConnectionStatus('Connected to SignalR!', 'success');
            connectBtn.disabled = true;
            disconnectBtn.disabled = false;
            testMessageBtn.disabled = false;
            updateConnectionDetails();
            
        } catch (error) {
            log(`Failed to connect: ${error}`, 'error');
            updateConnectionStatus(`Connection failed: ${error}`, 'error');
            updateConnectionDetails();
        }
    });
    
    disconnectBtn.addEventListener('click', async function() {
        if (connection) {
            try {
                await connection.stop();
                log('Disconnected from SignalR hub', 'info');
                updateConnectionStatus('Disconnected', 'info');
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                testMessageBtn.disabled = true;
                updateConnectionDetails();
            } catch (error) {
                log(`Error disconnecting: ${error}`, 'error');
            }
        }
    });
    
    testMessageBtn.addEventListener('click', async function() {
        if (connection) {
            try {
                log('Sending test message to hub...');
                await connection.invoke('JoinMigrationGroup', 'test-migration-id');
                log('Test message sent successfully!', 'success');
            } catch (error) {
                log(`Error sending test message: ${error}`, 'error');
            }
        }
    });
    
    // Initial log
    log('SignalR test page loaded. Click "Connect to SignalR" to test the connection.');
    updateConnectionDetails();
});
</script> 