﻿using CsvHelper.Configuration.Attributes;

namespace XQ360.DataMigration.Models
{
    // SPARE_MODEL_IMPORT_TEMPLATE.csv
    public class SpareModuleImportModel
    {
        public string? Dealer { get; set; }
        [Name("IoT Device ID")]
        public string? IoTDeviceID { get; set; }
        public int CCID { get; set; }
        [Name("RA Number")]
        public int RANumber { get; set; }
        [Name("Tech Number")]
        public int TechNumber { get; set; }
    }

    // PERSON_IMPORT_TEMPLATE.csv
    public class PersonImportModel
    {
        public string? Customer { get; set; }
        public string? Site { get; set; }
        public string? Department { get; set; }
        [Name("First Name")]
        public string? FirstName { get; set; }
        [Name("Last Name")]
        public string? LastName { get; set; }
        [Name("Send Deny Message")]
        public bool SendDenyMessage { get; set; }
        [Name("Website Access")]
        public bool WebsiteAccess { get; set; }
        [Name("IsDriver")]
        public bool IsDriver { get; set; }
        [Name("IsSupervisor")]
        public bool IsSupervisor { get; set; }
        [Name("VOR Activate/Deactivate")]
        public bool VORActivateDeactivate { get; set; }
        [Name("Normal Driver Access")]
        public bool NormalDriverAccess { get; set; }
        [Name("CanUnlockVehicle")]
        public bool CanUnlockVehicle { get; set; }
    }

    // CARD_IMPORT.csv
    public class CardImportModel
    {
        public string? Dealer { get; set; }
        public string? Customer { get; set; }
        public string? Site { get; set; }
        [Name("Department Name")]
        public string? DepartmentName { get; set; }
        [Name("First Name")]
        public string? FirstName { get; set; }
        [Name("Last Name")]
        public string? LastName { get; set; }
        [Name("Facility Code")]
        public string? FacilityCode { get; set; }
        [Name("Card Type")]
        public string? CardType { get; set; }
        [Name("Card No")]
        public string? CardNo { get; set; }
        [Name("Reader Type")]
        public string? ReaderType { get; set; }
        public string? Weigand { get; set; }
        [Name("Access Level")]
        public string? AccessLevel { get; set; }
    }

    // VEHICLE_IMPORT.csv
    public class VehicleImportModel
    {
        public string? Dealer { get; set; }
        public string? Customer { get; set; }
        public string? Site { get; set; }
        [Name("Department Name")]
        public string? DepartmentName { get; set; }
        [Name("Device ID")]
        public string? DeviceID { get; set; }
        [Name("Serial No")]
        public string? SerialNo { get; set; }
        [Name("Hire No")]
        public string? HireNo { get; set; }
        [Name("Model Name")]
        public string? ModelName { get; set; }
        [Name("Impact Lockout")]
        public bool ImpactLockout { get; set; }
        public bool IsCanbus { get; set; }
        [Name("On Hire")]
        public bool OnHire { get; set; }
        [Name("Timeout Enabled")]
        public bool TimeoutEnabled { get; set; }
        [Name("Idle Timer")]
        public int? IdleTimer { get; set; }
        [Name("Canrule Name")]
        public string? CanruleName { get; set; }
        [Name("Question Timeout")]
        public int? QuestionTimeout { get; set; }
        [Name("Show Comment")]
        public bool ShowComment { get; set; }
        public bool Randomisation { get; set; }
        [Name("Full Lockout Timeout")]
        public int? FullLockoutTimeout { get; set; }
        [Name("VOR Status")]
        public bool VORStatus { get; set; }
        [Name("Amber Alert Enabled")]
        public bool AmberAlertEnabled { get; set; }
        [Name("VOR Status Confirmed")]
        public bool VORStatusConfirmed { get; set; }
        [Name("Full Lockout")]
        public bool FullLockout { get; set; }
        [Name("Default Technician Access")]
        public bool DefaultTechnicianAccess { get; set; }
        [Name("Pedestrian Safety")]
        public bool PedestrianSafety { get; set; }
        [Name("Checklist Type")]
        public string? ChecklistType { get; set; }
        [Name("Time slot1")]
        public string? Timeslot1 { get; set; }
        [Name("Time slot2")]
        public string? Timeslot2 { get; set; }
        [Name("Time slot3")]
        public string? Timeslot3 { get; set; }
        [Name("Time slot4")]
        public string? Timeslot4 { get; set; }
    }

    // PREOP_CHECKLIST_IMPORT.csv
    public class PreOpChecklistImportModel
    {
        public string? Dealer { get; set; }
        public string? Customer { get; set; }
        public string? Site { get; set; }
        public string? Department { get; set; }
        public string? Model { get; set; }
        public string? Question { get; set; }
        [Name("Expected Answer")]
        public bool ExpectedAnswer { get; set; }
        public bool Critical { get; set; }
        public bool ExcludeFromRandom { get; set; }
        [Name("Sort Order")]
        public int SortOrder { get; set; }
    }

    // SUPERVISOR_ACCESS_IMPORT.csv
    public class SupervisorAccessImportModel
    {
        [Name("Person Dealer")]
        public string? PersonDealer { get; set; }
        [Name("Person Customer")]
        public string? PersonCustomer { get; set; }
        [Name("Person Site")]
        public string? PersonSite { get; set; }
        [Name("Person Department")]
        public string? PersonDepartment { get; set; }
        [Name("First Name")]
        public string? FirstName { get; set; }
        [Name("Last Name")]
        public string? LastName { get; set; }
        public string? Weigand { get; set; }
        [Name("Hire No")]
        public string? HireNo { get; set; }
        [Name("Serial NO")]
        public string? SerialNO { get; set; }
        [Name("GMTP ID")]
        public string? GMTPID { get; set; }
        [Name("Supervisor Authorization")]
        public string? SupervisorAuthorization { get; set; }
    }

    // Legacy models for compatibility
    public class SpareModuleModel
    {
        public string? Dealer { get; set; }
        [Name("IoT Device ID")]
        public string? IoTDeviceId { get; set; }
        public int CCID { get; set; }
        [Name("RA Number")]
        public int RANumber { get; set; }
        [Name("Tech Number")]
        public int TechNumber { get; set; }
    }

    public class PersonModel
    {
        public string? Customer { get; set; }
        public string? Site { get; set; }
        public string? Department { get; set; }
        [Name("First Name")]
        public string? FirstName { get; set; }
        [Name("Last Name")]
        public string? LastName { get; set; }
        [Name("Send Deny Message")]
        public bool SendDenyMessage { get; set; }
        [Name("Website Access")]
        public bool WebsiteAccess { get; set; }
        [Name("Is Driver")]
        public bool IsDriver { get; set; }
        [Name("Is Supervisor")]
        public bool IsSupervisor { get; set; }
        [Name("VOR Activate/Deactivate")]
        public bool VORActivateDeactivate { get; set; }
        [Name("Normal Driver Access")]
        public bool NormalDriverAccess { get; set; }
        [Name("Can Unlock Vehicle")]
        public bool CanUnlockVehicle { get; set; }
    }

    public class CardModel
    {
        public string? Customer { get; set; }
        public string? Site { get; set; }
        public string? Department { get; set; }
        [Name("First Name")]
        public string? FirstName { get; set; }
        [Name("Last Name")]
        public string? LastName { get; set; }
        [Name("Facility Code")]
        public int FacilityCode { get; set; }
        [Name("Card Type")]
        public string? CardType { get; set; }
        [Name("Card No")]
        public int CardNo { get; set; }
        [Name("Reader Type")]
        public string? ReaderType { get; set; }
        public string? Weigand { get; set; }
    }

    // Import Driver Blacklist.csv
    public class DriverBlacklistImportModel
    {
        [Name("Person Dealer")]
        public string? PersonDealer { get; set; }
        [Name("Person Customer")]
        public string? PersonCustomer { get; set; }
        [Name("Person Site")]
        public string? PersonSite { get; set; }
        [Name("Person Department")]
        public string? PersonDepartment { get; set; }
        [Name("First Name")]
        public string? FirstName { get; set; }
        [Name("Last Name")]
        public string? LastName { get; set; }
        public string? Weigand { get; set; }
        [Name("Hire No")]
        public string? HireNo { get; set; }
        [Name("Serial NO")]
        public string? SerialNO { get; set; }
        [Name("GMTP ID")]
        public string? GMTPID { get; set; }
    }

    // WEBSITE_USER_IMPORT.csv
    public class WebsiteUserImportModel
    {
        public string? Dealer { get; set; }
        public string? Customer { get; set; }
        public string? Site { get; set; }
        [Name("Department Name")]
        public string? DepartmentName { get; set; }
        [Name("First Name")]
        public string? FirstName { get; set; }
        [Name("Last Name")]
        public string? LastName { get; set; }
        public string? Username { get; set; }
        public string? Email { get; set; }
        public string? Password { get; set; }
        [Name("Preferred Locale")]
        public string? PreferredLocale { get; set; }
        [Name("Website Access Level")]
        public string? WebsiteAccessLevel { get; set; }
        [Name("Access Group")]
        public string? AccessGroup { get; set; }
        public string? Role { get; set; }
    }
}