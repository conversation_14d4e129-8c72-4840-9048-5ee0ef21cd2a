# Fix script for file path issues and deployment
# This script addresses the mismatch between XQ360Migration (deployment) and XQ360.DataMigration (application) folder names

Write-Host "=== Fixing File Path Issues and Deploying Application ===" -ForegroundColor Cyan
Write-Host ""

# Configuration
$appPoolName = "XQ360Migration"
$deployPath = "C:\inetpub\wwwroot\XQ360Migration"
$sourcePath = "C:\FleetXQ\XQ360.DataMigration\publish"

Write-Host "1. Stopping IIS Application Pool..." -ForegroundColor Yellow
try {
    Stop-WebAppPool -Name $appPoolName -ErrorAction Stop
    Write-Host "   ✓ Application pool stopped successfully" -ForegroundColor Green
}
catch {
    Write-Host "   ⚠️ Could not stop application pool (may not be running): $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "2. Backing up current deployment..." -ForegroundColor Yellow
$backupPath = "$deployPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
if (Test-Path $deployPath) {
    Copy-Item $deployPath $backupPath -Recurse -Force
    Write-Host "   ✓ Backup created: $backupPath" -ForegroundColor Green
} else {
    Write-Host "   ⚠️ No existing deployment found to backup" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "3. Deploying new application..." -ForegroundColor Yellow
if (Test-Path $deployPath) {
    Remove-Item $deployPath -Recurse -Force
    Write-Host "   ✓ Removed existing deployment" -ForegroundColor Green
}

Copy-Item $sourcePath $deployPath -Recurse -Force
Write-Host "   ✓ Application deployed to: $deployPath" -ForegroundColor Green

Write-Host ""

Write-Host "4. Creating CSV_Input directory..." -ForegroundColor Yellow
$csvInputPath = "$deployPath\CSV_Input"
if (!(Test-Path $csvInputPath)) {
    New-Item -ItemType Directory -Path $csvInputPath -Force | Out-Null
    Write-Host "   ✓ Created CSV_Input directory" -ForegroundColor Green
} else {
    Write-Host "   ✓ CSV_Input directory already exists" -ForegroundColor Green
}

Write-Host ""

Write-Host "5. Creating CSV_Template directory..." -ForegroundColor Yellow
$csvTemplatePath = "$deployPath\CSV_Template"
if (!(Test-Path $csvTemplatePath)) {
    New-Item -ItemType Directory -Path $csvTemplatePath -Force | Out-Null
    Write-Host "   ✓ Created CSV_Template directory" -ForegroundColor Green
} else {
    Write-Host "   ✓ CSV_Template directory already exists" -ForegroundColor Green
}

Write-Host ""

Write-Host "6. Copying CSV template files..." -ForegroundColor Yellow
# Copy template files from the source project to the deployment
$sourceTemplatePath = "C:\FleetXQ\XQ360.DataMigration\XQ360.DataMigration\CSV_Template"
if (Test-Path $sourceTemplatePath) {
    $templateFiles = Get-ChildItem $sourceTemplatePath -Filter "*.csv"
    if ($templateFiles.Count -gt 0) {
        Copy-Item "$sourceTemplatePath\*.csv" $csvTemplatePath -Force
        Copy-Item "$sourceTemplatePath\*.csv" $csvInputPath -Force
        Write-Host "   ✓ Copied $($templateFiles.Count) CSV template files to both CSV_Template and CSV_Input" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️ No CSV template files found in source" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ⚠️ Source CSV_Template directory not found" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "7. Setting permissions for CSV_Input directory..." -ForegroundColor Yellow
try {
    # Set permissions for IIS to access CSV_Input directory
    icacls $csvInputPath /grant "IIS_IUSRS:(OI)(CI)(F)" /T
    icacls $csvInputPath /grant "NETWORK SERVICE:(OI)(CI)(F)" /T
    icacls $csvInputPath /grant "IIS AppPool\$appPoolName:(OI)(CI)(F)" /T
    
    Write-Host "   ✓ Permissions set successfully" -ForegroundColor Green
}
catch {
    Write-Host "   ❌ Failed to set permissions: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   💡 You may need to run this script as Administrator" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "8. Starting IIS Application Pool..." -ForegroundColor Yellow
try {
    Start-WebAppPool -Name $appPoolName
    Write-Host "   ✓ Application pool started successfully" -ForegroundColor Green
}
catch {
    Write-Host "   ❌ Failed to start application pool: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   💡 You may need to run this script as Administrator" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "9. Testing CSV_Input directory access..." -ForegroundColor Yellow
$testFile = "$csvInputPath\test-write.tmp"
try {
    "Test write access" | Out-File -FilePath $testFile -Encoding UTF8
    Remove-Item $testFile -Force
    Write-Host "   ✓ CSV_Input directory is writable" -ForegroundColor Green
}
catch {
    Write-Host "   ❌ CSV_Input directory is not writable: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   💡 Check permissions on the CSV_Input directory" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "10. Verifying deployment..." -ForegroundColor Yellow
$csvFiles = Get-ChildItem $csvInputPath -Filter "*.csv"
Write-Host "   📁 CSV_Input directory contains $($csvFiles.Count) CSV files:" -ForegroundColor Cyan
foreach ($file in $csvFiles) {
    Write-Host "      - $($file.Name)" -ForegroundColor Gray
}

Write-Host ""

Write-Host "=== DEPLOYMENT COMPLETE ===" -ForegroundColor Green
Write-Host ""
Write-Host "✅ Application deployed successfully!" -ForegroundColor Green
Write-Host "✅ File path issues fixed!" -ForegroundColor Green
Write-Host "✅ CSV_Input directory configured!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Application URL: http://localhost/XQ360Migration" -ForegroundColor Cyan
Write-Host "📁 CSV files location: $csvInputPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Open the application in your web browser" -ForegroundColor Gray
Write-Host "   2. Upload your CSV files through the web interface" -ForegroundColor Gray
Write-Host "   3. Start the migration process" -ForegroundColor Gray
Write-Host ""
Write-Host "🔧 If you encounter any issues:" -ForegroundColor Yellow
Write-Host "   - Check the application logs in the web interface" -ForegroundColor Gray
Write-Host "   - Verify CSV files are in the correct format" -ForegroundColor Gray
Write-Host "   - Ensure database connection is working" -ForegroundColor Gray 