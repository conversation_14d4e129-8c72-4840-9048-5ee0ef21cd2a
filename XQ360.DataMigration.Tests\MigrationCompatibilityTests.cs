using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;
using Xunit;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Tests to verify database is compatible with migration operations
    /// These tests check for the columns that migration code actually uses
    /// </summary>
    public class MigrationCompatibilityTests
    {
        private readonly string _connectionString;

        public MigrationCompatibilityTests()
        {
            _connectionString = TestConfigurationHelper.GetTestConnectionString();
        }

        [Fact]
        public async Task Database_ShouldHaveEssentialTables()
        {
            var essentialTables = new[] { "Vehicle", "Person", "Card" };
            
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            foreach (var tableName in essentialTables)
            {
                var sql = @"
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_NAME = @TableName AND TABLE_TYPE = 'BASE TABLE'";
                
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@TableName", tableName);
                
                var result = await cmd.ExecuteScalarAsync();
                var tableExists = result != null && (int)result > 0;
                Assert.True(tableExists, $"Essential table '{tableName}' does not exist in database");
            }
        }

        [Fact]
        public async Task Database_ShouldBeWritable()
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            
            // Test connectivity
            var sql = "SELECT 1";
            using var cmd = new SqlCommand(sql, connection);
            var result = await cmd.ExecuteScalarAsync();
            
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task VehicleTable_ShouldHaveBasicStructure()
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if Vehicle table has at least an Id column
            var sql = @"
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'Vehicle' AND COLUMN_NAME = 'Id'";
            
            using var cmd = new SqlCommand(sql, connection);
            var result = await cmd.ExecuteScalarAsync();
            var hasId = result != null && (int)result > 0;
            
            Assert.True(hasId, "Vehicle table must have an 'Id' column for migration to work");
        }

        [Fact]
        public async Task Migration_ShouldHaveNecessaryPermissions()
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Basic permission check - try to read from a system view
            var sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
            using var cmd = new SqlCommand(sql, connection);
            var result = await cmd.ExecuteScalarAsync();
            
            Assert.True(result != null && (int)result > 0, "Database connection should allow reading table information");
        }
    }
} 