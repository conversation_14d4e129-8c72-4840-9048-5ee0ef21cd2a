using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;

namespace XQ360.DataMigration.DataSeeder.Tests
{
    /// <summary>
    /// Performance and load tests for DataSeeder implementations
    /// Tests performance characteristics, memory usage, and behavior under load
    /// </summary>
    public class DataSeederPerformanceTests : IDisposable
    {
        private readonly Mock<ILogger<BulkSeederService>> _mockBulkLogger;
        private readonly Mock<ILogger<MigrationPatternSeederService>> _mockMigrationLogger;
        private readonly Mock<IOptions<BulkSeederConfiguration>> _mockOptions;
        private readonly Mock<ISqlDataGenerationService> _mockSqlDataService;
        private readonly Mock<IEnvironmentConfigurationService> _mockEnvironmentService;
        private readonly Mock<IHubContext<MigrationHub>> _mockHubContext;
        private readonly Mock<IApiOrchestrationService> _mockApiOrchestrationService;
        private readonly Mock<IComplexEntityCreationService> _mockComplexEntityService;
        private readonly Mock<IStagingSchemaService> _mockStagingSchemaService;
        private readonly BulkSeederConfiguration _testConfig;

        public DataSeederPerformanceTests()
        {
            _mockBulkLogger = new Mock<ILogger<BulkSeederService>>();
            _mockMigrationLogger = new Mock<ILogger<MigrationPatternSeederService>>();
            _mockOptions = new Mock<IOptions<BulkSeederConfiguration>>();
            _mockSqlDataService = new Mock<ISqlDataGenerationService>();
            _mockEnvironmentService = new Mock<IEnvironmentConfigurationService>();
            _mockHubContext = new Mock<IHubContext<MigrationHub>>();
            _mockApiOrchestrationService = new Mock<IApiOrchestrationService>();
            _mockComplexEntityService = new Mock<IComplexEntityCreationService>();
            _mockStagingSchemaService = new Mock<IStagingSchemaService>();

            _testConfig = TestConfigurationHelper.GetBulkSeederConfiguration();
            _mockOptions.Setup(x => x.Value).Returns(_testConfig);

            SetupMockServices();
        }

        #region Performance Benchmarks

        [Fact]
        public async Task BulkSeederService_SmallLoad_ShouldCompleteWithinTimeLimit()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 100, vehiclesCount: 50);
            var stopwatch = Stopwatch.StartNew();

            // Act
            var result = await service.ExecuteSeederAsync(options);

            // Assert
            stopwatch.Stop();
            Assert.True(stopwatch.ElapsedMilliseconds < 5000, $"Small load took {stopwatch.ElapsedMilliseconds}ms, expected < 5000ms");
            Assert.NotNull(result);
            Assert.True(result.Success);
        }

        [Fact]
        public async Task BulkSeederService_MediumLoad_ShouldCompleteWithinTimeLimit()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 1000, vehiclesCount: 500);
            var stopwatch = Stopwatch.StartNew();

            // Act
            var result = await service.ExecuteSeederAsync(options);

            // Assert
            stopwatch.Stop();
            Assert.True(stopwatch.ElapsedMilliseconds < 15000, $"Medium load took {stopwatch.ElapsedMilliseconds}ms, expected < 15000ms");
            Assert.NotNull(result);
            Assert.True(result.Success);
        }

        [Fact]
        public async Task MigrationPatternSeederService_ApiCalls_ShouldRespectRateLimit()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: 100);
            options.UseApiForPersonCreation = true;
            options.ApiRateLimit = 10; // 10 calls per second
            
            var callTimes = new List<DateTime>();
            _mockApiOrchestrationService.Setup(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .Callback(() => callTimes.Add(DateTime.UtcNow))
                .ReturnsAsync(new ApiOrchestrationResult { Success = true, SuccessfulRequests = 10 });

            var stopwatch = Stopwatch.StartNew();

            // Act
            await service.ExecutePersonDriverSeederAsync(options);

            // Assert
            stopwatch.Stop();
            
            // Verify rate limiting was respected (should take at least 10 seconds for 100 drivers at 10/sec)
            Assert.True(stopwatch.ElapsedMilliseconds >= 1000, "Rate limiting should introduce delays");
            
            // Verify API was called
            _mockApiOrchestrationService.Verify(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }

        #endregion

        #region Memory Usage Tests

        [Fact]
        public async Task BulkSeederService_LargeLoad_ShouldNotExceedMemoryThreshold()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 10000, vehiclesCount: 5000);
            
            var initialMemory = GC.GetTotalMemory(true);

            // Act
            var result = await service.ExecuteSeederAsync(options);

            // Assert
            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = finalMemory - initialMemory;
            var memoryIncreaseMB = memoryIncrease / (1024 * 1024);

            Assert.True(memoryIncreaseMB < 100, $"Memory increase was {memoryIncreaseMB}MB, expected < 100MB");
            Assert.NotNull(result);
        }

        [Fact]
        public async Task MigrationPatternSeederService_LargeLoad_ShouldNotExceedMemoryThreshold()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: 5000, vehiclesCount: 2500);
            
            var initialMemory = GC.GetTotalMemory(true);

            // Act
            var result = await service.ExecutePersonDriverSeederAsync(options);

            // Assert
            var finalMemory = GC.GetTotalMemory(true);
            var memoryIncrease = finalMemory - initialMemory;
            var memoryIncreaseMB = memoryIncrease / (1024 * 1024);

            Assert.True(memoryIncreaseMB < 50, $"Memory increase was {memoryIncreaseMB}MB, expected < 50MB");
            Assert.NotNull(result);
        }

        #endregion

        #region Concurrent Load Tests

        [Fact]
        public async Task BulkSeederService_ConcurrentRequests_ShouldHandleCorrectly()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var tasks = new List<Task<SeederResult>>();
            
            // Create 5 concurrent seeding operations
            for (int i = 0; i < 5; i++)
            {
                var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 100, vehiclesCount: 50);
                tasks.Add(service.ExecuteSeederAsync(options));
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var results = await Task.WhenAll(tasks);

            // Assert
            stopwatch.Stop();
            Assert.True(stopwatch.ElapsedMilliseconds < 30000, $"Concurrent operations took {stopwatch.ElapsedMilliseconds}ms, expected < 30000ms");
            Assert.All(results, result => 
            {
                Assert.NotNull(result);
                Assert.True(result.Success);
                Assert.NotEmpty(result.SessionId);
            });
        }

        [Fact]
        public async Task MigrationPatternSeederService_ConcurrentApiCalls_ShouldHandleCorrectly()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var tasks = new List<Task<SeederResult>>();
            
            // Create 3 concurrent API-based seeding operations
            for (int i = 0; i < 3; i++)
            {
                var options = TestConfigurationHelper.CreateTestMigrationPatternOptions(driversCount: 50);
                options.UseApiForPersonCreation = true;
                tasks.Add(service.ExecutePersonDriverSeederAsync(options));
            }

            var stopwatch = Stopwatch.StartNew();

            // Act
            var results = await Task.WhenAll(tasks);

            // Assert
            stopwatch.Stop();
            Assert.True(stopwatch.ElapsedMilliseconds < 20000, $"Concurrent API operations took {stopwatch.ElapsedMilliseconds}ms, expected < 20000ms");
            Assert.All(results, result => 
            {
                Assert.NotNull(result);
                Assert.True(result.Success);
            });
        }

        #endregion

        #region Stress Tests

        [Fact]
        public async Task BulkSeederService_RepeatedOperations_ShouldMaintainPerformance()
        {
            // Arrange
            var service = CreateBulkSeederService();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 200, vehiclesCount: 100);
            var executionTimes = new List<long>();

            // Act - Run 10 iterations
            for (int i = 0; i < 10; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                var result = await service.ExecuteSeederAsync(options);
                stopwatch.Stop();
                
                executionTimes.Add(stopwatch.ElapsedMilliseconds);
                Assert.NotNull(result);
                Assert.True(result.Success);
            }

            // Assert - Performance should not degrade significantly
            var averageTime = executionTimes.Average();
            var maxTime = executionTimes.Max();
            var minTime = executionTimes.Min();
            
            Assert.True(maxTime - minTime < averageTime * 0.5, 
                $"Performance variance too high. Min: {minTime}ms, Max: {maxTime}ms, Avg: {averageTime:F2}ms");
        }

        [Fact]
        public async Task MigrationPatternSeederService_RepeatedValidations_ShouldMaintainPerformance()
        {
            // Arrange
            var service = CreateMigrationPatternSeederService();
            var executionTimes = new List<long>();

            // Act - Run 20 validation iterations
            for (int i = 0; i < 20; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                var result = await service.ValidateMigrationPatternPrerequisitesAsync();
                stopwatch.Stop();
                
                executionTimes.Add(stopwatch.ElapsedMilliseconds);
                Assert.NotNull(result);
            }

            // Assert - Validation should be consistently fast
            var averageTime = executionTimes.Average();
            Assert.True(averageTime < 1000, $"Average validation time was {averageTime:F2}ms, expected < 1000ms");
            Assert.All(executionTimes, time => Assert.True(time < 2000, $"Individual validation took {time}ms, expected < 2000ms"));
        }

        #endregion

        #region Helper Methods

        private BulkSeederService CreateBulkSeederService()
        {
            return new BulkSeederService(
                _mockBulkLogger.Object,
                _mockOptions.Object,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                _mockComplexEntityService.Object);
        }

        private MigrationPatternSeederService CreateMigrationPatternSeederService()
        {
            return new MigrationPatternSeederService(
                _mockMigrationLogger.Object,
                _mockOptions.Object,
                _mockSqlDataService.Object,
                _mockEnvironmentService.Object,
                _mockHubContext.Object,
                _mockApiOrchestrationService.Object,
                _mockComplexEntityService.Object,
                _mockStagingSchemaService.Object);
        }

        private void SetupMockServices()
        {
            // Setup environment service
            var testEnvironment = TestConfigurationHelper.GetTestEnvironment();
            var testMigrationConfig = TestConfigurationHelper.GetTestConfiguration();
            _mockEnvironmentService.Setup(x => x.CurrentEnvironment).Returns(testEnvironment);
            _mockEnvironmentService.Setup(x => x.CurrentMigrationConfiguration).Returns(testMigrationConfig);

            // Setup SignalR hub context
            var mockClients = new Mock<IHubClients>();
            var mockClientProxy = new Mock<IClientProxy>();
            _mockHubContext.Setup(x => x.Clients).Returns(mockClients.Object);
            mockClients.Setup(x => x.All).Returns(mockClientProxy.Object);

            // Setup SQL data service with realistic delays
            _mockSqlDataService.Setup(x => x.GenerateDriverDataAsync(
                It.IsAny<Guid>(), 
                It.IsAny<int>(), 
                It.IsAny<CancellationToken>()))
                .Returns<Guid, int, CancellationToken>(async (sessionId, count, token) =>
                {
                    // Simulate processing time based on count
                    await Task.Delay(Math.Min(count / 10, 1000), token);
                    return new DataGenerationResult 
                    { 
                        Success = true, 
                        GeneratedRows = count,
                        Duration = TimeSpan.FromMilliseconds(count / 10)
                    };
                });

            _mockSqlDataService.Setup(x => x.GenerateVehicleDataAsync(
                It.IsAny<Guid>(), 
                It.IsAny<int>(), 
                It.IsAny<CancellationToken>()))
                .Returns<Guid, int, CancellationToken>(async (sessionId, count, token) =>
                {
                    await Task.Delay(Math.Min(count / 15, 800), token);
                    return new DataGenerationResult 
                    { 
                        Success = true, 
                        GeneratedRows = count,
                        Duration = TimeSpan.FromMilliseconds(count / 15)
                    };
                });

            _mockSqlDataService.Setup(x => x.ProcessStagedDataAsync(
                It.IsAny<Guid>(), 
                It.IsAny<bool>(), 
                It.IsAny<CancellationToken>()))
                .Returns<Guid, bool, CancellationToken>(async (sessionId, dryRun, token) =>
                {
                    await Task.Delay(500, token);
                    return new ProcessingResult 
                    { 
                        Success = true, 
                        InsertedRows = 100,
                        UpdatedRows = 0
                    };
                });

            // Setup API orchestration with rate limiting simulation
            _mockApiOrchestrationService.Setup(x => x.CreatePersonDriverBatchAsync(
                It.IsAny<IEnumerable<PersonCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .Returns<IEnumerable<PersonCreateRequest>, int, CancellationToken>(async (requests, batchSize, token) =>
                {
                    var count = requests.Count();
                    // Simulate rate limiting delay
                    await Task.Delay(Math.Max(count * 100, 1000), token);
                    return new ApiOrchestrationResult 
                    { 
                        Success = true, 
                        SuccessfulRequests = count,
                        TotalRequests = count
                    };
                });

            _mockApiOrchestrationService.Setup(x => x.ValidateApiConnectivityAsync())
                .Returns<CancellationToken>(async (token) =>
                {
                    await Task.Delay(200, token);
                    return true;
                });

            // Setup complex entity service
            _mockComplexEntityService.Setup(x => x.CreateVehicleBatchAsync(
                It.IsAny<Guid>(),
                It.IsAny<IEnumerable<VehicleCreateRequest>>(),
                It.IsAny<CancellationToken>()))
                .Returns<Guid, IEnumerable<VehicleCreateRequest>, CancellationToken>(async (sessionId, requests, token) =>
                {
                    var count = requests.Count();
                    await Task.Delay(count * 50, token);
                    return new ComplexEntityResult 
                    { 
                        Success = true, 
                        SuccessfulRequests = count,
                        TotalRequests = count
                    };
                });

            _mockComplexEntityService.Setup(x => x.CreateCardAccessPermissionsBatchAsync(
                It.IsAny<Guid>(),
                It.IsAny<IEnumerable<CardAccessCreateRequest>>(),
                It.IsAny<CancellationToken>()))
                .Returns<Guid, IEnumerable<CardAccessCreateRequest>, CancellationToken>(async (sessionId, requests, token) =>
                {
                    var count = requests.Count();
                    await Task.Delay(count * 30, token);
                    return new ComplexEntityResult 
                    { 
                        Success = true, 
                        SuccessfulRequests = count,
                        TotalRequests = count
                    };
                });
        }

        public void Dispose()
        {
            // Cleanup any resources if needed
        }

        #endregion
    }
}
