import { ApiBaseService } from './api-base'
import type { Customer } from '@/types/customer'

export interface CustomerListRequest {
  dealerId: string
  query?: string
  pageNumber?: number
  pageSize?: number
  activeOnly?: boolean
}

export interface CustomerListResponse {
  customers: Customer[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
}

export interface CreateCustomerRequest {
  dealerId: string
  companyName: string
  countryId: string
  contactNumber?: string
  email?: string
  address?: string
  description?: string
  contractNumber?: string
  contractDate?: string
}

export interface CustomerValidationRequest {
  dealerId: string
  customerId: string
}

export interface CustomerValidationResponse {
  isValid: boolean
  exists: boolean
  isActive: boolean
  belongsToDealer: boolean
  customer?: Customer
  errorMessage?: string
  warnings: string[]
}

export class CustomerService extends ApiBaseService {
  constructor() {
    super('/api')
  }

  /**
   * Get customers for a specific dealer
   */
  async getCustomers(request: CustomerListRequest): Promise<CustomerListResponse> {
    try {
      const params = new URLSearchParams({
        dealerId: request.dealerId
      })
      
      if (request.query) params.append('query', request.query)
      if (request.pageNumber) params.append('pageNumber', request.pageNumber.toString())
      if (request.pageSize) params.append('pageSize', request.pageSize.toString())
      if (request.activeOnly !== undefined) params.append('activeOnly', request.activeOnly.toString())

      return await this.get<CustomerListResponse>(`/customers?${params.toString()}`)
    } catch (error) {
      console.error('Failed to fetch customers:', error)
      throw error
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomerById(id: string): Promise<Customer> {
    try {
      return await this.get<Customer>(`/customers/${id}`)
    } catch (error) {
      console.error(`Failed to fetch customer ${id}:`, error)
      throw error
    }
  }

  /**
   * Create a new customer
   */
  async createCustomer(request: CreateCustomerRequest): Promise<Customer> {
    try {
      return await this.post<Customer>('/customers', request)
    } catch (error) {
      console.error('Failed to create customer:', error)
      throw error
    }
  }

  /**
   * Validate customer-dealer relationship
   */
  async validateCustomer(request: CustomerValidationRequest): Promise<CustomerValidationResponse> {
    try {
      const params = new URLSearchParams({
        dealerId: request.dealerId,
        customerId: request.customerId
      })

      return await this.get<CustomerValidationResponse>(`/customers/validate?${params.toString()}`)
    } catch (error) {
      console.error('Failed to validate customer:', error)
      throw error
    }
  }

  /**
   * Search customers by name for a dealer
   */
  async searchCustomers(dealerId: string, query: string, limit: number = 10): Promise<Customer[]> {
    try {
      const response = await this.getCustomers({
        dealerId,
        query,
        pageSize: limit,
        pageNumber: 1,
        activeOnly: true
      })
      
      return response.customers
    } catch (error) {
      console.error('Failed to search customers:', error)
      throw error
    }
  }

  /**
   * Check if customer exists for dealer
   */
  async customerExists(dealerId: string, customerName: string): Promise<boolean> {
    try {
      const customers = await this.searchCustomers(dealerId, customerName, 5)
      return customers.some(customer => 
        customer.companyName.toLowerCase() === customerName.toLowerCase()
      )
    } catch (error) {
      console.error('Failed to check if customer exists:', error)
      return false
    }
  }

  /**
   * Get customer statistics
   */
  async getCustomerStats(customerId: string): Promise<{
    totalVehicles: number
    totalDrivers: number
    activeVehicles: number
    activeDrivers: number
    lastImportDate?: string
  }> {
    try {
      return await this.get<any>(`/customers/${customerId}/stats`)
    } catch (error) {
      console.error(`Failed to get customer stats for ${customerId}:`, error)
      // Return default stats on error
      return {
        totalVehicles: 0,
        totalDrivers: 0,
        activeVehicles: 0,
        activeDrivers: 0
      }
    }
  }

  /**
   * Get available countries for customer creation
   */
  async getCountries(): Promise<Array<{ id: string; name: string; code: string }>> {
    try {
      return await this.get<any[]>('/customers/countries')
    } catch (error) {
      console.error('Failed to fetch countries:', error)
      // Return common countries as fallback
      return [
        { id: '1', name: 'Australia', code: 'AU' },
        { id: '2', name: 'New Zealand', code: 'NZ' },
        { id: '3', name: 'United States', code: 'US' },
        { id: '4', name: 'Canada', code: 'CA' },
        { id: '5', name: 'United Kingdom', code: 'GB' }
      ]
    }
  }

  /**
   * Validate customer creation data
   */
  async validateCustomerData(request: CreateCustomerRequest): Promise<{
    isValid: boolean
    errors: Record<string, string[]>
    warnings: string[]
  }> {
    try {
      return await this.post<any>('/customers/validate', request)
    } catch (error) {
      console.error('Failed to validate customer data:', error)
      throw error
    }
  }

  /**
   * Get recently accessed customers for a dealer
   */
  async getRecentCustomers(dealerId: string, limit: number = 5): Promise<Customer[]> {
    try {
      const recentCustomerIds = this.getRecentCustomerIds(dealerId)
      
      if (recentCustomerIds.length === 0) {
        return []
      }

      // Fetch customer details for recent IDs
      const customers = await Promise.all(
        recentCustomerIds.slice(0, limit).map(async (id) => {
          try {
            return await this.getCustomerById(id)
          } catch {
            return null
          }
        })
      )

      return customers.filter(customer => customer !== null) as Customer[]
    } catch (error) {
      console.error('Failed to get recent customers:', error)
      return []
    }
  }

  /**
   * Add customer to recent list for dealer
   */
  addToRecentCustomers(dealerId: string, customerId: string): void {
    try {
      const key = `recent_customers_${dealerId}`
      const recentIds = this.getRecentCustomerIds(dealerId)
      const updatedIds = [customerId, ...recentIds.filter(id => id !== customerId)].slice(0, 10)
      localStorage.setItem(key, JSON.stringify(updatedIds))
    } catch (error) {
      console.error('Failed to add customer to recent list:', error)
    }
  }

  /**
   * Get recent customer IDs for dealer from storage
   */
  private getRecentCustomerIds(dealerId: string): string[] {
    try {
      const key = `recent_customers_${dealerId}`
      const stored = localStorage.getItem(key)
      return stored ? JSON.parse(stored) : []
    } catch {
      return []
    }
  }

  /**
   * Clear recent customers list for dealer
   */
  clearRecentCustomers(dealerId: string): void {
    const key = `recent_customers_${dealerId}`
    localStorage.removeItem(key)
  }
}
