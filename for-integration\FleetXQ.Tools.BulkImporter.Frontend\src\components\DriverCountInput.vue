<template>
  <div class="driver-count-input">
    <div class="mb-3">
      <label for="driverCount" class="form-label">
        No. of drivers ({{ displayCount }})
      </label>
      <input 
        type="number" 
        id="driverCount" 
        class="form-control" 
        :class="{ 'is-invalid': validationError }"
        v-model.number="driverCount"
        @input="onInput"
        @blur="onBlur"
        :min="minCount"
        :max="maxCount"
        placeholder="Enter driver count"
        required
      >
      
      <div v-if="validationError" class="invalid-feedback">
        {{ validationError }}
      </div>
      
      <div class="form-text d-flex justify-content-between align-items-center">
        <span>Range: {{ minCount.toLocaleString() }} - {{ maxCount.toLocaleString() }} drivers</span>
        <span v-if="environmentLimit && environmentLimit < maxCount" class="text-warning">
          <i class="fas fa-exclamation-triangle me-1"></i>
          Environment limit: {{ environmentLimit.toLocaleString() }}
        </span>
      </div>
    </div>

    <!-- Real-time Validation Feedback -->
    <div v-if="driverCount && !validationError" class="validation-feedback">
      <div class="alert alert-info">
        <div class="d-flex align-items-center mb-2">
          <i class="fas fa-info-circle me-2"></i>
          <strong>Driver Count Validation</strong>
        </div>
        
        <div class="validation-details">
          <div class="row">
            <div class="col-md-4">
              <div class="metric">
                <span class="metric-label">Drivers:</span>
                <span class="metric-value">{{ driverCount.toLocaleString() }}</span>
              </div>
            </div>
            <div class="col-md-4">
              <div class="metric">
                <span class="metric-label">Processing Time:</span>
                <span class="metric-value">{{ estimatedProcessingTime }}</span>
              </div>
            </div>
            <div class="col-md-4">
              <div class="metric">
                <span class="metric-label">Driver:Vehicle Ratio:</span>
                <span class="metric-value">{{ driverVehicleRatio }}</span>
              </div>
            </div>
          </div>
          
          <div class="progress-indicator mt-3">
            <div class="d-flex justify-content-between mb-2">
              <span class="small text-muted">Capacity Usage</span>
              <span class="small text-muted">{{ capacityPercentage }}%</span>
            </div>
            <div class="progress">
              <div 
                class="progress-bar" 
                :class="progressBarClass"
                role="progressbar" 
                :style="{ width: capacityPercentage + '%' }"
                :aria-valuenow="capacityPercentage" 
                aria-valuemin="0" 
                :aria-valuemax="100"
              ></div>
            </div>
          </div>
          
          <!-- Driver-Vehicle Ratio Analysis -->
          <div v-if="vehicleCount" class="ratio-analysis mt-3">
            <div class="alert" :class="ratioAlertClass">
              <div class="d-flex align-items-center mb-2">
                <i :class="ratioIcon" class="me-2"></i>
                <strong>{{ ratioAnalysis.title }}</strong>
              </div>
              <p class="mb-1">{{ ratioAnalysis.message }}</p>
              <div v-if="ratioAnalysis.recommendations.length > 0" class="recommendations mt-2">
                <ul class="list-unstyled mb-0">
                  <li v-for="rec in ratioAnalysis.recommendations" :key="rec" class="small mb-1">
                    <i class="fas fa-arrow-right me-2"></i>
                    {{ rec }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- Performance Warnings -->
          <div v-if="performanceWarnings.length > 0" class="performance-warnings mt-3">
            <div v-for="warning in performanceWarnings" :key="warning.type" class="alert alert-warning alert-sm mb-2">
              <i :class="warning.icon" class="me-2"></i>
              {{ warning.message }}
            </div>
          </div>
          
          <!-- Recommendations -->
          <div v-if="recommendations.length > 0" class="recommendations mt-3">
            <h6 class="text-muted mb-2">
              <i class="fas fa-lightbulb me-1"></i>
              Recommendations
            </h6>
            <ul class="list-unstyled mb-0">
              <li v-for="recommendation in recommendations" :key="recommendation" class="small text-muted mb-1">
                <i class="fas fa-arrow-right me-2"></i>
                {{ recommendation }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Large Volume Warning -->
    <div v-if="driverCount && driverCount >= largeVolumeThreshold" class="large-volume-warning">
      <div class="alert alert-warning">
        <div class="d-flex align-items-center mb-2">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>Large Volume Driver Operation</strong>
        </div>
        <p class="mb-2">
          You are about to import a large number of drivers ({{ driverCount.toLocaleString() }}). 
          This operation may require additional processing time and validation.
        </p>
        <ul class="mb-2">
          <li>Estimated processing time: {{ estimatedProcessingTime }}</li>
          <li>Driver data validation will be more thorough</li>
          <li>Consider breaking into smaller batches if possible</li>
          <li>Ensure driver data quality is high to minimize errors</li>
        </ul>
        <div class="form-check">
          <input 
            class="form-check-input" 
            type="checkbox" 
            id="acknowledgeDriverWarning"
            v-model="largeVolumeAcknowledged"
          >
          <label class="form-check-label" for="acknowledgeDriverWarning">
            I understand the implications of this large volume driver operation
          </label>
        </div>
      </div>
    </div>

    <!-- Driver Data Requirements -->
    <div v-if="driverCount && driverCount > 0" class="driver-requirements mt-3">
      <div class="alert alert-light">
        <div class="d-flex align-items-center mb-2">
          <i class="fas fa-clipboard-list me-2"></i>
          <strong>Driver Data Requirements</strong>
        </div>
        <div class="row">
          <div class="col-md-6">
            <h6 class="small fw-bold mb-2">Required Fields:</h6>
            <ul class="list-unstyled small">
              <li><i class="fas fa-check text-success me-2"></i>Driver License Number</li>
              <li><i class="fas fa-check text-success me-2"></i>Full Name</li>
              <li><i class="fas fa-check text-success me-2"></i>Date of Birth</li>
              <li><i class="fas fa-check text-success me-2"></i>License Expiry Date</li>
            </ul>
          </div>
          <div class="col-md-6">
            <h6 class="small fw-bold mb-2">Optional but Recommended:</h6>
            <ul class="list-unstyled small">
              <li><i class="fas fa-info text-info me-2"></i>Contact Information</li>
              <li><i class="fas fa-info text-info me-2"></i>Emergency Contact</li>
              <li><i class="fas fa-info text-info me-2"></i>Driver Classifications</li>
              <li><i class="fas fa-info text-info me-2"></i>Training Records</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
const props = defineProps<{
  modelValue?: number | null
  vehicleCount?: number | null
  minCount?: number
  maxCount?: number
  environmentLimit?: number | null
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: number | null]
  'validation-change': [isValid: boolean]
}>()

// Local state
const driverCount = ref<number | null>(null)
const validationError = ref('')
const largeVolumeAcknowledged = ref(false)

// Constants
const defaultMinCount = 1
const defaultMaxCount = 5000
const largeVolumeThreshold = 2500

// Computed properties
const minCount = computed(() => props.minCount || defaultMinCount)
const maxCount = computed(() => {
  if (props.environmentLimit && props.environmentLimit < (props.maxCount || defaultMaxCount)) {
    return props.environmentLimit
  }
  return props.maxCount || defaultMaxCount
})

const environmentLimit = computed(() => props.environmentLimit)
const vehicleCount = computed(() => props.vehicleCount)

const displayCount = computed(() => {
  return driverCount.value?.toLocaleString() || 'xxxx'
})

const capacityPercentage = computed(() => {
  if (!driverCount.value) return 0
  return Math.min(Math.round((driverCount.value / maxCount.value) * 100), 100)
})

const progressBarClass = computed(() => {
  const percentage = capacityPercentage.value
  if (percentage >= 90) return 'bg-danger'
  if (percentage >= 75) return 'bg-warning'
  if (percentage >= 50) return 'bg-info'
  return 'bg-success'
})

const estimatedProcessingTime = computed(() => {
  if (!driverCount.value) return 'N/A'
  
  // Rough estimation: 200 drivers per minute (faster than vehicles due to simpler data)
  const minutes = Math.ceil(driverCount.value / 200)
  
  if (minutes < 60) {
    return `~${minutes} minute${minutes !== 1 ? 's' : ''}`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `~${hours}h ${remainingMinutes}m`
  }
})

const driverVehicleRatio = computed(() => {
  if (!driverCount.value || !vehicleCount.value) return 'N/A'
  const ratio = vehicleCount.value / driverCount.value
  return `1:${ratio.toFixed(1)}`
})

const ratioAnalysis = computed(() => {
  if (!driverCount.value || !vehicleCount.value) {
    return {
      title: 'Ratio Analysis',
      message: 'Vehicle count needed for ratio analysis',
      type: 'info',
      recommendations: []
    }
  }
  
  const ratio = vehicleCount.value / driverCount.value
  
  if (ratio < 1) {
    return {
      title: 'More Drivers than Vehicles',
      message: `You have ${driverCount.value.toLocaleString()} drivers for ${vehicleCount.value.toLocaleString()} vehicles. This may indicate shared vehicle usage or fleet efficiency optimization.`,
      type: 'warning',
      recommendations: [
        'Verify this ratio is intentional for your fleet model',
        'Consider driver rotation schedules',
        'Review vehicle utilization patterns'
      ]
    }
  } else if (ratio <= 2) {
    return {
      title: 'Balanced Driver-Vehicle Ratio',
      message: `Good ratio of ${ratio.toFixed(1)} vehicles per driver. This suggests efficient fleet utilization.`,
      type: 'success',
      recommendations: [
        'Monitor driver utilization rates',
        'Plan for backup drivers during peak times'
      ]
    }
  } else if (ratio <= 5) {
    return {
      title: 'High Vehicle-to-Driver Ratio',
      message: `${ratio.toFixed(1)} vehicles per driver suggests either specialized drivers or automated/shared vehicle fleet.`,
      type: 'info',
      recommendations: [
        'Ensure drivers have proper certifications for multiple vehicle types',
        'Plan driver training for vehicle variety',
        'Consider driver scheduling optimization'
      ]
    }
  } else {
    return {
      title: 'Very High Vehicle-to-Driver Ratio',
      message: `${ratio.toFixed(1)} vehicles per driver is unusually high. Please verify these numbers are correct.`,
      type: 'warning',
      recommendations: [
        'Double-check vehicle and driver counts',
        'Consider if some vehicles are automated or specialized',
        'Plan for driver training and certification requirements'
      ]
    }
  }
})

const ratioAlertClass = computed(() => {
  switch (ratioAnalysis.value.type) {
    case 'success': return 'alert-success'
    case 'warning': return 'alert-warning'
    case 'info': return 'alert-info'
    default: return 'alert-light'
  }
})

const ratioIcon = computed(() => {
  switch (ratioAnalysis.value.type) {
    case 'success': return 'fas fa-check-circle'
    case 'warning': return 'fas fa-exclamation-triangle'
    case 'info': return 'fas fa-info-circle'
    default: return 'fas fa-chart-bar'
  }
})

const performanceWarnings = computed(() => {
  if (!driverCount.value) return []
  
  const warnings: Array<{ type: string; message: string; icon: string }> = []
  
  if (driverCount.value >= 4000) {
    warnings.push({
      type: 'very-large',
      message: 'Very large driver dataset - consider splitting into multiple batches for optimal performance',
      icon: 'fas fa-exclamation-circle'
    })
  } else if (driverCount.value >= largeVolumeThreshold) {
    warnings.push({
      type: 'large',
      message: 'Large driver dataset - additional validation time required',
      icon: 'fas fa-info-circle'
    })
  }
  
  if (capacityPercentage.value >= 90) {
    warnings.push({
      type: 'capacity',
      message: 'Approaching environment capacity limit for driver processing',
      icon: 'fas fa-tachometer-alt'
    })
  }
  
  return warnings
})

const recommendations = computed(() => {
  if (!driverCount.value) return []
  
  const recommendations: string[] = []
  
  if (driverCount.value >= 4000) {
    recommendations.push('Consider breaking into batches of 1,000-2,500 drivers')
    recommendations.push('Schedule during off-peak hours for optimal performance')
    recommendations.push('Ensure driver data validation rules are optimized')
  } else if (driverCount.value >= largeVolumeThreshold) {
    recommendations.push('Monitor system performance during driver validation')
    recommendations.push('Ensure driver data quality is high to minimize validation errors')
  }
  
  if (driverCount.value >= 1000) {
    recommendations.push('Enable detailed progress tracking for driver processing')
    recommendations.push('Prepare for extended validation time due to driver-specific checks')
  }
  
  return recommendations
})

const isValid = computed(() => {
  if (!driverCount.value) return false
  
  const hasValidationError = !!validationError.value
  const needsAcknowledgment = driverCount.value >= largeVolumeThreshold && !largeVolumeAcknowledged.value
  
  return !hasValidationError && !needsAcknowledgment
})

// Methods
const onInput = () => {
  validateInput()
  emit('update:modelValue', driverCount.value)
}

const onBlur = () => {
  validateInput()
}

const validateInput = () => {
  validationError.value = ''
  
  if (driverCount.value === null || driverCount.value === undefined) {
    validationError.value = 'Driver count is required'
    return false
  }
  
  if (!Number.isInteger(driverCount.value)) {
    validationError.value = 'Driver count must be a whole number'
    return false
  }
  
  if (driverCount.value < minCount.value) {
    validationError.value = `Driver count must be at least ${minCount.value}`
    return false
  }
  
  if (driverCount.value > maxCount.value) {
    validationError.value = `Driver count cannot exceed ${maxCount.value.toLocaleString()}`
    return false
  }
  
  return true
}

const validate = () => {
  const inputValid = validateInput()
  
  if (!inputValid) return false
  
  if (driverCount.value && driverCount.value >= largeVolumeThreshold && !largeVolumeAcknowledged.value) {
    validationError.value = 'Please acknowledge the large volume driver operation warning'
    return false
  }
  
  return true
}

// Watchers
watch(isValid, (newValue) => {
  emit('validation-change', newValue)
})

watch(() => props.modelValue, (newValue) => {
  driverCount.value = newValue || null
  if (newValue) {
    validateInput()
  }
})

watch(largeVolumeAcknowledged, () => {
  if (driverCount.value && driverCount.value >= largeVolumeThreshold) {
    validateInput()
  }
})

// Lifecycle
onMounted(() => {
  if (props.modelValue) {
    driverCount.value = props.modelValue
    validateInput()
  }
})

// Expose validation method for parent components
defineExpose({
  validate,
  isValid
})
</script>

<style scoped>
.driver-count-input {
  .validation-feedback {
    .alert {
      border-left: 4px solid var(--bs-info);
    }
    
    .metric {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.5rem;
      
      .metric-label {
        font-weight: 500;
        color: var(--bs-secondary);
      }
      
      .metric-value {
        font-weight: bold;
        color: var(--bs-primary);
      }
    }
    
    .progress {
      height: 0.5rem;
    }
    
    .alert-sm {
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
    }
  }
  
  .ratio-analysis {
    .alert {
      border-left: 4px solid;
    }
    
    .alert-success {
      border-left-color: var(--bs-success);
    }
    
    .alert-warning {
      border-left-color: var(--bs-warning);
    }
    
    .alert-info {
      border-left-color: var(--bs-info);
    }
  }
  
  .large-volume-warning {
    .alert {
      border-left: 4px solid var(--bs-warning);
    }
    
    ul {
      padding-left: 1.5rem;
    }
    
    .form-check {
      margin-top: 1rem;
      padding: 0.75rem;
      background-color: rgba(var(--bs-warning-rgb), 0.1);
      border-radius: 0.25rem;
    }
  }
  
  .driver-requirements {
    .alert {
      border-left: 4px solid var(--bs-secondary);
    }
  }
  
  .performance-warnings {
    .alert {
      border: 1px solid rgba(var(--bs-warning-rgb), 0.3);
      background-color: rgba(var(--bs-warning-rgb), 0.1);
    }
  }
  
  .recommendations {
    padding: 0.75rem;
    background-color: rgba(var(--bs-info-rgb), 0.05);
    border-radius: 0.25rem;
    border: 1px solid rgba(var(--bs-info-rgb), 0.2);
  }
}
</style>
