using System.ComponentModel.DataAnnotations;

namespace XQ360.DataMigration.Web.Models;

/// <summary>
/// View model for the bulk seeder wizard
/// </summary>
public class BulkSeederViewModel
{
    /// <summary>
    /// Selected dealer for the seeding operation
    /// </summary>
    public DealerInfo? SelectedDealer { get; set; }

    /// <summary>
    /// Selected customer for the seeding operation
    /// </summary>
    public CustomerInfo? SelectedCustomer { get; set; }

    /// <summary>
    /// Available dealers for selection
    /// </summary>
    public List<DealerInfo> Dealers { get; set; } = new();

    /// <summary>
    /// Available customers for selection
    /// </summary>
    public List<CustomerInfo> Customers { get; set; } = new();

    /// <summary>
    /// Number of vehicles to generate/seed
    /// </summary>
    [Range(1, 1000000, ErrorMessage = "Vehicle count must be between 1 and 1,000,000")]
    public int? VehicleCount { get; set; }

    /// <summary>
    /// Number of drivers to generate/seed
    /// </summary>
    [Range(1, 1000000, ErrorMessage = "Driver count must be between 1 and 1,000,000")]
    public int? DriverCount { get; set; }



    /// <summary>
    /// Current environment being used (from existing system)
    /// </summary>
    public string? CurrentEnvironment { get; set; }

    /// <summary>
    /// Available environments for selection
    /// </summary>
    public List<EnvironmentOption> AvailableEnvironments { get; set; } = new();

    /// <summary>
    /// Selected environment key for the seeding operation
    /// </summary>
    public string? SelectedEnvironment { get; set; }

    /// <summary>
    /// Current session ID if a seeding operation is active
    /// </summary>
    public string? ActiveSessionId { get; set; }

    /// <summary>
    /// Whether a seeding operation is currently in progress
    /// </summary>
    public bool IsImporting { get; set; }


}

/// <summary>
/// Information about a dealer
/// </summary>
public class DealerInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Subdomain { get; set; } = string.Empty;
    public string DisplayName => $"{Name} ({Subdomain})";
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Information about a customer
/// </summary>
public class CustomerInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? ContactName { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string DealerId { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}



/// <summary>
/// Request model for creating a new seeding session
/// </summary>
public class CreateSeederSessionRequest
{
    [Required]
    public string DealerId { get; set; } = string.Empty;

    [Required]
    public string CustomerId { get; set; } = string.Empty;

    [Range(1, 1000000)]
    public int VehicleCount { get; set; }

    [Range(1, 1000000)]
    public int DriverCount { get; set; }

    public bool GenerateData { get; set; } = true;
    public bool DryRun { get; set; } = false;
}



/// <summary>
/// Information about an available environment option
/// </summary>
public class EnvironmentOption
{
    public string Key { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
