@model XQ360.DataMigration.Web.Models.BulkSeederViewModel
@{
    ViewData["Title"] = "Data Seeder";
}

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Form Column -->
        <div class="col-lg-8">
            <!-- Import Progress Tracker (shown when import is active) -->
            @if (!string.IsNullOrEmpty(Model.ActiveSessionId))
            {
                <div class="mb-4">
                    <div id="progressTracker" class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Seeding Operation in Progress
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    0%
                                </div>
                            </div>
                            <div id="progressStatus" class="text-muted">Initializing...</div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelSeeding()">
                                    <i class="fas fa-stop me-1"></i>
                                    Cancel Operation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Simplified Seeder Form -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        Bulk Data Seeder Configuration
                    </h4>
                </div>
                
                <div class="card-body">
                    <form id="seederForm" asp-action="CreateSession" asp-controller="Seeder" method="post">
                        <!-- Environment Selection -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-server me-2"></i>
                                Environment
                            </label>
                            <select asp-for="SelectedEnvironment" class="form-select" id="environmentSelect" required>
                                <option value="">Select an environment...</option>
                                @foreach (var env in Model.AvailableEnvironments)
                                {
                                    <option value="@env.Key" data-description="@env.Description" 
                                            selected="@(env.Key == Model.SelectedEnvironment)">
                                        @env.DisplayName
                                    </option>
                                }
                            </select>
                            <div class="form-text">
                                <strong>Current:</strong> @Model.CurrentEnvironment
                            </div>
                        </div>

                        <!-- Dealer Selection -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-building me-2"></i>
                                Dealer
                            </label>
                            <div id="dealer-selection-container">
                                <input type="text" id="dealer-input" class="form-control" placeholder="Enter dealer name" required>
                                <div id="selected-dealer-display" class="mt-2" style="display: none;">
                                    <div class="alert alert-success d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong id="selected-dealer-name"></strong>
                                            <div class="small text-muted" id="selected-dealer-subdomain"></div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearDealer()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="selected-dealer-id" name="SelectedDealer.Id" value="@Model.SelectedDealer?.Id" />
                            <input type="hidden" id="selected-dealer-name-hidden" name="SelectedDealer.Name" value="@Model.SelectedDealer?.Name" />
                        </div>

                        <!-- Customer Selection -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-users me-2"></i>
                                Customer
                            </label>
                            <div id="customer-selection-container">
                                <input type="text" id="customer-input" class="form-control" placeholder="Enter customer name" required>
                                <div id="selected-customer-display" class="mt-2" style="display: none;">
                                    <div class="alert alert-success d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong id="selected-customer-name"></strong>
                                            <div class="small text-muted" id="selected-customer-contact"></div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearCustomer()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="selected-customer-id" name="SelectedCustomer.Id" value="@Model.SelectedCustomer?.Id" />
                            <input type="hidden" id="selected-customer-name-hidden" name="SelectedCustomer.Name" value="@Model.SelectedCustomer?.Name" />
                        </div>

                        <!-- Vehicle Count -->
                        <div class="mb-4">
                            <label for="vehicleCount" class="form-label">
                                <i class="fas fa-car me-2"></i>
                                Vehicle Count
                            </label>
                            <input type="number" 
                                   id="vehicleCount" 
                                   name="VehicleCount"
                                   class="form-control" 
                                   value="@Model.VehicleCount"
                                   min="1"
                                   max="100000"
                                   placeholder="Enter vehicle count"
                                   required>
                            <div class="form-text">Enter the number of vehicles to generate (1 - 100,000)</div>
                        </div>

                        <!-- Driver Count -->
                        <div class="mb-4">
                            <label for="driverCount" class="form-label">
                                <i class="fas fa-id-card me-2"></i>
                                Driver Count
                            </label>
                            <input type="number" 
                                   id="driverCount" 
                                   name="DriverCount"
                                   class="form-control" 
                                   value="@Model.DriverCount"
                                   min="1"
                                   max="200000"
                                   placeholder="Enter driver count"
                                   required>
                            <div class="form-text">Enter the number of drivers to generate (1 - 200,000)</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="validateForm()">
                                <i class="fas fa-check-circle me-1"></i>
                                Validate
                            </button>
                            
                            <button type="submit" class="btn btn-primary" id="startSeedingBtn">
                                <i class="fas fa-play me-1"></i>
                                Start Seeding Operation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Configuration Summary Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 1rem;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-ul me-2"></i>
                            Configuration Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="summary-environment" class="mb-3">
                            <h6>Environment</h6>
                            <div class="text-muted">@Model.CurrentEnvironment</div>
                        </div>
                        
                        <div id="summary-dealer" class="mb-3">
                            <h6>Dealer</h6>
                            <div class="text-muted">Not selected</div>
                        </div>
                        
                        <div id="summary-customer" class="mb-3">
                            <h6>Customer</h6>
                            <div class="text-muted">Not selected</div>
                        </div>
                        
                        <div id="summary-counts" class="mb-3">
                            <h6>Data to Generate</h6>
                            <div class="text-muted">
                                <div id="summary-vehicles">0 vehicles</div>
                                <div id="summary-drivers">0 drivers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            initializeSeederForm();
        });

        function initializeSeederForm() {
            // Initialize with existing selections if available
            @if (Model.SelectedDealer != null) {
                selectDealer('@Model.SelectedDealer.Id', '@Model.SelectedDealer.Name', '@Model.SelectedDealer.Subdomain');
            }
            @if (Model.SelectedCustomer != null) {
                selectCustomer('@Model.SelectedCustomer.Id', '@Model.SelectedCustomer.Name', '@Model.SelectedCustomer.ContactName', '@Model.SelectedCustomer.ContactEmail');
            }

            // Handle form input changes for summary updates
            $('#dealer-input, #customer-input, #vehicleCount, #driverCount').on('input change', updateSummary);
            updateSummary();
        }

        function selectDealer(id, name, subdomain) {
            $('#selected-dealer-id').val(id);
            $('#selected-dealer-name-hidden').val(name);
            $('#selected-dealer-name').text(name);
            $('#selected-dealer-subdomain').text(subdomain);
            $('#dealer-input').hide();
            $('#selected-dealer-display').show();
            updateSummary();
        }

        function clearDealer() {
            $('#selected-dealer-id').val('');
            $('#selected-dealer-name-hidden').val('');
            $('#dealer-input').val('').show();
            $('#selected-dealer-display').hide();
            clearCustomer(); // Clear customer when dealer changes
            updateSummary();
        }

        function selectCustomer(id, name, contactName, contactEmail) {
            $('#selected-customer-id').val(id);
            $('#selected-customer-name-hidden').val(name);
            $('#selected-customer-name').text(name);
            
            let contactInfo = contactName || '';
            if (contactEmail) {
                contactInfo += contactInfo ? ' (' + contactEmail + ')' : contactEmail;
            }
            $('#selected-customer-contact').text(contactInfo || 'No contact information');
            
            $('#customer-input').hide();
            $('#selected-customer-display').show();
            updateSummary();
        }

        function clearCustomer() {
            $('#selected-customer-id').val('');
            $('#selected-customer-name-hidden').val('');
            $('#customer-input').val('').show();
            $('#selected-customer-display').hide();
            updateSummary();
        }

        function updateSummary() {
            // Update dealer summary
            const dealerName = $('#selected-dealer-name').text();
            $('#summary-dealer .text-muted').text(dealerName || 'Not selected');

            // Update customer summary
            const customerName = $('#selected-customer-name').text();
            $('#summary-customer .text-muted').text(customerName || 'Not selected');

            // Update counts summary
            const vehicleCount = parseInt($('#vehicleCount').val()) || 0;
            const driverCount = parseInt($('#driverCount').val()) || 0;
            $('#summary-vehicles').text(vehicleCount.toLocaleString() + ' vehicles');
            $('#summary-drivers').text(driverCount.toLocaleString() + ' drivers');
        }

        function validateForm() {
            const errors = [];
            
            if (!$('#selected-dealer-id').val()) {
                errors.push('Please select a dealer');
            }
            
            if (!$('#selected-customer-id').val()) {
                errors.push('Please select a customer');
            }
            
            const vehicleCount = parseInt($('#vehicleCount').val()) || 0;
            if (vehicleCount <= 0) {
                errors.push('Vehicle count must be greater than 0');
            }
            
            const driverCount = parseInt($('#driverCount').val()) || 0;
            if (driverCount <= 0) {
                errors.push('Driver count must be greater than 0');
            }

            if (errors.length > 0) {
                alert('Validation errors:\n\n' + errors.join('\n'));
                return false;
            }

            alert('Configuration is valid!');
            return true;
        }

        function cancelSeeding() {
            if (confirm('Are you sure you want to cancel the seeding operation?')) {
                // In a real implementation, this would make an API call to cancel
                $('#progressTracker').hide();
                alert('Seeding operation cancelled');
            }
        }

        // Form submission handler
        $('#seederForm').on('submit', function(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return false;
            }

            // Simple dealer and customer name input for now
            // In a real implementation, you would integrate with your actual dealer/customer selection logic
            if (!$('#selected-dealer-id').val()) {
                const dealerName = $('#dealer-input').val().trim();
                if (dealerName) {
                    selectDealer('manual-' + Date.now(), dealerName, 'manual-entry');
                }
            }

            if (!$('#selected-customer-id').val()) {
                const customerName = $('#customer-input').val().trim();
                if (customerName) {
                    selectCustomer('manual-' + Date.now(), customerName, '', '');
                }
            }

            // Submit the form
            this.submit();
        });
    </script>
}
